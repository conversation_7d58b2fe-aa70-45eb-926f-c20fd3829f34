import React, { memo, useMemo, useCallback } from 'react';
import { <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, Loader2 } from 'lucide-react';
import { useDroppable } from '@dnd-kit/core';
import { SavedChart } from '@/types/chartTypes';
import { TabularDataItem } from '@/services/api/chart/chartTypes';
import { LazyDataTable } from './LazyComponents';
import { ResizableCard } from './ResizableCard';

interface CardState {
  id: string;
  type: 'chart' | 'table';
  chart: SavedChart | null;
  data: TabularDataItem[] | null;
  loading: boolean;
  createdAt: number;
}

// Helper function to create table columns from tabular data
const createTabularColumns = (data: TabularDataItem[]) => {
  if (!data || data.length === 0) return [];

  const firstRow = data[0];
  return Object.keys(firstRow).map((key) => ({
    accessorKey: key,
    header: key,
    cell: ({ row }: any) => (
      <div className="text-sm">{row.getValue(key)}</div>
    ),
  }));
};

interface TableCardProps {
  cardId: string;
  cardData?: CardState;
  hasCardContent: boolean;
  tableZoneChart?: SavedChart | null;
  tabularData?: TabularDataItem[] | null;
  loadingTabularData?: boolean;
  isOverTable: boolean;
  onUpdateCard?: (cardId: string, updates: Partial<CardState>) => void;
  onRemoveChart?: (zoneType: 'chart' | 'table') => void;
  onRemoveCard?: (cardId: string) => void;
}

export const TableCard: React.FC<TableCardProps> = memo(({
  cardId,
  cardData,
  hasCardContent,
  tableZoneChart,
  tabularData,
  loadingTabularData,
  isOverTable,
  onUpdateCard,
  onRemoveChart,
  onRemoveCard
}) => {
  // Memoize expensive computations
  const tableName = useMemo(() => 
    cardData?.chart?.chart_name || tableZoneChart?.chart_name,
    [cardData?.chart?.chart_name, tableZoneChart?.chart_name]
  );

  const hasTableData = useMemo(() => 
    (cardData?.chart && cardData?.data) || (tableZoneChart && tabularData && tabularData.length > 0),
    [cardData?.chart, cardData?.data, tableZoneChart, tabularData]
  );

  const isLoading = useMemo(() => 
    cardData?.loading || loadingTabularData,
    [cardData?.loading, loadingTabularData]
  );

  const tableColumns = useMemo(() => 
    createTabularColumns((cardData?.data as TabularDataItem[]) || tabularData || []),
    [cardData?.data, tabularData]
  );

  // Memoize event handlers
  const handleRemove = useCallback(() => {
    if (hasCardContent) {
      onUpdateCard?.(cardId, { chart: null, data: null });
    } else if (tableZoneChart && onRemoveChart) {
      onRemoveChart('table');
    } else {
      onRemoveCard?.(cardId);
    }
  }, [hasCardContent, cardId, onUpdateCard, tableZoneChart, onRemoveChart, onRemoveCard]);

  // Create unique droppable for this specific card
  const { isOver: isOverThisCard, setNodeRef: setThisCardNodeRef } = useDroppable({
    id: `table-${cardId}`,
    data: {
      type: 'table-zone',
      cardId: cardId,
    },
  });

  return (
    <div ref={setThisCardNodeRef}>
      <ResizableCard
        initialWidth={400}
        initialHeight={350}
        minWidth={300}
        minHeight={250}
        hasContent={hasCardContent || !!tableZoneChart}
        onRemove={handleRemove}
        showCloseIcon={!hasCardContent && !tableZoneChart}
      className={`transition-colors ${
          isOverThisCard || isOverTable
            ? 'border-2 border-dashed border-primary bg-primary/10'
            : hasCardContent || tableZoneChart
              ? 'border-none bg-card shadow-sm'
              : 'border-2 border-dashed border-primary/30 bg-primary/5 hover:border-primary/50'
        }`}
      >
        <CardHeader className="p-3 pb-2 flex-shrink-0">
          <CardTitle className="text-lg font-medium text-foreground flex items-center">
            <Table size={20} className="mr-2 text-secondary" />
            Table
            {tableName && (
              <span className="ml-2 text-sm font-normal text-muted-foreground truncate">
                - {tableName}
              </span>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="flex-1 p-0 overflow-hidden flex flex-col min-h-0">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <Loader2 className="h-6 w-6 text-secondary animate-spin" />
              <span className="ml-2 text-sm text-muted-foreground">Loading table...</span>
            </div>
          ) : hasTableData ? (
            <div className="flex-1 p-3 min-h-0">
              <div className="w-full h-full overflow-auto">
                <LazyDataTable
                  columns={tableColumns}
                  data={(cardData?.data as TabularDataItem[]) || tabularData}
                  className="text-xs"
                  pageSize={20}
                />
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center h-full text-center text-muted-foreground">
              <div>
                <Table size={48} className="mx-auto mb-3 text-muted-foreground/50" />
                <p className="text-sm">Drop chart here to view as table</p>
              </div>
            </div>
          )}
        </CardContent>
      </ResizableCard>
    </div>
  );
});

TableCard.displayName = 'TableCard';