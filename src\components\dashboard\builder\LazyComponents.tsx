import React, { lazy, Suspense, memo } from 'react';
import { Loader2 } from 'lucide-react';

// Lazy load heavy components
const ChartRenderer = lazy(() => import('@/components/charts/ChartRenderer'));
const DataTable = lazy(() => import('@/components/ui/data-table/data-table').then(module => ({ default: module.DataTable })));
const ResultTable = lazy(() => import('@/components/dashboard/dadaResultViews/ResultTable'));
const UniversalChart = lazy(() => import('@/components/charts/UniversalChart'));

// Loading fallback component
const LoadingFallback = memo(() => (
  <div className="flex items-center justify-center h-48 w-full bg-muted rounded">
    <div className="flex flex-col items-center gap-2">
      <Loader2 className="h-6 w-6 animate-spin text-primary" />
      <span className="text-sm text-muted-foreground">Loading component...</span>
    </div>
  </div>
));

LoadingFallback.displayName = 'LoadingFallback';

// Error fallback component
const ErrorFallback = memo(({ error, retry }: { error?: Error; retry?: () => void }) => (
  <div className="flex items-center justify-center h-48 w-full bg-destructive/5 border border-destructive/20 rounded">
    <div className="flex flex-col items-center gap-2 p-4 text-center">
      <span className="text-sm text-destructive font-medium">Failed to load component</span>
      {error && (
        <span className="text-xs text-muted-foreground">{error.message}</span>
      )}
      {retry && (
        <button 
          onClick={retry}
          className="text-xs text-primary hover:underline"
        >
          Try again
        </button>
      )}
    </div>
  </div>
));

ErrorFallback.displayName = 'ErrorFallback';

// Error boundary for lazy components
class LazyErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ComponentType<{ error?: Error; retry?: () => void }> },
  { hasError: boolean; error?: Error }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Lazy component loading error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      const Fallback = this.props.fallback || ErrorFallback;
      return (
        <Fallback 
          error={this.state.error}
          retry={() => this.setState({ hasError: false, error: undefined })}
        />
      );
    }

    return this.props.children;
  }
}

// Wrapper components with error boundaries and suspense
export const LazyChartRenderer = memo((props: any) => (
  <LazyErrorBoundary>
    <Suspense fallback={<LoadingFallback />}>
      <ChartRenderer {...props} />
    </Suspense>
  </LazyErrorBoundary>
));

export const LazyDataTable = memo((props: any) => (
  <LazyErrorBoundary>
    <Suspense fallback={<LoadingFallback />}>
      <DataTable {...props} />
    </Suspense>
  </LazyErrorBoundary>
));

export const LazyResultTable = memo((props: any) => (
  <LazyErrorBoundary>
    <Suspense fallback={<LoadingFallback />}>
      <ResultTable {...props} />
    </Suspense>
  </LazyErrorBoundary>
));

export const LazyUniversalChart = memo((props: any) => (
  <LazyErrorBoundary>
    <Suspense fallback={<LoadingFallback />}>
      <UniversalChart {...props} />
    </Suspense>
  </LazyErrorBoundary>
));

// Component preloader for critical components
export const preloadComponent = (componentName: 'chart' | 'table' | 'result-table' | 'universal-chart') => {
  switch (componentName) {
    case 'chart':
      return import('@/components/charts/ChartRenderer');
    case 'table':
      return import('@/components/ui/data-table/data-table').then(module => ({ default: module.DataTable }));
    case 'result-table':
      return import('@/components/dashboard/dadaResultViews/ResultTable');
    case 'universal-chart':
      return import('@/components/charts/UniversalChart');
    default:
      return Promise.resolve();
  }
};

// Component names for display
LazyChartRenderer.displayName = 'LazyChartRenderer';
LazyDataTable.displayName = 'LazyDataTable';
LazyResultTable.displayName = 'LazyResultTable';
LazyUniversalChart.displayName = 'LazyUniversalChart';