


import React from "react"
import { Mic, MicOff } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

interface VoiceInputButtonProps {
  isListening: boolean;
  setIsListening: (isListening: boolean) => void;
  isLoading: boolean;
  setInputValue: (value: string) => void;
  onActivate: () => void;
}

const VoiceInputButton = ({
  isListening,
  setIsListening,
  isLoading,
  onActivate,
}: VoiceInputButtonProps) => {
  return (
    <Button
      type="button"
      size="icon"
      className={`rounded-full ${isListening 
        ? "bg-red-500 text-white hover:bg-red-600" 
        : "bg-gray-300 text-gray-600 hover:bg-gray-400"}`}
      onClick={onActivate}
      disabled={isLoading}
    >
      {isListening ? <MicOff size={18} /> : <Mic size={18} />}
    </Button>
  );
};

export default VoiceInputButton;
