import React from 'react';
import { SavedChart, ChartDataPoint } from '@/types/chartTypes';
import { TabularDataItem } from '@/services/api/chart/chartTypes';
import ChartRenderer from '@/components/charts/ChartRenderer';
import MetricCards from './MetricCards';
import ChartTableGrid from './ChartTableGrid';

// Interface for individual card state
interface CardState {
  id: string;
  type: 'chart' | 'table';
  chart: SavedChart | null;
  data: ChartDataPoint[] | TabularDataItem[] | null;
  loading: boolean;
  createdAt: number;
}


interface DashboardGridProps {
  selectedCharts: SavedChart[];
  chartData: any[] | null;
  onRemoveChart: (chartId: string, displayComponent?: 'chart' | 'table') => void;
  // New props for drag & drop zones
  chartZoneChart?: SavedChart | null;
  tableZoneChart?: SavedChart | null;
  zoneChartData?: ChartDataPoint[] | null;
  loadingZoneData?: boolean;
  onRemoveChartFromZone?: (zoneType: 'chart' | 'table') => void;
  // Tabular data props
  tabularData?: TabularDataItem[] | null;
  loadingTabularData?: boolean;
  // Props for multiple cards
  chartCards?: string[];
  tableCards?: string[];
  onRemoveCard?: (cardId: string) => void;
  // New unified card state props
  cards?: CardState[];
  onUpdateCard?: (cardId: string, updates: Partial<CardState>) => void;
}

const DashboardGrid: React.FC<DashboardGridProps> = ({
  selectedCharts,
  chartData,
  onRemoveChart,
  chartZoneChart,
  tableZoneChart,
  zoneChartData,
  loadingZoneData,
  onRemoveChartFromZone,
  tabularData,
  loadingTabularData,
  chartCards = [],
  tableCards = [],
  onRemoveCard,
  cards = [],
  onUpdateCard
}) => {
  return (
    <div className="space-y-6">
      {/* Top 3 Metric Cards */}
      <MetricCards />

      {/* Chart and Table Cards */}
      <ChartTableGrid
        chartZoneChart={chartZoneChart}
        tableZoneChart={tableZoneChart}
        chartData={zoneChartData}
        loadingChartData={loadingZoneData}
        onRemoveChart={onRemoveChartFromZone}
        tabularData={tabularData}
        loadingTabularData={loadingTabularData}
        chartCards={chartCards}
        tableCards={tableCards}
        onRemoveCard={onRemoveCard}
        cards={cards}
        onUpdateCard={onUpdateCard}
      />

      {/* Show selected charts below if any (for now, keeping existing functionality) */}
      {selectedCharts.length > 0 && (
        <div className="mt-8">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Added Charts</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {selectedCharts.map((chart) => (
              <div key={`${chart.chart_id}-${chart.displayComponent || 'both'}`} className="bg-white rounded-lg shadow p-4">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-medium text-sm text-gray-900 truncate">
                    {chart.chart_name}
                  </h3>
                  <button
                    onClick={() => onRemoveChart(chart.chart_id, chart.displayComponent)}
                    className="text-gray-400 hover:text-red-500 transition-colors"
                  >
                    ×
                  </button>
                </div>

                {/* Chart visualization - only show if displayComponent is 'chart' or undefined */}
                {(!chart.displayComponent || chart.displayComponent === 'chart') && (
                  <div className="h-48 mb-3 border rounded bg-white">
                    {chartData && chartData.length > 0 ? (
                      <ChartRenderer chart={chart} chartData={chartData} />
                    ) : (
                      <div className="h-full flex items-center justify-center text-gray-500 text-sm">
                        {chart.chart_type || 'Chart'} Preview
                      </div>
                    )}
                  </div>
                )}

                {/* Table data - only show if displayComponent is 'table' or undefined */}
                {(!chart.displayComponent || chart.displayComponent === 'table') && (
                  <div className="border rounded overflow-hidden">
                    <div className="bg-gray-100 px-3 py-2 text-xs font-medium">Table Data</div>
                    <div className="max-h-32 overflow-y-auto">
                      {chartData && chartData.length > 0 ? (
                        <table className="w-full text-xs">
                          <thead className="bg-gray-50">
                            <tr>
                              {Object.keys(chartData[0]).map((key) => (
                                <th key={key} className="px-2 py-1 text-left border-b">{key}</th>
                              ))}
                            </tr>
                          </thead>
                          <tbody>
                            {chartData.slice(0, 5).map((row, i) => (
                              <tr key={i} className={i % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                                {Object.values(row).map((value, j) => (
                                  <td key={j} className="px-2 py-1 border-b">
                                    {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                                  </td>
                                ))}
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      ) : (
                        <div className="p-3 text-center text-xs text-gray-500">
                          No table data available
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default DashboardGrid;
