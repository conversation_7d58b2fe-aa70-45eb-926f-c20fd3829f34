import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { DatabaseService, MetadataService, DatabaseHelpers, type DatabaseConnection } from '@/services/api/databaseService';

export const useTableManagement = () => {
  // Connection state
  const [connections, setConnections] = useState<DatabaseConnection[]>([]);
  const [selectedConnection, setSelectedConnection] = useState<DatabaseConnection | null>(null);
  const [isLoadingConnections, setIsLoadingConnections] = useState(false);

  // Table state
  const [availableTables, setAvailableTables] = useState<string[]>([]);
  const [selectedTables, setSelectedTables] = useState<string[]>([]);
  const [isLoadingTables, setIsLoadingTables] = useState(false);
  const [tableSearchQuery, setTableSearchQuery] = useState('');

  // State for table columns data from get_tables_columns API
  const [tableColumnsData, setTableColumnsData] = useState<{[tableName: string]: string[]}>({});

  // Store the full GET API response in localStorage
  const storeTableColumnsInLocalStorage = (connectionId: string, apiResponse: any) => {
    const storageKey = `tableColumns_${connectionId}`;
    localStorage.setItem(storageKey, JSON.stringify(apiResponse));
    console.log(`💾 Stored GET API response in localStorage:`, storageKey, apiResponse);
  };

  // Get table columns from localStorage
  const getTableColumnsFromLocalStorage = (connectionId: string) => {
    const storageKey = `tableColumns_${connectionId}`;
    const stored = localStorage.getItem(storageKey);
    if (stored) {
      try {
        const parsed = JSON.parse(stored);
        console.log(`📦 Retrieved table columns from localStorage:`, storageKey, parsed);
        return parsed;
      } catch (error) {
        console.error('❌ Error parsing stored table columns:', error);
      }
    }
    return null;
  };
  const [filteredTables, setFilteredTables] = useState<string[]>([]);

  // Pagination state for available tables
  const [availableTablesPage, setAvailableTablesPage] = useState(1);
  const [availableTablesPerPage] = useState(3); // Reduced to 3 to make pagination more visible

  // Search and pagination state for selected tables
  const [selectedTableSearchQuery, setSelectedTableSearchQuery] = useState('');
  const [filteredSelectedTables, setFilteredSelectedTables] = useState<string[]>([]);
  const [selectedTablesPage, setSelectedTablesPage] = useState(1);
  const [selectedTablesPerPage] = useState(3); // Reduced to 3 to make pagination more visible

  // Table content state
  const [showTableContent, setShowTableContent] = useState(false);
  const [tableContentData, setTableContentData] = useState<any[]>([]);
  const [isLoadingTableContent, setIsLoadingTableContent] = useState(false);

  // Filter available tables based on search query
  useEffect(() => {
    if (tableSearchQuery.trim() === '') {
      setFilteredTables(availableTables);
    } else {
      const filtered = availableTables.filter(table =>
        table.toLowerCase().includes(tableSearchQuery.toLowerCase())
      );
      setFilteredTables(filtered);
    }
    setAvailableTablesPage(1); // Reset to first page when search changes
  }, [availableTables, tableSearchQuery]);

  // Filter selected tables based on search query
  useEffect(() => {
    if (selectedTableSearchQuery.trim() === '') {
      setFilteredSelectedTables(selectedTables);
    } else {
      const filtered = selectedTables.filter(table =>
        table.toLowerCase().includes(selectedTableSearchQuery.toLowerCase())
      );
      setFilteredSelectedTables(filtered);
    }
    setSelectedTablesPage(1); // Reset to first page when search changes
  }, [selectedTables, selectedTableSearchQuery]);

  // Monitor pagination state changes
  useEffect(() => {
    console.log('📄 Available Tables Page Changed:', availableTablesPage);
  }, [availableTablesPage]);

  useEffect(() => {
    console.log('📄 Selected Tables Page Changed:', selectedTablesPage);
  }, [selectedTablesPage]);

  // Fetch database connections
  const fetchConnections = async () => {
    try {
      setIsLoadingConnections(true);
      const userId = 'a6e3020d-984a-4394-ac73-da7ec5393314';

      const data = await DatabaseService.getUserConnectionsDetailed(userId);
      if (data.status === 'success' && Array.isArray(data.connections)) {
        setConnections(data.connections);
      } else {
        toast.error('Invalid response format from server');
      }
    } catch (error) {
      console.error('❌ Error fetching connections:', error);
      toast.error('Failed to fetch connections');
    } finally {
      setIsLoadingConnections(false);
    }
  };

  // Fetch tables for selected connection using query parameter API
  const fetchTablesForConnection = async (connectionId: number | string) => {
    try {
      setIsLoadingTables(true);
      console.log(`🔍 Fetching tables and columns for connection ID: ${connectionId}`);

      // Use query parameter version to get correct table columns
      const data = await MetadataService.getTablesColumnsQuery(parseInt(connectionId.toString()));
      console.log('📊 GET tables_columns API full response:', JSON.stringify(data, null, 2));

      if (data && data.table_list && Array.isArray(data.table_list)) {
        // Store the full API response in localStorage for later use
        storeTableColumnsInLocalStorage(connectionId.toString(), data);

        const tableNames = data.table_list.map((table: any) => table.table_name);
        setAvailableTables(tableNames);
        setFilteredTables(tableNames);

        // Store table columns data for UI display and save operations
        const columnsData: {[tableName: string]: string[]} = {};
        data.table_list.forEach((table: any) => {
          console.log(`🔍 Processing table "${table.table_name}" with columns:`, table.table_columns);
          if (table.table_columns && Array.isArray(table.table_columns) && table.table_columns.length > 0) {
            columnsData[table.table_name] = table.table_columns;
            console.log(`✅ Stored ${table.table_columns.length} columns for "${table.table_name}"`);
          } else {
            console.log(`⚠️ No columns found for "${table.table_name}", will use fallback`);
            columnsData[table.table_name] = [];
          }
        });
        setTableColumnsData(columnsData);
        console.log('📊 Final stored table columns data:', JSON.stringify(columnsData, null, 2));

        toast.success(`Found ${tableNames.length} tables with columns`);
      } else {
        setAvailableTables([]);
        setFilteredTables([]);
        setTableColumnsData({});
      }
    } catch (error) {
      console.error('❌ Error fetching tables:', error);
      toast.error('Failed to fetch tables and columns');
      setAvailableTables([]);
      setFilteredTables([]);
      setTableColumnsData({});
    } finally {
      setIsLoadingTables(false);
    }
  };

  // Fetch selected tables for connection (only from API for initial load)
  const fetchSelectedTablesFromAPI = async (connectionId: number | string) => {
    try {
      console.log(`🔍 Fetching selected tables from API for connection ID: ${connectionId}`);

      const API_BASE_URL = 'http://10.100.0.22:8001';
      const response = await fetch(`${API_BASE_URL}/metadata/get_selected_tables_columns?connection_id=${connectionId}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('📊 Selected tables API response:', data);

      if (data.selected_tables_columns && Array.isArray(data.selected_tables_columns)) {
        const selectedTableNames = data.selected_tables_columns.map((item: any) => item.table_name);
        console.log(`✅ Found ${selectedTableNames.length} selected tables from API:`, selectedTableNames);

        // Store in localStorage for future use
        const storageKey = `selectedTables_${connectionId}`;
        localStorage.setItem(storageKey, JSON.stringify(selectedTableNames));
        console.log(`💾 Saved API data to localStorage:`, storageKey, selectedTableNames);

        setSelectedTables(selectedTableNames);
        return selectedTableNames;
      } else if (data.table_list && Array.isArray(data.table_list)) {
        const selectedTableNames = data.table_list.map((item: any) => item.table_name);
        console.log(`✅ Found ${selectedTableNames.length} selected tables from API:`, selectedTableNames);

        // Store in localStorage for future use
        const storageKey = `selectedTables_${connectionId}`;
        localStorage.setItem(storageKey, JSON.stringify(selectedTableNames));
        console.log(`💾 Saved API data to localStorage:`, storageKey, selectedTableNames);

        setSelectedTables(selectedTableNames);
        return selectedTableNames;
      } else {
        console.log('ℹ️ No selected tables found in API for this connection');
        setSelectedTables([]);
        return [];
      }
    } catch (error) {
      console.error('❌ Error fetching selected tables from API:', error);
      setSelectedTables([]);
      return [];
    }
  };

  // Load selected tables from localStorage only
  const fetchSelectedTables = async (connectionId: number | string) => {
    const storageKey = `selectedTables_${connectionId}`;
    const storedTables = localStorage.getItem(storageKey);

    if (storedTables) {
      try {
        const parsedTables = JSON.parse(storedTables);
        console.log(`📂 Loaded selected tables from localStorage:`, storageKey, parsedTables);
        setSelectedTables(parsedTables);
        return parsedTables;
      } catch (error) {
        console.error('❌ Error parsing stored tables:', error);
        setSelectedTables([]);
        return [];
      }
    } else {
      console.log('📂 No stored tables found in localStorage, will load from API on first connection');
      setSelectedTables([]);
      return [];
    }
  };

  // Handle connection selection
  const handleConnectionSelect = async (connection: DatabaseConnection) => {
    try {
      setIsLoadingTables(true);
      setSelectedConnection(connection);
      setSelectedTables([]);
      setAvailableTables([]);

      console.log('🔗 Selected connection:', connection);
      toast.info(`Loading tables for ${connection.connection_name}...`);

      console.log(`🔍 Fetching tables for connection ID: ${connection.connection_id}`);

      // Fetch available tables and store the full response
      const data = await MetadataService.getTablesColumnsQuery(connection.connection_id);
      console.log('📊 GET tables_columns API response in handleConnectionSelect:', JSON.stringify(data, null, 2));

      if (data && data.table_list && Array.isArray(data.table_list)) {
        // Store the full API response in localStorage for later use
        storeTableColumnsInLocalStorage(connection.connection_id.toString(), data);

        const tableNames = data.table_list.map((table: any) => table.table_name);
        setAvailableTables(tableNames);
        setFilteredTables(tableNames);

        // Store table columns data for UI display
        const columnsData: {[tableName: string]: string[]} = {};
        data.table_list.forEach((table: any) => {
          if (table.table_columns && Array.isArray(table.table_columns) && table.table_columns.length > 0) {
            columnsData[table.table_name] = table.table_columns;
          }
        });
        setTableColumnsData(columnsData);

        toast.success(`Found ${tableNames.length} tables with columns`);
        console.log(`✅ Stored table columns data for ${tableNames.length} tables`);
      } else {
        setAvailableTables([]);
        setFilteredTables([]);
        setTableColumnsData({});
        toast.info('No tables found for this connection');
      }

      // Load selected tables from localStorage first
      const storageKey = `selectedTables_${connection.connection_id}`;
      const storedTables = localStorage.getItem(storageKey);

      if (storedTables) {
        try {
          const parsedTables = JSON.parse(storedTables);
          console.log(`📂 Loaded from localStorage:`, storageKey, parsedTables);
          setSelectedTables(parsedTables);
        } catch (error) {
          console.error('❌ Error parsing stored tables:', error);
          setSelectedTables([]);
          // Fallback to API if localStorage is corrupted
          await fetchSelectedTablesFromAPI(connection.connection_id);
        }
      } else {
        console.log('📂 No stored tables found, fetching from API for first time...');
        // Only fetch from API if no localStorage data (first time)
        await fetchSelectedTablesFromAPI(connection.connection_id);
      }

    } catch (error) {
      console.error('❌ Error loading tables:', error);
      toast.error('Failed to load tables for this connection');
      setAvailableTables([]);
      setFilteredTables([]);
    } finally {
      setIsLoadingTables(false);
    }
  };

  // Handle table selection with localStorage
  const handleTableSelect = (tableName: string) => {
    console.log(`🔄 Table selection: ${tableName}`);
    setSelectedTables(prev => {
      console.log(`📋 Current selected tables:`, prev);
      let newSelection: string[];

      if (prev.includes(tableName)) {
        console.log(`➖ Removing table: ${tableName}`);
        newSelection = prev.filter(t => t !== tableName);
      } else {
        console.log(`➕ Adding table: ${tableName}`);
        newSelection = [...prev, tableName];
      }

      // Store in localStorage with connection ID as key
      if (selectedConnection) {
        const storageKey = `selectedTables_${selectedConnection.connection_id}`;
        localStorage.setItem(storageKey, JSON.stringify(newSelection));
        console.log(`💾 Saved to localStorage:`, storageKey, newSelection);
      }

      console.log(`📋 New selected tables:`, newSelection);
      return newSelection;
    });
  };

  // Get table columns (fallback implementation)
  const getTableColumns = (tableName: string): string[] => {
    const fallbackColumns: { [key: string]: string[] } = {
      'patient_data': ['Patient_Id', 'Patient_First_Name', 'Patient_Last_Name', 'Active_Record', 'ETL_DT'],
      'claims': ['Patient_Id', 'PCP_Id', 'Claim_Dt', 'Claim_Line_Id', 'Claim_Line_Amount'],
      'provider': ['Provider_Id', 'Provider_Name', 'Provider_Type', 'Active_Record', 'ETL_DT'],
      'diagnosis': ['Diagnosis_Id', 'Patient_Id', 'Diagnosis_Code', 'Diagnosis_Description', 'ETL_DT'],
      'medication': ['Medication_Id', 'Patient_Id', 'Medication_Name', 'Dosage', 'ETL_DT']
    };
    
    return fallbackColumns[tableName] || ['id', 'name', 'created_at', 'updated_at'];
  };

  // Apply selected tables (save localStorage data to API)
  const applySelectedTables = async () => {
    console.log('🚀 applySelectedTables function called');
    console.log('🔗 selectedConnection:', selectedConnection);
    console.log('📋 selectedTables:', selectedTables);
    console.log('📊 selectedTables.length:', selectedTables.length);

    if (!selectedConnection || selectedTables.length === 0) {
      console.log('❌ Validation failed - no connection or no tables selected');
      toast.error('Please select a connection and at least one table');
      return false;
    }

    try {
      setIsLoadingTableContent(true);

      console.log(`💾 Saving ${selectedTables.length} selected tables from localStorage to API...`);
      console.log('📋 Tables to save:', selectedTables);

      // Get the stored GET API response from localStorage
      console.log('📦 Attempting to get stored API response from localStorage...');
      const storedApiResponse = getTableColumnsFromLocalStorage(selectedConnection.connection_id.toString());

      console.log('📦 Retrieved stored API response:', storedApiResponse);

      if (!storedApiResponse) {
        console.error('❌ No stored API response found in localStorage');
        throw new Error('No stored table columns data found. Please refresh the connection by selecting it again.');
      }

      if (!storedApiResponse.table_list) {
        console.error('❌ Stored API response has no table_list:', storedApiResponse);
        throw new Error('Invalid stored table columns data. Please refresh the connection by selecting it again.');
      }

      console.log('📦 Using stored GET API response:', storedApiResponse);

      // Create payload using only the selected tables from the stored API response
      const selectedTablesWithColumns = selectedTables.map(tableName => {
        // Find the table in the stored API response
        const tableFromApi = storedApiResponse.table_list.find((table: any) => table.table_name === tableName);

        if (tableFromApi && tableFromApi.table_columns) {
          console.log(`✅ Found table "${tableName}" in API response with ${tableFromApi.table_columns.length} columns:`, tableFromApi.table_columns);
          return {
            table_name: tableName,
            table_columns: tableFromApi.table_columns
          };
        } else {
          console.log(`⚠️ Table "${tableName}" not found in API response, using fallback`);
          return {
            table_name: tableName,
            table_columns: getTableColumns(tableName)
          };
        }
      });

      const payload = {
        connection_id: selectedConnection.connection_id.toString(),
        selected_tables_columns: selectedTablesWithColumns
      };

      console.log('📊 Final payload using stored API response:', JSON.stringify(payload, null, 2));

      console.log('📤 API Payload:', JSON.stringify(payload, null, 2));

      const result = await MetadataService.saveSelectedTablesColumns(payload);
      console.log('✅ Selected tables saved to API successfully:', result);

      toast.success(`Successfully saved ${selectedTables.length} selected tables to server`);
      return true;
    } catch (error) {
      console.error('❌ Error saving selected tables to API:', error);
      console.error('❌ Error details:', {
        message: error.message,
        stack: error.stack,
        error: error
      });

      // More specific error message
      const errorMessage = error.message || 'Unknown error occurred';
      toast.error(`Failed to save selected tables: ${errorMessage}`);
      return false;
    } finally {
      setIsLoadingTableContent(false);
    }
  };

  // Clear selections and localStorage
  const clearSelections = () => {
    setSelectedTables([]);
    setTableSearchQuery('');
    setSelectedTableSearchQuery('');
    setShowTableContent(false);
    setTableContentData([]);
    setAvailableTablesPage(1);
    setSelectedTablesPage(1);

    // Clear from localStorage
    if (selectedConnection) {
      const storageKey = `selectedTables_${selectedConnection.connection_id}`;
      localStorage.removeItem(storageKey);
      console.log(`🗑️ Cleared localStorage:`, storageKey);
      toast.info('Selection cleared');
    }
  };

  // Bulk actions for table selection
  const selectAllTables = () => {
    if (!selectedConnection) return;

    console.log('🔄 Selecting all available tables:', availableTables);
    setSelectedTables(availableTables);

    // Store in localStorage
    const storageKey = `selectedTables_${selectedConnection.connection_id}`;
    localStorage.setItem(storageKey, JSON.stringify(availableTables));
    console.log(`💾 Saved all tables to localStorage:`, storageKey, availableTables);

    toast.success(`Selected all ${availableTables.length} tables`);
  };

  const removeAllTables = () => {
    if (!selectedConnection) return;

    console.log('🔄 Removing all selected tables');
    setSelectedTables([]);

    // Clear from localStorage
    const storageKey = `selectedTables_${selectedConnection.connection_id}`;
    localStorage.setItem(storageKey, JSON.stringify([]));
    console.log(`💾 Cleared all tables from localStorage:`, storageKey);

    toast.success('Removed all selected tables');
  };

  // Get stored selections for debugging
  const getStoredSelections = () => {
    if (!selectedConnection) return null;

    const storageKey = `selectedTables_${selectedConnection.connection_id}`;
    const storedTables = localStorage.getItem(storageKey);

    try {
      return storedTables ? JSON.parse(storedTables) : [];
    } catch (error) {
      console.error('❌ Error parsing stored selections:', error);
      return [];
    }
  };

  // Pagination calculations for available tables
  const availableTablesTotalPages = Math.ceil(filteredTables.length / availableTablesPerPage);
  const availableTablesStartIndex = (availableTablesPage - 1) * availableTablesPerPage;
  const availableTablesEndIndex = availableTablesStartIndex + availableTablesPerPage;
  const paginatedAvailableTables = filteredTables.slice(availableTablesStartIndex, availableTablesEndIndex);

  // Debug available tables pagination
  console.log('📄 Available Tables Pagination:', {
    totalTables: filteredTables.length,
    currentPage: availableTablesPage,
    totalPages: availableTablesTotalPages,
    perPage: availableTablesPerPage,
    startIndex: availableTablesStartIndex,
    endIndex: availableTablesEndIndex,
    paginatedCount: paginatedAvailableTables.length
  });

  // Pagination calculations for selected tables
  const selectedTablesTotalPages = Math.ceil(filteredSelectedTables.length / selectedTablesPerPage);
  const selectedTablesStartIndex = (selectedTablesPage - 1) * selectedTablesPerPage;
  const selectedTablesEndIndex = selectedTablesStartIndex + selectedTablesPerPage;
  const paginatedSelectedTables = filteredSelectedTables.slice(selectedTablesStartIndex, selectedTablesEndIndex);

  // Debug selected tables pagination
  console.log('📄 Selected Tables Pagination:', {
    totalTables: filteredSelectedTables.length,
    currentPage: selectedTablesPage,
    totalPages: selectedTablesTotalPages,
    perPage: selectedTablesPerPage,
    startIndex: selectedTablesStartIndex,
    endIndex: selectedTablesEndIndex,
    paginatedCount: paginatedSelectedTables.length
  });

  // Simplified pagination handlers with forced re-render
  const goToAvailableTablesPage = (page: number) => {
    console.log('📄 goToAvailableTablesPage called:', {
      requestedPage: page,
      currentPage: availableTablesPage,
      totalPages: availableTablesTotalPages,
      filteredTablesLength: filteredTables.length,
      perPage: availableTablesPerPage
    });

    if (page >= 1 && page <= availableTablesTotalPages && page !== availableTablesPage) {
      console.log('📄 Setting available tables page to:', page);
      setAvailableTablesPage(page);
      // Force a small delay to ensure state update
      setTimeout(() => {
        console.log('📄 Available page after update:', availableTablesPage);
      }, 100);
    } else {
      console.log('📄 Page out of range or same page:', { page, totalPages: availableTablesTotalPages, currentPage: availableTablesPage });
    }
  };

  const goToSelectedTablesPage = (page: number) => {
    console.log('📄 goToSelectedTablesPage called:', {
      requestedPage: page,
      currentPage: selectedTablesPage,
      totalPages: selectedTablesTotalPages,
      filteredSelectedTablesLength: filteredSelectedTables.length,
      perPage: selectedTablesPerPage
    });

    if (page >= 1 && page <= selectedTablesTotalPages && page !== selectedTablesPage) {
      console.log('📄 Setting selected tables page to:', page);
      setSelectedTablesPage(page);
      // Force a small delay to ensure state update
      setTimeout(() => {
        console.log('📄 Selected page after update:', selectedTablesPage);
      }, 100);
    } else {
      console.log('📄 Page out of range or same page:', { page, totalPages: selectedTablesTotalPages, currentPage: selectedTablesPage });
    }
  };

  // Direct navigation functions with forced state updates
  const nextAvailablePage = () => {
    const nextPage = availableTablesPage + 1;
    console.log('📄 Next available page called:', {
      currentPage: availableTablesPage,
      nextPage,
      totalPages: availableTablesTotalPages,
      canGoNext: nextPage <= availableTablesTotalPages
    });

    if (nextPage <= availableTablesTotalPages) {
      console.log('📄 Setting available page from', availableTablesPage, 'to', nextPage);
      setAvailableTablesPage(nextPage);

      // Force a re-render check
      setTimeout(() => {
        console.log('📄 Available page after setState:', availableTablesPage);
      }, 50);
    } else {
      console.log('📄 Cannot go to next page - at limit');
    }
  };

  const prevAvailablePage = () => {
    const prevPage = availableTablesPage - 1;
    console.log('📄 Previous available page called:', {
      currentPage: availableTablesPage,
      prevPage,
      canGoPrev: prevPage >= 1
    });

    if (prevPage >= 1) {
      console.log('📄 Setting available page from', availableTablesPage, 'to', prevPage);
      setAvailableTablesPage(prevPage);

      // Force a re-render check
      setTimeout(() => {
        console.log('📄 Available page after setState:', availableTablesPage);
      }, 50);
    } else {
      console.log('📄 Cannot go to previous page - at minimum');
    }
  };

  const nextSelectedPage = () => {
    const nextPage = selectedTablesPage + 1;
    console.log('📄 Next selected page called:', {
      currentPage: selectedTablesPage,
      nextPage,
      totalPages: selectedTablesTotalPages,
      canGoNext: nextPage <= selectedTablesTotalPages
    });

    if (nextPage <= selectedTablesTotalPages) {
      console.log('📄 Setting selected page from', selectedTablesPage, 'to', nextPage);
      setSelectedTablesPage(nextPage);

      // Force a re-render check
      setTimeout(() => {
        console.log('📄 Selected page after setState:', selectedTablesPage);
      }, 50);
    } else {
      console.log('📄 Cannot go to next selected page - at limit');
    }
  };

  const prevSelectedPage = () => {
    const prevPage = selectedTablesPage - 1;
    console.log('📄 Previous selected page called:', {
      currentPage: selectedTablesPage,
      prevPage,
      canGoPrev: prevPage >= 1
    });

    if (prevPage >= 1) {
      console.log('📄 Setting selected page from', selectedTablesPage, 'to', prevPage);
      setSelectedTablesPage(prevPage);

      // Force a re-render check
      setTimeout(() => {
        console.log('📄 Selected page after setState:', selectedTablesPage);
      }, 50);
    } else {
      console.log('📄 Cannot go to previous selected page - at minimum');
    }
  };

  return {
    // Connection state
    connections,
    selectedConnection,
    isLoadingConnections,

    // Table state
    availableTables,
    selectedTables,
    isLoadingTables,
    tableSearchQuery,
    filteredTables,
    tableColumnsData,

    // Available tables pagination
    availableTablesPage,
    availableTablesTotalPages,
    availableTablesPerPage,
    paginatedAvailableTables,

    // Selected tables search and pagination
    selectedTableSearchQuery,
    filteredSelectedTables,
    selectedTablesPage,
    selectedTablesTotalPages,
    selectedTablesPerPage,
    paginatedSelectedTables,

    // Table content state
    showTableContent,
    tableContentData,
    isLoadingTableContent,

    // Actions
    fetchConnections,
    fetchTablesForConnection,
    fetchSelectedTables,
    fetchSelectedTablesFromAPI,
    handleConnectionSelect,
    handleTableSelect,
    applySelectedTables,
    clearSelections,
    selectAllTables,
    removeAllTables,
    setTableSearchQuery,
    setSelectedTableSearchQuery,
    goToAvailableTablesPage,
    goToSelectedTablesPage,
    nextAvailablePage,
    prevAvailablePage,
    nextSelectedPage,
    prevSelectedPage,
    getTableColumns,
    getStoredSelections
  };
};
