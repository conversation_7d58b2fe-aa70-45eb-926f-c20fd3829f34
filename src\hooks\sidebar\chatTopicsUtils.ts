
import { ChatTopic } from '@/components/dashboard/sidebar/types';

export const getInitialChatTopics = (): ChatTopic[] => [
  {
    id: '1',
    name: 'ChatTopic1',
    chats: ['Chat1', 'Chat2', 'Chat3'],
    expanded: true
  },
  {
    id: '2',
    name: 'ChatTopic2',
    chats: ['ChatA', 'ChatB'],
    expanded: false
  },
  {
    id: '3',
    name: 'ChatTopic3',
    chats: [],
    expanded: false
  }
];

export const toggleChatTopic = (topics: ChatTopic[], itemId: string): ChatTopic[] => {
  return topics.map(topic => 
    topic.id === itemId 
      ? { ...topic, expanded: !topic.expanded } 
      : topic
  );
};

export const addChatToChatTopic = (topics: ChatTopic[], itemId: string): ChatTopic[] => {
  return topics.map(topic => {
    if (topic.id === itemId) {
      const chatNumber = topic.chats.length + 1;
      return {
        ...topic,
        chats: [...topic.chats, `Chat${chatNumber}`],
        expanded: true
      };
    }
    return topic;
  });
};

export const addNewChatTopic = (topics: ChatTopic[]): ChatTopic[] => {
  const newTopicId = (topics.length + 1).toString();
  return [
    ...topics,
    {
      id: newTopicId,
      name: `ChatTopic${newTopicId}`,
      chats: [],
      expanded: false
    }
  ];
};

export const filterChatTopics = (topics: ChatTopic[], query: string): ChatTopic[] => {
  if (!query.trim()) return topics;
  
  const filterQuery = query.toLowerCase();
  
  return topics.map(topic => {
    const topicMatches = topic.name.toLowerCase().includes(filterQuery);
    
    const filteredChats = topic.chats.filter(chat => 
      chat.toLowerCase().includes(filterQuery)
    );
    
    if (topicMatches || filteredChats.length > 0) {
      return {
        ...topic,
        chats: filteredChats,
        expanded: true
      };
    }
    
    return null;
  }).filter(Boolean) as ChatTopic[];
};
