
import type { QueryResultData } from "@/components/dashboard/models"

interface ResultTableProps {
  size?: "large" | "default" | "small"
  database?: string
  queryResult?: QueryResultData | null
}

export const ResultTable = ({ size = "default", database = "SQL", queryResult }: ResultTableProps) => {
  let textSize = "text-sm"
  let padding = "px-2 py-1.5"

  if (size === "small") {
    textSize = "text-xs"
    padding = "px-2 py-1"
  } else if (size === "large") {
    textSize = "text-base"
    padding = "px-4 py-2.5"
  }

  if (!queryResult) {
    console.log("No query result data available")
    return (
      <div className="flex items-center justify-center p-8 border border-gray-200 rounded">
        <p className="text-gray-500">No data available</p>
      </div>
    )
  }

  // console.log("Rendering table with data:", queryResult)
  const { columns, rows } = queryResult.tableData

  // Check if columns and rows exist and have content
  if (!columns || !rows || columns.length === 0) {
    // console.log("Query result has empty columns or rows:", { columns, rows })
    return (
      <div className="flex items-center justify-center p-8 border border-gray-200 rounded">
        <p className="text-gray-500">No data available (empty result)</p>
      </div>
    )
  }

  // Determine if we need horizontal scrolling
  const needsScroll = columns.length > 5;

  return (
    <div className={`${needsScroll || size === "large" ? "overflow-x-auto" : ""} w-full`}>
      <table className={`min-w-full border border-gray-200 ${textSize}`}>
        <thead>
          <tr className="bg-gray-500 text-white">
            {columns.map((column, index) => (
              <th key={index} className={`${padding} border text-left`}>
                {column}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {rows.map((row, rowIndex) => (
            <tr key={rowIndex} className={rowIndex % 2 === 0 ? "" : "bg-white-500"}>
              {columns.map((column, colIndex) => (
                <td key={`${rowIndex}-${colIndex}`} className={`${padding} border`}>
                  {row[column] !== undefined ? String(row[column]) : ""}
                </td>
              ))}
            </tr>
          ))}
          {rows.length === 0 && (
            <tr>
              <td colSpan={columns.length} className="text-center py-4 text-gray-500">
                No results found
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  )
}

// Default export for lazy loading
export default ResultTable;
