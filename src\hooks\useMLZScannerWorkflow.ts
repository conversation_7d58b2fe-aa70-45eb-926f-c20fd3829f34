import { useState } from 'react';
import { toast } from 'sonner';
import { MetadataService, DatabaseHelpers } from '@/services/api/databaseService';

export const useMLZScannerWorkflow = () => {
  const [isScanning, setIsScanning] = useState(false);

  // Execute the complete MLZ Scanner 4-step workflow
  const executeMLZScannerWorkflow = async (
    selectedConnection: string,
    selectedConnectionName: string,
    onSuccess?: (metadataResult: any) => void
  ) => {
    if (!selectedConnection) {
      toast.error('No database connection selected');
      return false;
    }

    try {
      setIsScanning(true);
      console.log('🔍 Starting MLZ Data Scanner for connection:', selectedConnectionName);
      console.log('📡 Connection ID:', selectedConnection);
      toast.info('Starting MLZ data scanning...');

      // Step 1: Call GET API - get_selected_tables_columns with connection ID
      console.log('📡 Step 1: Calling GET API - get_selected_tables_columns');
      console.log(`🌐 Step 1 GET API URL: /metadata/get_selected_tables_columns?connection_id=${selectedConnection}`);
      console.log('🔍 Using connection ID:', selectedConnection);

      const getSelectedResult = await MetadataService.getSelectedTablesColumns(selectedConnection);
      console.log('✅ Step 1 GET API call successful:', getSelectedResult);
      console.log('📊 Step 1 GET API result:', JSON.stringify(getSelectedResult, null, 2));

      // Show success message with data count
      const dataCount = Array.isArray(getSelectedResult) ? getSelectedResult.length :
                       getSelectedResult.selected_tables_columns ? getSelectedResult.selected_tables_columns.length :
                       getSelectedResult.table_list ? getSelectedResult.table_list.length : 'unknown';

      toast.success(`✅ Step 1 - GET selected tables successful! Found ${dataCount} selected tables/columns`);

      // Step 2: Call DELETE API - delete_metadata_content_by_connection
      console.log('📡 Step 2: Calling DELETE API - delete_metadata_content_by_connection');
      console.log(`🌐 Step 2 DELETE API URL: /metadata/delete_metadata_content_by_connection/${selectedConnection}`);

      const deleteResult = await MetadataService.deleteMetadataContentByConnection(selectedConnection);
      console.log('✅ Step 2 DELETE API call successful:', deleteResult);
      toast.success('✅ Step 2 - DELETE API successful! Metadata content deleted');

      // Step 3: Call GET API - get_metadata_by_connection (response will be used for POST payload)
      console.log('📡 Step 3: Calling GET API - get_metadata_by_connection (response for POST payload)');
      console.log(`🌐 Step 3 GET metadata API URL: /metadata/get_metadata_by_connection/${selectedConnection}`);

      const metadataResult = await MetadataService.getMetadataByConnection(selectedConnection);
      console.log('✅ Step 3 GET metadata API call successful:', metadataResult);
      console.log('📊 Step 3 GET metadata API result (for POST payload):', JSON.stringify(metadataResult, null, 2));
      toast.success('✅ Step 3 - GET metadata API successful! Retrieved metadata for POST payload');

      // Step 4: Call POST API - create_ai_database_metadata using Step 3 GET metadata response
      console.log('📡 Step 4: Calling POST API - create_ai_database_metadata using Step 3 response');
      console.log(`🌐 Step 4 POST API URL: /metadata/create_ai_database_metadata`);
      console.log('🔄 Using Step 3 GET metadata response as POST payload');

      // Prepare request body using Step 3 GET metadata response
      const postRequestBody = DatabaseHelpers.createAIMetadataPayload(metadataResult, selectedConnection);
      console.log('📊 Step 4 POST API request body (using Step 3 GET metadata response):', JSON.stringify(postRequestBody, null, 2));

      const createResult = await MetadataService.createAIDatabaseMetadata(postRequestBody);
      console.log('✅ Step 4 POST API call successful:', createResult);
      toast.success('✅ Step 4 - POST create AI metadata successful!');

      // Final step: Call GET API again for UI update
      console.log('📡 Final step: Calling GET API - get_metadata_by_connection for UI update');
      console.log(`🌐 GET final metadata API URL: /metadata/get_metadata_by_connection/${selectedConnection}`);

      try {
        const finalMetadataResult = await MetadataService.getMetadataByConnection(selectedConnection);
        console.log('✅ GET final metadata API call successful:', finalMetadataResult);

        // Call success callback with final metadata result
        if (onSuccess) {
          onSuccess(finalMetadataResult);
        }
        
        toast.success('✅ MLZ Scanner completed! Updated table and column data');
        return true;
      } catch (metadataError) {
        console.warn(`GET metadata API failed:`, metadataError);
        toast.warning('Could not load updated metadata - showing empty data');
        
        // Call success callback with empty result
        if (onSuccess) {
          onSuccess(null);
        }
        
        return true; // Still consider workflow successful
      }

      console.log('🎉 MLZ Data Scanner 4-step workflow completed successfully');

    } catch (error) {
      console.error('❌ MLZ Scanner workflow error:', error);
      toast.error(`MLZ Scanner failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return false;
    } finally {
      setIsScanning(false);
    }
  };

  return {
    isScanning,
    executeMLZScannerWorkflow
  };
};
