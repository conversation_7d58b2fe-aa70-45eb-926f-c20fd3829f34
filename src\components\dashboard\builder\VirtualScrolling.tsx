import React, { memo, useMemo, useCallback, useState, useRef, useEffect } from 'react';

interface VirtualScrollProps<T> {
  items: T[];
  itemHeight: number;
  containerHeight: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  overscan?: number;
  className?: string;
  onScroll?: (scrollTop: number) => void;
}

function VirtualScrollComponent<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5,
  className = '',
  onScroll
}: VirtualScrollProps<T>) {
  const [scrollTop, setScrollTop] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  // Calculate which items should be visible
  const visibleRange = useMemo(() => {
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
    const endIndex = Math.min(
      items.length - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
    );
    return { startIndex, endIndex };
  }, [scrollTop, itemHeight, containerHeight, items.length, overscan]);

  // Calculate total height and offset
  const totalHeight = items.length * itemHeight;
  const offsetY = visibleRange.startIndex * itemHeight;

  // Memoize visible items to prevent unnecessary re-renders
  const visibleItems = useMemo(() => {
    return items.slice(visibleRange.startIndex, visibleRange.endIndex + 1);
  }, [items, visibleRange.startIndex, visibleRange.endIndex]);

  // Handle scroll events
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const newScrollTop = e.currentTarget.scrollTop;
    setScrollTop(newScrollTop);
    onScroll?.(newScrollTop);
  }, [onScroll]);

  // Auto-scroll to top when items change significantly
  useEffect(() => {
    if (containerRef.current && items.length === 0) {
      containerRef.current.scrollTop = 0;
      setScrollTop(0);
    }
  }, [items.length]);

  return (
    <div
      ref={containerRef}
      className={`overflow-auto ${className}`}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${offsetY}px)` }}>
          {visibleItems.map((item, index) => (
            <div
              key={visibleRange.startIndex + index}
              style={{ height: itemHeight }}
            >
              {renderItem(item, visibleRange.startIndex + index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Export memoized component
const VirtualScrollMemoized = memo(VirtualScrollComponent);
VirtualScrollMemoized.displayName = 'VirtualScroll';

export const VirtualScroll = VirtualScrollMemoized as <T>(
  props: VirtualScrollProps<T>
) => JSX.Element;

// Hook for virtual scrolling with dynamic item heights
export const useVirtualScroll = <T,>(
  items: T[],
  containerHeight: number,
  estimatedItemHeight: number = 50
) => {
  const [scrollTop, setScrollTop] = useState(0);
  const [itemHeights, setItemHeights] = useState<number[]>([]);

  // Calculate average item height
  const averageItemHeight = useMemo(() => {
    if (itemHeights.length === 0) return estimatedItemHeight;
    return itemHeights.reduce((sum, height) => sum + height, 0) / itemHeights.length;
  }, [itemHeights, estimatedItemHeight]);

  // Calculate visible range for dynamic heights
  const visibleRange = useMemo(() => {
    let accumulatedHeight = 0;
    let startIndex = 0;
    let endIndex = items.length - 1;

    // Find start index
    for (let i = 0; i < items.length; i++) {
      const itemHeight = itemHeights[i] || averageItemHeight;
      if (accumulatedHeight + itemHeight > scrollTop) {
        startIndex = Math.max(0, i - 2); // Add some buffer
        break;
      }
      accumulatedHeight += itemHeight;
    }

    // Find end index
    accumulatedHeight = 0;
    for (let i = startIndex; i < items.length; i++) {
      const itemHeight = itemHeights[i] || averageItemHeight;
      accumulatedHeight += itemHeight;
      if (accumulatedHeight > containerHeight) {
        endIndex = Math.min(items.length - 1, i + 2); // Add some buffer
        break;
      }
    }

    return { startIndex, endIndex };
  }, [scrollTop, containerHeight, itemHeights, averageItemHeight, items.length]);

  // Function to update item height
  const updateItemHeight = useCallback((index: number, height: number) => {
    setItemHeights(prev => {
      const newHeights = [...prev];
      newHeights[index] = height;
      return newHeights;
    });
  }, []);

  return {
    visibleRange,
    scrollTop,
    setScrollTop,
    updateItemHeight,
    averageItemHeight
  };
};

// Performance monitoring hook
export const useRenderPerformance = (componentName: string) => {
  const renderCount = useRef(0);
  const lastRenderTime = useRef(Date.now());

  useEffect(() => {
    renderCount.current += 1;
    const now = Date.now();
    const timeSinceLastRender = now - lastRenderTime.current;
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Performance] ${componentName} render #${renderCount.current}, time since last: ${timeSinceLastRender}ms`);
    }
    
    lastRenderTime.current = now;
  });

  return renderCount.current;
};