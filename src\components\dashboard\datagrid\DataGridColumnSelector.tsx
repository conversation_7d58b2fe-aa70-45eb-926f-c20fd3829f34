import React from 'react';
import { X, Table } from 'lucide-react';
import { SelectedColumn } from '@/services/api/chart/datagrid/datagridTypes';

interface DataGridColumnSelectorProps {
  selectedColumns: SelectedColumn[];
  onColumnAdd: (column: SelectedColumn) => void;
  onColumnRemove: (columnName: string) => void;
  onDrop: (e: React.DragEvent) => void;
  onDragOver: (e: React.DragEvent) => void;
}

const DataGridColumnSelector: React.FC<DataGridColumnSelectorProps> = ({
  selectedColumns,
  onColumnAdd,
  onColumnRemove,
  onDrop,
  onDragOver
}) => {
  return (
    <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 min-h-[120px] bg-gray-50 hover:bg-gray-100 transition-colors">
      <div
        className="w-full h-full flex flex-col items-center justify-center"
        onDrop={onDrop}
        onDragOver={onDragOver}
      >
        {selectedColumns.length === 0 ? (
          <div className="text-center">
            <div className="mb-2">
              <div className="mb-2">
                <Table size={32} className="mx-auto text-gray-400" />
              </div>
              <h3 className="text-base font-medium text-gray-700 mb-1">No DataGrid Displayed</h3>
              <p className="text-gray-500 text-xs">Drag columns here to build your result set</p>
            </div>
          </div>
        ) : (
          <div className="w-full">
            <div className="flex flex-wrap gap-2 justify-center">
              {selectedColumns.map((column, index) => (
                <div
                  key={index}
                  className="bg-blue-100 text-blue-800 px-3 py-2 rounded-lg text-sm flex items-center border border-blue-200"
                >
                  {column.name}
                  <button
                    className="ml-2 text-blue-600 hover:text-blue-800"
                    onClick={() => onColumnRemove(column.name)}
                  >
                    <X size={16} />
                  </button>
                </div>
              ))}
            </div>
            <div className="text-center mt-4">
              <p className="text-gray-500 text-sm">Drag more columns here to add them to your result set</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DataGridColumnSelector;