import { toast } from 'sonner';
import { centralApiClient } from '@/services/api/centralApiClient';

// Define types for the API response
export interface DatabaseConnection {
  connection_id: number;
  connection_name: string;
  database_dialect: string;
  database_name: string;
}

export interface ConnectionsResponse {
  status: string;
  connections: DatabaseConnection[];
}

/**
 * Fetches user database connections from the DADA AI API
 * @param userId The user ID (defaults to 1)
 * @returns Promise with user connections data
 */
export const fetchUserConnections = async (userId: string = "a6e3020d-984a-4394-ac73-da7ec5393314"): Promise<DatabaseConnection[]> => {
  try {
    console.log(`Fetching user connections for user ID: ${userId}`);

    const data = await centralApiClient.makeRequest<ConnectionsResponse>('dada', `/database/user-connections-detailed/${userId}`, {
      method: 'GET'
    });

    console.log('User connections API response:', data);

    return data.connections || [];
  } catch (error) {
    console.error('Error fetching user connections:', error);
    toast.error('Failed to load database connections', {
      description: error instanceof Error ? error.message : 'Unknown error'
    });
    return [];
  }
};