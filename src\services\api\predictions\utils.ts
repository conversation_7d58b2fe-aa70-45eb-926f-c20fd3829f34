
/**
 * Parse the input query to extract power keyword, command, and parameter
 */
export const parsePredictionQuery = (query: string): {
  powerKeyword?: string;
  command?: string;
  parameter?: string;
} => {
  if (!query.startsWith('@')) {
    return {};
  }

  // Strip the @ prefix, whether it's @ or @@
  const cleanQuery = query.startsWith('@@') ? query.substring(2) : query.substring(1);
  const parts = cleanQuery.trim().split(' ').filter(Boolean);
  const result: { powerKeyword?: string; command?: string; parameter?: string } = {};
  
  // Extract power keyword
  if (parts.length >= 1) {
    result.powerKeyword = parts[0];
  }
  
  // Extract command
  if (parts.length >= 2) {
    result.command = parts[1];
  }
  
  // Extract parameter
  if (parts.length >= 3) {
    result.parameter = parts.slice(2).join(' '); // Join all remaining parts as the parameter
  }
  
  return result;
};

/**
 * Determine if the input is in a power keyword sequence
 */
export const isInCommandSequence = (query: string): boolean => {
  return query.startsWith('@');
};

/**
 * Clean a power keyword to remove @ prefix
 */
export const cleanPowerKeyword = (powerKeyword?: string): string => {
  if (!powerKeyword) return '';
  return powerKeyword.replace(/^@+/, '');
};

/**
 * Format query parameters for API calls
 */
export const formatQueryParams = (params: {
  query: string;
  powerKeyword?: string;
  command?: string;
  parameter?: string;
}): URLSearchParams => {
  const urlParams = new URLSearchParams({
    query: params.query
  });
  
  if (params.powerKeyword) {
    // Send the power_keyword without @ prefix for the backend
    urlParams.append('power_keyword', params.powerKeyword);
  }
  
  if (params.command) urlParams.append('command', params.command);
  if (params.parameter) urlParams.append('parameter', params.parameter);
  
  return urlParams;
};
