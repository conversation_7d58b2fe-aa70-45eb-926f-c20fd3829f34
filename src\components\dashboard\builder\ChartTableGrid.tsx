import React, { memo, useCallback } from 'react';
import { useDroppable } from '@dnd-kit/core';
import { ChartCard } from './ChartCard';
import { TableCard } from './TableCard';
import { CardState, ChartTableGridProps } from './types';


const ChartTableGrid: React.FC<ChartTableGridProps> = memo(({
  className = '',
  chartZoneChart,
  tableZoneChart,
  chartData,
  loadingChartData,
  onRemoveChart,
  tabularData,
  loadingTabularData,
  chartCards = [],
  tableCards = [],
  onRemoveCard,
  cards = [],
  onUpdateCard
}) => {
  // Chart Zone Droppable
  const {
    isOver: isOverChart,
  } = useDroppable({
    id: 'chart-zone',
    data: {
      type: 'chart-zone',
    },
  });

  // Table Zone Droppable
  const {
    isOver: isOverTable,
  } = useDroppable({
    id: 'table-zone',
    data: {
      type: 'table-zone',
    },
  });

  // Memoize render functions to prevent unnecessary re-renders
  const renderChartCard = useCallback((cardId: string) => {
    const cardData = cards.find(card => card.id === cardId);
    const hasCardContent = cardData?.chart !== null;

    return (
      <ChartCard
        key={cardId}
        cardId={cardId}
        cardData={cardData}
        hasCardContent={hasCardContent}
        chartZoneChart={chartZoneChart}
        chartData={chartData}
        loadingChartData={loadingChartData}
        isOverChart={isOverChart}
        onUpdateCard={onUpdateCard}
        onRemoveChart={onRemoveChart}
        onRemoveCard={onRemoveCard}
      />
    );
  }, [cards, chartZoneChart, chartData, loadingChartData, isOverChart, onUpdateCard, onRemoveChart, onRemoveCard]);

  const renderTableCard = useCallback((cardId: string) => {
    const cardData = cards.find(card => card.id === cardId);
    const hasCardContent = cardData?.chart !== null;

    return (
      <TableCard
        key={cardId}
        cardId={cardId}
        cardData={cardData}
        hasCardContent={hasCardContent}
        tableZoneChart={tableZoneChart}
        tabularData={tabularData}
        loadingTabularData={loadingTabularData}
        isOverTable={isOverTable}
        onUpdateCard={onUpdateCard}
        onRemoveChart={onRemoveChart}
        onRemoveCard={onRemoveCard}
      />
    );
  }, [cards, tableZoneChart, tabularData, loadingTabularData, isOverTable, onUpdateCard, onRemoveChart, onRemoveCard]);

  // Memoized unified card rendering function
  const renderUnifiedCard = useCallback((card: CardState) => {
    if (card.type === 'chart') {
      return renderChartCard(card.id);
    } else {
      return renderTableCard(card.id);
    }
  }, [renderChartCard, renderTableCard]);

  return (
    <div className={`flex flex-wrap gap-6 ${className}`}>
      {/* Render cards in creation order */}
      {cards.map(card => (
        <div key={card.id}>
          {renderUnifiedCard(card)}
        </div>
      ))}

      {/* Fallback: Render legacy cards if no unified cards exist */}
      {cards.length === 0 && (
        <>
          {chartCards.map(cardId => renderChartCard(cardId))}
          {tableCards.map(cardId => renderTableCard(cardId))}
        </>
      )}
    </div>
  );
});

ChartTableGrid.displayName = 'ChartTableGrid';

export default ChartTableGrid;
