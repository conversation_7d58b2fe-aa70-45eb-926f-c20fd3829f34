
import { PayloadAction } from '@reduxjs/toolkit';
import { DatasetState } from '../types';

export const tableReducers = {
  toggleTableSelection: (state: DatasetState, action: PayloadAction<string>) => {
    const table = action.payload;
    if (state.selectedTables.includes(table)) {
      state.selectedTables = state.selectedTables.filter(t => t !== table);
    } else {
      state.selectedTables.push(table);
    }
  },
  
  addToSelectedTables: (state: DatasetState, action: PayloadAction<string[]>) => {
    action.payload.forEach(table => {
      if (!state.selectedTables.includes(table)) {
        state.selectedTables.push(table);
      }
    });
  },
  
  removeFromSelectedTables: (state: DatasetState, action: PayloadAction<string[]>) => {
    state.selectedTables = state.selectedTables.filter(
      t => !action.payload.includes(t)
    );
  },
};
