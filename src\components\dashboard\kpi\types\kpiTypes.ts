// KPI Types and Interfaces
export type KPIViewType = 'kpi-only' | 'current-vs-prior' | 'target-based';

export interface KPIFormData {
  viewType: KPIViewType;
  label: string;
  sql: string;
  additionalInfo: string;
  currentValue?: string;
  priorValue?: string;
  target?: string;
  // New fields for CurrentVsPrior
  currentLabel?: string;
  priorLabel?: string;
  priorSql?: string;
}

export interface KPIFieldConfig {
  label: string;
  placeholder: string;
  required: boolean;
  type: 'text' | 'textarea';
}

export interface KPIPreviewData {
  currentValue: number | string;
  priorValue?: number | string;
  target?: number | string;
  label: string;
  additionalInfo: string;
  changePercentage?: number;
  achievementPercentage?: number;
  trend?: 'up' | 'down' | 'neutral';
}

// API Response from KPI execution
export interface KPIAPIResponse {
  kpi_type: string;
  // For KPI Only
  value?: number;
  // For CurrentVsPrior
  current_label?: string;
  prior_label?: string;
  current_value?: number;
  prior_value?: number;
  percent_change?: number;
  // For TargetBased
  target_value?: number;
  percent_to_target?: number;
  // Common
  additional_info: string;
}

// Validation result interface
export interface KPIValidationResult {
  isValid: boolean;
  message?: string;
}

export interface KPIBuilderProps {
  onSave?: () => void;
  onExecute?: (kpiData: KPIFormData) => void;
  onValidateSQL?: (sql: string) => Promise<KPIValidationResult>;
  isLoading?: boolean;
  initialData?: Partial<KPIFormData>;
}

export interface KPIViewSelectorProps {
  selectedView: KPIViewType;
  onViewChange: (view: KPIViewType) => void;
}

export interface KPIFormFieldsProps {
  formData: KPIFormData;
  onFormChange: (data: Partial<KPIFormData>) => void;
  errors?: Record<string, string>;
  onValidateSQL?: (sql: string, fieldKey: string) => Promise<KPIValidationResult>;
  isValidating?: Record<string, boolean>;
  validationResults?: Record<string, KPIValidationResult | null>;
  onValidateField?: (fieldName: string) => boolean;
}

export interface KPIPreviewProps {
  formData: KPIFormData;
  previewData?: KPIPreviewData;
  isLoading?: boolean;
}



// Field configurations for different KPI types
export const KPI_FIELD_CONFIGS: Record<KPIViewType, KPIFieldConfig[]> = {
  'kpi-only': [
    {
      label: 'KPI Label',
      placeholder: 'Enter KPI label (e.g., Monthly Sales)',
      required: true,
      type: 'text'
    },
    {
      label: 'SQL Query',
      placeholder: 'Enter SQL query to fetch KPI data',
      required: true,
      type: 'textarea'
    },
    {
      label: 'Additional Info',
      placeholder: 'Display this text at the bottom of the KPI Card',
      required: false,
      type: 'textarea'
    }
  ],
  'current-vs-prior': [
    {
      label: 'KPI Current Label',
      placeholder: 'Enter current label (e.g., This Month)',
      required: true,
      type: 'text'
    },
    {
      label: 'KPI Prior Label',
      placeholder: 'Enter prior label (e.g., Last Month)',
      required: true,
      type: 'text'
    },
    {
      label: 'Current SQL Query',
      placeholder: 'Enter Current SQL query to fetch current value',
      required: true,
      type: 'textarea'
    },
    {
      label: 'Prior SQL Query',
      placeholder: 'Enter Prior SQL query to fetch prior value',
      required: true,
      type: 'textarea'
    },
    {
      label: 'Additional Info',
      placeholder: 'Display this text at the bottom of the KPI Card',
      required: false,
      type: 'textarea'
    }
  ],
  'target-based': [
    {
      label: 'KPI Label',
      placeholder: 'Enter KPI label (e.g., Monthly Sales)',
      required: true,
      type: 'text'
    },
    {
      label: 'Target',
      placeholder: 'Enter target value',
      required: true,
      type: 'text'
    },
    {
      label: 'SQL Query',
      placeholder: 'Enter SQL query to fetch current value',
      required: true,
      type: 'textarea'
    },
    {
      label: 'Additional Info',
      placeholder: 'Display this text at the bottom of the KPI Card',
      required: false,
      type: 'textarea'
    }
  ]
};
