
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import DashboardContainer from '@/components/dashboard/layout/DashboardContainer';
import DashboardMain from '@/components/dashboard/layout/DashboardMain';
import Header from '@/components/dashboard/Header';
import ListDashboardView from '@/components/dashboard/sidebar/views/ListDashboardView';
import ChartboardView from '@/components/dashboard/ChartboardView';

const ChartboardPage: React.FC = () => {
  const { chartId } = useParams<{ chartId?: string }>();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentViewType, setCurrentViewType] = useState<'chart' | 'kpi' | 'datagrid'>('chart');
  const isEditMode = Boolean(chartId);
  const navigate = useNavigate();
  
  const toggleSidebar = () => setSidebarCollapsed(prev => !prev);
  
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };
  
  const handleHeaderSearch = (query: string) => {
    console.log("Search query:", query);
  };

  const handleBackToMarketPlace = () => {
    navigate('/saved-charts');
  };

  const handleViewTypeChange = (viewType: 'chart' | 'kpi' | 'datagrid') => {
    setCurrentViewType(viewType);
  };

  const getDynamicDashboardName = () => {
    switch (currentViewType) {
      case 'chart':
        return 'Chart Builder';
      case 'kpi':
        return 'KPI Builder';
      case 'datagrid':
        return 'DataGrid Builder';
      default:
        return 'Chart Builder';
    }
  };

  return (
    <DashboardContainer textSize="medium">
      <Header
        toggleSidebar={toggleSidebar}
        sidebarCollapsed={sidebarCollapsed}
        rightSidebarVisible={false}
        dashboardName={getDynamicDashboardName()}
        toggleRightSidebar={() => {}}
        onHeaderSearch={handleHeaderSearch}
        dashboardType={2}
      />
      
      <DashboardMain>
        <div className="flex h-full w-full">
          {/* Always render sidebar - it will handle collapsed state internally */}
          <ListDashboardView
            searchQuery={searchQuery}
            handleSearch={handleSearch}
            filteredItems={[]}
            toggleItem={() => {}}
            addChatToItem={() => {}}
            addNewItem={() => {}}
            dashboardType={2}
            onToggle={toggleSidebar}
            collapsed={sidebarCollapsed}
          />
          
          <div className="flex-1 overflow-y-auto">
            {isEditMode && (
              <div className="flex justify-end p-4">
                <Button
                  variant='outline'
                  className="flex items-center gap-2 text-blue-600 hover:text-blue-800 text-sm"
                  onClick={handleBackToMarketPlace}
                > 
                  Back to Market Place
                  <ArrowRight size={16} />
                </Button>
              </div>
            )}
            <ChartboardView onViewTypeChange={handleViewTypeChange} />
          </div>
        </div>
      </DashboardMain>
    </DashboardContainer>
  );
};

export default ChartboardPage;
