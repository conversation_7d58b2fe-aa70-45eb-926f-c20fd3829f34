<!DOCTYPE html>
<html>
<head>
    <title>Test Chart Debug</title>
</head>
<body>
    <h1>Chart Debug Test</h1>
    <p>Open the browser console to see DADA AI chart debug output.</p>
    <script>
        // Simulate the DADA AI data structure you provided
        const testQueryResult = {
            "sql_query": "SELECT\n    COALESCE(\"gender\", 'Unknown') AS \"gender\",\n    COUNT(*)::INTEGER AS \"patient_count\"\nFROM\n    \"patient\"\nGROUP BY\n    COALESCE(\"gender\", 'Unknown')\nORDER BY\n    \"patient_count\" DESC\nLIMIT 3;",
            "sql_result": [
                {
                    "gender": "Male",
                    "patient_count": 344
                },
                {
                    "gender": "Other",
                    "patient_count": 330
                },
                {
                    "gender": "Female",
                    "patient_count": 326
                }
            ],
            "user_query": "give top 3 patients gender count",
            "visualization": {
                "can_visualize": true,
                "best_chart_type": "bar",
                "chart_title": "Top 3 Patients Gender Count",
                "x_axis_key_name": "gender",
                "y_axis_key_name": "patient_count"
            }
        };

        console.log('=== TEST DATA ===');
        console.log('DADA AI Query Result:', testQueryResult);
        console.log('SQL Result Data:', testQueryResult.sql_result);
        console.log('Expected Values:', testQueryResult.sql_result.map(item => item.patient_count));
        console.log('This should show [344, 330, 326] on the chart Y-axis, NOT [0.8, 0.6, etc.]');
    </script>
</body>
</html>