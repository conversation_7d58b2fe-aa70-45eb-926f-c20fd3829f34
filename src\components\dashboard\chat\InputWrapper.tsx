
import React, { forwardRef, useRef } from "react";
import { ChatTextarea } from "./ChatTextarea";

interface InputWrapperProps {
  dashboardType: 1 | 2 | 3;
  placeholder: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  onKeyDown: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
  onFocus: () => void;
  disabled: boolean;
}

const InputWrapper = forwardRef<HTMLTextAreaElement, InputWrapperProps>(
  ({ dashboardType, placeholder, value, onChange, onKeyDown, onFocus, disabled }, ref) => {
    return (
      <div className="relative w-full">
        <ChatTextarea 
          ref={ref}
          placeholder={placeholder}
          value={value}
          onChange={onChange}
          onKeyDown={onKeyDown}
          onFocus={onFocus}
          disabled={disabled}
          dashboardType={dashboardType}
        />
      </div>
    );
  }
);

InputWrapper.displayName = "InputWrapper";

export default InputWrapper;
