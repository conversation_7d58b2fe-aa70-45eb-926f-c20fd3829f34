
import { useState, useEffect } from 'react';
import { useAppDispatch } from '@/hooks/useRedux';
import { setError, clearError } from '@/stores/errorSlice';

export const useNetworkStatus = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [wasOffline, setWasOffline] = useState(false);
  const dispatch = useAppDispatch();

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      if (wasOffline) {
        dispatch(clearError());
        setWasOffline(false);
      }
    };

    const handleOffline = () => {
      setIsOnline(false);
      setWasOffline(true);
      dispatch(setError({
        message: 'You are currently offline. Please check your internet connection.',
        statusCode: 0
      }));
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Initial check
    if (!navigator.onLine) {
      handleOffline();
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [dispatch, wasOffline]);

  return { isOnline, wasOffline };
};
