
import React from 'react';
import { Share, Plus } from 'lucide-react';
import { ChatTopic } from './types';

interface ChatTopicsListProps {
  items?: ChatTopic[];
  toggleItem?: (id: string) => void;
  addChatToItem?: (id: string) => void;
  // For backward compatibility
  topics?: ChatTopic[];
  toggleTopic?: (id: string) => void;
  addChatToTopic?: (id: string) => void;
}

const ChatTopicsList: React.FC<ChatTopicsListProps> = ({ 
  items, 
  toggleItem,
  addChatToItem,
  // Support both naming conventions for backward compatibility
  topics,
  toggleTopic,
  addChatToTopic
}) => {
  // Use items if provided, otherwise use topics, or default to empty array
  const topicsList = items || topics || [];
  // Use the provided toggle function or default to a no-op function
  const handleToggle = toggleItem || toggleTopic || ((id: string) => {});
  // Use the provided add function or default to undefined
  const handleAddChat = addChatToItem || addChatToTopic;
  
  return (
    <div className="space-y-2">
      {topicsList.map((topic) => (
        <div key={topic.id} className="mb-1">
          <div 
            className="flex items-center justify-between p-2 hover:bg-gray-100 rounded cursor-pointer"
            onClick={() => handleToggle(topic.id)}
          >
            <span className={`text-sm ${topic.expanded ? 'font-medium text-green-700' : 'text-gray-700'}`}>
              {topic.name}
            </span>
            <div className="flex items-center space-x-2">
              <button 
                className="text-gray-500 hover:text-gray-700 p-1"
                onClick={(e) => {
                  e.stopPropagation();
                  handleAddChat && handleAddChat(topic.id);
                }}
              >
                <Plus size={16} />
              </button>
              <button 
                className="text-gray-500 hover:text-gray-700 p-1"
                onClick={(e) => e.stopPropagation()}
              >
                <Share size={16} />
              </button>
            </div>
          </div>
          
          {topic.expanded && topic.chats && topic.chats.length > 0 && (
            <div className="ml-4 mt-1 space-y-1">
              {topic.chats.map((chat, index) => (
                <div 
                  key={index}
                  className="p-2 hover:bg-gray-100 rounded cursor-pointer text-sm text-gray-600"
                >
                  {chat}
                </div>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default ChatTopicsList;
