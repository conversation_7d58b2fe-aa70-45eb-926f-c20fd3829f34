import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ArrowLeft, Plus } from 'lucide-react';
import { toast } from 'sonner';

interface Dataview {
  id: string;
  name: string;
}

interface DatabaseConnection {
  id: string;
  name: string;
}

const DataviewBuilder: React.FC = () => {
  const navigate = useNavigate();
  const [dataviews, setDataviews] = useState<Dataview[]>([]);
  const [selectedDataview, setSelectedDataview] = useState<Dataview | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isValidating, setIsValidating] = useState(false);
  
  // Form state
  const [dataviewName, setDataviewName] = useState('DV_MonthlySales');
  const [viewDescription, setViewDescription] = useState('This view can be used to analyze monthly sales');
  const [sourceConnection, setSourceConnection] = useState('');

  const [sqlQuery, setSqlQuery] = useState('Select Col1,\nCol2 From Table1 where');
  
  // Mock data
  const [databaseConnections] = useState<DatabaseConnection[]>([
    { id: '1', name: 'Sales Database' },
    { id: '2', name: 'Analytics DB' },
    { id: '3', name: 'Customer DB' }
  ]);

  useEffect(() => {
    const mockDataviews: Dataview[] = [
      { id: '1', name: 'Dataview1' },
      { id: '2', name: 'Dataview2' },
      { id: '3', name: 'Dataview3' }
    ];
    
    setTimeout(() => {
      setDataviews(mockDataviews);
      setSelectedDataview(mockDataviews[0]);
      setIsLoading(false);
    }, 500);
  }, []);

  const filteredDataviews = dataviews.filter(dataview =>
    dataview.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleDataviewSelect = (dataview: Dataview) => {
    setSelectedDataview(dataview);
  };

  const handleAddDataview = () => {
    const newDataview: Dataview = {
      id: Date.now().toString(),
      name: `Dataview${dataviews.length + 1}`
    };
    setDataviews(prev => [...prev, newDataview]);
    setSelectedDataview(newDataview);
    toast.success('New dataview added');
  };

  const handlePreview = () => {
    toast.info('Preview functionality coming soon');
  };

  const handleSendForApproval = () => {
    toast.success('Dataview sent for approval');
  };

  const handleSave = () => {
    toast.success('Dataview saved successfully');
  };

  const handleModify = () => {
    toast.info('Modify functionality enabled');
  };

  const handleValidate = async () => {
    if (!sqlQuery.trim()) {
      toast.error('Please enter a SQL query to validate');
      return;
    }

    setIsValidating(true);

    try {
      console.log('🔍 Validating SQL Query:', sqlQuery);

      const response = await fetch('http://10.100.0.5:8000/api/dataviews/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sql_query: sqlQuery
        }),
      });

      console.log('📥 Validation Response Status:', response.status);

      if (!response.ok) {
        const errorData = await response.text();
        console.error('❌ Validation Error Response:', errorData);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorData}`);
      }

      const result = await response.json();
      console.log('✅ Validation Result:', result);

      toast.success('SQL query validated successfully');
    } catch (error) {
      console.error('❌ Error validating SQL query:', error);
      toast.error(`Failed to validate SQL query: ${error.message}`);
    } finally {
      setIsValidating(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg font-normal text-gray-600">Loading dataview builder...</div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Left Sidebar */}
      <div className="w-80 bg-white border-r border-gray-200 flex flex-col shadow-sm">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <h3 className="text-base font-medium text-gray-700 mb-3">Dataviews</h3>
          <div className="flex items-center space-x-3">
            <Input
              type="text"
              placeholder="Search dataviews..."
              className="flex-1 text-sm border-gray-300 focus:border-teal-500 focus:ring-1 focus:ring-teal-500"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Button
              size="sm"
              onClick={handleAddDataview}
              className="bg-teal-600 hover:bg-teal-700 text-white font-medium text-sm px-3 py-2 transition-colors duration-200"
            >
              <Plus className="w-4 h-4 mr-1" />
              Add
            </Button>
          </div>
        </div>

        {/* Dataview List */}
        <div className="flex-1 overflow-y-auto">
          {filteredDataviews.map((dataview) => (
            <div
              key={dataview.id}
              className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 font-medium text-sm transition-colors duration-150 ${
                selectedDataview?.id === dataview.id
                  ? 'bg-teal-50 border-l-4 border-l-teal-600 text-teal-700'
                  : 'text-gray-700 hover:text-gray-900'
              }`}
              onClick={() => handleDataviewSelect(dataview)}
            >
              <div className="flex items-center justify-between">
                <span>{dataview.name}</span>
                {selectedDataview?.id === dataview.id && (
                  <div className="w-2 h-2 bg-teal-600 rounded-full"></div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header with Back Button */}
        <div className="p-6 bg-white border-b border-gray-200 shadow-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={() => navigate('/Admin')}
                className="p-2 text-teal-600 hover:text-teal-800 hover:bg-teal-50 font-medium transition-colors duration-200"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Admin
              </Button>
              <div className="h-6 w-px bg-gray-300"></div>
              <h1 className="text-xl font-semibold text-gray-800">Dataview Builder</h1>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white border-b border-gray-200">
          <div className="flex px-6">
            <button
              className="px-6 py-3 text-sm font-medium border-b-2 border-teal-600 text-teal-600 bg-white"
            >
              {selectedDataview?.name || 'Dataview1'}
            </button>
            {/* <button
              className="px-6 py-3 text-sm font-medium text-gray-500 hover:text-gray-700 hover:bg-gray-50 transition-colors duration-200"
            >
              Dataview2
            </button> */}
          </div>
        </div>

        {/* Form Content */}
        <div className="flex-1 p-6 overflow-y-auto bg-gray-50">
          <div className="max-w-6xl mx-auto">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Left Column */}
                <div className="space-y-6">
                  {/* Dataview Name */}
                  <div className="space-y-2">
                    <Label htmlFor="dataviewName" className="text-sm font-medium text-gray-700">
                      Dataview Name <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="dataviewName"
                      value={dataviewName}
                      onChange={(e) => setDataviewName(e.target.value)}
                      className="border-gray-300 focus:border-teal-500 focus:ring-1 focus:ring-teal-500 transition-all duration-200"
                      placeholder="Enter dataview name"
                    />
                  </div>

                  {/* View Description */}
                  <div className="space-y-2">
                    <Label htmlFor="viewDescription" className="text-sm font-medium text-gray-700">
                      View Description
                    </Label>
                    <Textarea
                      id="viewDescription"
                      value={viewDescription}
                      onChange={(e) => setViewDescription(e.target.value)}
                      className="border-gray-300 focus:border-teal-500 focus:ring-1 focus:ring-teal-500 h-24 resize-none transition-all duration-200"
                      placeholder="Describe the purpose of this dataview"
                    />
                  </div>


                </div>

                {/* Right Column */}
                <div className="space-y-6">
                  {/* Source DB Connection */}
                  <div className="space-y-2">
                    <Label htmlFor="sourceConnection" className="text-sm font-medium text-gray-700">
                      Source DB Connection <span className="text-red-500">*</span>
                    </Label>
                    <Select value={sourceConnection} onValueChange={setSourceConnection}>
                      <SelectTrigger className="border-gray-300 focus:border-teal-500 focus:ring-1 focus:ring-teal-500 transition-all duration-200">
                        <SelectValue placeholder="Select connection" />
                      </SelectTrigger>
                      <SelectContent>
                        {databaseConnections.map((conn) => (
                          <SelectItem key={conn.id} value={conn.id} className="font-medium">
                            {conn.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>


                </div>
              </div>

              {/* SQL Query Section */}
              <div className="mt-8 space-y-3">
                <div className="flex items-center justify-between">
                  <Label htmlFor="sqlQuery" className="text-sm font-medium text-gray-700">
                    SQL Query <span className="text-red-500">*</span>
                  </Label>
                  <div className="flex space-x-3">
                    <Button
                      onClick={handleModify}
                      className="bg-teal-600 hover:bg-teal-700 text-white font-medium px-4 py-2 text-sm transition-colors duration-200"
                    >
                      Modify
                    </Button>
                    <Button
                      onClick={handleValidate}
                      disabled={isValidating}
                      className="bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-medium px-4 py-2 text-sm transition-colors duration-200"
                    >
                      {isValidating ? 'Validating...' : 'Validate'}
                    </Button>
                  </div>
                </div>
                <Textarea
                  id="sqlQuery"
                  value={sqlQuery}
                  onChange={(e) => setSqlQuery(e.target.value)}
                  className="border-gray-300 focus:border-teal-500 focus:ring-1 focus:ring-teal-500 h-48 font-mono text-sm transition-all duration-200"
                  placeholder="Enter your SQL query here..."
                />
              </div>

              {/* Bottom Action Buttons */}
              <div className="flex justify-between items-center mt-8 pt-6 border-t border-gray-200">
                <Button
                  onClick={handlePreview}
                  variant="outline"
                  className="border-gray-300 text-gray-700 hover:bg-gray-50 font-medium px-6 py-2 transition-colors duration-200"
                >
                  Preview
                </Button>
                <div className="flex space-x-3">
                  <Button
                    onClick={handleSendForApproval}
                    className="bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-2 transition-colors duration-200"
                  >
                    Send for Approval
                  </Button>
                  <Button
                    onClick={handleSave}
                    className="bg-teal-600 hover:bg-teal-700 text-white font-medium px-6 py-2 transition-colors duration-200"
                  >
                    Save
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Preview Section */}
        <div className="bg-white border-t border-gray-200 p-6 shadow-sm">
          <div className="max-w-6xl mx-auto">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <span className="text-sm font-medium text-gray-700">Dataview Preview:</span>
                <div className="flex items-center space-x-3">
                  <Input
                    placeholder="Enter preview name"
                    className="w-48 text-sm border-gray-300 focus:border-teal-500 focus:ring-1 focus:ring-teal-500 transition-all duration-200"
                  />
                  <Button
                    className="bg-teal-600 hover:bg-teal-700 text-white font-medium px-4 py-2 transition-colors duration-200"
                  >
                    GO
                  </Button>
                </div>
              </div>
              <div className="text-xs text-gray-500">
                Last saved: {new Date().toLocaleTimeString()}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DataviewBuilder;
