import { centralApiClient } from '@/services/api/centralApiClient';

// Define the User interface to match the API payload
export interface CreateUserPayload {
  username: string;
  name: string;
  source: string;
  email: string;
  status: string;
  password: string;
}

// Define the User interface for the response
export interface User {
  id: string;
  email: string;
  name: string;
  source: 'External' | 'DADA';
  isAdmin: boolean;
  status: 'Active' | 'In Active';
  createdAt: string;
  username?: string;
}

// Function to create a new user
export const createUser = async (userData: CreateUserPayload): Promise<User> => {
  try {
    console.log('Creating user with data:', userData);

    const responseData = await centralApiClient.makeRequest<any>('dada', '/account-management/create-admin-user', {
      method: 'POST',
      body: userData
    });

    console.log('User created successfully:', responseData);
    
    // Transform the response to match our User interface
    const transformedUser: User = {
      id: responseData.id || responseData.user_id || Date.now().toString(),
      email: responseData.email || userData.email,
      name: responseData.name || userData.name,
      source: (responseData.source || userData.source) as 'External' | 'DADA',
      isAdmin: responseData.isAdmin || responseData.is_admin || false,
      status: (responseData.status || userData.status) as 'Active' | 'In Active',
      createdAt: responseData.created_at || responseData.createdAt || new Date().toISOString().split('T')[0],
      username: responseData.username || userData.username
    };

    return transformedUser;
  } catch (error) {
    console.error('Error creating user:', error);
    throw error;
  }
};

// Function to delete a user
export const deleteUser = async (userId: string): Promise<void> => {
  try {
    console.log('Deleting user with ID:', userId);

    await centralApiClient.makeRequest<any>('dada', `/account-management/delete-user/${userId}`, {
      method: 'DELETE'
    });

    console.log('User deleted successfully:', userId);
  } catch (error) {
    console.error('Error deleting user:', error);
    throw error;
  }
};

// Function to delete multiple users
export const deleteMultipleUsers = async (userIds: string[]): Promise<void> => {
  try {
    console.log('Deleting multiple users with IDs:', userIds);

    // Delete users one by one since the API doesn't support bulk delete
    const deletePromises = userIds.map(userId => deleteUser(userId));
    await Promise.all(deletePromises);

    console.log('Multiple users deleted successfully');
  } catch (error) {
    console.error('Error deleting multiple users:', error);
    throw error;
  }
};

// Function to get all users using the correct endpoint
export const getUsers = async (): Promise<User[]> => {
  try {
    console.log('Fetching all users');

    const responseData = await centralApiClient.makeRequest<any>('dada', '/account-management/get-all-users', {
      method: 'GET'
    });

    console.log('Users fetched successfully:', responseData);

    // Handle different response formats
    let users: any[] = [];
    if (Array.isArray(responseData)) {
      users = responseData;
    } else if (responseData && responseData.users && Array.isArray(responseData.users)) {
      users = responseData.users;
    } else if (responseData && responseData.data && Array.isArray(responseData.data)) {
      users = responseData.data;
    }

    // Transform the response to match our User interface
    return users.map((user: any): User => ({
      id: user.id || user.user_id || user.userId,
      email: user.email,
      name: user.name || `${user.first_name || ''} ${user.last_name || ''}`.trim(),
      source: (user.source || 'DADA') as 'External' | 'DADA',
      isAdmin: user.isAdmin || user.is_admin || false,
      status: (user.status || 'Active') as 'Active' | 'In Active',
      createdAt: user.created_at || user.createdAt || new Date().toISOString().split('T')[0],
      username: user.username
    }));
  } catch (error) {
    console.error('Error fetching users:', error);
    // Return empty array if the endpoint doesn't exist or fails
    return [];
  }
};

// Function to update a user
export const updateUser = async (userId: string, userData: Partial<CreateUserPayload>): Promise<User> => {
  try {
    console.log('Updating user with ID:', userId, userData);

    const responseData = await centralApiClient.makeRequest<any>('dada', `/account-management/update-user/${userId}`, {
      method: 'PUT',
      body: userData
    });

    console.log('User updated successfully:', responseData);

    // Transform the response to match our User interface
    const transformedUser: User = {
      id: responseData.id || responseData.user_id || userId,
      email: responseData.email || userData.email || '',
      name: responseData.name || userData.name || '',
      source: (responseData.source || userData.source || 'DADA') as 'External' | 'DADA',
      isAdmin: responseData.isAdmin || responseData.is_admin || false,
      status: (responseData.status || userData.status || 'Active') as 'Active' | 'In Active',
      createdAt: responseData.created_at || responseData.createdAt || new Date().toISOString().split('T')[0],
      username: responseData.username || userData.username
    };

    return transformedUser;
  } catch (error) {
    console.error('Error updating user:', error);
    throw error;
  }
};

// Helper function to transform form data to API payload
export const transformFormDataToPayload = (formData: {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  source: 'External' | 'DADA';
  isAdmin: boolean;
}): CreateUserPayload => {
  return {
    username: formData.email, // Use email as username if no separate username field
    name: `${formData.firstName} ${formData.lastName}`.trim(),
    source: formData.source,
    email: formData.email,
    status: 'Active', // Default status for new users
    password: formData.password
  };
};

// Helper function to transform user data to form data for editing
export const transformUserToFormData = (user: User): {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  source: 'External' | 'DADA';
  isAdmin: boolean;
} => {
  const nameParts = user.name.split(' ');
  const firstName = nameParts[0] || '';
  const lastName = nameParts.slice(1).join(' ') || '';

  return {
    firstName,
    lastName,
    email: user.email,
    password: '', // Don't populate password for security
    source: user.source,
    isAdmin: user.isAdmin
  };
};
