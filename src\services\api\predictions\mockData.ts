
import { PredictionParams } from "./types";

/**
 * Get appropriate mock predictions based on the query stage
 */
export const getMockPredictions = (
  query: string, 
  powerKeyword?: string, 
  command?: string, 
  parameter?: string
): string[] => {
  console.log("Getting mock predictions with:", { query, powerKeyword, command, parameter });
  
  // Case 1: Initial @@ - return power keywords
  if (query === '@' || (query.startsWith('@') && !powerKeyword)) {
    return ["@select", "@filter", "@show", "@aggregate"];
  }
  
  // Case 2: Power keyword selected - return commands
  if (powerKeyword && !command) {
    switch(powerKeyword.toLowerCase()) {
      case 'select':
        return ["@select all", "@select where", "@@elect top"];
      case 'filter':
        return ["@filter equals", "@filter contains", "@ilter greater"];
      case 'show':
        return ["@show patients", "@@show doctors", "@@show appointments"];
      default:
        return [`@${powerKeyword} command1`, `@${powerKeyword} command2`];
    }
  }
  
  // Case 3: Command selected - return parameters
  if (powerKeyword && command && !parameter) {
    if (powerKeyword.toLowerCase() === 'show' && command.toLowerCase() === 'patients') {
      return ["@show patients all", "@show patients active", "@show patients by-doctor"];
    } else if (powerKeyword.toLowerCase() === 'select' && command.toLowerCase() === 'where') {
      return ["@select where id=", "@select where name=", "@select where date="];
    } else {
      return [`@${powerKeyword} ${command} param1`, `@${powerKeyword} ${command} param2`];
    }
  }
  
  // Case 4: Full query with parameter - return final suggestions
  if (powerKeyword && command && parameter) {
    if (powerKeyword.toLowerCase() === 'show' && 
        command.toLowerCase() === 'patients' && 
        parameter.toLowerCase() === 'all') {
      return ["Show all patient records from the database with their contact details"];
    }
    return [`Complete query: ${query}`];
  }
  
  // Default case for regular text input
  if (query.length >= 3 && !query.startsWith('@')) {
    const defaultSuggestions = [
      "find all books in the library",
      "show me sales data for last quarter",
      "list all employees in marketing department",
    ];
    
    return defaultSuggestions.filter(p => 
      p.toLowerCase().includes(query.toLowerCase())
    ).slice(0, 3);
  }
  
  return [];
};
