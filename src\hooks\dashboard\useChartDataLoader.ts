import { useCallback } from 'react';
import { SavedChart, ChartDataPoint } from '@/types/chartTypes';
import { TabularDataItem } from '@/services/api/chart/chartTypes';
import { fetchChartTabularData } from '@/services/api/chart';
import { toast } from 'sonner';
import { useChartsData } from '@/hooks/useChartsData';
import { useConnectionManager } from '@/hooks/useConnectionManager';

interface ChartDataLoaderHook {
  loadChartDataForCard: (chart: SavedChart, cardId: string, onUpdate: (cardId: string, updates: any) => void) => Promise<void>;
  loadTabularDataForCard: (chart: SavedChart, cardId: string, onUpdate: (cardId: string, updates: any) => void) => Promise<void>;
  loadChartDataForZone: (chart: SavedChart, onSetData: (data: any) => void, onSetLoading: (loading: boolean) => void) => Promise<void>;
  loadTabularDataForZone: (chart: SavedChart, onSetData: (data: any) => void, onSetLoading: (loading: boolean) => void) => Promise<void>;
}

export const useChartDataLoader = (): ChartDataLoaderHook => {
  const { loadChartById, loadChartData } = useChartsData();
  const { getCurrentConnection } = useConnectionManager();

  // Load chart data for specific card
  const loadChartDataForCard = useCallback(async (
    chart: SavedChart, 
    cardId: string, 
    onUpdate: (cardId: string, updates: any) => void
  ) => {
    try {
      const connection = getCurrentConnection();
      if (!connection) {
        toast.error('No database connection available');
        onUpdate(cardId, { loading: false });
        return;
      }

      const chartDetails = await loadChartById(chart.chart_id);
      if (chartDetails) {
        const connectionId = chartDetails.connection_id || connection.id;
        const chartData = await loadChartData(chartDetails, connectionId);

        if (chartData && chartData.length > 0) {
          onUpdate(cardId, {
            data: chartData,
            loading: false
          });
        } else {
          toast.error('Failed to load chart data');
          onUpdate(cardId, {
            data: null,
            loading: false
          });
        }
      }
    } catch (error) {
      console.error('Error loading chart data for card:', error);
      toast.error('Error loading chart data');
      onUpdate(cardId, {
        data: null,
        loading: false
      });
    }
  }, [loadChartById, loadChartData, getCurrentConnection]);

  // Load tabular data for specific card
  const loadTabularDataForCard = useCallback(async (
    chart: SavedChart, 
    cardId: string, 
    onUpdate: (cardId: string, updates: any) => void
  ) => {
    try {
      const connection = getCurrentConnection();
      if (!connection) {
        toast.error('No database connection available');
        onUpdate(cardId, { loading: false });
        return;
      }

      const response = await fetchChartTabularData(chart.chart_id);
      if (response && response.length > 0) {
        onUpdate(cardId, {
          data: response,
          loading: false
        });
      } else {
        toast.error('Failed to load table data');
        onUpdate(cardId, {
          data: null,
          loading: false
        });
      }
    } catch (error) {
      console.error('Error loading tabular data for card:', error);
      toast.error('Error loading table data');
      onUpdate(cardId, {
        data: null,
        loading: false
      });
    }
  }, [getCurrentConnection]);

  // Load chart data for the dropped zone (legacy)
  const loadChartDataForZone = useCallback(async (
    chart: SavedChart,
    onSetData: (data: any) => void,
    onSetLoading: (loading: boolean) => void
  ) => {
    try {
      onSetLoading(true);
      onSetData(null);

      const chartDetails = await loadChartById(chart.chart_id);

      if (chartDetails) {
        const connectionId = chartDetails.connection_id || getCurrentConnection();

        if (connectionId) {
          const loadedData = await loadChartData(chartDetails, connectionId);

          if (loadedData && loadedData.length > 0) {
            onSetData(loadedData);
          }
        }
      }
    } catch (error) {
      console.error('Error loading chart data for zone:', error);
      toast.error('Failed to load chart data');
    } finally {
      onSetLoading(false);
    }
  }, [loadChartById, loadChartData, getCurrentConnection]);

  // Load tabular data for the dropped zone (legacy)
  const loadTabularDataForZone = useCallback(async (
    chart: SavedChart,
    onSetData: (data: any) => void,
    onSetLoading: (loading: boolean) => void
  ) => {
    try {
      onSetLoading(true);
      onSetData(null);

      console.log('Loading tabular data for chart:', chart.chart_id);
      const tabularDataResponse = await fetchChartTabularData(chart.chart_id);

      if (tabularDataResponse && tabularDataResponse.length > 0) {
        onSetData(tabularDataResponse);
        console.log('Tabular data loaded successfully:', tabularDataResponse);
      } else {
        console.log('No tabular data received');
        onSetData([]);
      }
    } catch (error) {
      console.error('Error loading tabular data for zone:', error);
      toast.error('Failed to load tabular data');
      onSetData([]);
    } finally {
      onSetLoading(false);
    }
  }, []);

  return {
    loadChartDataForCard,
    loadTabularDataForCard,
    loadChartDataForZone,
    loadTabularDataForZone,
  };
};