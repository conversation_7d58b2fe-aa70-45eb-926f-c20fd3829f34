
import React, { useRef, useState, useEffect } from "react";
import { usePredictions } from "@/hooks/usePredictions";

interface UseChatInputProps {
  inputValue: string;
  setInputValue: (value: string) => void;
  handleSubmit: (e: React.FormEvent) => void;
  isLoading: boolean;
  dashboardType: 1 | 2 | 3;
}

export const useChatInput = ({
  inputValue,
  setInputValue,
  handleSubmit,
  isLoading,
  dashboardType
}: UseChatInputProps) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const formRef = useRef<HTMLFormElement>(null);
  const inputWrapperRef = useRef<HTMLDivElement>(null);
  const [powerModeEnabled, setPowerModeEnabled] = useState(false);
  
  const {
    predictions,
    showPredictions,
    setShowPredictions,
    isChainSequence,
    handlePredictionSelect,
    lastSelectedPrediction,
    isPredictionsEnabled,
    enablePowerMode,
    togglePowerMode
  } = usePredictions({
    inputValue,
    dashboardType, 
    powerModeEnabled
  });

  // Close predictions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (formRef.current && !formRef.current.contains(event.target as Node)) {
        setShowPredictions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [setShowPredictions]);

  // Handle power mode toggle
  const handlePowerModeToggle = () => {
    const newPowerMode = !powerModeEnabled;
    setPowerModeEnabled(newPowerMode);
    togglePowerMode(newPowerMode);
    
    // Focus the textarea but don't automatically add @
    if (newPowerMode && textareaRef.current) {
      setTimeout(() => {
        textareaRef.current?.focus();
      }, 0);
    }
  };

  const onPredictionSelect = (selectedPrediction: string) => {
    console.log("Selecting prediction:", selectedPrediction);
    handlePredictionSelect(selectedPrediction, setInputValue);
    textareaRef.current?.focus();
  };

  const handleFocus = () => {
    // Only show predictions if predictions are enabled for this dashboard type
    if (isPredictionsEnabled && inputValue) {
      if ((inputValue.startsWith('@') || inputValue.trim().length >= 3) && predictions.length > 0) {
        setShowPredictions(true);
      }
    }
  };

  // Custom placeholder based on dashboard type
  const getPlaceholder = () => {
    if (dashboardType === 3) {
      return powerModeEnabled 
        ? "Type @ for power keywords..."
        : "Ask anything... (Toggle power icon for keywords)";
    } else if (dashboardType === 2) {
      return "Ask about the transcript...";
    } else {
      return "Type your message...";
    }
  };

  return {
    textareaRef,
    formRef,
    inputWrapperRef,
    predictions,
    showPredictions,
    setShowPredictions,
    isChainSequence,
    powerModeEnabled,
    handlePowerModeToggle,
    onPredictionSelect,
    handleFocus,
    getPlaceholder,
    isPredictionsEnabled
  };
};
