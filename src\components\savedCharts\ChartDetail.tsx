
import React, { useRef, useEffect, useState } from 'react';
import { ArrowLeft, Loader2, Minimize, Maximize } from 'lucide-react';
import { SavedChart, ChartDataPoint } from '@/types/chartTypes';
import ChartRenderer from '@/components/charts/ChartRenderer';
import ChartActions from '@/components/charts/ChartActions';
import { formatDate, getTableNames } from '@/utils/chartFormatters';
import VerifiedIcon from '@/components/ui/VerifiedIcon';

// Add the getBaseChartOptions function
const getBaseChartOptions = () => {
  return {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: {
          boxWidth: 10,
          padding: 10,
          font: {
            size: 11
          }
        }
      },
      tooltip: {
        enabled: true,
        titleFont: {
          size: 11
        },
        bodyFont: {
          size: 11
        }
      }
    }
  };
};

interface ChartDetailProps {
  chart: SavedChart | null;
  chartData: ChartDataPoint[];
  loadingChartData: boolean;
  onBack: () => void;
  onEdit: () => void;
  onRefresh: () => void;
}

const ChartDetail: React.FC<ChartDetailProps> = ({
  chart,
  chartData,
  loadingChartData,
  onBack,
  onEdit,
  onRefresh
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const [chartOptions, setChartOptions] = useState<any>(null);
  const [chartDataState, setChartData] = useState<ChartDataPoint[]>([]);
  const [detailsMinimized, setDetailsMinimized] = useState(false);

  // Toggle details card visibility
  const toggleDetailsCard = () => {
    setDetailsMinimized(!detailsMinimized);
  };

  useEffect(() => {
    if (chart) {
      console.log("Chart data received in ChartDetail:", chart);
      
      // If the chart has a connection_id, log it
      if (chart.connection_id) {
        console.log("Chart has connection_id:", chart.connection_id);
      }
      
      // Check if chart has chart_response data
      if (chart.chart_response && chart.chart_response.data && chart.chart_response.data.length > 0) {
        console.log("Using chart data from chart_response:", chart.chart_response.data.length, "items");
        setChartData(chart.chart_response.data);
      } 
      // If no chart_response, check for chart_data
      else if (chart.chart_data && chart.chart_data.length > 0) {
        console.log("Using chart data from chart_data:", chart.chart_data.length, "items");
        setChartData(chart.chart_data);
      }
      // If we have data directly in the response
      else if (chartData && chartData.length > 0) {
        console.log("Using chart data from chartData prop:", chartData.length, "items");
        setChartData(chartData);
      }
      // If no data is available, try to fetch it
      else {
        console.log("No chart data available, will attempt to fetch it");
        setChartData([]);
      }
      
      // Set chart options
      setChartOptions({
        ...getBaseChartOptions(),
        title: {
          text: chart.chart_name,
          align: 'center'
        }
      });
    }
  }, [chart, chartData]);

  if (!chart) {
    return (
      <div className="flex flex-col items-center justify-center h-64 p-6">
        <div className="text-base text-gray-500 mb-4">No chart selected</div>
        <button 
          className="px-3 py-1.5 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center text-sm"
          onClick={onBack}
        >
          <ArrowLeft size={16} className="mr-1" /> Back to Charts
        </button>
      </div>
    );
  }

  // const tableNames = getTableNames(chart);
  // const tableCount = chart.tables && Array.isArray(chart.tables) 
  //   ? chart.tables.length 
  //   : (chart.table_name ? 1 : 0);

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-4">
        <button 
          className="flex items-center text-blue-600 hover:text-blue-800 text-sm"
          onClick={onBack}
        >
          <ArrowLeft size={16} className="mr-1" /> Back to Charts
        </button>
        
        <ChartActions
          chart={chart}
          onEdit={onEdit}
          onRefresh={onRefresh}
        />
      </div>
      
      {/* Wrap both chart details and visualization in a single container for export */}
      <div className="flex flex-col gap-4 chart-export-container">
        {/* Chart details card - first */}
        <div className={`bg-white rounded-lg shadow transition-all duration-300 ${
          detailsMinimized ? 'h-10' : ''
        }`}>
          <div className="flex justify-between items-center p-2 border-b">
            <h3 className="font-medium text-sm">Chart Details</h3>
            <div className="flex items-center">
              <button 
                onClick={toggleDetailsCard}
                className="p-1 rounded-full hover:bg-gray-100 export-exclude"
                title={detailsMinimized ? "Expand details" : "Minimize details"}
              >
                {detailsMinimized ? <Maximize size={16} /> : <Minimize size={16} />}
              </button>
            </div>
          </div>
          
          {!detailsMinimized && (
            <div className="p-3">
              <div className="flex items-center justify-between">
              <h2 className="text-base font-bold mb-3">{chart.chart_name || 'Unnamed Chart'}</h2>
              {/* Verified Icon in details card */}
              {chart.verified && (
                <div className="flex items-center space-x-1">
                  <VerifiedIcon />
                </div>
              )}
              </div>
              <div className="grid grid-cols-2 gap-3 mb-3 text-xs">
                <div>
                  <div className="text-xs font-medium text-gray-500">Chart Type</div>
                  <div>{chart.chart_type}</div>
                </div>
                <div>
                  <div className="text-xs font-medium text-gray-500">Database</div>
                  <div>{chart.db_type}</div>
                </div>
                <div>
                  <div className="text-xs font-medium text-gray-500">Table</div>
                  <div>{getTableNames(chart)}</div>
                </div>
                <div>
                  <div className="text-xs font-medium text-gray-500">Created</div>
                  <div>{formatDate(chart.created_at)}</div>
                </div>
              </div>
            </div>
          )}
        </div>
        
        {/* Chart visualization area - second */}
        <div className="bg-white rounded-lg shadow p-4">
          <div className="h-64 border rounded bg-gray-50 chart-detail-container" ref={chartRef}>
            {loadingChartData ? (
              <div className="h-full flex items-center justify-center">
                <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />
                <span className="ml-2 text-gray-600 text-sm">Loading chart data...</span>
              </div>
            ) : chartData.length > 0 ? (
              // Prioritize rendering the interactive chart if we have data
              <ChartRenderer chart={chart} chartData={chartData} />
            ) : chart.chart_image ? (
              // Fall back to the image only if we don't have data
              <div className="h-full flex items-center justify-center">
                <img 
                  src={chart.chart_image} 
                  alt={chart.chart_name}
                  className="max-h-full object-contain"
                />
              </div>
            ) : (
              <div className="h-full flex items-center justify-center text-gray-400 text-sm">
                No chart data available
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChartDetail;
