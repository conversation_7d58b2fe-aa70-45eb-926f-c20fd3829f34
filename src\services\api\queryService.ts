
import { QueryResultData } from '@/components/dashboard/models';
import { centralApiClient } from '@/services/api/centralApiClient';

const exportToCSV = async (prompt: string): Promise<string> => {
  try {
    console.log(`Sending export request with prompt: ${prompt}`);

    const blob = await centralApiClient.makeRequest<Blob>('dada', `/api/export?nl_question=${encodeURIComponent(prompt)}`, {
      method: 'POST',
      headers: {
        'Accept': 'text/csv',
      }
    });

    return handleCSVDownload(blob);
  } catch (error) {
    console.error("Export API Error:", error instanceof Error ? error.message : error);
    throw error;
  }
};

const regenerateQuery = async (prompt: string): Promise<QueryResultData> => {
  try {
    console.log(`Sending regenerate request with prompt: ${prompt}`);

    const data = await centralApiClient.makeRequest<any>('dada', `/api/regenerate?nl_question=${encodeURIComponent(prompt)}`, {
      method: 'POST'
    });

    console.log("Received API regenerate response:", data);

    return {
      query: data.query,
      tableData: {
        columns: data.columns,
        rows: data.rows,
      },
    };
  } catch (error) {
    console.error("Regenerate API Error:", error instanceof Error ? error.message : error);
    throw error;
  }
};

// Helper function to handle CSV file download
const handleCSVDownload = (blob: Blob): string => {
  const filename = 'query_result.csv';
  
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  window.URL.revokeObjectURL(url);
  
  return filename;
};

// Export as a default object
const queryService = {
  exportToCSV,
  regenerateQuery
};

export default queryService;
