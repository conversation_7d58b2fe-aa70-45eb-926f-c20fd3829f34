import React from 'react';
import { Saved<PERSON><PERSON> } from '@/types/chartTypes';
import UniversalChart from './UniversalChart';

interface ChartThumbnailProps {
  chart: SavedChart;
  onClick?: (chart: SavedChart) => void;
  className?: string;
}

const ChartThumbnail: React.FC<ChartThumbnailProps> = ({ chart, onClick, className }) => {
  const handleClick = () => {
    if (onClick) {
      onClick(chart);
    }
  };

  // If the chart has a saved image, use it
  if (chart.chart_image) {
    return (
      <div 
        className={`w-full h-full flex items-center justify-center cursor-pointer ${className}`}
        onClick={handleClick}
      >
        <img 
          src={chart.chart_image} 
          alt={chart.chart_name}
          className="max-w-full max-h-full object-contain"
        />
      </div>
    );
  }

  // Otherwise, use UniversalChart for consistent rendering
  const sampleData = [
    { category: 'Jan', value: 12 },
    { category: 'Feb', value: 19 },
    { category: 'Mar', value: 3 },
    { category: 'Apr', value: 5 },
    { category: 'May', value: 2 },
    { category: 'Jun', value: 3 }
  ];

  return (
    <div 
      className={`w-full h-full cursor-pointer ${className}`}
      onClick={handleClick}
    >
      <UniversalChart
        rawData={sampleData}
        xAxis="category"
        yAxis={["value"]}
        chartType={chart.chart_type as any}
        size="small"
        context="thumbnail"
        showTitle={false}
        showLegend={false}
        showAxes={false}
        className="w-full h-full"
      />
    </div>
  );
};

export default ChartThumbnail;
