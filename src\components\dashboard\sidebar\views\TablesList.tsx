import React from 'react';
import { Plus, Minus, Loader2, Search } from 'lucide-react';
import { TableWithColumns } from '@/services/api/chart/chartTypes';
import { useAppDispatch } from '@/hooks/useRedux';
import { updateTable } from '@/stores/chartSlice';
import { fetchTableColumns } from '@/services/api/chart/databaseService';
import { toast } from 'sonner';
import { useTableCache } from '@/hooks/shared/useTableCache';
import { useDragAndDrop } from '@/hooks/shared/useDragAndDrop';

interface TablesListProps {
  tables: TableWithColumns[];
  connectionId: string;
  isLoading: boolean;
  searchQuery: string;
  onSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const TablesList: React.FC<TablesListProps> = ({
  tables,
  connectionId,
  isLoading,
  searchQuery,
  onSearchChange
}) => {
  const dispatch = useAppDispatch();
  const { getCachedColumns, setCachedColumns } = useTableCache();
  const { handleDragStart } = useDragAndDrop();

  // Filter tables based on search query
  const filteredTables = tables.filter(table => 
    table.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleToggleTable = async (tableName: string) => {
    // Find the table
    const table = tables.find(t => t.name === tableName);
    if (!table) return;
    
    console.log(`Toggling table ${tableName}, current expanded state:`, table.expanded);
    
    // Create updated table object
    const updatedTable = {
      ...table,
      expanded: !table.expanded,
      isLoading: !table.expanded && table.columns.length === 0 ? true : false
    };
    
    // Update the table in Redux
    dispatch(updateTable(updatedTable));
    console.log(`Updated table in Redux:`, updatedTable);
    
    // If expanding and no columns loaded yet, fetch columns
    if (updatedTable.expanded && table.columns.length === 0) {
      fetchColumnsForTable(tableName);
    }
  };

  const fetchColumnsForTable = async (tableName: string) => {
    try {
      console.log(`Fetching columns for table ${tableName}`);
      
      // Check cache first
      const cachedColumns = getCachedColumns(connectionId, tableName);
      if (cachedColumns) {
        console.log(`Using cached columns for table ${tableName}:`, cachedColumns);
        
        const table = tables.find(t => t.name === tableName);
        if (table) {
          const updatedTable = {
            ...table,
            columns: cachedColumns,
            isLoading: false,
            expanded: true
          };
          dispatch(updateTable(updatedTable));
        }
        return;
      }
      
      // Fetch from API if not cached
      const columns = await fetchTableColumns(connectionId, tableName);
      console.log(`Received columns for table ${tableName}:`, columns);
      
      // Cache the columns
      setCachedColumns(connectionId, tableName, columns);
      
      // Find the table and update it with columns
      const table = tables.find(t => t.name === tableName);
      if (table) {
        const updatedTable = {
          ...table,
          columns,
          isLoading: false,
          expanded: true // Ensure it stays expanded after loading
        };
        
        console.log(`Updating table with columns:`, updatedTable);
        dispatch(updateTable(updatedTable));
      }
    } catch (error) {
      console.error(`Failed to load columns for table ${tableName}:`, error);
      toast.error(`Failed to load columns for ${tableName}`, {
        description: 'Could not retrieve column information'
      });
      
      // Update the loading state even on error
      const table = tables.find(t => t.name === tableName);
      if (table) {
        const updatedTable = {
          ...table,
          isLoading: false,
          expanded: true // Keep it expanded even on error
        };
        
        dispatch(updateTable(updatedTable));
      }
    }
  };

  return (
    <div className="mt-2">
      <div className="mb-2 relative">
        <input
          type="text"
          placeholder="Search tables..."
          value={searchQuery}
          onChange={onSearchChange}
          className="w-full pl-8 pr-4 py-1 border border-gray-300 rounded-md text-sm"
        />
        <Search className="absolute left-2 top-1.5 text-gray-400" size={16} />
      </div>
      
      {isLoading ? (
        <div className="flex justify-center items-center py-4">
          <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />
          <span className="ml-2 text-sm text-gray-500">Loading tables...</span>
        </div>
      ) : (
        filteredTables.length > 0 ? (
          <div className="overflow-y-auto max-h-[calc(100vh-200px)] border rounded bg-white">
            <ul className="divide-y divide-gray-200">
              {filteredTables.map((table) => (
                <li 
                  key={table.name}
                  className={`sidebar-table ${table.name.toLowerCase().replace(/\s+/g, '-')} mb-1 rounded-md overflow-hidden border border-gray-200`}
                  data-table-name={table.name}
                  data-expanded={table.expanded ? "true" : "false"}
                >
                  <div 
                    className="flex items-center justify-between px-3 py-2 bg-white cursor-pointer hover:bg-gray-50"
                    onClick={() => handleToggleTable(table.name)}
                  >
                    <span className="text-sm font-medium text-gray-700">{table.name}</span>
                    <div className="flex items-center">
                      {table.expanded ? (
                        <Minus className="h-4 w-4 text-gray-500" />
                      ) : (
                        <Plus className="h-4 w-4 text-gray-500" />
                      )}
                    </div>
                  </div>
                  
                  {/* Columns list */}
                  {table.expanded && (
                    <div className="bg-gray-50 border-t border-gray-200">
                      {table.isLoading ? (
                        <div className="flex justify-center items-center py-2">
                          <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />
                        </div>
                      ) : table.columns.length > 0 ? (
                        <ul className="py-1">
                          {table.columns.map((column) => (
                            <li 
                              key={`${table.name}-${column.name}`}
                              className="px-6 py-1 text-xs text-gray-600 hover:bg-gray-100 cursor-grab"
                              draggable
                              onDragStart={handleDragStart({
                                name: column.name,
                                type: column.type || 'VARCHAR',
                                tableName: table.name
                              })}
                              data-column-name={column.name}
                              data-column-type={column.type || 'VARCHAR'}
                              data-table-name={table.name}
                            >
                              {/* Display type indicator based on column type */}
                              {column.type && column.type.toLowerCase().match(/int|float|double|decimal|numeric/) ? (
                                <span className="text-green-600 font-mono mr-1">123</span>
                              ) : column.type && column.type.toLowerCase().match(/date|time|timestamp/) ? (
                                <span className="text-blue-600 font-mono mr-1">📅</span>
                              ) : (
                                <span className="text-orange-500 font-mono mr-1">ABC</span>
                              )}
                              {column.name}
                            </li>
                          ))}
                        </ul>
                      ) : (
                        <div className="px-6 py-2 text-xs text-gray-500 italic">
                          No columns available
                        </div>
                      )}
                    </div>
                  )}
                </li>
              ))}
            </ul>
          </div>
        ) : (
          <div className="text-center p-4 text-sm text-gray-500 bg-white border rounded">
            No tables match your search
          </div>
        )
      )}
    </div>
  );
};

export default TablesList;




