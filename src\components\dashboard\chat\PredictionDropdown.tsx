
import React, { useEffect, useState, useRef } from "react"

interface PredictionDropdownProps {
  predictions: string[];
  inputValue: string;
  onSelect: (prediction: string) => void;
  autoChainPredictions?: boolean;
}

const PredictionDropdown = ({ 
  predictions, 
  inputValue, 
  onSelect,
  autoChainPredictions = true
}: PredictionDropdownProps) => {
  const [selectedIndex, setSelectedIndex] = useState<number>(-1);
  const listRef = useRef<HTMLDivElement>(null);
  const itemsRef = useRef<(HTMLDivElement | null)[]>([]);

  // Reset selected index when predictions change
  useEffect(() => {
    // If we have new predictions, select the first one
    if (predictions && predictions.length > 0) {
      setSelectedIndex(0);
    } else {
      setSelectedIndex(-1);
    }
  }, [predictions]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!predictions || predictions.length === 0) return;

      if (e.key === 'ArrowDown') {
        e.preventDefault();
        setSelectedIndex(prev => (prev < predictions.length - 1 ? prev + 1 : 0));
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        setSelectedIndex(prev => (prev > 0 ? prev - 1 : predictions.length - 1));
      } else if (e.key === 'Enter' && selectedIndex >= 0) {
        e.preventDefault();
        onSelect(predictions[selectedIndex]);
      } else if (e.key === 'Tab' && predictions.length > 0) {
        e.preventDefault();
        onSelect(selectedIndex >= 0 ? predictions[selectedIndex] : predictions[0]);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [predictions, selectedIndex, onSelect]);

  // Scroll selected item into view
  useEffect(() => {
    if (selectedIndex !== -1 && itemsRef.current[selectedIndex]) {
      itemsRef.current[selectedIndex]?.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest'
      });
    }
  }, [selectedIndex]);

  // Handle auto-selection for the first prediction if in power keyword mode
  useEffect(() => {
    if (autoChainPredictions && 
        inputValue === '@' && 
        predictions && 
        predictions.length > 0) {
      console.log("Highlighting first power keyword:", predictions[0]);
      setSelectedIndex(0);
    }
  }, [autoChainPredictions, inputValue, predictions]);

  if (!predictions || !predictions.length || !inputValue) {
    return null;
  }

  const handleItemClick = (prediction: string) => {
    console.log("Clicked on prediction:", prediction);
    onSelect(prediction);
  };

  // Helper to determine if an item is a power keyword, command, or parameter
  const getItemClass = (prediction: string) => {
    if (!prediction.startsWith('@')) {
      return 'bg-blue-100 hover:bg-blue-200'; // Regular prediction
    }
    
    const parts = prediction.substring(prediction.startsWith('@@') ? 2 : 1).split(' ').filter(Boolean);
    
    if (parts.length === 1) {
      return 'bg-indigo-100 hover:bg-indigo-200'; // Power keyword
    } else if (parts.length === 2) {
      return 'bg-purple-100 hover:bg-purple-200'; // Command
    } else {
      return 'bg-pink-100 hover:bg-pink-200'; // Parameter
    }
  };

  // Format the display of predictions for better readability
  const formatPredictionDisplay = (prediction: string): string => {
    if (!prediction.startsWith('@')) {
      return prediction;
    }
    
    // Remove the @ prefix for display
    try {
      if (prediction.includes("{Parameter:")) {
        // For parameter suggestions, remove @ and show formatted text
        const cleanText = prediction.replace(/^@+\s*/, '');
        return cleanText;
      }
      
      // For power keywords and commands, strip the @ prefix
      return prediction.substring(prediction.startsWith('@@') ? 2 : 1);
    } catch (e) {
      console.error("Error formatting prediction:", e);
      return prediction;
    }
  };

  return (
    <div 
      ref={listRef}
      className="bg-white border border-gray-300 rounded-md shadow-lg overflow-hidden w-full max-h-64 overflow-y-auto"
      role="listbox"
    >
      {predictions.map((suggestion, index) => {
        const itemClass = getItemClass(suggestion);
        const displayText = formatPredictionDisplay(suggestion);
        
        return (
          <div 
            key={index} 
            ref={el => itemsRef.current[index] = el}
            className="border-b border-gray-100 last:border-0"
          >
            <div 
              className={`flex items-center px-4 py-3 cursor-pointer ${itemClass} ${
                selectedIndex === index ? 'ring-2 ring-blue-500' : ''
              }`}
              onClick={() => handleItemClick(suggestion)}
              role="option"
              aria-selected={selectedIndex === index}
            >
              <span className="text-gray-800">{displayText}</span>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default PredictionDropdown;
