import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";

interface SaveChartDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (chartName: string) => void;
  isSaving: boolean;
  initialChartName?: string;
  isEditMode?: boolean;
  selectedDatabase?: string;
}

const SaveChartDialog: React.FC<SaveChartDialogProps> = ({
  isOpen,
  onClose,
  onSave,
  isSaving,
  initialChartName = '',
  isEditMode = false,
  selectedDatabase
}) => {
  const [chartName, setChartName] = useState(initialChartName);

  // Update chartName when initialChartName changes
  useEffect(() => {
    console.log("SaveChartDialog initialChartName:", initialChartName);
    console.log("SaveChartDialog isEditMode:", isEditMode);
    
    if (initialChartName) {
      setChartName(initialChartName);
      console.log("Set chart name to:", initialChartName);
    }
  }, [initialChartName, isEditMode]);

  // Add logging to check if selectedDatabase is passed correctly
  useEffect(() => {
    console.log("SaveChartDialog received selectedDatabase:", selectedDatabase);
  }, [selectedDatabase]);

  // Handle save button click
  const handleSave = () => {
    onSave(chartName);
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{isEditMode ? 'Update Chart' : 'Save Chart'}</DialogTitle>
          <DialogDescription>
            {isEditMode 
              ? 'Update your chart with a name to identify it later.'
              : 'Give your chart a name to save it for later use.'}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="chart-name" className="text-right">
              Chart Name
            </Label>
            <Input
              id="chart-name"
              name="chart-name"
              value={chartName}
              onChange={(e) => setChartName(e.target.value)}
              className="col-span-3"
              placeholder="Enter chart name"
              autoFocus
              aria-describedby="chart-name-description"
            />
          </div>
          {/* Display the selected database */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right">Database</Label>
            <div className="col-span-3">
              {selectedDatabase ? (
                <span className="text-sm text-gray-700">{selectedDatabase}</span>
              ) : (
                <span className="text-sm text-red-500">No database selected</span>
              )}
            </div>
          </div>
        </div>
        <DialogFooter className="sm:justify-end">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isSaving}
          >
            Cancel
          </Button>
          <Button 
            type="button"
            onClick={handleSave}
            variant='greenmind'
            disabled={isSaving || !chartName.trim()}
          >
            {isSaving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {isEditMode ? 'Updating...' : 'Saving...'}
              </>
            ) : (
              isEditMode ? 'Update Chart' : 'Save Chart'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SaveChartDialog;

