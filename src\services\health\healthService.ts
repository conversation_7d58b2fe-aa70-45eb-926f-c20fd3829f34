import { centralApiClient } from '../api/centralApiClient';

// Health check status
type HealthStatus = 'connected' | 'disconnected' | 'checking';

// Subscribers
const subscribers: ((status: HealthStatus) => void)[] = [];

// Current status
let currentStatus: HealthStatus = 'checking';
let checkInProgress = false;
let pollInterval = 60000; // 1 minute
let maxInterval = 300000; // 5 minutes
let intervalId: number | null = null;

// Check health
const checkHealth = async (): Promise<void> => {
  if (checkInProgress) return;
  
  checkInProgress = true;
  updateStatus('checking');
  
  try {
    // First check if browser is online
    if (!navigator.onLine) {
      updateStatus('disconnected');
      increaseInterval();
      checkInProgress = false;
      return;
    }
    
    // Then check backend health
    const isHealthy = await centralApiClient.healthCheck('dada');  // Changed from 'audio' to 'dada'
    
    if (isHealthy) {
      updateStatus('connected');
      resetInterval();
    } else {
      updateStatus('disconnected');
      increaseInterval();
    }
  } catch (error) {
    console.error('Health check failed:', error);
    updateStatus('disconnected');
    increaseInterval();
  } finally {
    checkInProgress = false;
  }
};

// Update status and notify subscribers
const updateStatus = (status: HealthStatus): void => {
  if (status === currentStatus) return;
  
  currentStatus = status;
  
  // Notify all subscribers
  subscribers.forEach(callback => callback(status));
  
  // Dispatch event for legacy components
  window.dispatchEvent(new CustomEvent(
    status === 'connected' ? 'backend-connected' : 'backend-disconnected'
  ));
};

// Reset interval on success
const resetInterval = (): void => {
  pollInterval = 60000;
  scheduleNextCheck();
};

// Increase interval on failure (exponential backoff)
const increaseInterval = (): void => {
  pollInterval = Math.min(pollInterval * 1.5, maxInterval);
  scheduleNextCheck();
};

// Schedule next check
const scheduleNextCheck = (): void => {
  if (intervalId !== null) {
    clearTimeout(intervalId);
  }
  
  intervalId = window.setTimeout(checkHealth, pollInterval);
};

// Start health monitoring
export const startHealthMonitoring = (): void => {
  // Initial check
  checkHealth();
  
  // Schedule regular checks
  scheduleNextCheck();
  
  // Listen for online/offline events
  window.addEventListener('online', () => {
    console.log('Browser online event received');
    checkHealth();
  });
  
  window.addEventListener('offline', () => {
    console.log('Browser offline event received');
    updateStatus('disconnected');
  });
};

// Subscribe to health status changes
export const subscribeToHealthStatus = (callback: (status: HealthStatus) => void): () => void => {
  subscribers.push(callback);
  
  // Immediately call with current status
  callback(currentStatus);
  
  // Return unsubscribe function
  return () => {
    const index = subscribers.indexOf(callback);
    if (index !== -1) {
      subscribers.splice(index, 1);
    }
  };
};

// Get current health status
export const getCurrentHealthStatus = (): HealthStatus => {
  return currentStatus;
};

// Force a health check
export const forceHealthCheck = (): void => {
  checkHealth();
};
