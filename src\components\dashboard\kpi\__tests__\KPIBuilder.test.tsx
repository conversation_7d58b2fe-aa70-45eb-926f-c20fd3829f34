import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { vi } from 'vitest';
import { KPIBuilder } from '../index';
import { KPIFormData } from '../types/kpiTypes';

// Mock the child components to focus on KPIBuilder logic
vi.mock('../KPIViewSelector', () => ({
  default: ({ selectedView, onViewChange }: any) => (
    <div data-testid="kpi-view-selector">
      <button onClick={() => onViewChange('kpi-only')}>KPI Only</button>
      <button onClick={() => onViewChange('current-vs-prior')}>Current vs Prior</button>
      <button onClick={() => onViewChange('target-based')}>Target Based</button>
      <span>Selected: {selectedView}</span>
    </div>
  )
}));

vi.mock('../KPIFormFields', () => ({
  default: ({ formData, onFormChange, onValidateSQL }: any) => (
    <div data-testid="kpi-form-fields">
      <input
        data-testid="kpi-label-input"
        value={formData.label}
        onChange={(e) => onFormChange({ label: e.target.value })}
        placeholder="KPI Label"
      />
      <textarea
        data-testid="kpi-sql-input"
        value={formData.sql}
        onChange={(e) => onFormChange({ sql: e.target.value })}
        placeholder="SQL Query"
      />
      <button
        data-testid="validate-sql-button"
        onClick={() => onValidateSQL && onValidateSQL(formData.sql)}
      >
        Validate SQL
      </button>
    </div>
  )
}));

vi.mock('../KPIPreview', () => ({
  default: ({ formData, isLoading }: any) => (
    <div data-testid="kpi-preview">
      {isLoading ? 'Loading...' : `Preview for ${formData.label || 'Untitled KPI'}`}
    </div>
  )
}));



describe('KPIBuilder', () => {
  const mockOnSave = vi.fn();
  const mockOnExecute = vi.fn();
  const mockOnValidateSQL = vi.fn().mockResolvedValue(true);

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders all components correctly', () => {
    render(
      <KPIBuilder
        onSave={mockOnSave}
        onExecute={mockOnExecute}
        onValidateSQL={mockOnValidateSQL}
      />
    );

    expect(screen.getByTestId('kpi-view-selector')).toBeInTheDocument();
    expect(screen.getByTestId('kpi-form-fields')).toBeInTheDocument();
    expect(screen.getByTestId('kpi-preview')).toBeInTheDocument();
  });

  it('initializes with default form data', () => {
    render(<KPIBuilder />);
    
    expect(screen.getByText('Selected: kpi-only')).toBeInTheDocument();
    expect(screen.getByTestId('kpi-label-input')).toHaveValue('');
    expect(screen.getByTestId('kpi-sql-input')).toHaveValue('');
  });

  it('updates form data when user types', () => {
    render(<KPIBuilder />);
    
    const labelInput = screen.getByTestId('kpi-label-input');
    const sqlInput = screen.getByTestId('kpi-sql-input');
    
    fireEvent.change(labelInput, { target: { value: 'Monthly Sales' } });
    fireEvent.change(sqlInput, { target: { value: 'SELECT SUM(sales) FROM table1' } });
    
    expect(labelInput).toHaveValue('Monthly Sales');
    expect(sqlInput).toHaveValue('SELECT SUM(sales) FROM table1');
  });

  it('changes view type when selector is clicked', () => {
    render(<KPIBuilder />);
    
    expect(screen.getByText('Selected: kpi-only')).toBeInTheDocument();
    
    fireEvent.click(screen.getByText('Current vs Prior'));
    
    expect(screen.getByText('Selected: current-vs-prior')).toBeInTheDocument();
  });

  it('calls onExecute when Execute button is clicked', () => {
    render(
      <KPIBuilder
        onExecute={mockOnExecute}
      />
    );
    
    // Fill in required fields
    fireEvent.change(screen.getByTestId('kpi-label-input'), { 
      target: { value: 'Test KPI' } 
    });
    fireEvent.change(screen.getByTestId('kpi-sql-input'), { 
      target: { value: 'SELECT 1' } 
    });
    
    fireEvent.click(screen.getByText('Execute'));
    
    expect(mockOnExecute).toHaveBeenCalledWith(
      expect.objectContaining({
        viewType: 'kpi-only',
        label: 'Test KPI',
        sql: 'SELECT 1'
      })
    );
  });

  it('calls onSave when Save button is clicked', () => {
    render(
      <KPIBuilder
        onSave={mockOnSave}
      />
    );
    
    // Fill in required fields
    fireEvent.change(screen.getByTestId('kpi-label-input'), { 
      target: { value: 'Test KPI' } 
    });
    fireEvent.change(screen.getByTestId('kpi-sql-input'), { 
      target: { value: 'SELECT 1' } 
    });
    
    fireEvent.click(screen.getByText('Save'));
    
    expect(mockOnSave).toHaveBeenCalled();
  });

  it('validates SQL when Validate button is clicked', async () => {
    render(
      <KPIBuilder
        onValidateSQL={mockOnValidateSQL}
      />
    );
    
    fireEvent.change(screen.getByTestId('kpi-sql-input'), { 
      target: { value: 'SELECT COUNT(*) FROM users' } 
    });
    
    fireEvent.click(screen.getByTestId('validate-sql-button'));
    
    await waitFor(() => {
      expect(mockOnValidateSQL).toHaveBeenCalledWith('SELECT COUNT(*) FROM users');
    });
  });

  it('shows loading state when isLoading is true', () => {
    render(<KPIBuilder isLoading={true} />);
    
    expect(screen.getByText('Executing...')).toBeInTheDocument();
  });

  it('initializes with provided initial data', () => {
    const initialData: Partial<KPIFormData> = {
      viewType: 'target-based',
      label: 'Sales Target',
      sql: 'SELECT SUM(amount) FROM sales',
      target: '100000'
    };
    
    render(<KPIBuilder initialData={initialData} />);
    
    expect(screen.getByText('Selected: target-based')).toBeInTheDocument();
    expect(screen.getByTestId('kpi-label-input')).toHaveValue('Sales Target');
    expect(screen.getByTestId('kpi-sql-input')).toHaveValue('SELECT SUM(amount) FROM sales');
  });
});
