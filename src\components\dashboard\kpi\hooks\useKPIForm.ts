import { useState, useCallback } from 'react';
import { KPIFormData, KPIViewType } from '../types/kpiTypes';

interface UseKPIFormProps {
  initialData?: Partial<KPIFormData>;
  onFormChange?: () => void;
  onClearValidation?: (fieldKey?: string) => void;
}

interface UseKPIFormReturn {
  data: KPIFormData;
  errors: Record<string, string>;
  isValid: boolean;
  isFormValid: boolean;
  requiredFields: string[];
  handleFormChange: (updates: Partial<KPIFormData>) => void;
  handleViewTypeChange: (viewType: KPIViewType) => void;
  handleClear: () => void;
  validateForm: () => boolean;
  validateField: (fieldName: string) => boolean;
  getRequiredFields: () => string[];
  setErrors: React.Dispatch<React.SetStateAction<Record<string, string>>>;
}

const useKPIForm = ({
  initialData = {},
  onFormChange,
  onClearValidation
}: UseKPIFormProps = {}): UseKPIFormReturn => {
  const [formData, setFormData] = useState<KPIFormData>({
    viewType: 'kpi-only',
    label: '',
    sql: '',
    additionalInfo: '',
    currentValue: '',
    priorValue: '',
    target: '',
    currentLabel: '',
    priorLabel: '',
    priorSql: '',
    ...initialData
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleFormChange = useCallback((updates: Partial<KPIFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
    
    // Clear related errors when user starts typing
    const updatedFields = Object.keys(updates);
    if (updatedFields.length > 0) {
      setErrors(prev => {
        const newErrors = { ...prev };
        updatedFields.forEach(field => {
          delete newErrors[field];
        });
        return newErrors;
      });
      
      // Clear validation state for changed fields
      if (onClearValidation) {
        updatedFields.forEach(field => {
          if (field === 'sql' || field === 'priorSql') {
            onClearValidation(field);
          }
        });
      }

      // Notify parent of form change
      if (onFormChange) {
        onFormChange();
      }
    }
  }, [onFormChange, onClearValidation]);

  const handleViewTypeChange = useCallback((viewType: KPIViewType) => {
    setFormData({
      viewType,
      // Clear ALL fields when switching view types
      label: '',
      sql: '',
      additionalInfo: '',
      currentValue: '',
      priorValue: '',
      target: '',
      currentLabel: '',
      priorLabel: '',
      priorSql: ''
    });
    setErrors({});

    // Clear validation state
    if (onClearValidation) {
      onClearValidation();
    }

    // Notify parent of form change
    if (onFormChange) {
      onFormChange();
    }
  }, [onFormChange, onClearValidation]);

  // Get required fields based on KPI type
  const getRequiredFields = useCallback((): string[] => {
    switch (formData.viewType) {
      case 'current-vs-prior':
        return ['sql', 'currentLabel', 'priorLabel', 'priorSql'];
      case 'target-based':
        return ['label', 'sql', 'target'];
      case 'kpi-only':
      default:
        return ['label', 'sql'];
    }
  }, [formData.viewType]);

  // Validate a specific field
  const validateField = useCallback((fieldName: string): boolean => {
    const value = formData[fieldName as keyof KPIFormData];
    const requiredFields = getRequiredFields();

    if (!requiredFields.includes(fieldName)) {
      return true; // Field is not required
    }

    // Check if field has value
    const hasValue = typeof value === 'string' ? value.trim() !== '' : Boolean(value);

    if (!hasValue) {
      setErrors(prev => ({
        ...prev,
        [fieldName]: `${getFieldDisplayName(fieldName)} is required`
      }));
      return false;
    } else {
      // Clear error if field is now valid
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldName];
        return newErrors;
      });
      return true;
    }
  }, [formData, getRequiredFields]);

  // Helper function to get display name for field
  const getFieldDisplayName = (fieldName: string): string => {
    const displayNames: Record<string, string> = {
      label: 'KPI Label',
      sql: 'SQL Query',
      currentLabel: 'Current Label',
      priorLabel: 'Prior Label',
      priorSql: 'Prior SQL Query',
      target: 'Target'
    };
    return displayNames[fieldName] || fieldName;
  };

  const validateForm = useCallback((): boolean => {
    const requiredFields = getRequiredFields();
    const newErrors: Record<string, string> = {};

    requiredFields.forEach(fieldName => {
      const value = formData[fieldName as keyof KPIFormData];
      const hasValue = typeof value === 'string' ? value.trim() !== '' : Boolean(value);

      if (!hasValue) {
        newErrors[fieldName] = `${getFieldDisplayName(fieldName)} is required`;
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData, getRequiredFields]);

  const handleClear = useCallback(() => {
    setFormData({
      viewType: 'kpi-only',
      label: '',
      sql: '',
      additionalInfo: '',
      currentValue: '',
      priorValue: '',
      target: '',
      currentLabel: '',
      priorLabel: '',
      priorSql: ''
    });
    setErrors({});

    // Clear validation state
    if (onClearValidation) {
      onClearValidation();
    }

    // Notify parent of form change
    if (onFormChange) {
      onFormChange();
    }
  }, [onFormChange, onClearValidation]);

  // Computed properties
  const requiredFields = getRequiredFields();

  // Check if form is valid (all required fields filled)
  const isFormValid = requiredFields.every(fieldName => {
    const value = formData[fieldName as keyof KPIFormData];
    return typeof value === 'string' ? value.trim() !== '' : Boolean(value);
  });

  // Legacy isValid for backward compatibility
  const isValid = Object.keys(errors).length === 0 && isFormValid;

  return {
    data: formData,
    errors,
    isValid,
    isFormValid,
    requiredFields,
    handleFormChange,
    handleViewTypeChange,
    handleClear,
    validateForm,
    validateField,
    getRequiredFields,
    setErrors
  };
};

export default useKPIForm;
