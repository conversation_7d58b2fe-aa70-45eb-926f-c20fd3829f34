import React from 'react';
import { DataTable } from '@/components/ui/data-table/data-table';
import { ColumnDef } from '@tanstack/react-table';

interface DataGridTableProps {
  data: Record<string, any>[];
  loading?: boolean;
  error?: string | null;
}

const DataGridTable: React.FC<DataGridTableProps> = ({
  data,
  loading = false,
  error = null
}) => {
  // Utility function to transform column names to user-friendly headers
  const transformColumnName = (columnName: string): string => {
    // Handle special cases first
    const specialCases: Record<string, string> = {
      'id': 'ID',
      'url': 'URL',
      'api': 'API',
      'sql': 'SQL',
      'html': 'HTML',
      'css': 'CSS',
      'json': 'JSON',
      'xml': 'XML',
      'uuid': 'UUID',
      'ip': 'IP',
      'gps': 'GPS',
      'sms': 'SMS',
      'pdf': 'PDF'
    };

    // Check if the entire column name is a special case
    if (specialCases[columnName.toLowerCase()]) {
      return specialCases[columnName.toLowerCase()];
    }

    // Transform the column name
    return columnName
      .split('_') // Split by underscores
      .map(word => {
        // Check if individual word is a special case
        if (specialCases[word.toLowerCase()]) {
          return specialCases[word.toLowerCase()];
        }
        // Capitalize first letter of each word
        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
      })
      .join(' '); // Join with spaces
  };

  // Generate columns dynamically from data
  const columns: ColumnDef<Record<string, any>>[] = React.useMemo(() => {
    if (!data || data.length === 0) return [];

    const firstRow = data[0];
    return Object.keys(firstRow).map((key) => ({
      accessorKey: key,
      header: transformColumnName(key),
      cell: ({ row }) => {
        const value = row.getValue(key);
        return value !== null && value !== undefined ? String(value) : '';
      },
    }));
  }, [data]);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8 border border-gray-200 rounded">
        <div className="flex items-center space-x-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          <p className="text-gray-600">Loading data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center p-8 border border-red-200 rounded bg-red-50">
        <p className="text-red-600">Error: {error}</p>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center p-8 border border-gray-200 rounded">
        <p className="text-gray-500">No data available</p>
      </div>
    );
  }

  return (
    <div className="datagrid-table-wrapper">
      <DataTable
        columns={columns}
        data={data}
        pageSize={20}
        className="text-sm"
      />

      <style dangerouslySetInnerHTML={{
        __html: `
          .datagrid-table-wrapper thead {
            background-color: rgb(229 231 235);
          }
          .datagrid-table-wrapper thead th {
            color: rgb(55 65 81);
          }
        `
      }} />
    </div>
  );
};

export default DataGridTable;