
import { useState, useCallback, useRef, useEffect } from 'react';
import { useAppDispatch, useAppSelector } from './useRedux';
import { 
  setConnectionId,
  setSelectedDatabase,
  selectConnectionId,
  selectSelectedDatabase
} from '@/stores/chartSlice';
import { connectToDatabase, disconnectDatabase } from '@/services/api/chart/databaseService';
import { toast } from 'sonner';

// Dashboard-specific session storage keys
const SESSION_CONNECTION_KEY_CHART = 'chartbuilder_session_connection';
const SESSION_DATABASE_KEY_CHART = 'chartbuilder_session_database';
const SESSION_CONNECTION_KEY_DADA = 'dada_session_connection';
const SESSION_DATABASE_KEY_DADA = 'dada_session_database';

// Connection timeout (1 hour)
const CONNECTION_TIMEOUT = 60 * 60 * 1000;

// Global connection state to avoid duplicate calls - separate for each dashboard
let globalConnectionIdChart: string | null = null;
let globalDatabaseTypeChart: string | null = null;
let globalConnectionIdDada: string | null = null;
let globalDatabaseTypeDada: string | null = null;
let isConnecting = false;

export const useConnectionManager = (dashboardType: 'chart' | 'dada' = 'chart') => {
  const dispatch = useAppDispatch();
  const connectionId = useAppSelector(selectConnectionId);
  const selectedDatabase = useAppSelector(selectSelectedDatabase);
  const [isConnectingState, setIsConnectingState] = useState(false);
  const connectionPromiseRef = useRef<Promise<string | null> | null>(null);

  // Get dashboard-specific session keys
  const getSessionKeys = useCallback(() => {
    if (dashboardType === 'dada') {
      return {
        connectionKey: SESSION_CONNECTION_KEY_DADA,
        databaseKey: SESSION_DATABASE_KEY_DADA
      };
    }
    return {
      connectionKey: SESSION_CONNECTION_KEY_CHART,
      databaseKey: SESSION_DATABASE_KEY_CHART
    };
  }, [dashboardType]);

  // Get dashboard-specific global state
  const getGlobalState = useCallback(() => {
    if (dashboardType === 'dada') {
      return {
        connectionId: globalConnectionIdDada,
        databaseType: globalDatabaseTypeDada
      };
    }
    return {
      connectionId: globalConnectionIdChart,
      databaseType: globalDatabaseTypeChart
    };
  }, [dashboardType]);

  // Set dashboard-specific global state
  const setGlobalState = useCallback((connectionId: string | null, databaseType: string | null) => {
    if (dashboardType === 'dada') {
      globalConnectionIdDada = connectionId;
      globalDatabaseTypeDada = databaseType;
    } else {
      globalConnectionIdChart = connectionId;
      globalDatabaseTypeChart = databaseType;
    }
  }, [dashboardType]);

  // Clear session cache on logout - MOVED BEFORE disconnectAndClear
  const clearSessionCache = useCallback(() => {
    try {
      const { connectionKey, databaseKey } = getSessionKeys();
      sessionStorage.removeItem(connectionKey);
      sessionStorage.removeItem(databaseKey);
      setGlobalState(null, null);
      console.log(`${dashboardType.toUpperCase()}: Session connection cache cleared`);
    } catch (error) {
      console.error(`${dashboardType.toUpperCase()}: Failed to clear session cache:`, error);
    }
  }, [dashboardType, getSessionKeys, setGlobalState]);

  // Initialize session connection on mount
  useEffect(() => {
    const initializeSessionConnection = () => {
      try {
        const { connectionKey, databaseKey } = getSessionKeys();
        const sessionConnection = sessionStorage.getItem(connectionKey);
        const sessionDatabase = sessionStorage.getItem(databaseKey);
        
        if (sessionConnection && sessionDatabase) {
          console.log(`${dashboardType.toUpperCase()}: Restoring session connection:`, sessionConnection, 'for database:', sessionDatabase);
          setGlobalState(sessionConnection, sessionDatabase);
          
          // Only set in Redux if not already set and it's chart dashboard
          if (dashboardType === 'chart') {
            if (!connectionId) {
              dispatch(setConnectionId(sessionConnection));
            }
            if (!selectedDatabase) {
              dispatch(setSelectedDatabase(sessionDatabase));
            }
          }
        }
      } catch (error) {
        console.error(`${dashboardType.toUpperCase()}: Failed to restore session connection:`, error);
      }
    };

    initializeSessionConnection();
  }, [dispatch, connectionId, selectedDatabase, dashboardType, getSessionKeys, setGlobalState]);

  // Store connection and database type in session storage
  const setSessionConnection = useCallback((connectionId: string, databaseType: string) => {
    try {
      const { connectionKey, databaseKey } = getSessionKeys();
      sessionStorage.setItem(connectionKey, connectionId);
      sessionStorage.setItem(databaseKey, databaseType);
      setGlobalState(connectionId, databaseType);
      console.log(`${dashboardType.toUpperCase()}: Connection stored in session:`, connectionId, 'for database:', databaseType);
    } catch (error) {
      console.error(`${dashboardType.toUpperCase()}: Failed to store session connection:`, error);
    }
  }, [dashboardType, getSessionKeys, setGlobalState]);

  // Get session connection
  const getSessionConnection = useCallback((): { connectionId: string | null; databaseType: string | null } => {
    try {
      const { connectionKey, databaseKey } = getSessionKeys();
      const sessionConnection = sessionStorage.getItem(connectionKey);
      const sessionDatabase = sessionStorage.getItem(databaseKey);
      
      if (sessionConnection && sessionDatabase) {
        setGlobalState(sessionConnection, sessionDatabase);
        return { connectionId: sessionConnection, databaseType: sessionDatabase };
      }
    } catch (error) {
      console.error(`${dashboardType.toUpperCase()}: Failed to get session connection:`, error);
    }
    return { connectionId: null, databaseType: null };
  }, [dashboardType, getSessionKeys, setGlobalState]);

  // Disconnect from database and clear session - NOW AFTER clearSessionCache
  const disconnectAndClear = useCallback(async (connectionId?: string) => {
    const connectionToDisconnect = connectionId || getCurrentConnection();
    
    if (connectionToDisconnect) {
      try {
        console.log(`${dashboardType.toUpperCase()}: Disconnecting connection:`, connectionToDisconnect);
        await disconnectDatabase(connectionToDisconnect);
        console.log(`${dashboardType.toUpperCase()}: Successfully disconnected from database`);
      } catch (error) {
        console.error(`${dashboardType.toUpperCase()}: Failed to disconnect from database:`, error);
        // Don't throw error - we still want to clear local state even if API call fails
      }
    }
    
    // Clear all local state regardless of API call success
    if (dashboardType === 'chart') {
      dispatch(setConnectionId(''));
      dispatch(setSelectedDatabase(''));
    }
    setGlobalState(null, null);
    clearSessionCache();
  }, [dashboardType, dispatch, clearSessionCache]);

  // Check if we have a valid existing connection for the database type
  const hasValidConnectionForDatabase = useCallback((dbType: string): boolean => {
    const session = getSessionConnection();
    const global = getGlobalState();
    return (
      session.connectionId !== null && 
      session.databaseType === dbType &&
      (global.connectionId !== null || (dashboardType === 'chart' && connectionId !== null))
    );
  }, [connectionId, getSessionConnection, getGlobalState, dashboardType]);

  const getOrCreateConnection = useCallback(async (dbType: string, reuseExisting = true): Promise<string | null> => {
    // Priority 1: Check session storage first (OPTIMIZATION - avoid unnecessary API calls)
    if (reuseExisting) {
      const session = getSessionConnection();
      if (session.connectionId && session.databaseType === dbType) {
        console.log(`${dashboardType.toUpperCase()}: Reusing existing session connection for database:`, dbType, 'connection:', session.connectionId);
        if (dashboardType === 'chart') {
          dispatch(setConnectionId(session.connectionId));
          dispatch(setSelectedDatabase(dbType));
        }
        return session.connectionId;
      }
    }

    // Priority 2: Use Redux connection if available and matches (chart dashboard only)
    if (reuseExisting && dashboardType === 'chart' && connectionId && selectedDatabase === dbType) {
      console.log(`${dashboardType.toUpperCase()}: Reusing Redux connection:`, connectionId);
      setGlobalState(connectionId, dbType);
      setSessionConnection(connectionId, dbType);
      return connectionId;
    }

    // Priority 3: Prevent duplicate connection requests
    if (isConnecting || connectionPromiseRef.current) {
      console.log(`${dashboardType.toUpperCase()}: Connection request already in progress, waiting...`);
      return await (connectionPromiseRef.current || Promise.resolve(null));
    }

    // Priority 4: Create new connection only when absolutely necessary
    console.log(`${dashboardType.toUpperCase()}: Creating new connection for:`, dbType, '- No existing connection available');
    
    isConnecting = true;
    const connectionPromise = (async () => {
      try {
        setIsConnectingState(true);
        console.log(`${dashboardType.toUpperCase()}: Making API call to create connection for:`, dbType);
        
        const response = await connectToDatabase({ 
          db_type: dbType,
          host: '',
          port: '',
          database: '',
          username: '',
          password: '',
          table_name: ''
        });
        
        if (response?.connection_id) {
          if (dashboardType === 'chart') {
            dispatch(setConnectionId(response.connection_id));
            dispatch(setSelectedDatabase(dbType));
          }
          setSessionConnection(response.connection_id, dbType);
          console.log(`${dashboardType.toUpperCase()}: New connection created and stored in session:`, response.connection_id);
          return response.connection_id;
        }
        
        throw new Error('No connection ID received');
      } catch (error) {
        console.error(`${dashboardType.toUpperCase()}: Failed to create connection:`, error);
        toast.error('Failed to connect to database');
        return null;
      } finally {
        setIsConnectingState(false);
        isConnecting = false;
        connectionPromiseRef.current = null;
      }
    })();

    connectionPromiseRef.current = connectionPromise;
    return await connectionPromise;
  }, [connectionId, selectedDatabase, dispatch, getSessionConnection, setSessionConnection, hasValidConnectionForDatabase, dashboardType, setGlobalState]);

  const clearConnection = useCallback(() => {
    if (dashboardType === 'chart') {
      dispatch(setConnectionId(''));
      dispatch(setSelectedDatabase(''));
    }
    setGlobalState(null, null);
    clearSessionCache();
  }, [dispatch, clearSessionCache, dashboardType, setGlobalState]);

  // Get current active connection (prioritizes session storage and global state)
  const getCurrentConnection = useCallback(() => {
    const global = getGlobalState();
    const session = getSessionConnection();
    return global.connectionId || session.connectionId || (dashboardType === 'chart' ? connectionId : null);
  }, [connectionId, getSessionConnection, getGlobalState, dashboardType]);

  // Get current database type
  const getCurrentDatabase = useCallback(() => {
    const global = getGlobalState();
    const session = getSessionConnection();
    return global.databaseType || session.databaseType || (dashboardType === 'chart' ? selectedDatabase : null);
  }, [selectedDatabase, getSessionConnection, getGlobalState, dashboardType]);

  return {
    connectionId: getCurrentConnection(),
    selectedDatabase: getCurrentDatabase(),
    isConnecting: isConnectingState,
    getOrCreateConnection,
    clearConnection,
    clearSessionCache,
    disconnectAndClear,
    getCurrentConnection,
    hasValidConnectionForDatabase
  };
};
