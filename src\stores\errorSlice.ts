import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface ErrorState {
  isVisible: boolean;
  message: string;
  statusCode?: number;
}

const initialState: ErrorState = {
  isVisible: false,
  message: '',
  statusCode: undefined,
};

const errorSlice = createSlice({
  name: 'error',
  initialState,
  reducers: {
    setError: (state, action: PayloadAction<{ message: string; statusCode?: number }>) => {
      state.isVisible = true;
      state.message = action.payload.message;
      state.statusCode = action.payload.statusCode;
    },
    clearError: (state) => {
      state.isVisible = false;
      state.message = '';
      state.statusCode = undefined;
    },
  },
});

export const { setError, clearError } = errorSlice.actions;
export default errorSlice.reducer;

