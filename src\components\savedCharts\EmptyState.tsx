
import React from 'react';
import { Plus } from 'lucide-react';

interface EmptyStateProps {
  onCreateNewChart: () => void;
}

const EmptyState: React.FC<EmptyStateProps> = ({ onCreateNewChart }) => {
  return (
    <div className="flex flex-col items-center justify-center h-64 text-gray-500">
      <div className="text-xl mb-2">No saved charts found</div>
      <div className="text-sm mb-4">Create a new chart in the Chart Builder</div>
      <button 
        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center"
        onClick={onCreateNewChart}
      >
        <Plus size={18} className="mr-2" /> Create New Chart
      </button>
    </div>
  );
};

export default EmptyState;
