import { useState, useCallback } from 'react';
import {
  fetchDataGrid,
  transformColumnsToRequest,
  executeDataGridSQL as executeDataGridSQLAPI
} from '../../services/api/chart/datagrid';
import {
  DataGridResponse,
  SelectedColumn,
  DataGridSQLRequest
} from '../../services/api/chart/datagrid/datagridTypes';
import { validateKPISQL } from '../../services/api/chart/kpiService';

interface UseDataGridReturn {
  data: Record<string, any>[];
  loading: boolean;
  error: string | null;
  executeDataGrid: (
    selectedColumns: SelectedColumn[],
    connectionId: string,
    filterConditions?: { column: string; operator: string; value: string }[]
  ) => Promise<void>;
  executeDataGridSQL: (sql: string, connectionId: string) => Promise<void>;
  validateSQL: (sql: string, connectionId?: string) => Promise<{ isValid: boolean; message?: string }>;
  clearData: () => void;
  clearError: () => void;
}

export const useDataGrid = (): UseDataGridReturn => {
  const [data, setData] = useState<Record<string, any>[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const executeDataGrid = useCallback(async (
    selectedColumns: SelectedColumn[],
    connectionId: string,
    filterConditions?: { column: string; operator: string; value: string }[]
  ): Promise<void> => {
    if (!connectionId) {
      setError('Connection ID is required');
      return;
    }

    if (!selectedColumns || selectedColumns.length === 0) {
      setError('Please select at least one column');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Transform selected columns to API request format with filter support
      const request = transformColumnsToRequest(
        connectionId,
        selectedColumns,
        filterConditions
      );

      console.log('DataGrid Hook: Executing with request:', JSON.stringify(request, null, 2));

      // Call the API
      const response: DataGridResponse = await fetchDataGrid(request);

      if (response.status === 'success' && response.data) {
        setData(response.data);
      } else {
        setError('Failed to fetch data: Invalid response format');
        setData([]);
      }
    } catch (err: any) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
      setData([]);
    } finally {
      setLoading(false);
    }
  }, []);

  const clearData = useCallback(() => {
    setData([]);
    setError(null);
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const executeDataGridSQL = useCallback(async (
    sql: string,
    connectionId: string
  ): Promise<void> => {
    if (!connectionId) {
      setError('Connection ID is required');
      return;
    }

    if (!sql.trim()) {
      setError('SQL query is required');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const request: DataGridSQLRequest = {
        connection_id: connectionId,
        sql: sql.trim()
      };

      console.log('DataGrid SQL Hook: Executing with request:', JSON.stringify(request, null, 2));

      // Call the SQL API
      const response: DataGridResponse = await executeDataGridSQLAPI(request);

      if (response.status === 'success' && response.data) {
        setData(response.data);
      } else {
        setError('Failed to execute SQL: Invalid response format');
        setData([]);
      }
    } catch (err: any) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
      setData([]);
    } finally {
      setLoading(false);
    }
  }, []);



  const validateSQL = useCallback(async (
    sql: string,
    connectionId?: string
  ): Promise<{ isValid: boolean; message?: string }> => {
    try {
      // Reuse the KPI SQL validation function
      return await validateKPISQL(sql, connectionId);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Validation error occurred';
      return {
        isValid: false,
        message: errorMessage
      };
    }
  }, []);

  return {
    data,
    loading,
    error,
    executeDataGrid,
    executeDataGridSQL,
    validateSQL,
    clearData,
    clearError,
  };
};
