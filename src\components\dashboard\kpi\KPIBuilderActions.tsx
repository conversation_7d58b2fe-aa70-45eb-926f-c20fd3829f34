import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Eraser, CirclePlay, Save } from "lucide-react";

interface KPIBuilderActionsProps {
  onClear: () => void;
  onExecute: () => void;
  onSave: () => void;
  isExecuting: boolean;
  isLoading: boolean;
  canSave: boolean;
  canExecute: boolean;
}

const KPIBuilderActions: React.FC<KPIBuilderActionsProps> = ({
  onClear,
  onExecute,
  onSave,
  isExecuting,
  isLoading,
  canSave,
  canExecute
}) => {
  return (
    <div className="flex items-center justify-end">
      <div className="flex items-center space-x-3">
        <Button
          onClick={onClear}
          size="sm"
          variant="outline"
          disabled={isLoading || isExecuting}
        >
          <Eraser size={16} className="mr-1.5" />
          Clear
        </Button>
        <Button
          onClick={onExecute}
          variant="blue"
          disabled={!canExecute || isLoading || isExecuting}
          size="sm"
        >
          <CirclePlay size={16} className="mr-1.5" />
          {isLoading || isExecuting ? 'Executing...' : 'Execute'}
        </Button>
        <Button
          onClick={onSave}
          disabled={!canSave}
          size="sm"
          variant="greenmind"
        >
          <Save size={16} className="mr-1.5" />
          Save
        </Button>
      </div>
    </div>
  );
};

export default KPIBuilderActions;
