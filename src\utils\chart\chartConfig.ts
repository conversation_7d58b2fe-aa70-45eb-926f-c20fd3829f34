import { ChartType, ChartSize, ChartContext, BASE_CHART_OPTIONS, MINIMAL_CHART_OPTIONS } from '@/constants/chartConstants';

export interface ChartConfiguration {
  options: any;
  height: string;
  showTitle: boolean;
  showLegend: boolean;
  showAxes: boolean;
  showTooltips: boolean;
  showGridLines: boolean;
  fontSize: {
    title: number;
    legend: number;
    axes: number;
    tooltip: number;
  };
  spacing: {
    padding: number;
    margin: number;
  };
}

/**
 * Context-aware chart configuration system
 */
export class ChartConfigurationManager {
  
  /**
   * Get configuration based on context, size, and chart type
   */
  static getConfiguration(
    context: ChartContext,
    size: ChartSize = 'default',
    chartType: ChartType = 'bar'
  ): ChartConfiguration {
    
    const baseConfig = this.getBaseConfiguration(size);
    const contextConfig = this.getContextConfiguration(context);
    const typeConfig = this.getTypeConfiguration(chartType);
    
    return this.mergeConfigurations(baseConfig, contextConfig, typeConfig);
  }

  /**
   * Get base configuration by size
   */
  private static getBaseConfiguration(size: ChartSize): Partial<ChartConfiguration> {
    switch (size) {
      case 'small':
        return {
          height: 'h-32',
          fontSize: {
            title: 10,
            legend: 8,
            axes: 8,
            tooltip: 9
          },
          spacing: {
            padding: 4,
            margin: 2
          }
        };
      case 'large':
        return {
          height: 'h-96',
          fontSize: {
            title: 16,
            legend: 12,
            axes: 12,
            tooltip: 12
          },
          spacing: {
            padding: 20,
            margin: 10
          }
        };
      default: // 'default'
        return {
          height: 'h-64',
          fontSize: {
            title: 13,
            legend: 11,
            axes: 10,
            tooltip: 11
          },
          spacing: {
            padding: 10,
            margin: 5
          }
        };
    }
  }

  /**
   * Get context-specific configuration
   */
  private static getContextConfiguration(context: ChartContext): Partial<ChartConfiguration> {
    switch (context) {
      case 'thumbnail':
        return {
          options: { ...MINIMAL_CHART_OPTIONS },
          showTitle: false,
          showLegend: false,
          showAxes: false,
          showTooltips: false,
          showGridLines: false
        };
      
      case 'results':
        return {
          showTitle: true,
          showLegend: false,
          showAxes: true,
          showTooltips: true,
          showGridLines: true
        };
      
      case 'builder':
        return {
          showTitle: true,
          showLegend: true,
          showAxes: true,
          showTooltips: true,
          showGridLines: true
        };
      
      case 'chartboard':
        return {
          showTitle: true,
          showLegend: true,
          showAxes: true,
          showTooltips: true,
          showGridLines: true
        };
      
      default: // 'dashboard'
        return {
          showTitle: true,
          showLegend: true,
          showAxes: true,
          showTooltips: true,
          showGridLines: true
        };
    }
  }

  /**
   * Get chart type-specific configuration
   */
  private static getTypeConfiguration(chartType: ChartType): Partial<ChartConfiguration> {
    switch (chartType) {
      case 'pie':
      case 'doughnut':
        return {
          showAxes: false,
          showGridLines: false,
          options: {
            scales: {
              x: { display: false },
              y: { display: false }
            }
          }
        };
      
      case 'line':
        return {
          options: {
            elements: {
              point: {
                radius: 3,
                hoverRadius: 5
              },
              line: {
                tension: 0.2
              }
            }
          }
        };
      
      default:
        return {};
    }
  }

  /**
   * Merge multiple configurations
   */
  private static mergeConfigurations(...configs: Partial<ChartConfiguration>[]): ChartConfiguration {
    const defaultConfig: ChartConfiguration = {
      options: { ...BASE_CHART_OPTIONS },
      height: 'h-64',
      showTitle: true,
      showLegend: true,
      showAxes: true,
      showTooltips: true,
      showGridLines: true,
      fontSize: {
        title: 13,
        legend: 11,
        axes: 10,
        tooltip: 11
      },
      spacing: {
        padding: 10,
        margin: 5
      }
    };

    let result = defaultConfig;
    
    for (const config of configs) {
      result = {
        options: this.deepMerge(result.options, config.options || {}),
        height: config.height || result.height,
        showTitle: config.showTitle !== undefined ? config.showTitle : result.showTitle,
        showLegend: config.showLegend !== undefined ? config.showLegend : result.showLegend,
        showAxes: config.showAxes !== undefined ? config.showAxes : result.showAxes,
        showTooltips: config.showTooltips !== undefined ? config.showTooltips : result.showTooltips,
        showGridLines: config.showGridLines !== undefined ? config.showGridLines : result.showGridLines,
        fontSize: { ...result.fontSize, ...(config.fontSize || {}) },
        spacing: { ...result.spacing, ...(config.spacing || {}) }
      };
    }
    
    return result;
  }

  /**
   * Deep merge objects (for chart options)
   */
  private static deepMerge(target: any, source: any): any {
    const result = { ...target };
    
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(result[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
    
    return result;
  }

  /**
   * Apply configuration to chart options
   */
  static applyConfiguration(
    baseOptions: any,
    config: ChartConfiguration,
    title?: string,
    xAxisLabel?: string,
    yAxisLabel?: string
  ): any {
    const options = { ...config.options };

    // Apply title
    if (config.showTitle && title) {
      options.plugins.title = {
        ...options.plugins.title,
        display: true,
        text: title,
        font: {
          size: config.fontSize.title,
          weight: 'bold'
        },
        padding: {
          bottom: config.spacing.padding
        }
      };
    }

    // Apply legend
    if (config.showLegend) {
      options.plugins.legend = {
        ...options.plugins.legend,
        display: true,
        labels: {
          ...options.plugins.legend.labels,
          font: {
            size: config.fontSize.legend
          },
          padding: config.spacing.padding
        }
      };
    } else {
      options.plugins.legend.display = false;
    }

    // Apply axes
    if (config.showAxes && options.scales) {
      // X-axis
      if (options.scales.x) {
        options.scales.x.display = true;
        options.scales.x.ticks = {
          ...options.scales.x.ticks,
          font: { size: config.fontSize.axes }
        };
        
        if (xAxisLabel) {
          options.scales.x.title = {
            display: true,
            text: xAxisLabel,
            font: {
              size: config.fontSize.axes,
              weight: 'bold'
            }
          };
        }
      }

      // Y-axis
      if (options.scales.y) {
        options.scales.y.display = true;
        options.scales.y.beginAtZero = true;
        // PRESERVE RAW DADA AI VALUES - NO NORMALIZATION
        options.scales.y.min = 0;
        options.scales.y.ticks = {
          ...options.scales.y.ticks,
          font: { size: config.fontSize.axes },
          // Display actual numeric values without transformations
          callback: function(value: any) {
            // Return the raw value as-is for DADA AI data
            return typeof value === 'number' ? value : parseFloat(value) || 0;
          }
        };
        
        if (yAxisLabel) {
          options.scales.y.title = {
            display: true,
            text: yAxisLabel,
            font: {
              size: config.fontSize.axes,
              weight: 'bold'
            }
          };
        }
      }
    }

    // Apply grid lines
    if (!config.showGridLines && options.scales) {
      if (options.scales.x?.grid) options.scales.x.grid.display = false;
      if (options.scales.y?.grid) options.scales.y.grid.display = false;
    }

    // Apply tooltips
    if (config.showTooltips) {
      options.plugins.tooltip = {
        ...options.plugins.tooltip,
        enabled: true,
        titleFont: { size: config.fontSize.tooltip },
        bodyFont: { size: config.fontSize.tooltip }
      };
    } else {
      options.plugins.tooltip.enabled = false;
    }

    return options;
  }
}