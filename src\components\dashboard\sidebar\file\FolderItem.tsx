
import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Folder, FolderOpen, Plus, Trash, File } from "lucide-react";
import { FolderItem as FolderItemType } from '../types';

interface FolderItemProps {
  folder: FolderItemType;
  iconColor: string;
  isExpanded: boolean;
  onToggle: (folderName: string) => void;
  onCreateSubFolder: (parentFolder: string) => void;
  onDeleteFolder: (folderName: string) => void;
  onDeleteSubFolder: (parentFolder: string, subFolderName: string) => void;
}

const FolderItem: React.FC<FolderItemProps> = ({
  folder,
  iconColor,
  isExpanded,
  onToggle,
  onCreateSubFolder,
  onDeleteFolder,
  onDeleteSubFolder,
}) => {
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);

  return (
    <div className="mb-1">
      <div 
        className="flex items-center justify-between text-sm text-gray-600 py-1 cursor-pointer hover:text-blue-600 group"
        onMouseEnter={() => setHoveredItem(folder.name)}
        onMouseLeave={() => setHoveredItem(null)}
      >
        <div 
          className="flex items-center flex-1"
          onClick={() => onToggle(folder.name)}
        >
          {isExpanded ? (
            <FolderOpen className={`h-4 w-4 mr-2 ${iconColor}`} />
          ) : (
            <Folder className={`h-4 w-4 mr-2 ${iconColor}`} />
          )}
          {folder.name}
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 hover:bg-gray-100"
            onClick={(e) => {
              e.stopPropagation();
              onCreateSubFolder(folder.name);
            }}
          >
            <Plus className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 hover:bg-gray-100"
            onClick={(e) => {
              e.stopPropagation();
              onDeleteFolder(folder.name);
            }}
          >
            <Trash className="h-4 w-4 text-gray-400 hover:text-red-500 opacity-0 group-hover:opacity-100 transition-opacity" />
          </Button>
        </div>
      </div>
      
      {isExpanded && (
        <div className="ml-6 mt-1">
          {folder.subFolders && folder.subFolders.map((subFolder) => (
            <div
              key={subFolder.name}
              className="flex items-center justify-between text-sm text-gray-600 py-1 cursor-pointer hover:text-blue-600 group"
              onMouseEnter={() => setHoveredItem(subFolder.name)}
              onMouseLeave={() => setHoveredItem(null)}
            >
              <div className="flex items-center">
                <Folder className="h-4 w-4 mr-2" />
                {subFolder.name}
              </div>
              {hoveredItem === subFolder.name && (
                <span
                  className="text-gray-400 pr-1 hover:text-red-500 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDeleteSubFolder(folder.name, subFolder.name);
                  }}
                >
                  <Trash size={16} />
                </span>
              )}
            </div>
          ))}
          
          {folder.files.map((fileName, index) => (
            <div
              key={index}
              className="flex items-center text-sm text-gray-600 py-1 cursor-pointer hover:text-blue-600 group"
            >
              <File className="h-4 w-4 mr-2" />
              {fileName}
            </div>
          ))}
          
          {(!folder.files?.length && (!folder.subFolders || !folder.subFolders.length)) && (
            <div className="text-sm text-gray-400 py-1">
              No files or subfolders
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default FolderItem;
