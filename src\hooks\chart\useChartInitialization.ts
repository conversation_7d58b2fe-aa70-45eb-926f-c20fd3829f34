
import { useState, useCallback, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useChartStatePersistence } from '@/hooks/useChartStatePersistence';
import { useConnectionManager } from '@/hooks/useConnectionManager';
import { validateNavigationPayload } from '@/components/dashboard/chartboard/navigationUtils';
import { identifyChartKeys } from '@/components/dashboard/chartboard/chartUtils';
import { ChartInitializationData } from './types';

export const useChartInitialization = () => {
  const location = useLocation();
  const { hasEditingData, getEditingData } = useChartStatePersistence();
  const { getCurrentConnection, getOrCreateConnection } = useConnectionManager();
  const [dataInitialized, setDataInitialized] = useState(false);

  const initializeChartData = useCallback(async (): Promise<ChartInitializationData | null> => {
    if (dataInitialized) return null;
    
    let chartDataSource = null;
    
    // Priority 1: Check Redux store for editing chart data
    if (hasEditingData()) {
      console.log("Found editing chart data in Redux store - using existing data with session connection");
      chartDataSource = getEditingData();
      
      if (chartDataSource) {
        console.log("Processing editing chart data:", chartDataSource);
        
        // Extract filters from the chart data
        const filters = chartDataSource.originalChart?.filters ||
                       chartDataSource.filters ||
                       {};

        // Enhanced table association reconstruction for editing mode
        let xAxisTable = '';
        let yAxisTables: string[] = [];
        let yAxisColumns: string[] = [];
        let extractedAggregationType: string | null = null;

        // Handle different data structures from saved charts
        if (chartDataSource.tables && Array.isArray(chartDataSource.tables)) {
          console.log("Reconstructing table associations from tables array:", chartDataSource.tables);

          // Extract x-axis table by matching x_axis column
          const xAxisColumn = chartDataSource.x_axis || '';

          for (const table of chartDataSource.tables) {
            if (table.columns && table.columns.includes(xAxisColumn)) {
              xAxisTable = table.table_name;
              console.log("Found x-axis table:", xAxisTable, "for column:", xAxisColumn);
              break;
            }
          }

          // Extract y-axis columns and their tables
          const yAxis = chartDataSource.y_axis;
          if (Array.isArray(yAxis)) {
            // Handle array of y-axis columns (could be strings or objects)
            yAxis.forEach((yItem, index) => {
              if (typeof yItem === 'string') {
                yAxisColumns.push(yItem);
                // Find which table contains this column
                for (const table of chartDataSource.tables) {
                  if (table.columns && table.columns.includes(yItem)) {
                    yAxisTables.push(table.table_name);
                    console.log("Found y-axis table:", table.table_name, "for column:", yItem);
                    break;
                  }
                }
              } else if (typeof yItem === 'object' && yItem.column) {
                yAxisColumns.push(yItem.column);
                yAxisTables.push(yItem.table_name || xAxisTable);

                // Extract aggregation type from the y-axis object
                if (yItem.aggregation && !extractedAggregationType) {
                  extractedAggregationType = yItem.aggregation;
                  console.log("Extracted aggregation type:", extractedAggregationType, "from y-axis object:", yItem);
                }

                console.log("Using y-axis object:", yItem);
              }
            });
          } else if (typeof yAxis === 'string') {
            yAxisColumns = [yAxis];
            // Find which table contains this column
            for (const table of chartDataSource.tables) {
              if (table.columns && table.columns.includes(yAxis)) {
                yAxisTables.push(table.table_name);
                console.log("Found y-axis table:", table.table_name, "for column:", yAxis);
                break;
              }
            }
          }
          
          // Fallback: if no tables found, use first table
          if (!xAxisTable && chartDataSource.tables.length > 0) {
            xAxisTable = chartDataSource.tables[0].table_name;
            console.log("Using fallback x-axis table:", xAxisTable);
          }
          
          if (yAxisTables.length === 0 && chartDataSource.tables.length > 0) {
            yAxisTables = [chartDataSource.tables[0].table_name];
            console.log("Using fallback y-axis tables:", yAxisTables);
          }
        } else {
          // Fallback to original logic for legacy data
          xAxisTable = chartDataSource.table_name || '';
          yAxisTables = [chartDataSource.table_name || ''];
          yAxisColumns = Array.isArray(chartDataSource.y_axis) 
            ? chartDataSource.y_axis.map(y => typeof y === 'string' ? y : y.column)
            : [chartDataSource.y_axis || ''];
        }
        
        console.log("Final table associations:", {
          xAxisTable,
          yAxisTables,
          xAxisColumn: chartDataSource.x_axis,
          yAxisColumns
        });

        const initData: ChartInitializationData = {
          chartStyle: chartDataSource.chart_type || 'bar',
          xAxisColumn: chartDataSource.x_axis || '',
          yAxisColumns,
          xAxisTable,
          yAxisTables,
          aggregationType: extractedAggregationType || chartDataSource.aggregation || null,
          connectionId: getCurrentConnection() || chartDataSource.connectionId,
          filters // Include filters in the returned data
        };

        console.log("Chart initialization data created:", {
          ...initData,
          aggregationType: initData.aggregationType,
          hasAggregation: !!initData.aggregationType
        });

        // Prioritize existing chart data to avoid API calls
        if (chartDataSource.chartData && Array.isArray(chartDataSource.chartData) && chartDataSource.chartData.length > 0) {
          console.log("Using existing chart data, skipping API call - optimized for edit mode");
          initData.chartData = chartDataSource.chartData;
          initData.hasExistingData = true;
        } else if (chartDataSource.chart_response && chartDataSource.chart_response.data && Array.isArray(chartDataSource.chart_response.data)) {
          console.log("Using chart data from chart_response");
          initData.chartData = chartDataSource.chart_response.data;
          initData.hasExistingData = true;
        }

        // Use session connection instead of creating new one in edit mode
        const sessionConnectionId = getCurrentConnection();
        if (!sessionConnectionId && chartDataSource.connectionId && !chartDataSource.hasExistingData) {
          console.log("Establishing connection only if no existing data and no session connection");
          await getOrCreateConnection(chartDataSource.db_type || 'postgres', true);
        }

        setDataInitialized(true);
        return initData;
      }
    }
    // Priority 2: Check location state for direct navigation
    else if (location.state && validateNavigationPayload(location.state)) {
      console.log("Found chart data in navigation state");
      chartDataSource = location.state;
      
      const { chartData } = chartDataSource;
      
      const initData: ChartInitializationData = {
        chartStyle: chartData.chart_type as 'bar' | 'line' | 'pie',
        xAxisColumn: chartData.x_axis,
        yAxisColumns: [chartData.y_axis],
        xAxisTable: chartData.table_name,
        yAxisTables: [chartData.table_name],
        aggregationType: chartData.aggregation || null
      };

      // Use session connection if available, otherwise establish new one
      const sessionConnectionId = getCurrentConnection();
      if (sessionConnectionId) {
        console.log("Using existing session connection from location state");
        initData.connectionId = sessionConnectionId;
      } else if (chartData.db_type) {
        await getOrCreateConnection(chartData.db_type, true);
        initData.connectionId = getCurrentConnection();
      }

      setDataInitialized(true);
      return initData;
    }
    
    setDataInitialized(true);
    return null;
  }, [location.state, hasEditingData, getEditingData, getOrCreateConnection, dataInitialized, getCurrentConnection]);

  // Initialize on mount
  useEffect(() => {
    initializeChartData();
  }, [initializeChartData]);

  return {
    initializeChartData,
    dataInitialized
  };
};
