
import { useEffect } from 'react';

interface UseSearchFilterProps {
  searchQuery: string;
  dashboardType: 1 | 2 | 3;
  filterItems: (query: string, dashboardType: 1 | 2 | 3) => void;
}

export const useSearchFilter = ({ 
  searchQuery, 
  dashboardType, 
  filterItems 
}: UseSearchFilterProps, 
deps: React.DependencyList = []
) => {
  useEffect(() => {
    if (searchQuery) {
      filterItems(searchQuery, dashboardType);
    }
  }, [searchQuery, dashboardType, filterItems, ...deps]);
};
