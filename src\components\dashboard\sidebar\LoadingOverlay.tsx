
import React from 'react';
import { Loader2 } from "lucide-react";

interface LoadingOverlayProps {
  isVisible: boolean;
}

const LoadingOverlay: React.FC<LoadingOverlayProps> = ({ isVisible }) => {
  if (!isVisible) return null;
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-4 rounded-lg flex items-center space-x-3">
        <Loader2 className="h-8 w-8 text-blue-500 animate-spin" />
        <span className="text-gray-600">Loading your transcript file...</span>
      </div>
    </div>
  );
};

export default LoadingOverlay;
