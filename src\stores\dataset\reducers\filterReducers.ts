
import { PayloadAction } from '@reduxjs/toolkit';
import { DatasetState, FilterCondition } from '../types';

export const filterReducers = {
  addFilterCondition: (state: DatasetState, action: PayloadAction<FilterCondition>) => {
    state.filterConditions.push(action.payload);
  },
  
  updateFilterCondition: (state: DatasetState, action: PayloadAction<{index: number; filter: FilterCondition}>) => {
    const { index, filter } = action.payload;
    if (index >= 0 && index < state.filterConditions.length) {
      state.filterConditions[index] = filter;
    }
  },
  
  removeFilterCondition: (state: DatasetState, action: PayloadAction<number>) => {
    state.filterConditions.splice(action.payload, 1);
  },
};
