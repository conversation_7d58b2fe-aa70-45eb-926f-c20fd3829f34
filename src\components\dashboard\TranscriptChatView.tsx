
import React, { useState, useEffect } from 'react';
import { Message } from './models';
import { ChatInput } from './chat';
import { useToast } from '@/hooks/use-toast';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';

interface TranscriptChatViewProps {
  onSendMessage: (message: string) => void;
  onClearHistory: () => void;
  transcript?: string;
}

interface ApiResponseMessage {
  content: string;
  type: 'response' | 'error';
}

const TranscriptChatView = ({ onSendMessage, onClearHistory, transcript }: TranscriptChatViewProps) => {
  const [inputValue, setInputValue] = useState('');
  const { toast: uiToast } = useToast();
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  
  const API_URL = '';
  const API_KEY = '';

  // Initialize with a welcome message when transcript changes
  useEffect(() => {
    if (transcript) {
      setMessages([
        { 
          type: 'response', 
          content: 'Transcript has been loaded. You can now ask questions about it.', 
          minimized: false 
        }
      ]);
    }
  }, [transcript]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim() || !transcript) return;

    // Add user message
    const newUserMessage: Message = {
      type: 'query',
      content: inputValue,
      minimized: false
    };
    
    setMessages(prev => [...prev, newUserMessage]);
    setIsLoading(true);
    
    try {
      // Make API call
      const response = await fetchResponseFromAPI(inputValue, transcript);
      
      const responseMessage: Message = {
        type: 'response',
        content: response.content,
        minimized: false
      };
      
      setMessages(prev => [...prev, responseMessage]);
      onSendMessage(inputValue);
    } catch (error) {
      console.error('Error getting response:', error);
      
      // Add error message
      const errorMessage: Message = {
        type: 'response',
        content: 'Sorry, I encountered an error trying to process your question. Please try again.',
        minimized: false
      };
      
      setMessages(prev => [...prev, errorMessage]);
      
      toast.error('Error processing question', {
        description: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    } finally {
      setIsLoading(false);
      setInputValue('');
    }
  };

  const fetchResponseFromAPI = async (question: string, transcriptText: string): Promise<ApiResponseMessage> => {
    try {
      const payload = {
        question,
        transcript: transcriptText
      };
      
      const response = await fetch(`${API_URL}/query-transcript/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-API-Key': API_KEY
        },
        body: JSON.stringify(payload)
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `Server responded with status ${response.status}`);
      }
      
      const data = await response.json();
      return {
        content: data.response || 'No response provided',
        type: 'response'
      };
    } catch (error) {
      console.error('API call failed:', error);
      throw error;
    }
  };

  const handleClearHistory = () => {
    setMessages([]);
    onClearHistory();
  };

  const activateVoiceInput = () => {
    console.log("Voice input activated from parent component");
    uiToast({
      title: "Voice Input Activated",
      description: "Speak now. Your speech is being converted to text.",
    });
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex justify-between items-center p-3 border-b border-gray-200">
        <Button 
          onClick={handleClearHistory}
          variant='blue'
        >
          Clear Chat History
        </Button>
        <Button  variant='indigo'>
          Save
        </Button>
      </div>
      
      <div className="flex-1 overflow-y-auto p-3 space-y-4 mb-2">
        {messages.length > 0 ? (
          messages.map((message, index) => (
            <div key={index} className="border border-gray-200 rounded-md overflow-hidden">
              <div className="p-2 bg-gray-50 border-b border-gray-200">
                {message.type === 'query' ? 'Question' : 'Chat result'}
              </div>
              <div className="p-3">
                <p className="text-sm">{message.content}</p>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center text-gray-500 py-10">
            <p>No messages yet. Ask a question about the transcript.</p>
          </div>
        )}
        
        {isLoading && (
          <div className="text-center py-4">
            <div className="inline-block animate-spin rounded-full h-6 w-6 border-2 border-blue-500 border-t-transparent"></div>
            <p className="text-sm text-gray-500 mt-2">Processing your question...</p>
          </div>
        )}
      </div>
      
      <div className="pt-1 pb-3 border-t border-gray-200">
        <ChatInput 
          inputValue={inputValue} 
          setInputValue={setInputValue}
          handleSubmit={handleSubmit}
          activateVoiceInput={activateVoiceInput}
          isLoading={isLoading}
          dashboardType={2} // Explicitly set to type 2 to disable predictions
        />
      </div>
    </div>
  );
};

export default TranscriptChatView;
