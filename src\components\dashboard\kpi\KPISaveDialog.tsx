import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';

interface KPISaveDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (name: string) => Promise<void>;
  isLoading?: boolean;
  kpiType: string;
}

const KPISaveDialog: React.FC<KPISaveDialogProps> = ({
  isOpen,
  onClose,
  onSave,
  isLoading = false,
  kpiType
}) => {
  const [name, setName] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  const handleSave = async () => {
    const trimmedName = name.trim();
    
    if (!trimmedName) {
      toast.error('Please enter a name for the KPI');
      return;
    }

    if (trimmedName.length < 3) {
      toast.error('KPI name must be at least 3 characters long');
      return;
    }

    try {
      setIsSaving(true);
      await onSave(trimmedName);

      // Reset form and close dialog on success
      setName('');
      onClose();
      toast.success('KPI saved successfully!');
    } catch (error) {
      console.error('Error saving KPI:', error);

      // Extract meaningful error message
      let errorMessage = 'Failed to save KPI';
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      // Show user-friendly error message
      toast.error(errorMessage, {
        description: 'Please try again or contact support if the issue persists.'
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleClose = () => {
    if (!isSaving) {
      setName('');
      onClose();
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isSaving) {
      handleSave();
    }
  };

  const getKPITypeLabel = (type: string) => {
    switch (type) {
      case 'kpi-only':
        return 'KPI Only';
      case 'current-vs-prior':
        return 'Current vs Prior';
      case 'target-based':
        return 'Target Based';
      default:
        return 'KPI';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Save {getKPITypeLabel(kpiType)} KPI</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="kpi-name">KPI Name</Label>
            <Input
              id="kpi-name"
              placeholder="Enter a name for your KPI"
              value={name}
              onChange={(e) => setName(e.target.value)}
              onKeyPress={handleKeyPress}
              disabled={isSaving}
              autoFocus
              maxLength={100}
            />
            <p className="text-xs text-muted-foreground">
              Choose a descriptive name for your KPI (3-100 characters)
            </p>
          </div>
        </div>

        <DialogFooter className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isSaving}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={isSaving || !name.trim() || name.trim().length < 3}
            variant='greenmind'
          >
            {isSaving ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              'Save KPI'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default KPISaveDialog;
