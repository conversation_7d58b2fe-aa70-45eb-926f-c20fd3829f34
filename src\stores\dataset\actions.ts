
import { createSlice } from '@reduxjs/toolkit';
import { initialState } from './initialState';
import { navigationReducers } from './reducers/navigationReducers';
import { connectionReducers } from './reducers/connectionReducers';
import { tableReducers } from './reducers/tableReducers';
import { columnReducers } from './reducers/columnReducers';
import { derivedColumnReducers } from './reducers/derivedColumnReducers';
import { filterReducers } from './reducers/filterReducers';
import { definitionReducers } from './reducers/definitionReducers';

const datasetSlice = createSlice({
  name: 'dataset',
  initialState,
  reducers: {
    // Navigation actions
    ...navigationReducers,
    
    // Connection actions
    ...connectionReducers,
    
    // Table actions
    ...tableReducers,
    
    // Column actions
    ...columnReducers,
    
    // Derived column actions
    ...derivedColumnReducers,
    
    // Filter actions
    ...filterReducers,
    
    // Dataset definition actions
    ...definitionReducers,
  },
});

export const { 
  // Navigation actions
  setShowDatasetScreen, 
  setCurrentStep,
  setStepNumber,
  setSearchQuery,
  resetDatasetState,
  nextStep,
  previousStep,
  
  // Connection actions
  toggleConnectionSelection,
  addToSelectedConnections,
  removeFromSelectedConnections,
  
  // Table actions
  toggleTableSelection,
  addToSelectedTables,
  removeFromSelectedTables,
  
  // Column actions
  addColumn,
  removeColumn,
  
  // Derived column actions
  addDerivedColumn,
  updateDerivedColumn,
  removeDerivedColumn,
  
  // Filter actions
  addFilterCondition,
  updateFilterCondition,
  removeFilterCondition,
  
  // Dataset definition actions
  updateDatasetDefinition,
} = datasetSlice.actions;

export default datasetSlice.reducer;
