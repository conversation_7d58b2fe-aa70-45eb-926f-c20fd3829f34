
import React, { useState, useEffect } from 'react';
import { useAppSelector, useAppDispatch } from '@/hooks/useRedux';
import { setTables, setIsLoadingTables } from '@/stores/chartSlice';
import SearchInput from '../search/SearchInput';
import { ChatTopic, Project, Meeting } from '../types';
import { fetchTables } from '@/services/api/chart/databaseService';
import { toast } from 'sonner';
import DatabaseConnectionDropdown from './DatabaseConnectionDropdown';
import TablesList from './TablesList';
import ProjectsList from '../ProjectsList';
import MinimizedSidebar from '../../MinimizedSidebar';
import { useConnectionManager } from '@/hooks/useConnectionManager';
import DadaConnectionDropdown from './DadaConnectionDropdown';

interface ListDashboardViewProps {
  searchQuery: string;
  handleSearch: (e: React.ChangeEvent<HTMLInputElement>) => void;
  filteredItems: ChatTopic[] | Project[] | Meeting[];
  toggleItem: (id: string) => void;
  addChatToItem: (id: string) => void;
  addNewItem: () => void;
  dashboardType: 1 | 2 | 3;
  onToggle: () => void;
  collapsed?: boolean;
}

const ListDashboardView: React.FC<ListDashboardViewProps> = ({
  searchQuery,
  handleSearch,
  filteredItems,
  toggleItem,
  addChatToItem,
  addNewItem,
  dashboardType,
  onToggle,
  collapsed = false
}) => {
  // ALL HOOKS MUST BE CALLED AT THE TOP - BEFORE ANY CONDITIONAL LOGIC
  const dispatch = useAppDispatch();
  const { 
    tables = [], 
    isLoadingTables = false
  } = useAppSelector(state => (state as any).chart || {});
  
  const { connectionId, selectedDatabase, getCurrentConnection } = useConnectionManager();
  
  const [tableSearchQuery, setTableSearchQuery] = useState('');
  const [lastConnectionId, setLastConnectionId] = useState<string | null>(null);
  const [lastSelectedDatabase, setLastSelectedDatabase] = useState<string | null>(null);

  // Clear tables and reset tracking when connection OR database changes
  useEffect(() => {
    const activeConnectionId = getCurrentConnection();
    const currentDatabase = selectedDatabase;
    
    // Check if either connection or database has changed
    const connectionChanged = activeConnectionId !== lastConnectionId;
    const databaseChanged = currentDatabase !== lastSelectedDatabase;
    
    if (connectionChanged || databaseChanged) {
      console.log('Connection or database changed:', {
        connection: { from: lastConnectionId, to: activeConnectionId },
        database: { from: lastSelectedDatabase, to: currentDatabase },
        connectionChanged,
        databaseChanged
      });
      
      // Clear tables when switching (always refresh tables)
      if (dashboardType === 2) {
        dispatch(setTables([]));
      }
      
      setLastConnectionId(activeConnectionId);
      setLastSelectedDatabase(currentDatabase);
    }
  }, [getCurrentConnection(), selectedDatabase, lastConnectionId, lastSelectedDatabase, dashboardType, dispatch]);

  // ALWAYS fetch tables when database selection changes (remove caching to fix the issue)
  useEffect(() => {
    const activeConnectionId = getCurrentConnection();
    const currentDatabase = selectedDatabase;
    
    // Always fetch tables when we have a connection and it's dashboard-2
    if (activeConnectionId && dashboardType === 2 && currentDatabase) {
      const loadTables = async () => {
        try {
          console.log('Loading tables for connection:', activeConnectionId, 'database:', currentDatabase);
          dispatch(setIsLoadingTables(true));
          const tablesList = await fetchTables(activeConnectionId);
          
          // Convert simple table names to TableWithColumns objects
          const tablesWithColumns = tablesList.map(tableName => ({
            name: tableName,
            expanded: false,
            columns: [],
            isLoading: false
          }));
          
          dispatch(setTables(tablesWithColumns));
          console.log('Tables loaded successfully:', tablesWithColumns.length, 'for database:', currentDatabase);
        } catch (error) {
          console.error('Failed to load tables:', error);
          toast.error('Failed to load tables', {
            description: 'Could not retrieve tables for this connection'
          });
        } finally {
          dispatch(setIsLoadingTables(false));
        }
      };
      
      loadTables();
    }
  }, [getCurrentConnection(), selectedDatabase, dashboardType, dispatch]); // Remove loadedDatabases dependency to always fetch

  // Helper functions
  const handleTableSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTableSearchQuery(e.target.value);
  };

  const getSearchPlaceholder = () => {
    switch (dashboardType) {
      case 1:
        return "Search topics...";
      case 2:
        return "Search tables...";
      case 3:
        return "Search projects...";
      default:
        return "Search...";
    }
  };

  const activeConnectionId = getCurrentConnection();

  // CONDITIONAL RENDERING - NOT EARLY RETURN
  if (collapsed) {
    return (
      <MinimizedSidebar 
        isCollapsed={collapsed}
        onToggle={onToggle}
      />
    );
  }

  return (
    <div className="w-64 flex flex-col h-full bg-gray-200 border group">
      <div className="p-3">
        {dashboardType === 2 ? (
          <DatabaseConnectionDropdown onToggleSidebar={onToggle} />
        ) : dashboardType === 3 ? (
          <DadaConnectionDropdown onToggleSidebar={onToggle} />
        ) : null}
        
        {/* Only show search input for non-database views or when no connection */}
        {!(dashboardType === 2 && activeConnectionId) && (
          <SearchInput
            searchQuery={searchQuery}
            handleSearch={handleSearch}
            placeholder={getSearchPlaceholder()}
          />
        )}
      </div>

      <div className="flex-1 overflow-y-auto px-3 pb-3">
        {dashboardType === 2 ? (
          activeConnectionId ? (
            <TablesList 
              tables={tables}
              connectionId={activeConnectionId}
              isLoading={isLoadingTables}
              searchQuery={tableSearchQuery}
              onSearchChange={handleTableSearch}
            />
          ) : (
            <div className="text-center p-4 text-sm text-gray-500 bg-white border rounded mt-2">
              Select a database to view tables
            </div>
          )
        ) : (
          // Render other dashboard types (projects, etc.)
          <ProjectsList
            items={filteredItems as Project[]}
            toggleItem={toggleItem}
            addChatToItem={addChatToItem}
          />
        )}
      </div>
    </div>
  );
};

export default ListDashboardView;
