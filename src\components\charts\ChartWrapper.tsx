import React from 'react';
import <PERSON><PERSON>hart from './UniversalChart';
import { ChartType, ChartSize, ChartContext } from '@/constants/chartConstants';
import { SavedChart, ChartDataPoint } from '@/types/chartTypes';
import { ChartDataItem } from '@/services/api/chart/chartTypes';
import type { QueryResultData } from '@/components/dashboard/models';

interface ChartWrapperProps {
  // Legacy data sources - for backward compatibility
  savedChart?: SavedChart;
  chartData?: ChartDataPoint[];
  queryResult?: QueryResultData | null;
  rawData?: ChartDataItem[];
  
  // Chart configuration
  chartType?: ChartType;
  size?: ChartSize;
  context?: ChartContext;
  title?: string;
  xAxis?: string;
  yAxis?: string | string[];
  
  // Visual customization
  colors?: string[];
  className?: string;
  showTitle?: boolean;
  showLegend?: boolean;
  showAxes?: boolean;
  
  // Callbacks
  onSave?: () => void;
  onClick?: (data: any) => void;
  onHover?: (data: any) => void;
}

/**
 * ChartWrapper - Provides backward compatibility for all existing chart usage
 * This component ensures that all chart rendering goes through UniversalChart
 */
const ChartWrapper: React.FC<ChartWrapperProps> = (props) => {
  return <UniversalChart {...props} />;
};

export default ChartWrapper;