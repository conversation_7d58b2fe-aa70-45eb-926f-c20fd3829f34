import { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { toast } from 'sonner';
import { DatabaseService, MetadataService, DatabaseHelpers } from '@/services/api/databaseService';

export const useMLZScanner = () => {
  const [searchParams] = useSearchParams();
  
  // Connection state
  const [selectedConnection, setSelectedConnection] = useState('');
  const [selectedConnectionName, setSelectedConnectionName] = useState('');
  const [isLoadingConnection, setIsLoadingConnection] = useState(false);
  const [availableConnections, setAvailableConnections] = useState<any[]>([]);

  // Tables state
  const [allTables, setAllTables] = useState<any[]>([]);
  const [currentTableIndex, setCurrentTableIndex] = useState(0);
  const [tableName, setTableName] = useState('Table Name');
  const [tableDescription, setTableDescription] = useState('Table Description');
  const [totalTables, setTotalTables] = useState(0);
  const [isLoadingTables, setIsLoadingTables] = useState(false);

  // Columns state
  const [allColumns, setAllColumns] = useState<any[]>([]);
  const [currentColumnIndex, setCurrentColumnIndex] = useState(0);
  const [columnName, setColumnName] = useState('Column Name');
  const [columnDescription, setColumnDescription] = useState('Table Description');
  const [columnAlias, setColumnAlias] = useState('Column Name');
  const [columnDatatype, setColumnDatatype] = useState('String');
  const [isPrimaryKey, setIsPrimaryKey] = useState(false);
  const [isUniqueKey, setIsUniqueKey] = useState(false);
  const [totalColumns, setTotalColumns] = useState(0);
  const [isLoadingColumns, setIsLoadingColumns] = useState(false);

  // Search state
  const [searchQuery, setSearchQuery] = useState('');
  const [showDropdown, setShowDropdown] = useState(false);
  const [filteredTables, setFilteredTables] = useState<any[]>([]);

  // Scanner state
  const [isScanning, setIsScanning] = useState(false);

  // Handle URL parameters for connection selection or load first connection
  useEffect(() => {
    const connectionId = searchParams.get('connectionId');
    const connectionName = searchParams.get('connectionName');

    if (connectionId && connectionName) {
      setSelectedConnection(connectionId);
      setSelectedConnectionName(decodeURIComponent(connectionName));
      fetchConnectionDetails(connectionId);
      fetchTablesForConnection(parseInt(connectionId));
      toast.success(`Loaded connection: ${decodeURIComponent(connectionName)}`);
    } else {
      fetchFirstAvailableConnection();
    }
  }, [searchParams]);

  // Fetch first available database connection
  const fetchFirstAvailableConnection = async () => {
    try {
      setIsLoadingConnection(true);
      const userId = '1';

      console.log('🔍 Fetching first available database connection...');
      const result = await DatabaseService.getUserConnections(userId);
      console.log('📊 Available connections:', result);

      if (result.status === 'success' && result.connections && result.connections.length > 0) {
        const firstConnection = result.connections[0];
        setAvailableConnections(result.connections);
        setSelectedConnection(firstConnection.connection_id.toString());
        setSelectedConnectionName(firstConnection.connection_name);

        console.log(`✅ Auto-loaded first connection: ${firstConnection.connection_name} (ID: ${firstConnection.connection_id})`);

        fetchConnectionDetails(firstConnection.connection_id.toString());
        resetTablesAndColumns();
        toast.success(`Auto-loaded connection: ${firstConnection.connection_name}`);
      } else {
        console.log('⚠️ No database connections found');
        toast.info('No database connections available');
      }
    } catch (error) {
      console.error('❌ Error fetching first connection:', error);
      toast.error('Failed to load database connections');
    } finally {
      setIsLoadingConnection(false);
    }
  };

  // Fetch connection details from API
  const fetchConnectionDetails = async (connectionId: string) => {
    try {
      setIsLoadingConnection(true);
      console.log(`Fetching connection details for ID: ${connectionId}`);
      
      const result = await DatabaseService.getConnectionDetails(connectionId);
      console.log('Connection details fetched:', result);

      if (result.status === 'success' && result.connection) {
        const conn = result.connection;
        setSelectedConnectionName(conn.connection_name || 'Unknown Connection');
        toast.success(`Connection details loaded: ${conn.connection_name}`);
      } else {
        toast.error('Failed to load connection details');
      }
    } catch (error) {
      console.error('Error fetching connection details:', error);
      toast.error('Failed to load connection details');
    } finally {
      setIsLoadingConnection(false);
    }
  };

  // Fetch tables for the selected connection
  const fetchTablesForConnection = async (connectionId: number) => {
    try {
      setIsLoadingTables(true);
      console.log(`🔍 Fetching tables for connection ID: ${connectionId}`);
      
      const data = await MetadataService.getMetadataByConnection(connectionId.toString());
      console.log(`Received table data:`, data);

      if (Array.isArray(data) && data.length > 0) {
        setAllTables(data);
        setTotalTables(data.length);

        if (data[0]) {
          setCurrentTableIndex(0);
          updateCurrentTable(0, data);
        }

        toast.success(`Found ${data.length} tables`);
      } else {
        resetTablesAndColumns();
        toast.error('No tables found for this connection');
      }
    } catch (error) {
      console.error('Error fetching tables:', error);
      toast.error('Failed to fetch tables');
      resetTablesAndColumns();
    } finally {
      setIsLoadingTables(false);
    }
  };

  // Reset tables and columns to initial state
  const resetTablesAndColumns = () => {
    setAllTables([]);
    setTotalTables(0);
    setTableName('Table Name');
    setTableDescription('Table Description');
    setAllColumns([]);
    setTotalColumns(0);
    setColumnName('Column Name');
    setColumnDescription('Table Description');
    setColumnAlias('Column Name');
    setColumnDatatype('String');
    setIsPrimaryKey(false);
    setIsUniqueKey(false);
  };

  // Update current table and patch columns
  const updateCurrentTable = (index: number, tables = allTables) => {
    if (tables[index]) {
      const currentTable = tables[index];
      setTableName(currentTable.Table_Name || 'Table Name');
      setTableDescription(currentTable.Table_Description || 'Table Description');
      setSearchQuery(currentTable.Table_Name || '');
      patchColumnsForCurrentTable(currentTable);
    }
  };

  // Patch columns for the current table
  const patchColumnsForCurrentTable = (currentTable: any) => {
    if (!currentTable || !currentTable.Columns_List) {
      setAllColumns([]);
      setTotalColumns(0);
      setCurrentColumnIndex(0);
      setColumnName('No columns found');
      setColumnDescription('No description available');
      setColumnAlias('No alias');
      setColumnDatatype('String');
      setIsPrimaryKey(false);
      setIsUniqueKey(false);
      return;
    }

    try {
      setIsLoadingColumns(true);
      const columnsData = currentTable.Columns_List;
      const columns = [];

      if (typeof columnsData === 'object' && columnsData !== null) {
        for (const key in columnsData) {
          if (columnsData.hasOwnProperty(key)) {
            const column = columnsData[key];
            columns.push({
              column_name: column,
              data_type: 'String',
              column_description: currentTable.Table_Description || 'Table Description',
              column_alias: currentTable.Table_Name || 'Table Name',
              is_nullable: false,
              default_value: null
            });
          }
        }
      }

      if (columns.length > 0) {
        setAllColumns(columns);
        setTotalColumns(columns.length);
        setCurrentColumnIndex(0);

        const firstColumn = columns[0];
        setColumnName(firstColumn.column_name);
        setColumnDescription(firstColumn.column_description);
        setColumnAlias(firstColumn.column_alias);
        setColumnDatatype(firstColumn.data_type);

        const isPrimary = checkIfPrimaryKey(firstColumn.column_name, currentTable);
        const isUnique = checkIfUniqueKey(firstColumn.column_name, currentTable);
        setIsPrimaryKey(isPrimary);
        setIsUniqueKey(isUnique);
      } else {
        setAllColumns([]);
        setTotalColumns(0);
        setColumnName('No columns found');
        setColumnDescription('No description available');
        setColumnAlias('No alias');
        setColumnDatatype('String');
        setIsPrimaryKey(false);
        setIsUniqueKey(false);
      }
    } catch (error) {
      console.error('Error auto-patching columns:', error);
      setAllColumns([]);
      setTotalColumns(0);
    } finally {
      setIsLoadingColumns(false);
    }
  };

  // Helper functions to check key types
  const checkIfPrimaryKey = (columnName: string, table: any): boolean => {
    if (table.Primary_Key_Columns) {
      if (Array.isArray(table.Primary_Key_Columns)) {
        return table.Primary_Key_Columns.includes(columnName);
      } else if (typeof table.Primary_Key_Columns === 'object') {
        return Object.values(table.Primary_Key_Columns).includes(columnName);
      }
    }
    return false;
  };

  const checkIfUniqueKey = (columnName: string, table: any): boolean => {
    if (table.Unique_Key_Columns) {
      if (Array.isArray(table.Unique_Key_Columns)) {
        return table.Unique_Key_Columns.includes(columnName);
      } else if (typeof table.Unique_Key_Columns === 'object') {
        return Object.values(table.Unique_Key_Columns).includes(columnName);
      }
    }
    return false;
  };

  return {
    // Connection state
    selectedConnection,
    selectedConnectionName,
    isLoadingConnection,
    availableConnections,

    // Tables state
    allTables,
    currentTableIndex,
    tableName,
    tableDescription,
    totalTables,
    isLoadingTables,

    // Columns state
    allColumns,
    currentColumnIndex,
    columnName,
    columnDescription,
    columnAlias,
    columnDatatype,
    isPrimaryKey,
    isUniqueKey,
    totalColumns,
    isLoadingColumns,

    // Search state
    searchQuery,
    showDropdown,
    filteredTables,

    // Scanner state
    isScanning,

    // Actions
    setSearchQuery,
    setShowDropdown,
    setFilteredTables,
    setCurrentTableIndex,
    setCurrentColumnIndex,
    setColumnName,
    setColumnDescription,
    setColumnAlias,
    setColumnDatatype,
    setIsPrimaryKey,
    setIsUniqueKey,
    setIsScanning,
    updateCurrentTable,
    patchColumnsForCurrentTable,
    fetchConnectionDetails,
    fetchTablesForConnection,
    resetTablesAndColumns
  };
};
