
import React, { useEffect, useCallback, useState } from 'react';
import { useAppSelector, useAppDispatch } from '@/hooks/useRedux';

import { useDeleteDialog } from '@/hooks/useDeleteDialog';
import DeleteConfirmationDialog from './DeleteConfirmationDialog';
import LoadingOverlay from './LoadingOverlay';
// import FilesDashboardView from './views/FilesDashboardView';
import ListDashboardView from './views/ListDashboardView';
import { ChatTopic, Meeting, Project } from './types';
// import { togglePersonalExpanded, toggleProjectExpanded, toggleTrackerExpanded } from '@/stores/fileSlice';


interface SidebarContentProps {
  collapsed: boolean;
  searchQuery: string;
  handleSearch: (e: React.ChangeEvent<HTMLInputElement>) => void;
  filteredItems: ChatTopic[] | Meeting[] | Project[];
  toggleItem: (id: string) => void;
  addChatToItem: (id: string) => void;
  addNewItem: () => void;
  dashboardType: 1 | 2 | 3;
  onToggle: () => void;
}

const SidebarContent: React.FC<SidebarContentProps> = ({
  collapsed,
  searchQuery,
  handleSearch,
  filteredItems,
  toggleItem,
  addChatToItem,
  addNewItem,
  dashboardType,
  onToggle
}) => {




  const dispatch = useAppDispatch();


 



  


  if (collapsed) {
    return null;
  }

 


  // if (dashboardType === 2) {
  //   return (
  //     <>
  //       <LoadingOverlay />
  //       <div className="flex flex-col h-full">
  //         {/* Remove development mode indicator and connecting message */}
  //         <div className="flex-1 overflow-y-auto">
  //           <FilesDashboardView
  //             searchQuery={searchQuery}
  //             handleSearch={handleSearch}
  //             personalFiles={personalFiles}
  //             projectFiles={projectFiles}
  //             isPersonalExpanded={isPersonalExpanded}
  //             isProjectExpanded={isProjectExpanded}
  //             // isLoadingFiles={isLoadingFiles}
  //             // handleFileClick={handleFileClick}
  //             handleDeleteClick={(fileId, folderType) => {
  //               // Find the file to get the filename
  //               const files = folderType === 'personal' ? personalFiles : projectFiles;
  //               const file = files.find(f => f.file_id === fileId);
  //               if (file) {
  //                 handleDeleteClick(file.filename, fileId);
  //               }
  //             }}
  //             togglePersonalExpanded={handleTogglePersonalExpanded}
  //             toggleProjectExpanded={handleToggleProjectExpanded}
  //             onToggle={onToggle}
  //           />
  //           <DeleteConfirmationDialog
  //             isOpen={isDeleteDialogOpen}
  //             onClose={() => setIsDeleteDialogOpen(false)}
  //             fileName={fileToDelete?.fileName || null}
  //             onConfirm={handleDelete}
  //             status={deleteStatus}
  //           />
  //         </div>
  //       </div>
  //     </>
  //   );
  // }

  return (
    <>
      <LoadingOverlay isVisible={false} />
      <div className="flex flex-col h-full">
        {/* Remove development mode indicator and connecting message */}
        <div className="flex-1 overflow-y-auto">
          <ListDashboardView
            searchQuery={searchQuery}
            handleSearch={(e) => {
              // Only update sidebar search, don't affect main content
              handleSearch(e);
            }}
            filteredItems={filteredItems}
            toggleItem={toggleItem}
            addChatToItem={addChatToItem}
            addNewItem={addNewItem}
            dashboardType={dashboardType}
            onToggle={onToggle}
          />
        </div>
      </div>
    </>
  );
};

export default React.memo(SidebarContent);

