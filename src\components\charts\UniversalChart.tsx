import React, { useMemo } from 'react';
import { Bar, Line, Pie, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

import { ChartType, ChartSize, ChartContext, CHART_COLORS, generateColorPalette } from '@/constants/chartConstants';
import { UniversalDataAdapter, ProcessedChartInfo } from '@/utils/chart/dataAdapter';
import { ChartConfigurationManager } from '@/utils/chart/chartConfig';
import { SavedChart, ChartDataPoint } from '@/types/chartTypes';
import { ChartDataItem } from '@/services/api/chart/chartTypes';
import type { QueryResultData } from '@/components/dashboard/models';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

export interface UniversalChartProps {
  // Data sources (provide one of these)
  savedChart?: SavedChart;
  chartData?: ChartDataPoint[];
  queryResult?: QueryResultData | null;
  rawData?: ChartDataItem[];
  
  // Chart configuration
  chartType?: ChartType;
  size?: ChartSize;
  context?: ChartContext;
  title?: string;
  xAxis?: string;
  yAxis?: string | string[];
  
  // Visual customization
  colors?: string[];
  className?: string;
  showTitle?: boolean;
  showLegend?: boolean;
  showAxes?: boolean;
  
  // Callbacks
  onSave?: () => void;
  onClick?: (data: any) => void;
  onHover?: (data: any) => void;
}

/**
 * Universal Chart Component - Single source of truth for all chart rendering
 */
const UniversalChart: React.FC<UniversalChartProps> = ({
  // Data sources
  savedChart,
  chartData,
  queryResult,
  rawData,
  
  // Configuration
  chartType,
  size = 'default',
  context = 'dashboard',
  title,
  xAxis,
  yAxis,
  
  // Visual customization
  colors,
  className = '',
  showTitle,
  showLegend,
  showAxes,
  
  // Callbacks
  onSave,
  onClick,
  onHover
}) => {
  
  // Process data into universal format
  const processedData = useMemo((): ProcessedChartInfo => {
    try {
      // Priority: savedChart -> queryResult -> rawData
      if (savedChart && chartData) {
        return UniversalDataAdapter.fromSavedChart(savedChart, chartData);
      }
      
      if (queryResult) {
        return UniversalDataAdapter.fromQueryResult(queryResult);
      }
      
      if (rawData && xAxis && yAxis) {
        const yAxisArray = Array.isArray(yAxis) ? yAxis : [yAxis];
        return UniversalDataAdapter.fromChartDataItems(
          rawData, 
          xAxis, 
          yAxisArray, 
          chartType || 'bar'
        );
      }
      
      // Return empty data if no valid source
      return {
        data: { labels: [], datasets: [] },
        chartType: chartType || 'bar',
        xAxisLabel: '',
        yAxisLabel: '',
        title: 'No Data Available',
        canVisualize: false
      };
    } catch (error) {
      console.error('[UniversalChart] Error processing chart data:', error);
      return {
        data: { labels: [], datasets: [] },
        chartType: chartType || 'bar',
        xAxisLabel: '',
        yAxisLabel: '',
        title: 'Error Loading Data',
        canVisualize: false
      };
    }
  }, [savedChart, chartData, queryResult, rawData, xAxis, yAxis, chartType]);

  // Get chart configuration - PRIORITIZE DADA AI CHART TYPE
  const config = useMemo(() => {
    // Use processedData.chartType (from DADA AI) over passed chartType prop
    const finalChartType = processedData.chartType || chartType || 'bar';
    return ChartConfigurationManager.getConfiguration(
      context,
      size,
      finalChartType as ChartType
    );
  }, [context, size, chartType, processedData.chartType]);

  // Apply colors to datasets
  const finalData = useMemo(() => {
    console.log('[UniversalChart] Processing final data:', {
      hasDatasets: processedData.data.datasets.length > 0,
      datasets: processedData.data.datasets,
      labels: processedData.data.labels,
      processedData: processedData
    });

    if (!processedData.data.datasets.length) {
      console.log('[UniversalChart] No datasets found, returning empty data');
      return processedData.data;
    }

    const data = { ...processedData.data };
    const finalChartType = processedData.chartType || chartType || 'bar';
    const isRadialChart = finalChartType === 'pie' || finalChartType === 'doughnut';

    console.log('[UniversalChart] Chart type:', finalChartType, 'isRadialChart:', isRadialChart);

    data.datasets = data.datasets.map((dataset, datasetIndex) => {
      if (isRadialChart) {
        // For pie/doughnut: each segment needs its own color
        const segmentColors = data.labels.map((_, i) => CHART_COLORS[i % CHART_COLORS.length].bg);
        const segmentBorders = data.labels.map((_, i) => CHART_COLORS[i % CHART_COLORS.length].border);

        return {
          ...dataset,
          backgroundColor: segmentColors,
          borderColor: segmentBorders,
          borderWidth: 2
        };
      } else {
        // For bar/line charts: check if we should use different colors per data point
        const shouldUseMultipleColors = data.datasets.length === 1 && data.labels.length > 1;

        if (shouldUseMultipleColors) {
          // Single dataset with multiple data points - use different color for each bar
          const dataPointColors = data.labels.map((_, i) => CHART_COLORS[i % CHART_COLORS.length].bg);
          const dataPointBorders = data.labels.map((_, i) => CHART_COLORS[i % CHART_COLORS.length].border);

          return {
            ...dataset,
            backgroundColor: dataPointColors,
            borderColor: dataPointBorders,
            borderWidth: 1
          };
        } else {
          // Multiple datasets or single data point - use single color per dataset
          return {
            ...dataset,
            backgroundColor: CHART_COLORS[datasetIndex % CHART_COLORS.length].bg,
            borderColor: CHART_COLORS[datasetIndex % CHART_COLORS.length].border,
            borderWidth: 1
          };
        }
      }
    });

    console.log('[UniversalChart] Final data with colors:', {
      labels: data.labels,
      datasets: data.datasets,
      datasetData: data.datasets.map(d => d.data)
    });

    return data;
  }, [processedData.data, chartType, processedData.chartType, colors]);

  // Apply final options
  const finalOptions = useMemo(() => {
    const overrides = {
      showTitle: showTitle ?? config.showTitle,
      showLegend: showLegend ?? config.showLegend,
      showAxes: showAxes ?? config.showAxes
    };

    return ChartConfigurationManager.applyConfiguration(
      config.options,
      { ...config, ...overrides },
      title || processedData.title,
      processedData.xAxisLabel,
      processedData.yAxisLabel
    );
  }, [config, title, processedData, showTitle, showLegend, showAxes]);

  // Event handlers
  const handleClick = (event: any, elements: any[]) => {
    if (onClick && elements.length > 0) {
      const element = elements[0];
      const dataIndex = element.index;
      const datasetIndex = element.datasetIndex;
      
      onClick({
        label: finalData.labels[dataIndex],
        value: finalData.datasets[datasetIndex].data[dataIndex],
        dataIndex,
        datasetIndex
      });
    }
  };

  // No data display - handle DADA AI visualization decisions
  if (processedData.canVisualize === false || !finalData.datasets.length || finalData.labels.length === 0) {
    const message = processedData.reasoning || 'No data available';
    const subtitle = processedData.reasoning ? 'Data contains no suitable values for visualization' : 'Please check your data source';
    
    console.log('[UniversalChart] Showing no data state:', { 
      canVisualize: processedData.canVisualize, 
      reasoning: processedData.reasoning,
      datasetLength: finalData.datasets.length,
      labelsLength: finalData.labels.length 
    });
    
    return (
      <div className={`${config.height} bg-gray-50 rounded border border-gray-200 flex items-center justify-center ${className}`}>
        <div className="text-center max-w-md px-4">
          <div className="mb-2">
            <svg className="w-12 h-12 text-gray-400 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <p className="text-gray-600 text-sm font-medium">{processedData.title || 'Cannot Display Chart'}</p>
          <p className="text-gray-500 text-xs mt-2 leading-relaxed">{message}</p>
          {processedData.reasoning && (
            <p className="text-gray-400 text-xs mt-1">{subtitle}</p>
          )}
        </div>
      </div>
    );
  }

  // Render appropriate chart component
  const renderChart = () => {
    const commonProps = {
      data: finalData,
      options: {
        ...finalOptions,
        onClick: handleClick,
        onHover: onHover
      }
    };

    // PRIORITIZE DADA AI CHART TYPE over passed prop
    const finalChartType = processedData.chartType || chartType || 'bar';

    switch (finalChartType) {
      case 'pie':
        return <Pie {...commonProps} />;
      
      case 'doughnut':
        return <Doughnut {...commonProps} />;
      
      case 'line':
        return <Line {...commonProps} />;
      
      case 'bar':
      case 'verticalBar':
      default:
        return <Bar {...commonProps} />;
        
      case 'horizontalBar':
        return <Bar {...commonProps} options={{
          ...commonProps.options,
          indexAxis: 'y' as const
        }} />;
    }
  };

  return (
    <div className={`${config.height} bg-white rounded border border-gray-200 relative ${className}`}>
      {/* Remove save button for DADA AI results context */}
      {context !== 'thumbnail' && context !== 'results' && onSave && (
        <button
          onClick={onSave}
          className="absolute top-2 right-2 z-10 p-1 bg-white rounded shadow hover:bg-gray-50"
          title="Save Chart"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
          </svg>
        </button>
      )}
      <div className="w-full h-full p-2">
        {renderChart()}
      </div>
    </div>
  );
};

export default UniversalChart;