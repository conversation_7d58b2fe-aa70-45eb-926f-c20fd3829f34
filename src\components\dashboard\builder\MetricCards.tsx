import React, { memo, useMemo } from 'react';
import { Card, CardContent } from '@/components/ui/card';

interface MetricCardsProps {
  className?: string;
}

const MetricCards: React.FC<MetricCardsProps> = memo(({ className = '' }) => {
  // Memoize static data to prevent recreation on every render
  const metricCards = useMemo(() => [
    { id: 1, title: 'Metric value' },
    { id: 2, title: 'values' },
    { id: 3, title: 'values' }
  ], []);

  return (
    <div className={`grid grid-cols-1 md:grid-cols-3 gap-4 mb-6 ${className}`}>
      {metricCards.map((metric) => (
        <Card key={metric.id} className="border-2 border-dashed border-gray-300 bg-gray-50 hover:border-gray-400 transition-colors">
          <CardContent className="flex items-center justify-center h-24">
            <span className="text-gray-500 text-sm font-medium">
              {metric.title}
            </span>
          </CardContent>
        </Card>
      ))}
    </div>
  );
});

MetricCards.displayName = 'MetricCards';

export default MetricCards;
