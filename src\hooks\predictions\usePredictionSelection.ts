
import { useState } from 'react';
import { ChainStage } from '@/services/api/predictions/types';
import { 
  getPredictions,
  parsePredictionQuery
} from "@/services/api/predictions";

interface UsePredictionSelectionProps {
  isChainSequence: boolean;
  chainStage: ChainStage;
  setChainStage: (stage: ChainStage) => void;
  setPredictions: (predictions: string[]) => void;
  setShowPredictions: (show: boolean) => void;
}

export const usePredictionSelection = ({
  isChainSequence,
  chainStage,
  setChainStage,
  setPredictions,
  setShowPredictions
}: UsePredictionSelectionProps) => {
  const [lastSelectedPrediction, setLastSelectedPrediction] = useState<string>('');

  const handlePredictionSelect = (selectedPrediction: string, setInputValue: (value: string) => void) => {
    console.log("Selected prediction:", selectedPrediction);
    setLastSelectedPrediction(selectedPrediction);
    setInputValue(selectedPrediction);
    
    // Always fetch next stage of predictions immediately after selection
    if (isChainSequence && selectedPrediction.startsWith('@')) {
      // Parse the selected prediction to get components
      const { powerKeyword, command, parameter } = parsePredictionQuery(selectedPrediction);
      
      console.log("Parsed selected prediction:", { powerKeyword, command, parameter });
      
      // Determine the next chainStage
      let nextStage: ChainStage = 'powerKeyword';
      if (powerKeyword && !command) {
        nextStage = 'command';
        console.log("Moving to command stage");
      } else if (powerKeyword && command && !parameter) {
        nextStage = 'parameter';
        console.log("Moving to parameter stage");
      } else if (powerKeyword && command && parameter) {
        nextStage = 'final';
        console.log("Moving to final stage");
      }
      
      setChainStage(nextStage);
      
      // Don't fetch more predictions for final stage
      if (nextStage !== 'final') {
        const params = { 
          query: selectedPrediction,
          powerKeyword,
          command,
          parameter
        };

        console.log("Fetching next chain predictions with:", params);
        
        // Force the next prediction request immediately
        getPredictions(params).then(result => {
          console.log("Next chain predictions:", result);
          // Format predictions to remove @ prefix for display
          const formattedResults = result?.map(item => item) || [];
          setPredictions(formattedResults);
          setShowPredictions(formattedResults && formattedResults.length > 0);
        }).catch(error => {
          console.error("Error fetching chain predictions:", error);
          setPredictions([]);
          setShowPredictions(false);
        });
      } else {
        // For final stage, hide predictions
        setPredictions([]);
        setShowPredictions(false);
      }
    } else {
      // For regular predictions, just hide the dropdown
      setPredictions([]);
      setShowPredictions(false);
    }
  };

  return {
    lastSelectedPrediction,
    handlePredictionSelect
  };
};
