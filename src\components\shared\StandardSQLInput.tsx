import React from 'react';
import { SQLQueryInput } from '@/components/ui/sql-query-input';
import { useSQLInput, SQLValidationResult } from '@/hooks/shared/useSQLInput';

export interface StandardSQLInputProps {
  value: string;
  onChange: (value: string) => void;
  onValidate?: (sql: string) => Promise<SQLValidationResult>;
  onBlur?: () => void;
  placeholder?: string;
  label?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  rows?: number;
  showValidation?: boolean;
  error?: string;
  fieldKey?: string;
  viewType?: string;
}

const StandardSQLInput: React.FC<StandardSQLInputProps> = ({
  value,
  onChange,
  onValidate,
  onBlur,
  placeholder = "Enter your SQL query here...",
  label = "SQL Query",
  required = false,
  disabled = false,
  className = "",
  rows = 4,
  showValidation = true,
  error,
  fieldKey,
  viewType
}) => {
  const sqlInput = useSQLInput({
    initialValue: value,
    onValidate,
    onChange,
    onBlur,
    fieldKey,
    viewType
  });

  // Sync external value changes
  React.useEffect(() => {
    if (value !== sqlInput.value) {
      sqlInput.setValue(value);
    }
  }, [value, sqlInput]);

  return (
    <SQLQueryInput
      value={sqlInput.value}
      onChange={sqlInput.handleChange}
      onValidate={sqlInput.handleValidate}
      onBlur={sqlInput.handleBlur}
      validationResult={sqlInput.validationResult}
      isValidating={sqlInput.isValidating}
      placeholder={placeholder}
      label={label}
      required={required}
      disabled={disabled}
      className={className}
      rows={rows}
      showValidation={showValidation}
      error={error}
    />
  );
};

export default StandardSQLInput;