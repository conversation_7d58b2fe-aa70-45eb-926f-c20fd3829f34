
import React, { useState } from 'react';
import { Search, PanelLeftClose, Plus, Loader2 } from 'lucide-react';
import MinimizedSidebar from '../dashboard/MinimizedSidebar';

interface SharedSidebarProps {
  collapsed: boolean;
  onToggle: () => void;
  searchQuery: string;
  onSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  searchPlaceholder?: string;
  loading?: boolean;
  children: React.ReactNode;
  onCreateNew?: () => void;
  createNewLabel?: string;
}

const SharedSidebar: React.FC<SharedSidebarProps> = ({
  collapsed,
  onToggle,
  searchQuery,
  onSearchChange,
  searchPlaceholder = "Search...",
  loading = false,
  children,
  onCreateNew,
  createNewLabel = "Create New"
}) => {
  if (collapsed) {
    return (
      <MinimizedSidebar 
        isCollapsed={collapsed}
        onToggle={onToggle}
      />
    );
  }
  
  return (
    <div className="w-64 bg-gray-100 border-r border-gray-200 flex flex-col h-full">
      <div className="p-3 border-b border-gray-200">
        <div className="flex items-center space-x-2 group">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder={searchPlaceholder}
              className="w-full pl-8 pr-2 py-2 border rounded text-sm"
              value={searchQuery}
              onChange={onSearchChange}
            />
          </div>
          <button 
            onClick={onToggle}
            className="p-2 text-gray-500 hover:text-gray-700 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
          >
            <PanelLeftClose size={18} />
          </button>
        </div>
      </div>
      
      <div className="flex-1 overflow-y-auto py-2 [scrollbar-width:none] [-ms-overflow-style:none] [&::-webkit-scrollbar]:hidden">
        {loading ? (
          <div className="flex justify-center items-center h-full">
            <Loader2 className="h-6 w-6 text-blue-500 animate-spin" />
            <span className="ml-2 text-gray-600">Loading...</span>
          </div>
        ) : (
          <>
            {children}
            {/* Removed the Create New button section */}
          </>
        )}
      </div>
    </div>
  );
};

export default SharedSidebar;
