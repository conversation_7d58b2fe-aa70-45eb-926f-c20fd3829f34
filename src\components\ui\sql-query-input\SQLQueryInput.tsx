import React, { useState, useCallback, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { CheckCircle, XCircle, AlertCircle, Loader2 } from 'lucide-react';

export interface SQLValidationResult {
  isValid: boolean;
  message?: string;
}

export interface SQLQueryInputProps {
  value: string;
  onChange: (value: string) => void;
  onValidate?: (sql: string) => Promise<SQLValidationResult>;
  onBlur?: () => void;
  placeholder?: string;
  label?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  rows?: number;
  showValidation?: boolean;
  validationResult?: SQLValidationResult | null;
  isValidating?: boolean;
  error?: string;
}

const SQLQueryInput: React.FC<SQLQueryInputProps> = ({
  value,
  onChange,
  onValidate,
  onBlur,
  placeholder = "Enter your SQL query here...",
  label = "SQL Query",
  required = false,
  disabled = false,
  className = "",
  rows = 4,
  showValidation = true,
  validationResult: externalValidationResult,
  isValidating: externalIsValidating = false,
  error
}) => {
  const [internalValidationResult, setInternalValidationResult] = useState<SQLValidationResult | null>(null);
  const [internalIsValidating, setInternalIsValidating] = useState(false);
  const [showHelperText, setShowHelperText] = useState(false);
  const [isTouched, setIsTouched] = useState(false);

  // Use external validation state if provided, otherwise use internal state
  const validationResult = externalValidationResult ?? internalValidationResult;
  const isValidating = externalIsValidating || internalIsValidating;

  // Watch for external value changes (like when Clear button is clicked)
  useEffect(() => {
    if (value.trim().length === 0) {
      setShowHelperText(false);
      setInternalValidationResult(null);
    }
  }, [value]);

  const handleInputChange = (newValue: string) => {
    onChange(newValue);

    // Show helper text when user starts typing and no validation result exists
    setShowHelperText(newValue.trim().length > 0 && !validationResult?.isValid);

    // Clear validation result when user modifies the query
    if (internalValidationResult) {
      setInternalValidationResult(null);
    }

    // Clear validation result when input becomes empty
    if (newValue.trim().length === 0) {
      setInternalValidationResult(null);
      setShowHelperText(false);
    }
  };

  const handleBlur = () => {
    setIsTouched(true);
    onBlur?.();
  };

  const handleValidate = useCallback(async () => {
    if (!onValidate || !value.trim()) return;

    setInternalIsValidating(true);
    setShowHelperText(false);

    try {
      const result = await onValidate(value.trim());
      setInternalValidationResult(result);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Validation error occurred';
      setInternalValidationResult({
        isValid: false,
        message: errorMessage
      });
    } finally {
      setInternalIsValidating(false);
    }
  }, [onValidate, value]);

  const getValidationIcon = () => {
    if (isValidating) {
      return <Loader2 className="w-4 h-4 animate-spin" />;
    }
    
    if (!validationResult) {
      return <AlertCircle className="w-4 h-4" />;
    }
    
    return validationResult.isValid 
      ? <CheckCircle className="w-4 h-4 text-green-600" />
      : <XCircle className="w-4 h-4 text-red-600" />;
  };



  const hasError = validationResult && !validationResult.isValid;
  const hasSuccess = validationResult && validationResult.isValid && value.trim().length > 0;
  const showRequiredError = required && isTouched && !value.trim() && !error;

  return (
    <div className={`flex flex-col space-y-2 ${className}`}>
      {/* Label and Validation Button Row */}
      <div className="flex justify-between items-center">
        <div className="flex items-center">
          <label className="text-sm font-medium text-gray-700">
            {required && <span className="text-red-500 mr-1">*</span>}
            {label}:
          </label>

          {/* Helper Text - Display after colon on same line */}
          {showHelperText && (
            <span className="text-blue-600 text-xs flex items-center ml-2">
              <AlertCircle className="w-3 h-3 mr-1" />
              Please validate your SQL query
            </span>
          )}

          {/* Error Message - Display after colon on same line */}
          {error && (
            <span className="text-red-500 text-xs flex items-center ml-2">
              <XCircle className="w-3 h-3 mr-1" />
              {error}
            </span>
          )}

          {/* Required Message - Display after colon on same line */}
          {showRequiredError && (
            <span className="text-red-500 text-xs flex items-center ml-2">
              <XCircle className="w-3 h-3 mr-1" />
              {label} is required
            </span>
          )}

          {/* Validation Message - Display after colon on same line */}
          {validationResult?.message && !error && !showRequiredError && (
            <span className={`text-xs flex items-center ml-2 ${
              validationResult.isValid ? 'text-green-600' : 'text-red-600'
            }`}>
              {validationResult.isValid
                ? <CheckCircle className="w-3 h-3 mr-1" />
                : <XCircle className="w-3 h-3 mr-1" />
              }
              {validationResult.message}
            </span>
          )}
        </div>

        {/* Validate SQL button */}
        {showValidation && onValidate && (
          <Button
            onClick={handleValidate}
            disabled={isValidating || !value.trim() || disabled}
            variant="outline"
            size="sm"
            className={`flex items-center space-x-2 py-1 h-7 text-xs ${
              isValidating || !value.trim() || disabled
                ? 'border-gray-300 text-gray-400 cursor-not-allowed'
                : hasError
                  ? 'border-red-600 text-red-600 hover:bg-red-50'
                  : hasSuccess
                    ? 'border-green-600 text-green-600 hover:bg-green-50'
                    : 'border-blue-600 text-blue-600 hover:bg-blue-50'
            }`}
          >
            {getValidationIcon()}
            <span>Validate SQL</span>
          </Button>
        )}
      </div>

      {/* SQL Textarea */}
      <textarea
        value={value}
        onChange={(e) => handleInputChange(e.target.value)}
        onBlur={handleBlur}
        placeholder={placeholder}
        disabled={disabled}
        rows={rows}
        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 resize-none transition-colors font-mono text-sm ${
          hasError || error || showRequiredError
            ? 'border-red-500 focus:ring-red-200'
            : hasSuccess
              ? 'border-green-500 focus:ring-green-200'
              : required && !value.trim()
                ? 'border-orange-200 focus:ring-blue-500'
                : 'border-gray-300 focus:ring-blue-500'
        } ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
      />




    </div>
  );
};

export default SQLQueryInput;
