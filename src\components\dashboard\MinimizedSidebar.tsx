import React from 'react';
import { PanelLeftOpen } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface MinimizedSidebarProps {
  isCollapsed: boolean;
  onToggle: () => void;
}

const MinimizedSidebar: React.FC<MinimizedSidebarProps> = ({
  isCollapsed,
  onToggle
}) => {
  return (
    <div className="w-16 bg-gray-200 border-r border-gray-200 flex flex-col items-center">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <button 
              onClick={onToggle}
              className="w-10 h-10 mt-4 rounded-md flex items-center justify-center hover:bg-gray-100"
              aria-label="Open sidebar"
            >
              <PanelLeftOpen size={20} className="h-5 w-5 text-gray-600" />
            </button>
          </TooltipTrigger>
          <TooltipContent side="right">
            <p>Open sidebar</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
};

export default MinimizedSidebar;









