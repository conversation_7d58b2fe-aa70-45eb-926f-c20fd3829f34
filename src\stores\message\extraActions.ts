
import { toast } from 'sonner';
import { createAction } from '@reduxjs/toolkit';

// Create an action that can be dispatched
export const voiceInputActivated = createAction('message/voiceInputActivated');

export const activateVoiceInput = () => (dispatch: any) => {
  console.log('Voice input activated');
  toast.info('Voice Input', {
    description: 'Voice input activated. This would use the browser\'s speech recognition API in a real implementation.',
    style: { backgroundColor: '#FEF7CD', color: '#505050', border: '1px solid #f0c000' }
  });
  
  // Return the action so it can be properly dispatched
  return dispatch(voiceInputActivated());
};
