import React from 'react';
import { X, Trash2, Plus } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";

// Filter interface
export interface FilterCondition {
  column: string;
  operator: string;
  value: string;
  columnType?: string; // Add column type property
}

interface FilterSectionProps {
  filterConditions: FilterCondition[];
  setFilterConditions: (conditions: FilterCondition[]) => void;
  handleDragOver: (e: React.DragEvent) => void;
}

export const FilterSection: React.FC<FilterSectionProps> = ({
  filterConditions,
  setFilterConditions,
  handleDragOver
}) => {
  // Add a function to log the current filter conditions whenever they change
  React.useEffect(() => {
    console.log("Current filter conditions:", filterConditions);
  }, [filterConditions]);

  // Handlers for filters
  const handleFilterColumnDrop = (e: React.DragEvent, index: number) => {
    e.preventDefault();
    e.currentTarget.classList.remove('bg-gray-100');
    
    // Get column data from the drag event
    let columnName = '';
    let columnType = '';
    let tableName = '';
    
    // First try to get individual properties
    columnName = e.dataTransfer.getData('column-name');
    columnType = e.dataTransfer.getData('column-type');
    tableName = e.dataTransfer.getData('table-name');
    
    // If that fails, try to get JSON data
    if (!columnName) {
      try {
        const jsonData = e.dataTransfer.getData('application/json');
        if (jsonData) {
          const columnData = JSON.parse(jsonData);
          columnName = columnData.name;
          columnType = columnData.type;
        }
      } catch (error) {
        console.error('Error parsing JSON data:', error);
      }
    }
    
    // If all else fails, use plain text
    if (!columnName) {
      columnName = e.dataTransfer.getData('text/plain');
    }
    
    if (columnName) {
      const newFilterConditions = [...filterConditions];
      newFilterConditions[index].column = columnName;
      newFilterConditions[index].columnType = columnType;
      newFilterConditions[index].operator = newFilterConditions[index].operator || '='; // Default operator
      setFilterConditions(newFilterConditions);
      console.log('Updated filter condition:', newFilterConditions[index]);
    }
  };

  // Helper function to get type indicator
  const getTypeIndicator = (columnType?: string) => {
    if (!columnType) return null;
    
    const type = columnType.toLowerCase();
    
    // Check if the column type is numeric
    const isNumeric = type.includes('int') || 
                      type.includes('float') || 
                      type.includes('double') || 
                      type.includes('decimal') || 
                      type.includes('numeric') ||
                      type === 'number';
                      
    // Check if it's a date type
    const isDate = type.includes('date') || 
                   type.includes('time') || 
                   type.includes('timestamp');
    
    if (isNumeric) {
      return <span className="text-green-600 font-mono mr-1">123</span>;
    } else if (isDate) {
      return <span className="text-blue-600 font-mono mr-1">📅</span>;
    } else {
      return <span className="text-orange-500 font-mono mr-1">ABC</span>;
    }
  };

  const handleFilterOperatorChange = (operator: '=' | '>' | '<' | '>=' | '<=' | '!=' | 'LIKE' | 'in' | 'not_in', index: number) => {
    const newFilterConditions = [...filterConditions];
    newFilterConditions[index].operator = operator;
    setFilterConditions(newFilterConditions);
    console.log('Updated filter operator:', operator, 'for column:', newFilterConditions[index].column);
  };

  const handleFilterValueChange = (value: string, index: number) => {
    const newFilterConditions = [...filterConditions];
    newFilterConditions[index].value = value;
    setFilterConditions(newFilterConditions);
  };

  const handleAddFilterCondition = () => {
    setFilterConditions([...filterConditions, { column: '', operator: '=', value: '' }]);
  };

  const handleRemoveFilterCondition = (index: number) => {
    const newFilterConditions = [...filterConditions];
    newFilterConditions.splice(index, 1);
    setFilterConditions(newFilterConditions);
  };

  const handleClearAllFilters = () => {
    setFilterConditions([{ column: '', operator: '=', value: '' }]);
  };

  return (
    <div className="border border-gray-200 rounded-md p-3 mb-4">
      <div className="flex justify-between items-center mb-2">
        <h3 className="text-sm font-medium text-gray-700">Filter Conditions</h3>
        <div className="flex space-x-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleClearAllFilters}
            className="text-xs h-7"
          >
            <Trash2 size={12} className="mr-1 text-red-500" />
            Clear All
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleAddFilterCondition}
            className="text-xs h-7"
          >
            <Plus size={12} className="mr-1 text-green-500" />
            Add Filter
          </Button>
        </div>
      </div>
      
      {filterConditions.map((condition, index) => (
        <div key={index} className="grid grid-cols-12 gap-2 mb-2 items-center">
          {/* Column Selector - 4 cols */}
          <div 
            className="col-span-4 h-9 border rounded px-2 py-1 flex items-center"
            onDrop={(e) => handleFilterColumnDrop(e, index)}
            onDragOver={(e) => {
              e.preventDefault();
              e.currentTarget.classList.add('bg-gray-100');
            }}
            onDragLeave={(e) => {
              e.currentTarget.classList.remove('bg-gray-100');
            }}
            onDragEnter={(e) => e.preventDefault()}
          >
            {condition.column ? (
              <div className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm flex items-center">
                {condition.column}
                <button 
                  className="ml-1 text-blue-600 hover:text-blue-800"
                  onClick={() => {
                    const newConditions = [...filterConditions];
                    newConditions[index].column = '';
                    newConditions[index].columnType = undefined;
                    setFilterConditions(newConditions);
                  }}
                >
                  <X size={12} />
                </button>
              </div>
            ) : (
              <span className="text-gray-400 text-xs">Drag column here</span>
            )}
          </div>
          
          {/* Operator Symbol - 2 cols */}
          <div className="col-span-2">
            <Select
              value={condition.operator}
              onValueChange={(value) => handleFilterOperatorChange(value as '=' | '>' | '<' | '>=' | '<=' | '!=' | 'LIKE' | 'in' | 'not_in', index)}
            >
              <SelectTrigger className="h-9 text-xs">
                <SelectValue placeholder="=" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="=">=</SelectItem>
                <SelectItem value="gt">Greater Than (&gt;)</SelectItem>
                <SelectItem value="lt">Less Than (&lt;)</SelectItem>
                <SelectItem value="gte">Greater Than or Equal (&gt;=)</SelectItem>
                <SelectItem value="lte">Less Than or Equal (&lt;=)</SelectItem>
                <SelectItem value="ne">Not Equal (!=)</SelectItem>
                <SelectItem value="like">Like</SelectItem>
                <SelectItem value="in">In</SelectItem>
                <SelectItem value="not_in">Not In</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {/* Value Input - 5 cols */}
          {condition.operator === 'in' || condition.operator === 'not_in' ? (
            <Input
              type="text"
              placeholder="Comma-separated values"
              value={condition.value}
              onChange={(e) => handleFilterValueChange(e.target.value, index)}
              className="h-9 text-xs col-span-5"
            />
          ) : (
            <Input
              type="text"
              placeholder="Enter value"
              value={condition.value}
              onChange={(e) => handleFilterValueChange(e.target.value, index)}
              className="h-9 text-xs col-span-5"
            />
          )}
          
          {/* Remove Button - 1 col */}
          <div className="col-span-1 flex justify-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleRemoveFilterCondition(index)}
              disabled={filterConditions.length === 1}
              className="h-7 w-7 p-0"
            >
              <X size={14} />
            </Button>
          </div>
        </div>
      ))}
    </div>
  );
};

export default FilterSection;














