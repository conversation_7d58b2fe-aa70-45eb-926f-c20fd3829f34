import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { connectToDatabase } from '@/services/api/chart/databaseService';
import { DatabaseConnectionParams } from '@/services/api/chart/chartTypes';
import { toast } from 'sonner';

interface DatabaseConnectionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (connectionId: string) => void;
  selectedDatabase: string;
}

const DatabaseConnectionDialog: React.FC<DatabaseConnectionDialogProps> = ({
  isOpen,
  onClose,
  onSuccess,
  selectedDatabase
}) => {
  const [formData, setFormData] = useState<DatabaseConnectionParams>({
    db_type: selectedDatabase || '',
    host: '',
    port: '',
    database: '',
    username: '',
    password: '',
    table_name: '' // Add the required table_name field
  });
  
  const [errors, setErrors] = useState({
    db_type: '',
    host: '',
    port: '',
    database: '',
    username: '',
    password: ''
  });
  
  const [touched, setTouched] = useState({
    db_type: false,
    host: false,
    port: false,
    database: false,
    username: false,
    password: false
  });
  
  const [isFormValid, setIsFormValid] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [attemptedSubmit, setAttemptedSubmit] = useState(false);

  // Update form when selectedDatabase changes
  useEffect(() => {
    setFormData(prev => ({
      ...prev,
      db_type: selectedDatabase || ''
    }));
    setTouched(prev => ({
      ...prev,
      db_type: selectedDatabase ? true : false
    }));
  }, [selectedDatabase]);

  // Validate form on data change
  useEffect(() => {
    const valid = validateForm();
    setIsFormValid(valid);
  }, [formData, touched]);

  const validateField = (name: string, value: string): string => {
    switch (name) {
      case 'db_type':
        return !value.trim() ? 'Database type is required' : '';
      case 'host':
        return !value.trim() ? 'Host is required' : '';
      case 'port':
        if (!value.trim()) return 'Port is required';
        const portNum = parseInt(value, 10);
        return isNaN(portNum) || portNum <= 0 || portNum > 65535 
          ? 'Port must be a valid number between 1 and 65535' 
          : '';
      case 'database':
        return !value.trim() ? 'Database name is required' : '';
      case 'username':
        return !value.trim() ? 'Username is required' : '';
      case 'password':
        return !value.trim() ? 'Password is required' : '';
      default:
        return '';
    }
  };

  const validateForm = () => {
    const newErrors = {
      db_type: validateField('db_type', formData.db_type),
      host: validateField('host', formData.host),
      port: validateField('port', formData.port),
      database: validateField('database', formData.database),
      username: validateField('username', formData.username),
      password: validateField('password', formData.password)
    };
    
    setErrors(newErrors);
    
    // Form is valid if there are no error messages
    return !Object.values(newErrors).some(error => error !== '');
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Mark field as touched
    setTouched(prev => ({ ...prev, [name]: true }));
    
    // If field is touched or user attempted submit, validate it
    if (touched[name as keyof typeof touched] || attemptedSubmit) {
      setErrors(prev => ({ ...prev, [name]: validateField(name, value) }));
    }
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const { name } = e.target;
    setTouched(prev => ({ ...prev, [name]: true }));
    setErrors(prev => ({ ...prev, [name]: validateField(name, formData[name as keyof typeof formData] as string) }));
  };

  const handleDbTypeChange = (value: string) => {
    setFormData(prev => ({ ...prev, db_type: value }));
    setTouched(prev => ({ ...prev, db_type: true }));
    setErrors(prev => ({ ...prev, db_type: validateField('db_type', value) }));
  };

  const handleSubmit = async () => {
    setAttemptedSubmit(true);
    
    // Mark all fields as touched
    const allTouched = Object.keys(touched).reduce((acc, key) => {
      acc[key as keyof typeof touched] = true;
      return acc;
    }, {} as typeof touched);
    
    setTouched(allTouched);
    
    // Validate the form
    const isValid = validateForm();
    if (!isValid) {
      toast.error('Please fill in all required fields correctly');
      return;
    }
    
    try {
      setIsLoading(true);
      const response = await connectToDatabase(formData);
      
      if (response.status === 'connected') {
        toast.success('Database connected successfully');
        onSuccess(response.connection_id);
        
        // Reset form data
        setFormData({
          db_type: '',
          host: '',
          port: '',
          database: '',
          username: '',
          password: '',
          table_name: ''
        });
        
        // Reset touched state
        setTouched({
          db_type: false,
          host: false,
          port: false,
          database: false,
          username: false,
          password: false
        });
        
        // Reset errors
        setErrors({
          db_type: '',
          host: '',
          port: '',
          database: '',
          username: '',
          password: ''
        });
        
        // Reset attempted submit flag
        setAttemptedSubmit(false);
        
        onClose();
      } else {
        toast.error('Failed to connect to database');
      }
    } catch (error) {
      console.error('Error connecting to database:', error);
      toast.error('Connection error', {
        description: error instanceof Error ? error.message : 'Failed to connect to database'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Check if all required fields are filled
  const areAllFieldsFilled = () => {
    return Object.entries(formData).every(([key, value]) => {
      // Skip table_name for validation since it's optional for connection
      if (key === 'table_name') return true;
      // Ensure value is treated as string
      const strValue = String(value);
      return strValue.trim() !== '';
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Connect to Database</DialogTitle>
        </DialogHeader>
        <div className="py-4 space-y-4">
          <div className="space-y-2">
            <Label htmlFor="db_type">Database Type</Label>
            <Select 
              value={formData.db_type} 
              onValueChange={handleDbTypeChange}
            >
              <SelectTrigger id="db_type" className={errors.db_type && touched.db_type ? "border-red-500" : ""}>
                <SelectValue placeholder="Select database type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="postgres">PostgreSQL</SelectItem>
                <SelectItem value="mysql">MySQL</SelectItem>
              </SelectContent>
            </Select>
            {errors.db_type && touched.db_type && (
              <p className="text-red-500 text-xs mt-1">{errors.db_type}</p>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="host">Host</Label>
            <Input
              id="host"
              name="host"
              placeholder="localhost"
              value={formData.host}
              onChange={handleChange}
              onBlur={handleBlur}
              className={errors.host && touched.host ? "border-red-500" : ""}
            />
            {errors.host && touched.host && (
              <p className="text-red-500 text-xs mt-1">{errors.host}</p>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="port">Port</Label>
            <Input
              id="port"
              name="port"
              placeholder="5432"
              value={formData.port}
              onChange={handleChange}
              onBlur={handleBlur}
              className={errors.port && touched.port ? "border-red-500" : ""}
            />
            {errors.port && touched.port && (
              <p className="text-red-500 text-xs mt-1">{errors.port}</p>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="database">Database</Label>
            <Input
              id="database"
              name="database"
              placeholder="mydatabase"
              value={formData.database}
              onChange={handleChange}
              onBlur={handleBlur}
              className={errors.database && touched.database ? "border-red-500" : ""}
            />
            {errors.database && touched.database && (
              <p className="text-red-500 text-xs mt-1">{errors.database}</p>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="username">Username</Label>
            <Input
              id="username"
              name="username"
              placeholder="username"
              value={formData.username}
              onChange={handleChange}
              onBlur={handleBlur}
              className={errors.username && touched.username ? "border-red-500" : ""}
            />
            {errors.username && touched.username && (
              <p className="text-red-500 text-xs mt-1">{errors.username}</p>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              name="password"
              type="password"
              placeholder="••••••••"
              value={formData.password}
              onChange={handleChange}
              onBlur={handleBlur}
              className={errors.password && touched.password ? "border-red-500" : ""}
            />
            {errors.password && touched.password && (
              <p className="text-red-500 text-xs mt-1">{errors.password}</p>
            )}
          </div>
        </div>
        <DialogFooter>
          <Button variant="white" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit} 
            disabled={!areAllFieldsFilled() || !isFormValid || isLoading}
            className={`${!areAllFieldsFilled() || !isFormValid ? 'bg-blue-300 cursor-not-allowed' : 'bg-blue-500 hover:bg-blue-600'} text-white`}
          >
            {isLoading ? 'Connecting...' : 'Connect'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DatabaseConnectionDialog;
