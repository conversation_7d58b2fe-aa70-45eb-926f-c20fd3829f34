
import { ChartDataItem } from '@/services/api/chart/chartTypes';
import { store } from '@/stores/store';

/**
 * Function to identify chart keys from data with improved error handling
 */
export const identifyChartKeys = (
  data: ChartDataItem[],
  xAxisColumn: string,
  yAxisColumns: string[]
): { xAxisKey: string, yAxisKey: string } => {
  if (!data || data.length === 0) {
    console.error('identifyChartKeys: No data provided');
    return { xAxisKey: '', yAxisKey: '' };
  }
  
  const firstItem = data[0];
  const keys = Object.keys(firstItem);
  
  console.log('identifyChartKeys: Available keys in data:', keys);
  console.log('identifyChartKeys: Looking for x-axis:', xAxisColumn, 'y-axis:', yAxisColumns);
  
  // For aggregated data, we need to identify the correct keys
  let xAxisKey = '';
  let yAxisKey = '';
  
  // First check if the selected columns exist in the data
  if (xAxisColumn && keys.includes(xAxisColumn)) {
    xAxisKey = xAxisColumn;
    console.log('identifyChartKeys: Found exact x-axis key:', xAxisKey);
  }
  
  if (yAxisColumns.length > 0 && keys.includes(yAxisColumns[0])) {
    yAxisKey = yAxisColumns[0];
    console.log('identifyChartKeys: Found exact y-axis key:', yAxisKey);
  }
  
  // If not found directly, try to find keys that might contain the column names
  if (!xAxisKey && xAxisColumn) {
    xAxisKey = keys.find(key => key.includes(xAxisColumn)) || keys[0];
    console.log('identifyChartKeys: Using fallback x-axis key:', xAxisKey);
  }
  
  if (!yAxisKey && yAxisColumns.length > 0) {
    const yColumn = yAxisColumns[0];
    // Look for aggregated column names like "sum_column", "count_column", etc.
    yAxisKey = keys.find(key => 
      key.includes(yColumn) || 
      key.toLowerCase().includes('sum_') ||
      key.toLowerCase().includes('count_') ||
      key.toLowerCase().includes('avg_') ||
      key.toLowerCase().includes('min_') ||
      key.toLowerCase().includes('max_')
    ) || keys[1] || keys[0];
    console.log('identifyChartKeys: Using fallback y-axis key:', yAxisKey);
  }
  
  // Final fallback to ensure we have some keys
  if (!xAxisKey) xAxisKey = keys[0] || 'x';
  if (!yAxisKey) yAxisKey = keys[1] || keys[0] || 'y';
  
  console.log(`identifyChartKeys: Final keys - X: ${xAxisKey}, Y: ${yAxisKey}`);
  
  return { xAxisKey, yAxisKey };
};

/**
 * Function to fetch chart data that can be reused across components
 */
export const getExistingChartData = (): ChartDataItem[] | null => {
  console.log('getExistingChartData: Checking for existing chart data');
  
  // Check if we have chart data in the editing chart data
  const state = store.getState();
  const editingChartData = state.chart.editingChartData;
  
  if (editingChartData) {
    // Check multiple possible locations for chart data
    if (editingChartData.chartData && Array.isArray(editingChartData.chartData) && editingChartData.chartData.length > 0) {
      console.log("Using existing chart data from Redux:", editingChartData.chartData.length, "items");
      return editingChartData.chartData;
    }
    
    if (editingChartData.chart_response && Array.isArray(editingChartData.chart_response.data) && editingChartData.chart_response.data.length > 0) {
      console.log("Using chart data from chart_response:", editingChartData.chart_response.data.length, "items");
      return editingChartData.chart_response.data;
    }
    
    if (editingChartData.originalChart && editingChartData.originalChart.chart_response && 
        Array.isArray(editingChartData.originalChart.chart_response.data) && 
        editingChartData.originalChart.chart_response.data.length > 0) {
      console.log("Using chart data from originalChart.chart_response:", editingChartData.originalChart.chart_response.data.length, "items");
      return editingChartData.originalChart.chart_response.data;
    }
  }
  
  return null;
};
