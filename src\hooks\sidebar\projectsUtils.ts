
import { Project } from '@/components/dashboard/sidebar/types';

export const getInitialProjects = (): Project[] => [
  {
    id: '1',
    name: 'Project1',
    chats: ['Chat1', 'Chat2', 'Chat3'],
    expanded: true
  },
  {
    id: '2',
    name: 'Project2',
    chats: ['ChatA', 'ChatB'],
    expanded: false
  },
  {
    id: '3',
    name: 'Project3',
    chats: [],
    expanded: false
  }
];

export const toggleProject = (projects: Project[], itemId: string): Project[] => {
  return projects.map(project => 
    project.id === itemId 
      ? { ...project, expanded: !project.expanded } 
      : project
  );
};

export const addChatToProject = (projects: Project[], itemId: string): Project[] => {
  return projects.map(project => {
    if (project.id === itemId) {
      const chatNumber = project.chats.length + 1;
      return {
        ...project,
        chats: [...project.chats, `Chat${chatNumber}`],
        expanded: true
      };
    }
    return project;
  });
};

export const addNewProject = (projects: Project[]): Project[] => {
  const newProjectId = (projects.length + 1).toString();
  return [
    ...projects,
    {
      id: newProjectId,
      name: `Project${newProjectId}`,
      chats: [],
      expanded: false
    }
  ];
};

export const filterProjects = (projects: Project[], query: string): Project[] => {
  if (!query.trim()) return projects;
  
  const filterQuery = query.toLowerCase();
  
  return projects.map(project => {
    const projectMatches = project.name.toLowerCase().includes(filterQuery);
    
    const filteredChats = project.chats.filter(chat => 
      chat.toLowerCase().includes(filterQuery)
    );
    
    if (projectMatches || filteredChats.length > 0) {
      return {
        ...project,
        chats: filteredChats,
        expanded: true
      };
    }
    
    return null;
  }).filter(Boolean) as Project[];
};
