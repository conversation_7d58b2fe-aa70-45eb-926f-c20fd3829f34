import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON>alogHeader,
  <PERSON>alogTit<PERSON>,
  <PERSON>alogTrigger,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { ArrowLeft, Plus, Trash2 } from 'lucide-react';
import { toast } from 'sonner';

interface Group {
  id: string;
  name: string;
  members: number;
  source: 'Azure' | 'DADA';
  permissionSet: string;
  groupStatus: 'Active' | 'In Active';
  description?: string;
}

interface GroupFormData {
  group_name: string;
  source: string;
  group_description: string;
}

interface User {
  id: string;
  name: string;
  email?: string;
  username?: string;
}

interface Workspace {
  id: string;
  name: string;
  description?: string;
  workspace_name?: string;
}

interface Marketplace {
  id: string;
  name: string;
  description?: string;
  marketplace_name?: string;
}

const Groups: React.FC = () => {
  const navigate = useNavigate();
  const [groups, setGroups] = useState<Group[]>([]);
  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [formData, setFormData] = useState<GroupFormData>({
    group_name: '',
    source: 'Azure',
    group_description: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Additional data states
  const [users, setUsers] = useState<User[]>([]);
  const [workspaces, setWorkspaces] = useState<Workspace[]>([]);
  const [marketplaces, setMarketplaces] = useState<Marketplace[]>([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);
  const [isLoadingWorkspaces, setIsLoadingWorkspaces] = useState(false);
  const [isLoadingMarketplaces, setIsLoadingMarketplaces] = useState(false);

  // Group-specific user states
  const [availableUsers, setAvailableUsers] = useState<User[]>([]);
  const [assignedUsers, setAssignedUsers] = useState<User[]>([]);
  const [isLoadingAvailableUsers, setIsLoadingAvailableUsers] = useState(false);
  const [isLoadingAssignedUsers, setIsLoadingAssignedUsers] = useState(false);

  // Group-specific workspace states
  const [availableWorkspaces, setAvailableWorkspaces] = useState<Workspace[]>([]);
  const [assignedWorkspaces, setAssignedWorkspaces] = useState<Workspace[]>([]);
  const [isLoadingAvailableWorkspaces, setIsLoadingAvailableWorkspaces] = useState(false);
  const [isLoadingAssignedWorkspaces, setIsLoadingAssignedWorkspaces] = useState(false);

  // Group-specific marketplace states
  const [availableMarketplaces, setAvailableMarketplaces] = useState<Marketplace[]>([]);
  const [assignedMarketplaces, setAssignedMarketplaces] = useState<Marketplace[]>([]);
  const [isLoadingAvailableMarketplaces, setIsLoadingAvailableMarketplaces] = useState(false);
  const [isLoadingAssignedMarketplaces, setIsLoadingAssignedMarketplaces] = useState(false);

  // Fetch groups from API
  const fetchGroups = async () => {
    try {
      setIsLoading(true);
      console.log('🔍 Fetching groups from API...');

      const response = await fetch('http://***********:8001/account-management/get-all-groups', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('📥 Get groups response status:', response.status);

      if (!response.ok) {
        const errorData = await response.text();
        console.error('❌ Get groups error:', errorData);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorData}`);
      }

      const data = await response.json();
      console.log('✅ Groups fetched successfully:', data);

      // Handle different response formats
      let groupsData: any[] = [];
      if (Array.isArray(data)) {
        groupsData = data;
      } else if (data && Array.isArray(data.groups)) {
        groupsData = data.groups;
      } else if (data && Array.isArray(data.data)) {
        groupsData = data.data;
      }

      // Map API response to Group interface
      const mappedGroups: Group[] = groupsData.map((group: any) => ({
        id: group.id || group.group_id || Date.now().toString(),
        name: group.group_name || group.name || 'Unnamed Group',
        members: group.members || group.member_count || 0,
        source: (group.source === 'Azure' || group.source === 'DADA') ? group.source : 'DADA',
        permissionSet: group.permission_set || group.permissionSet || '',
        groupStatus: (group.status === 'Active' || group.status === 'In Active') ? group.status : 'Active',
        description: group.group_description || group.description || ''
      }));

      setGroups(mappedGroups);
      if (mappedGroups.length > 0) {
        setSelectedGroup(mappedGroups[0]); // Select first group by default
      }
    } catch (error) {
      console.error('❌ Error fetching groups:', error);
      toast.error(`Failed to fetch groups: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch users from API
  const fetchUsers = async () => {
    try {
      setIsLoadingUsers(true);
      console.log('🔍 Fetching users from API...');

      const response = await fetch('http://***********:8001/account-management/get-all-users', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('📥 Get users response status:', response.status);

      if (!response.ok) {
        const errorData = await response.text();
        console.error('❌ Get users error:', errorData);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorData}`);
      }

      const data = await response.json();
      console.log('✅ Users fetched successfully:', data);

      // Handle different response formats
      let usersData: any[] = [];
      if (Array.isArray(data)) {
        usersData = data;
      } else if (data && Array.isArray(data.users)) {
        usersData = data.users;
      } else if (data && Array.isArray(data.data)) {
        usersData = data.data;
      }

      // Map API response to User interface
      const mappedUsers: User[] = usersData.map((user: any) => ({
        id: user.id || user.user_id || Date.now().toString(),
        name: user.name || user.username || user.user_name || 'Unknown User',
        email: user.email || user.user_email || '',
        username: user.username || user.user_name || ''
      }));

      setUsers(mappedUsers);
    } catch (error) {
      console.error('❌ Error fetching users:', error);
      toast.error(`Failed to fetch users: ${error.message}`);
    } finally {
      setIsLoadingUsers(false);
    }
  };

  // Fetch workspaces from API
  const fetchWorkspaces = async () => {
    try {
      setIsLoadingWorkspaces(true);
      console.log('🔍 Fetching workspaces from API...');

      const response = await fetch('http://***********:8001/account-management/get-all-workspaces', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('📥 Get workspaces response status:', response.status);

      if (!response.ok) {
        const errorData = await response.text();
        console.error('❌ Get workspaces error:', errorData);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorData}`);
      }

      const data = await response.json();
      console.log('✅ Workspaces fetched successfully:', data);

      // Handle different response formats
      let workspacesData: any[] = [];
      if (Array.isArray(data)) {
        workspacesData = data;
      } else if (data && Array.isArray(data.workspaces)) {
        workspacesData = data.workspaces;
      } else if (data && Array.isArray(data.data)) {
        workspacesData = data.data;
      }

      // Map API response to Workspace interface
      const mappedWorkspaces: Workspace[] = workspacesData.map((workspace: any) => ({
        id: workspace.id || workspace.workspace_id || Date.now().toString(),
        name: workspace.workspace_name || workspace.name || 'Unknown Workspace',
        description: workspace.description || workspace.workspace_description || ''
      }));

      setWorkspaces(mappedWorkspaces);
    } catch (error) {
      console.error('❌ Error fetching workspaces:', error);
      toast.error(`Failed to fetch workspaces: ${error.message}`);
    } finally {
      setIsLoadingWorkspaces(false);
    }
  };

  // Fetch marketplaces from API
  const fetchMarketplaces = async () => {
    try {
      setIsLoadingMarketplaces(true);
      console.log('🔍 Fetching marketplaces from API...');

      const response = await fetch('http://***********:8001/account-management/get-all-marketplaces', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('📥 Get marketplaces response status:', response.status);

      if (!response.ok) {
        const errorData = await response.text();
        console.error('❌ Get marketplaces error:', errorData);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorData}`);
      }

      const data = await response.json();
      console.log('✅ Marketplaces fetched successfully:', data);

      // Handle different response formats
      let marketplacesData: any[] = [];
      if (Array.isArray(data)) {
        marketplacesData = data;
      } else if (data && Array.isArray(data.marketplaces)) {
        marketplacesData = data.marketplaces;
      } else if (data && Array.isArray(data.data)) {
        marketplacesData = data.data;
      }

      // Map API response to Marketplace interface
      const mappedMarketplaces: Marketplace[] = marketplacesData.map((marketplace: any) => ({
        id: marketplace.id || marketplace.marketplace_id || Date.now().toString(),
        name: marketplace.marketplace_name || marketplace.name || 'Unknown Marketplace',
        description: marketplace.description || marketplace.marketplace_description || ''
      }));

      setMarketplaces(mappedMarketplaces);
    } catch (error) {
      console.error('❌ Error fetching marketplaces:', error);
      toast.error(`Failed to fetch marketplaces: ${error.message}`);
    } finally {
      setIsLoadingMarketplaces(false);
    }
  };

  // Fetch users not in group (available users)
  const fetchAvailableUsers = async (groupId: string) => {
    try {
      setIsLoadingAvailableUsers(true);
      console.log(`🔍 Fetching available users for group ID: ${groupId}`);

      const response = await fetch(`http://***********:8001/account-management/get-users-not-in-group/${groupId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('📥 Get available users response status:', response.status);

      if (!response.ok) {
        const errorData = await response.text();
        console.error('❌ Get available users error:', errorData);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorData}`);
      }

      const data = await response.json();
      console.log('✅ Available users fetched successfully:', data);

      // Handle different response formats
      let usersData: any[] = [];
      if (Array.isArray(data)) {
        usersData = data;
      } else if (data && Array.isArray(data.users)) {
        usersData = data.users;
      } else if (data && Array.isArray(data.data)) {
        usersData = data.data;
      }

      // Map API response to User interface
      const mappedUsers: User[] = usersData.map((user: any) => ({
        id: user.id || user.user_id || Date.now().toString(),
        name: user.name || user.username || user.user_name || 'Unknown User',
        email: user.email || user.user_email || '',
        username: user.username || user.user_name || ''
      }));

      setAvailableUsers(mappedUsers);
    } catch (error) {
      console.error('❌ Error fetching available users:', error);
      toast.error(`Failed to fetch available users: ${error.message}`);
    } finally {
      setIsLoadingAvailableUsers(false);
    }
  };

  // Fetch users in group (assigned users)
  const fetchAssignedUsers = async (groupId: string) => {
    try {
      setIsLoadingAssignedUsers(true);
      console.log(`🔍 Fetching assigned users for group ID: ${groupId}`);

      const response = await fetch(`http://***********:8001/account-management/get-users-in-group/${groupId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('📥 Get assigned users response status:', response.status);

      if (!response.ok) {
        const errorData = await response.text();
        console.error('❌ Get assigned users error:', errorData);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorData}`);
      }

      const data = await response.json();
      console.log('✅ Assigned users fetched successfully:', data);

      // Handle different response formats
      let usersData: any[] = [];
      if (Array.isArray(data)) {
        usersData = data;
      } else if (data && Array.isArray(data.users)) {
        usersData = data.users;
      } else if (data && Array.isArray(data.data)) {
        usersData = data.data;
      }

      // Map API response to User interface
      const mappedUsers: User[] = usersData.map((user: any) => ({
        id: user.id || user.user_id || Date.now().toString(),
        name: user.name || user.username || user.user_name || 'Unknown User',
        email: user.email || user.user_email || '',
        username: user.username || user.user_name || ''
      }));

      setAssignedUsers(mappedUsers);
    } catch (error) {
      console.error('❌ Error fetching assigned users:', error);
      toast.error(`Failed to fetch assigned users: ${error.message}`);
    } finally {
      setIsLoadingAssignedUsers(false);
    }
  };

  // Fetch workspaces not in group (available workspaces)
  const fetchAvailableWorkspaces = async (groupId: string) => {
    try {
      setIsLoadingAvailableWorkspaces(true);
      console.log(`🔍 Fetching available workspaces for group ID: ${groupId}`);

      const response = await fetch(`http://***********:8001/account-management/get-workspaces-not-in-group/${groupId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('📥 Get available workspaces response status:', response.status);

      if (!response.ok) {
        const errorData = await response.text();
        console.error('❌ Get available workspaces error:', errorData);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorData}`);
      }

      const data = await response.json();
      console.log('✅ Available workspaces fetched successfully:', data);

      // Handle different response formats
      let workspacesData: any[] = [];
      if (Array.isArray(data)) {
        workspacesData = data;
      } else if (data && Array.isArray(data.workspaces)) {
        workspacesData = data.workspaces;
      } else if (data && Array.isArray(data.data)) {
        workspacesData = data.data;
      }

      // Map API response to Workspace interface
      const mappedWorkspaces: Workspace[] = workspacesData.map((workspace: any) => ({
        id: workspace.id || workspace.workspace_id || Date.now().toString(),
        name: workspace.workspace_name || workspace.name || 'Unknown Workspace',
        description: workspace.description || workspace.workspace_description || ''
      }));

      setAvailableWorkspaces(mappedWorkspaces);
    } catch (error) {
      console.error('❌ Error fetching available workspaces:', error);
      toast.error(`Failed to fetch available workspaces: ${error.message}`);
    } finally {
      setIsLoadingAvailableWorkspaces(false);
    }
  };

  // Fetch workspaces in group (assigned workspaces)
  const fetchAssignedWorkspaces = async (groupId: string) => {
    try {
      setIsLoadingAssignedWorkspaces(true);
      console.log(`🔍 Fetching assigned workspaces for group ID: ${groupId}`);

      const response = await fetch(`http://***********:8001/account-management/get-all-workspaces-in-group/${groupId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('📥 Get assigned workspaces response status:', response.status);

      if (!response.ok) {
        const errorData = await response.text();
        console.error('❌ Get assigned workspaces error:', errorData);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorData}`);
      }

      const data = await response.json();
      console.log('✅ Assigned workspaces fetched successfully:', data);

      // Handle different response formats
      let workspacesData: any[] = [];
      if (Array.isArray(data)) {
        workspacesData = data;
      } else if (data && Array.isArray(data.workspaces)) {
        workspacesData = data.workspaces;
      } else if (data && Array.isArray(data.data)) {
        workspacesData = data.data;
      }

      // Map API response to Workspace interface
      const mappedWorkspaces: Workspace[] = workspacesData.map((workspace: any) => ({
        id: workspace.id || workspace.workspace_id || Date.now().toString(),
        name: workspace.workspace_name || workspace.name || 'Unknown Workspace',
        description: workspace.description || workspace.workspace_description || ''
      }));

      setAssignedWorkspaces(mappedWorkspaces);
    } catch (error) {
      console.error('❌ Error fetching assigned workspaces:', error);
      toast.error(`Failed to fetch assigned workspaces: ${error.message}`);
    } finally {
      setIsLoadingAssignedWorkspaces(false);
    }
  };

  // Fetch marketplaces not in group (available marketplaces)
  const fetchAvailableMarketplaces = async (groupId: string) => {
    try {
      setIsLoadingAvailableMarketplaces(true);
      console.log(`🔍 Fetching available marketplaces for group ID: ${groupId}`);

      const response = await fetch(`http://***********:8001/account-management/get-all-marketplaces-not-in-group/${groupId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('📥 Get available marketplaces response status:', response.status);

      if (!response.ok) {
        const errorData = await response.text();
        console.error('❌ Get available marketplaces error:', errorData);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorData}`);
      }

      const data = await response.json();
      console.log('✅ Available marketplaces fetched successfully:', data);

      // Handle different response formats
      let marketplacesData: any[] = [];
      if (Array.isArray(data)) {
        marketplacesData = data;
      } else if (data && Array.isArray(data.marketplaces)) {
        marketplacesData = data.marketplaces;
      } else if (data && Array.isArray(data.data)) {
        marketplacesData = data.data;
      }

      // Map API response to Marketplace interface
      const mappedMarketplaces: Marketplace[] = marketplacesData.map((marketplace: any) => ({
        id: marketplace.id || marketplace.marketplace_id || Date.now().toString(),
        name: marketplace.marketplace_name || marketplace.name || 'Unknown Marketplace',
        description: marketplace.description || marketplace.marketplace_description || ''
      }));

      setAvailableMarketplaces(mappedMarketplaces);
    } catch (error) {
      console.error('❌ Error fetching available marketplaces:', error);
      toast.error(`Failed to fetch available marketplaces: ${error.message}`);
    } finally {
      setIsLoadingAvailableMarketplaces(false);
    }
  };

  // Fetch marketplaces in group (assigned marketplaces)
  const fetchAssignedMarketplaces = async (groupId: string) => {
    try {
      setIsLoadingAssignedMarketplaces(true);
      console.log(`🔍 Fetching assigned marketplaces for group ID: ${groupId}`);

      const response = await fetch(`http://***********:8001/account-management/get-all-marketplaces-in-group/${groupId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('📥 Get assigned marketplaces response status:', response.status);

      if (!response.ok) {
        const errorData = await response.text();
        console.error('❌ Get assigned marketplaces error:', errorData);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorData}`);
      }

      const data = await response.json();
      console.log('✅ Assigned marketplaces fetched successfully:', data);

      // Handle different response formats
      let marketplacesData: any[] = [];
      if (Array.isArray(data)) {
        marketplacesData = data;
      } else if (data && Array.isArray(data.marketplaces)) {
        marketplacesData = data.marketplaces;
      } else if (data && Array.isArray(data.data)) {
        marketplacesData = data.data;
      }

      // Map API response to Marketplace interface
      const mappedMarketplaces: Marketplace[] = marketplacesData.map((marketplace: any) => ({
        id: marketplace.id || marketplace.marketplace_id || Date.now().toString(),
        name: marketplace.marketplace_name || marketplace.name || 'Unknown Marketplace',
        description: marketplace.description || marketplace.marketplace_description || ''
      }));

      setAssignedMarketplaces(mappedMarketplaces);
    } catch (error) {
      console.error('❌ Error fetching assigned marketplaces:', error);
      toast.error(`Failed to fetch assigned marketplaces: ${error.message}`);
    } finally {
      setIsLoadingAssignedMarketplaces(false);
    }
  };

  // Fetch group-specific users, workspaces, and marketplaces when group is selected
  useEffect(() => {
    if (selectedGroup) {
      fetchAvailableUsers(selectedGroup.id);
      fetchAssignedUsers(selectedGroup.id);
      fetchAvailableWorkspaces(selectedGroup.id);
      fetchAssignedWorkspaces(selectedGroup.id);
      fetchAvailableMarketplaces(selectedGroup.id);
      fetchAssignedMarketplaces(selectedGroup.id);
    } else {
      setAvailableUsers([]);
      setAssignedUsers([]);
      setAvailableWorkspaces([]);
      setAssignedWorkspaces([]);
      setAvailableMarketplaces([]);
      setAssignedMarketplaces([]);
    }
  }, [selectedGroup]);

  useEffect(() => {
    fetchGroups();
    fetchUsers();
    fetchWorkspaces();
    fetchMarketplaces();
  }, []);

  const handleInputChange = (field: keyof GroupFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCreateGroup = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.group_name.trim() || !formData.group_description.trim()) {
      toast.error('Please fill in all required fields');
      return;
    }

    setIsSubmitting(true);

    try {
      const requestBody = {
        group_name: formData.group_name,
        source: formData.source,
        group_description: formData.group_description
      };

      console.log('🚀 Creating group with data:', requestBody);

      const response = await fetch('http://***********:8001/account-management/create-group', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      console.log('📥 Create group response status:', response.status);

      if (!response.ok) {
        const errorData = await response.text();
        console.error('❌ Create group error:', errorData);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorData}`);
      }

      const result = await response.json();
      console.log('✅ Group created successfully:', result);

      // Refresh the groups list to get the latest data
      await fetchGroups();
      setIsCreateDialogOpen(false);
      setFormData({ group_name: '', source: 'Azure', group_description: '' });
      toast.success('Group created successfully!');
    } catch (error) {
      console.error('❌ Error creating group:', error);
      toast.error(`Failed to create group: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteGroup = async (groupId: string, groupName: string) => {
    if (!window.confirm(`Are you sure you want to delete the group "${groupName}"?`)) {
      return;
    }

    try {
      console.log(`🗑️ Deleting group: ${groupName} (ID: ${groupId})`);

      const response = await fetch(`http://***********:8001/account-management/delete-group/${groupId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('📥 Delete group response status:', response.status);

      if (!response.ok) {
        const errorData = await response.text();
        console.error('❌ Delete group error:', errorData);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorData}`);
      }

      console.log('✅ Group deleted successfully');

      // Refresh the groups list
      await fetchGroups();

      // Clear selected group if it was the deleted one
      if (selectedGroup?.id === groupId) {
        setSelectedGroup(null);
      }

      toast.success(`Group "${groupName}" deleted successfully!`);
    } catch (error) {
      console.error('❌ Error deleting group:', error);
      toast.error(`Failed to delete group: ${error.message}`);
    }
  };

  const handleAddUserToGroup = async (userId: string, userName: string) => {
    if (!selectedGroup) {
      toast.error('Please select a group first');
      return;
    }

    try {
      console.log(`👤 Adding user ${userName} (ID: ${userId}) to group ${selectedGroup.name} (ID: ${selectedGroup.id})`);

      // Build URL with query parameters
      const url = `http://***********:8001/account-management/add-user-to-group?group_id=${selectedGroup.id}&user_id=${userId}`;
      console.log('🔗 API URL:', url);

      const response = await fetch(url, {
        method: 'POST',
      });

      console.log('📥 Add user to group response status:', response.status);

      if (!response.ok) {
        const errorData = await response.text();
        console.error('❌ Add user to group error:', errorData);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorData}`);
      }

      const result = await response.json();
      console.log('✅ User added to group successfully:', result);

      toast.success(`User "${userName}" added to group "${selectedGroup.name}" successfully!`);

      // Refresh both available and assigned users lists
      await fetchAvailableUsers(selectedGroup.id);
      await fetchAssignedUsers(selectedGroup.id);

    } catch (error) {
      console.error('❌ Error adding user to group:', error);
      toast.error(`Failed to add user to group: ${error.message}`);
    }
  };

  const handleAddMarketplaceToGroup = async (marketplaceId: string, marketplaceName: string) => {
    if (!selectedGroup) {
      toast.error('Please select a group first');
      return;
    }

    try {
      console.log(`🏪 Adding marketplace ${marketplaceName} (ID: ${marketplaceId}) to group ${selectedGroup.name} (ID: ${selectedGroup.id})`);

      // Build URL with query parameters
      const url = `http://***********:8001/account-management/add-marketplace-to-group?group_id=${selectedGroup.id}&marketplace_id=${marketplaceId}`;
      console.log('🔗 API URL:', url);

      const response = await fetch(url, {
        method: 'POST',
      });

      console.log('📥 Add marketplace to group response status:', response.status);

      if (!response.ok) {
        const errorData = await response.text();
        console.error('❌ Add marketplace to group error:', errorData);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorData}`);
      }

      const result = await response.json();
      console.log('✅ Marketplace added to group successfully:', result);

      toast.success(`Marketplace "${marketplaceName}" added to group "${selectedGroup.name}" successfully!`);

      // Refresh both available and assigned marketplaces lists
      await fetchAvailableMarketplaces(selectedGroup.id);
      await fetchAssignedMarketplaces(selectedGroup.id);

    } catch (error) {
      console.error('❌ Error adding marketplace to group:', error);
      toast.error(`Failed to add marketplace to group: ${error.message}`);
    }
  };

  const handleAddWorkspaceToGroup = async (workspaceId: string, workspaceName: string) => {
    if (!selectedGroup) {
      toast.error('Please select a group first');
      return;
    }

    try {
      console.log(`🏢 Adding workspace ${workspaceName} (ID: ${workspaceId}) to group ${selectedGroup.name} (ID: ${selectedGroup.id})`);

      // Build URL with query parameters
      const url = `http://***********:8001/account-management/add-workspace-to-group?group_id=${selectedGroup.id}&workspace_id=${workspaceId}`;
      console.log('🔗 API URL:', url);

      const response = await fetch(url, {
        method: 'POST',
      });

      console.log('📥 Add workspace to group response status:', response.status);

      if (!response.ok) {
        const errorData = await response.text();
        console.error('❌ Add workspace to group error:', errorData);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorData}`);
      }

      const result = await response.json();
      console.log('✅ Workspace added to group successfully:', result);

      toast.success(`Workspace "${workspaceName}" added to group "${selectedGroup.name}" successfully!`);

      // Refresh both available and assigned workspaces lists
      await fetchAvailableWorkspaces(selectedGroup.id);
      await fetchAssignedWorkspaces(selectedGroup.id);

    } catch (error) {
      console.error('❌ Error adding workspace to group:', error);
      toast.error(`Failed to add workspace to group: ${error.message}`);
    }
  };

  const handleRemoveUserFromGroup = async (userId: string, userName: string) => {
    if (!selectedGroup) {
      toast.error('Please select a group first');
      return;
    }

    try {
      console.log(`👤❌ Removing user ${userName} (ID: ${userId}) from group ${selectedGroup.name} (ID: ${selectedGroup.id})`);

      // Build URL with query parameters
      const url = `http://***********:8001/account-management/remove-user-from-group?group_id=${selectedGroup.id}&user_id=${userId}`;
      console.log('🔗 API URL:', url);

      const response = await fetch(url, {
        method: 'POST',
      });

      console.log('📥 Remove user from group response status:', response.status);

      if (!response.ok) {
        const errorData = await response.text();
        console.error('❌ Remove user from group error:', errorData);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorData}`);
      }

      const result = await response.json();
      console.log('✅ User removed from group successfully:', result);

      toast.success(`User "${userName}" removed from group "${selectedGroup.name}" successfully!`);

      // Refresh both available and assigned users lists
      await fetchAvailableUsers(selectedGroup.id);
      await fetchAssignedUsers(selectedGroup.id);

    } catch (error) {
      console.error('❌ Error removing user from group:', error);
      toast.error(`Failed to remove user from group: ${error.message}`);
    }
  };

  const handleRemoveWorkspaceFromGroup = async (workspaceId: string, workspaceName: string) => {
    if (!selectedGroup) {
      toast.error('Please select a group first');
      return;
    }

    try {
      console.log(`🏢❌ Removing workspace ${workspaceName} (ID: ${workspaceId}) from group ${selectedGroup.name} (ID: ${selectedGroup.id})`);

      // Build URL with query parameters
      const url = `http://***********:8001/account-management/remove-workspace-from-group?group_id=${selectedGroup.id}&workspace_id=${workspaceId}`;
      console.log('🔗 API URL:', url);

      const response = await fetch(url, {
        method: 'POST',
      });

      console.log('📥 Remove workspace from group response status:', response.status);

      if (!response.ok) {
        const errorData = await response.text();
        console.error('❌ Remove workspace from group error:', errorData);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorData}`);
      }

      const result = await response.json();
      console.log('✅ Workspace removed from group successfully:', result);

      toast.success(`Workspace "${workspaceName}" removed from group "${selectedGroup.name}" successfully!`);

      // Refresh both available and assigned workspaces lists
      await fetchAvailableWorkspaces(selectedGroup.id);
      await fetchAssignedWorkspaces(selectedGroup.id);

    } catch (error) {
      console.error('❌ Error removing workspace from group:', error);
      toast.error(`Failed to remove workspace from group: ${error.message}`);
    }
  };

  const handleRemoveMarketplaceFromGroup = async (marketplaceId: string, marketplaceName: string) => {
    if (!selectedGroup) {
      toast.error('Please select a group first');
      return;
    }

    try {
      console.log(`🏪❌ Removing marketplace ${marketplaceName} (ID: ${marketplaceId}) from group ${selectedGroup.name} (ID: ${selectedGroup.id})`);

      // Build URL with query parameters
      const url = `http://***********:8001/account-management/remove-marketplace-from-group?group_id=${selectedGroup.id}&marketplace_id=${marketplaceId}`;
      console.log('🔗 API URL:', url);

      const response = await fetch(url, {
        method: 'POST',
      });

      console.log('📥 Remove marketplace from group response status:', response.status);

      if (!response.ok) {
        const errorData = await response.text();
        console.error('❌ Remove marketplace from group error:', errorData);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorData}`);
      }

      const result = await response.json();
      console.log('✅ Marketplace removed from group successfully:', result);

      toast.success(`Marketplace "${marketplaceName}" removed from group "${selectedGroup.name}" successfully!`);

      // Refresh both available and assigned marketplaces lists
      await fetchAvailableMarketplaces(selectedGroup.id);
      await fetchAssignedMarketplaces(selectedGroup.id);

    } catch (error) {
      console.error('❌ Error removing marketplace from group:', error);
      toast.error(`Failed to remove marketplace from group: ${error.message}`);
    }
  };

  const handleCancel = () => {
    setFormData({ group_name: '', source: 'Azure', group_description: '' });
    setIsCreateDialogOpen(false);
  };



  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg font-normal text-gray-600">Loading groups...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Back to Admin Button */}
      <div className="flex items-center mb-4">
        <Button
          variant="ghost"
          onClick={() => navigate('/Admin')}
          className="p-0 text-green-600 hover:text-green-800 hover:bg-transparent font-normal"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Admin
        </Button>
      </div>

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-normal text-gray-700">Groups</h1>
          <p className="text-sm font-normal text-gray-500 mt-1">(Predefined groups/ Customer groups)</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button
              className="text-white hover:opacity-90 transition-opacity font-normal"
              style={{ backgroundColor: 'rgb(0, 130, 130)' }}
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Group
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle className="font-normal">Create New Group</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleCreateGroup} className="space-y-4">
              {/* Group Name */}
              <div className="space-y-2">
                <Label htmlFor="group_name" className="font-normal">Group Name *</Label>
                <Input
                  id="group_name"
                  type="text"
                  placeholder="Enter group name"
                  value={formData.group_name}
                  onChange={(e) => handleInputChange('group_name', e.target.value)}
                  className="font-normal"
                />
              </div>

              {/* Source */}
              <div className="space-y-2">
                <Label htmlFor="source" className="font-normal">Source *</Label>
                <select
                  id="source"
                  value={formData.source}
                  onChange={(e) => handleInputChange('source', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm font-normal focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="Azure">Azure</option>
                  <option value="DADA">DADA</option>
                </select>
              </div>

              {/* Group Description */}
              <div className="space-y-2">
                <Label htmlFor="group_description" className="font-normal">Group Description *</Label>
                <Input
                  id="group_description"
                  type="text"
                  placeholder="Enter group description"
                  value={formData.group_description}
                  onChange={(e) => handleInputChange('group_description', e.target.value)}
                  className="font-normal"
                />
              </div>

              <DialogFooter className="gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancel}
                  disabled={isSubmitting}
                  className="font-normal"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-blue-600 hover:bg-blue-700 font-normal"
                >
                  {isSubmitting ? 'Creating...' : 'Create Group'}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Groups Table */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
        <Table>
          <TableHeader>
            <TableRow className="border-b border-gray-200">
              <TableHead className="font-normal text-black">Name</TableHead>
              <TableHead className="font-normal text-black">Members</TableHead>
              <TableHead className="font-normal text-black">Source</TableHead>
              <TableHead className="font-normal text-black">Permission Set</TableHead>
              <TableHead className="font-normal text-black">Group Status</TableHead>
              <TableHead className="font-normal text-black">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {groups.map((group) => (
              <TableRow
                key={group.id}
                className="border-b border-gray-200 hover:bg-gray-50"
              >
                <TableCell className="font-normal text-gray-700">
                  {group.name}
                </TableCell>
                <TableCell className="font-normal text-gray-700">
                  {group.members}
                </TableCell>
                <TableCell>
                  <span className={`px-2 py-1 rounded-full text-xs font-normal ${
                    group.source === 'Azure'
                      ? 'bg-blue-100 text-blue-800'
                      : 'bg-green-100 text-green-800'
                  }`}>
                    {group.source}
                  </span>
                </TableCell>
                <TableCell className="font-normal text-gray-700">
                  {group.permissionSet}
                </TableCell>
                <TableCell>
                  <span className={`px-2 py-1 rounded-full text-xs font-normal ${
                    group.groupStatus === 'Active'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {group.groupStatus}
                  </span>
                </TableCell>
                <TableCell>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteGroup(group.id, group.name)}
                    className="text-red-500 hover:text-red-700 hover:bg-red-50 p-2"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {groups.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500 font-normal">No groups found. Create your first one!</p>
        </div>
      )}

      {/* Group Selection Dropdown */}
      {groups.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-normal text-gray-700 mb-2">Group Management</h3>
              <p className="text-sm text-gray-500">Select a group to manage its permissions, members, and assignments</p>
            </div>
            <div className="min-w-[300px]">
              <Label htmlFor="groupSelect" className="block text-sm font-normal text-gray-700 mb-2">
                Select Group
              </Label>
              <select
                id="groupSelect"
                value={selectedGroup?.id || ''}
                onChange={(e) => {
                  const group = groups.find(g => g.id === e.target.value);
                  setSelectedGroup(group || null);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm font-normal focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white"
              >
                <option value="">Select a group...</option>
                {groups.map((group) => (
                  <option key={group.id} value={group.id}>
                    {group.name} ({group.source}) - {group.members} members
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Assignment Sections */}
      {selectedGroup && (
        <div className="grid grid-cols-1 lg:grid-cols-5 gap-4 mt-8">
          {/* Group Permissions */}
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
            <div className="p-3 border-b border-gray-200 bg-gray-50">
              <h3 className="text-sm font-normal text-gray-700">Group Permissions</h3>
              <p className="text-xs text-gray-500 mt-1">for {selectedGroup.name}</p>
            </div>
            <div className="p-3">
              <div className="space-y-2">
                <h4 className="text-sm font-normal text-gray-700 mb-2">Application permissions</h4>
                <div className="space-y-1 text-xs font-normal text-gray-600">
                  <div className="flex items-center space-x-2">
                    <input type="checkbox" className="w-3 h-3" defaultChecked />
                    <span>DADA_Admin (Checkbox)</span>
                  </div>
                  <div>MyQL_Analyst</div>
                  <div>PowerQuery_Editor</div>
                  <div>PowerQuery_Executor</div>
                  <div>Insights_Admin</div>
                  <div>Insights_Analyst</div>
                  <div>Insights_pattern_Admin</div>
                  <div>Insights_Pattern_Analyst</div>
                  <div>Insights_Chart_Editor</div>
                  <div>Insights_Chart_Viewer</div>
                  <div>Predict_Analyst</div>
                  <div>Predict_Viewer</div>
                  <div>DBConnections</div>
                  <div>Workspaces-Admin</div>
                  <div>Marketplace-Admin</div>
                  <div>Permission_Set</div>
                  <div>Approval_Set</div>
                  <div>Approval_UI</div>
                  <div>Dataview_Builder</div>
                  <div>Dataview_Scheduler</div>
                  <div>Analysis_Builder</div>
                </div>
              </div>
            </div>
          </div>

          {/* Member Assignment */}
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
            <div className="p-3 border-b border-gray-200 bg-gray-50">
              <h3 className="text-sm font-normal text-gray-700">Member Assignment</h3>
              <p className="text-xs text-gray-500 mt-1">for {selectedGroup.name}</p>
            </div>
            <div className="p-3">
              <div className="space-y-3">
                <Input
                  placeholder={isLoadingAvailableUsers ? "Loading available users..." : "Search available users"}
                  className="text-sm font-normal"
                  disabled={isLoadingAvailableUsers}
                />
                <div className="space-y-2">
                  <h5 className="text-sm font-normal text-gray-700 mb-2">Available Users:</h5>
                  {isLoadingAvailableUsers ? (
                    <div className="text-sm text-gray-500">Loading available users...</div>
                  ) : availableUsers.length === 0 ? (
                    <div className="text-sm text-gray-500">No available users found</div>
                  ) : (
                    <div className="max-h-32 overflow-y-auto space-y-1">
                      {availableUsers.map((user) => (
                        <div key={user.id} className="flex items-center justify-between">
                          <span className="text-sm font-normal text-gray-700">
                            {user.name} {user.email && `(${user.email})`}
                          </span>
                          <Button
                            size="sm"
                            className="text-xs font-normal bg-gray-600 hover:bg-gray-700"
                            onClick={() => handleAddUserToGroup(user.id, user.name)}
                          >
                            Add
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}

                  <div className="mt-4">
                    <h5 className="text-sm font-normal text-gray-700 mb-2">Assigned Users:</h5>
                    {isLoadingAssignedUsers ? (
                      <div className="text-sm text-gray-500">Loading assigned users...</div>
                    ) : assignedUsers.length === 0 ? (
                      <div className="text-sm text-gray-500 italic">No users assigned to this group</div>
                    ) : (
                      <div className="max-h-32 overflow-y-auto space-y-1">
                        {assignedUsers.map((user) => (
                          <div key={user.id} className="flex items-center justify-between">
                            <span className="text-sm font-normal text-gray-700">
                              {user.name} {user.email && `(${user.email})`}
                            </span>
                            <Button
                              size="sm"
                              variant="outline"
                              className="text-xs font-normal text-red-600 border-red-300 hover:bg-red-50"
                              onClick={() => handleRemoveUserFromGroup(user.id, user.name)}
                            >
                              Remove
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Workspace Assignment */}
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
            <div className="p-3 border-b border-gray-200 bg-gray-50">
              <h3 className="text-sm font-normal text-gray-700">Workspace Assignment</h3>
              <p className="text-xs text-gray-500 mt-1">for {selectedGroup.name}</p>
            </div>
            <div className="p-3">
              <div className="space-y-3">
                <Input
                  placeholder={isLoadingAvailableWorkspaces ? "Loading available workspaces..." : "Search available workspaces"}
                  className="text-sm font-normal"
                  disabled={isLoadingAvailableWorkspaces}
                />
                <div className="space-y-2">
                  <h5 className="text-sm font-normal text-gray-700 mb-2">Available Workspaces:</h5>
                  {isLoadingAvailableWorkspaces ? (
                    <div className="text-sm text-gray-500">Loading available workspaces...</div>
                  ) : availableWorkspaces.length === 0 ? (
                    <div className="text-sm text-gray-500">No available workspaces found</div>
                  ) : (
                    <div className="max-h-32 overflow-y-auto space-y-1">
                      {availableWorkspaces.map((workspace) => (
                        <div key={workspace.id} className="flex items-center justify-between">
                          <span className="text-sm font-normal text-gray-700">
                            {workspace.name}
                          </span>
                          <Button
                            size="sm"
                            className="text-xs font-normal bg-gray-600 hover:bg-gray-700"
                            onClick={() => handleAddWorkspaceToGroup(workspace.id, workspace.name)}
                          >
                            Add
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}

                  <div className="mt-4">
                    <h5 className="text-sm font-normal text-gray-700 mb-2">Assigned Workspaces:</h5>
                    {isLoadingAssignedWorkspaces ? (
                      <div className="text-sm text-gray-500">Loading assigned workspaces...</div>
                    ) : assignedWorkspaces.length === 0 ? (
                      <div className="text-sm text-gray-500 italic">No workspaces assigned to this group</div>
                    ) : (
                      <div className="max-h-32 overflow-y-auto space-y-1">
                        {assignedWorkspaces.map((workspace) => (
                          <div key={workspace.id} className="flex items-center justify-between">
                            <span className="text-sm font-normal text-gray-700">
                              {workspace.name}
                            </span>
                            <Button
                              size="sm"
                              variant="outline"
                              className="text-xs font-normal text-red-600 border-red-300 hover:bg-red-50"
                              onClick={() => handleRemoveWorkspaceFromGroup(workspace.id, workspace.name)}
                            >
                              Remove
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Marketplace Assignment */}
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
            <div className="p-3 border-b border-gray-200 bg-gray-50">
              <h3 className="text-sm font-normal text-gray-700">Marketplace Assignment</h3>
              <p className="text-xs text-gray-500 mt-1">for {selectedGroup.name}</p>
            </div>
            <div className="p-3">
              <div className="space-y-3">
                <Input
                  placeholder={isLoadingAvailableMarketplaces ? "Loading available marketplaces..." : "Search available marketplaces"}
                  className="text-sm font-normal"
                  disabled={isLoadingAvailableMarketplaces}
                />
                <div className="space-y-2">
                  <h5 className="text-sm font-normal text-gray-700 mb-2">Available Marketplaces:</h5>
                  {isLoadingAvailableMarketplaces ? (
                    <div className="text-sm text-gray-500">Loading available marketplaces...</div>
                  ) : availableMarketplaces.length === 0 ? (
                    <div className="text-sm text-gray-500">No available marketplaces found</div>
                  ) : (
                    <div className="max-h-32 overflow-y-auto space-y-1">
                      {availableMarketplaces.map((marketplace) => (
                        <div key={marketplace.id} className="flex items-center justify-between">
                          <span className="text-sm font-normal text-gray-700">
                            {marketplace.name}
                          </span>
                          <Button
                            size="sm"
                            className="text-xs font-normal bg-gray-600 hover:bg-gray-700"
                            onClick={() => handleAddMarketplaceToGroup(marketplace.id, marketplace.name)}
                          >
                            Add
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}

                  <div className="mt-4">
                    <h5 className="text-sm font-normal text-gray-700 mb-2">Assigned Marketplaces:</h5>
                    {isLoadingAssignedMarketplaces ? (
                      <div className="text-sm text-gray-500">Loading assigned marketplaces...</div>
                    ) : assignedMarketplaces.length === 0 ? (
                      <div className="text-sm text-gray-500 italic">No marketplaces assigned to this group</div>
                    ) : (
                      <div className="max-h-32 overflow-y-auto space-y-1">
                        {assignedMarketplaces.map((marketplace) => (
                          <div key={marketplace.id} className="flex items-center justify-between">
                            <span className="text-sm font-normal text-gray-700">
                              {marketplace.name}
                            </span>
                            <Button
                              size="sm"
                              variant="outline"
                              className="text-xs font-normal text-red-600 border-red-300 hover:bg-red-50"
                              onClick={() => handleRemoveMarketplaceFromGroup(marketplace.id, marketplace.name)}
                            >
                              Remove
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Permission set Assignment */}
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
            <div className="p-3 border-b border-gray-200 bg-gray-50">
              <h3 className="text-sm font-normal text-gray-700">Permission set Assignment</h3>
              <p className="text-xs text-gray-500 mt-1">for {selectedGroup.name}</p>
            </div>
            <div className="p-3">
              <div className="space-y-3">
                <Input
                  placeholder="Search"
                  className="text-sm font-normal"
                />
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-normal text-gray-700">Marketplace1</span>
                    <Button size="sm" className="text-xs font-normal bg-gray-600 hover:bg-gray-700">Add</Button>
                  </div>
                  <div className="text-sm font-normal text-gray-700">Marketplace2</div>
                  <div className="mt-4">
                    <h5 className="text-sm font-normal text-gray-700 mb-2">Permission Set:</h5>
                    <div className="space-y-1">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-normal text-gray-700">Marketplace1</span>
                        <Button size="sm" variant="outline" className="text-xs font-normal">Remove from group</Button>
                      </div>
                      <div className="text-sm font-normal text-gray-700">Marketplace2</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Groups;
