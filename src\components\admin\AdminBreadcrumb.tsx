import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { ChevronRight, Home } from 'lucide-react';

interface BreadcrumbItem {
  label: string;
  path: string;
  isActive?: boolean;
}

const AdminBreadcrumb: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const getBreadcrumbItems = (): BreadcrumbItem[] => {
    const path = location.pathname;
    const items: BreadcrumbItem[] = [
      { label: 'Admin', path: '/Admin' }
    ];

    if (path === '/Admin' || path === '/Admin/') {
      items[0].isActive = true;
      return items;
    }

    if (path.includes('/Admin/menu')) {
      items.push({ label: 'DB Connections', path: '/Admin/menu', isActive: true });
    } else if (path.includes('/Admin/mlz-scanner')) {
      items.push({ label: 'MLZ Data Scanner', path: '/Admin/mlz-scanner', isActive: true });
    } else if (path.includes('/Admin/database')) {
      items.push({ label: 'Table Access Management', path: '/Admin/database', isActive: true });
    } else if (path.includes('/Admin/users')) {
      items.push({ label: 'Users', path: '/Admin/users', isActive: true });
    } else if (path.includes('/Admin/service-principal')) {
      items.push({ label: 'Service Principal', path: '/Admin/service-principal', isActive: true });
    } else if (path.includes('/Admin/TableRelationshipMapper')) {
      const searchParams = new URLSearchParams(window.location.search);
      const isNew = searchParams.get('new') === 'true';
      items.push({
        label: isNew ? 'New Relationship' : 'Table Relationship Mapper',
        path: '/Admin/TableRelationshipMapper',
        isActive: true
      });
    } else if (path.includes('/Admin/approval-set')) {
      items.push({ label: 'Approval Set', path: '/Admin/approval-set', isActive: true });
    } else if (path.includes('/Admin/permission-set')) {
      items.push({ label: 'Permission Set', path: '/Admin/permission-set', isActive: true });
    } else if (path.includes('/Admin/workspace')) {
      items.push({ label: 'Workspace', path: '/Admin/workspace', isActive: true });
    } else if (path.includes('/Admin/marketplace')) {
      items.push({ label: 'Marketplace', path: '/Admin/marketplace', isActive: true });
    } else if (path.includes('/Admin/edit-connection')) {
      items.push({ label: 'DB Connections', path: '/Admin/menu' });
      items.push({ label: 'Edit Connection', path: path, isActive: true });
    } else {
      // For any other admin page
      const pathSegments = path.split('/').filter(Boolean);
      if (pathSegments.length > 1) {
        const pageName = pathSegments[pathSegments.length - 1]
          .split('-')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
        items.push({ label: pageName, path: path, isActive: true });
      }
    }

    return items;
  };

  const breadcrumbItems = getBreadcrumbItems();

  const handleBreadcrumbClick = (item: BreadcrumbItem) => {
    if (!item.isActive) {
      navigate(item.path);
    }
  };

  return (
    <div className="flex items-center space-x-2 text-sm text-gray-600 mb-6">
      <Home
        size={16}
        className="text-gray-400 cursor-pointer transition-colors"
        onMouseEnter={(e) => e.currentTarget.style.color = 'rgb(0, 130, 130)'}
        onMouseLeave={(e) => e.currentTarget.style.color = 'rgb(156, 163, 175)'}
        onClick={() => navigate('/Admin')}
      />
      
      {breadcrumbItems.map((item) => (
        <React.Fragment key={item.path}>
          <ChevronRight size={14} className="text-gray-400" />
          <span
            onClick={() => handleBreadcrumbClick(item)}
            className={`transition-colors ${
              item.isActive
                ? 'text-gray-900 font-normal'
                : 'cursor-pointer hover:underline'
            }`}
            style={!item.isActive ? {
              color: 'rgb(0, 130, 130)',
            } : {}}
            onMouseEnter={(e) => {
              if (!item.isActive) {
                e.currentTarget.style.color = 'rgb(0, 100, 100)';
              }
            }}
            onMouseLeave={(e) => {
              if (!item.isActive) {
                e.currentTarget.style.color = 'rgb(0, 130, 130)';
              }
            }}
          >
            {item.label}
          </span>
        </React.Fragment>
      ))}
    </div>
  );
};

export default AdminBreadcrumb;
