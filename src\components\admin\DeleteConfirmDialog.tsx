import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Loader2, Check, X } from "lucide-react";

interface DeleteConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => Promise<void>;
  title: string;
  description: string;
}

const DeleteConfirmDialog: React.FC<DeleteConfirmDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  description
}) => {
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [errorCode, setErrorCode] = useState<number | undefined>(undefined);

  const handleConfirm = async () => {
    setStatus('loading');
    try {
      await onConfirm();
      setStatus('success');
      
      // Auto close after success
      setTimeout(() => {
        setStatus('idle');
        onClose();
      }, 2000);
    } catch (error) {
      setStatus('error');
      setErrorCode(error?.status || 500);
      
      // Reset to idle after showing error
      setTimeout(() => {
        setStatus('idle');
      }, 3000);
    }
  };

  const renderContent = () => {
    switch (status) {
      case 'loading':
        return (
          <div className="flex flex-col items-center justify-center py-6">
            <Loader2 className="h-12 w-12 text-blue-500 animate-spin mb-4" />
            <p className="text-gray-600">Deleting your project...</p>
          </div>
        );
      
      case 'success':
        return (
          <div className="flex flex-col items-center justify-center py-6">
            <Check className="h-12 w-12 text-green-500 mb-4 bg-green-100 p-2 rounded-full" />
            <p className="text-green-600">Your project has been deleted successfully!</p>
          </div>
        );
      
      case 'error':
        return (
          <div className="flex flex-col items-center justify-center py-6">
            <X className="h-12 w-12 text-red-500 mb-4" />
            <p className="text-red-600">Failed to delete project</p>
            {errorCode && <p className="text-gray-500 text-sm">Status Code: {errorCode}</p>}
          </div>
        );
      
      default:
        return (
          <>
            <DialogHeader>
              <DialogTitle className="text-red-600">{title}</DialogTitle>
              <DialogDescription>
                {description}
              </DialogDescription>
            </DialogHeader>
            <DialogFooter className="flex justify-end space-x-2 pt-4">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button variant="destructive" onClick={handleConfirm}>
                Delete
              </Button>
            </DialogFooter>
          </>
        );
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      if (!open && status !== 'loading') onClose();
    }}>
      <DialogContent>
        {renderContent()}
      </DialogContent>
    </Dialog>
  );
};

export default DeleteConfirmDialog;
