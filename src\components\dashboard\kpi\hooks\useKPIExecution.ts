import { useState, useCallback } from 'react';
import { KPIFormData, KPIPreviewData, KPIValidationResult } from '../types/kpiTypes';
import { executeKPI, saveKPI } from '@/services/api/chart/kpiService';

interface UseKPIExecutionProps {
  onExecute?: (kpiData: KPIFormData) => void;
  onSave?: (kpiData: KPIFormData, name: string) => void;
  isLoading?: boolean;
}

interface UseKPIExecutionReturn {
  previewData: KPIPreviewData | undefined;
  isExecuting: boolean;
  hasSuccessfulExecution: boolean;
  canSave: boolean;
  execute: (formData: KPIFormData, validateForm: () => boolean) => Promise<void>;
  saveKPI: (formData: KPIFormData, name: string, validateForm: () => boolean) => Promise<void>;
  clearExecution: () => void;
  setValidationError: (error: KPIValidationResult) => void;
}

const useKPIExecution = ({ 
  onExecute, 
  onSave, 
  isLoading = false 
}: UseKPIExecutionProps = {}): UseKPIExecutionReturn => {
  const [previewData, setPreviewData] = useState<KPIPreviewData | undefined>();
  const [isExecuting, setIsExecuting] = useState(false);
  const [hasSuccessfulExecution, setHasSuccessfulExecution] = useState(false);

  const execute = useCallback(async (
    formData: KPIFormData, 
    validateForm: () => boolean
  ) => {
    if (!validateForm()) {
      return;
    }

    setIsExecuting(true);
    try {
      // Execute KPI using the API service
      const apiResponse = await executeKPI({
        viewType: formData.viewType,
        sql: formData.sql,
        additionalInfo: formData.additionalInfo,
        currentLabel: formData.currentLabel,
        priorLabel: formData.priorLabel,
        priorSql: formData.priorSql,
        target: formData.target
      });

      // Convert API response to preview data format
      const previewData: KPIPreviewData = {
        currentValue: apiResponse.value ?? apiResponse.current_value,
        priorValue: apiResponse.prior_value,
        target: apiResponse.target_value,
        label: formData.label,
        additionalInfo: apiResponse.additional_info,
        changePercentage: apiResponse.percent_change,
        achievementPercentage: apiResponse.percent_to_target,
        trend: apiResponse.percent_change ?
          (apiResponse.percent_change > 0 ? 'up' : apiResponse.percent_change < 0 ? 'down' : 'neutral') :
          undefined
      };

      setPreviewData(previewData);
      setHasSuccessfulExecution(true);

      // Call the parent execute callback if provided
      if (onExecute) {
        onExecute(formData);
      }
    } catch (error) {
      console.error('KPI execution failed:', error);
      // Error handling will be done by the parent component
      throw error;
    } finally {
      setIsExecuting(false);
    }
  }, [onExecute]);

  const saveKPICallback = useCallback(async (
    formData: KPIFormData,
    name: string,
    validateForm: () => boolean
  ) => {
    if (!validateForm()) {
      throw new Error('Form validation failed');
    }

    if (!hasSuccessfulExecution) {
      throw new Error('Please execute the KPI first before saving');
    }

    try {
      // Call the API to save the KPI
      const response = await saveKPI(formData, name);

      if (!response.success) {
        throw new Error(response.message || 'Failed to save KPI');
      }

      // Call the parent save callback if provided
      if (onSave) {
        onSave(formData, name);
      }

      return response;
    } catch (error) {
      console.error('KPI save failed:', error);
      throw error;
    }
  }, [onSave, hasSuccessfulExecution]);

  const clearExecution = useCallback(() => {
    setPreviewData(undefined);
    setHasSuccessfulExecution(false);
    setIsExecuting(false);
  }, []);

  const setValidationError = useCallback((error: KPIValidationResult) => {
    // This can be used by parent to set validation errors from execution
    setHasSuccessfulExecution(false);
  }, []);

  const canSave = hasSuccessfulExecution && !isLoading && !isExecuting;

  return {
    previewData,
    isExecuting,
    hasSuccessfulExecution,
    canSave,
    execute,
    saveKPI: saveKPICallback,
    clearExecution,
    setValidationError
  };
};

export default useKPIExecution;
