
import { useState, useCallback } from 'react';
import { useAppDispatch } from '@/hooks/useRedux';
import { ChartDataItem } from '@/services/api/chart/chartTypes';
import { fetchChartData } from '@/services/api/chart';
import { identifyChartKeys, createTablesArray } from '@/components/dashboard/chartboard/chartUtils';
import { setTableData } from '@/stores/chartSlice';
import { toast } from 'sonner';
import { ChartExecutionParams } from './types';
import { FilterCondition } from '@/components/dashboard/chartboard/FilterSection';

export const useChartExecution = () => {
  const dispatch = useAppDispatch();
  const [isLoading, setIsLoading] = useState(false);

  const executeChart = useCallback(async (params: ChartExecutionParams): Promise<{
    data: ChartDataItem[];
    xAxisKey: string;
    yAxisKey: string;
  } | null> => {
    const { xAxisColumn, yAxisColumns, xAxisTable, yAxisTables, aggregationType, connectionId, filterConditions } = params;

    console.log('Chart execution started with params:', params);

    if (!connectionId) {
      console.error('No database connection available');
      toast.error('No database connection available');
      return null;
    }

    // Enhanced validation logic for multi-table scenarios
    const hasValidTables = xAxisTable || yAxisTables.length > 0;
    const hasValidColumns = xAxisColumn || yAxisColumns.length > 0;
    
    if (!hasValidTables) {
      console.error('No tables selected for chart execution');
      toast.error('Please select at least one table');
      return null;
    }
    
    if (aggregationType) {
      // For aggregated queries, require x-axis and y-axis columns
      if (!xAxisColumn || yAxisColumns.length === 0) {
        console.error('Aggregated queries require both x-axis and y-axis columns');
        toast.error('Please select columns for both X and Y axes when using aggregation');
        return null;
      }
    } else if (!hasValidColumns) {
      // For non-aggregated queries, require at least some columns
      console.error('No columns selected for chart execution');
      toast.error('Please select at least one column');
      return null;
    }

    try {
      setIsLoading(true);
      console.log('Fetching chart data via API using session connection:', connectionId);
      
      // Enhanced table creation with better validation
      const tablesSet = new Set<string>();
      if (xAxisTable) {
        tablesSet.add(xAxisTable);
        console.log('Added x-axis table:', xAxisTable);
      }
      yAxisTables.forEach((table, index) => {
        if (table) {
          tablesSet.add(table);
          console.log('Added y-axis table:', table, 'for column:', yAxisColumns[index]);
        }
      });
      
      const tables = Array.from(tablesSet).map(tableName => {
        const columns = [];
        
        // Add X-axis column if it belongs to this table
        if (xAxisTable === tableName && xAxisColumn) {
          columns.push(xAxisColumn);
          console.log('Added x-axis column to table:', tableName, xAxisColumn);
        }
        
        // Add Y-axis columns that belong to this table
        yAxisColumns.forEach((col, index) => {
          if (!col) return; // Skip empty columns
          
          const yTable = yAxisTables[index] || xAxisTable;
          if (yTable === tableName) {
            columns.push(col);
            console.log('Added y-axis column to table:', tableName, col);
          }
        });
        
        return {
          table_name: tableName,
          columns: [...new Set(columns)] // Remove duplicates
        };
      }).filter(table => table.columns.length > 0); // Only include tables with columns
      
      console.log('Final tables array for API call:', tables);
      
      if (tables.length === 0) {
        console.error('No valid tables with columns found');
        toast.error('No valid table-column combinations found');
        return null;
      }
      
      console.log('API call parameters:', {
        connectionId,
        tables,
        aggregationType,
        isAggregatedQuery: Boolean(aggregationType),
        xAxisColumn,
        yAxisColumns
      });
      
      // Convert filter conditions to the new format expected by the API
      const filters = {};
      if (filterConditions && filterConditions.length > 0) {
        filterConditions.forEach(condition => {
          if (condition.column && condition.value) {
            // Format based on operator type
            if (condition.operator === 'in' || condition.operator === 'not_in') {
              // Handle array values for 'in' operator
              const values = condition.value.split(',').map(v => v.trim());
              filters[condition.column] = { [condition.operator]: values };
            } else if (condition.operator === '=') {
              // Handle equals operator
              filters[condition.column] = condition.value;
            } else {
              // Handle all other operators (gt, lt, like, etc.)
              filters[condition.column] = { [condition.operator]: condition.value };
            }
            console.log(`Added filter: ${condition.column} ${condition.operator} ${condition.value}`);
          }
        });
      }

      console.log('Executing chart with filters:', filters);

      // Conditional API call based on aggregation
      let data;
      if (aggregationType) {
        // For aggregated queries, send x_axis, y_axis, and group_by
        const yAxisParams = yAxisColumns.map((col, index) => ({
          column: col,
          table_name: yAxisTables[index] || xAxisTable,
          aggregation: aggregationType
        }));
        
        console.log('Aggregated query parameters:', {
          xAxisColumn,
          yAxisParams,
          groupBy: xAxisColumn,
          filters: Object.keys(filters).length > 0 ? filters : undefined
        });
        
        data = await fetchChartData(
          connectionId,
          tables,
          xAxisColumn,
          yAxisParams,
          xAxisColumn,
          Object.keys(filters).length > 0 ? filters : undefined
        );
      } else {
        // For non-aggregated queries, send only connection_id and tables
        console.log('Non-aggregated query - sending only tables with selected columns and filters');
        
        data = await fetchChartData(
          connectionId,
          tables,
          undefined,
          undefined,
          undefined,
          Object.keys(filters).length > 0 ? filters : undefined
        );
      }
      
      console.log('API response received:', data ? `${data.length} items` : 'null/empty');
      
      if (data && data.length > 0) {
        let xAxisKey = '';
        let yAxisKey = '';
        
        if (aggregationType) {
          // For aggregated data, identify chart keys
          const { xAxisKey: xKey, yAxisKey: yKey } = identifyChartKeys(data, xAxisColumn, yAxisColumns);
          xAxisKey = xKey;
          yAxisKey = yKey;
          console.log('Identified aggregated chart keys:', { xAxisKey, yAxisKey });
        } else {
          // For non-aggregated data, use first available keys
          const keys = Object.keys(data[0]);
          xAxisKey = keys[0] || '';
          yAxisKey = keys[1] || keys[0] || '';
          console.log('Using non-aggregated chart keys:', { xAxisKey, yAxisKey });
        }
        
        // Store data in Redux for the primary table
        const primaryTable = xAxisTable || yAxisTables[0] || 'unknown';
        dispatch(setTableData({ tableName: primaryTable, data }));
        
        toast.success(`Chart data loaded successfully (${data.length} records)`);
        
        return { data, xAxisKey, yAxisKey };
      } else {
        console.error('No data returned from query');
        toast.error('No data returned from query');
        return null;
      }
    } catch (error) {
      console.error('Error executing chart:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      toast.error('Failed to execute chart', {
        description: errorMessage
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [dispatch]);

  return {
    executeChart,
    isLoading
  };
};
