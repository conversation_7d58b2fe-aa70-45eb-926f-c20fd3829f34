import React, { useState } from 'react';
import K<PERSON><PERSON><PERSON><PERSON>ields from './KPIFormFields';
import KPI<PERSON>uilderHeader from './KPIBuilderHeader';
import KPIBuilderActions from './KPIBuilderActions';
import KPIBuilderPreview from './KPIBuilderPreview';
import KPISaveDialog from './KPISaveDialog';
import { KPIBuilderProps } from './types/kpiTypes';
import { useKPIForm, useKPIValidation, useKPIExecution } from './hooks';

const KPIBuilder: React.FC<KPIBuilderProps> = ({
  onSave,
  onExecute,
  onValidateSQL,
  isLoading = false,
  initialData = {}
}) => {
  // State for save dialog
  const [isSaveDialogOpen, setIsSaveDialogOpen] = useState(false);

  // Use custom hooks to manage state and logic
  const validation = useKPIValidation();

  const form = useKPIForm({
    initialData,
    onFormChange: () => {
      // Reset execution state when form changes
      execution.clearExecution();
    },
    onClearValidation: () => {
      // Clear validation state when form changes or clears
      validation.clearValidation();
    }
  });

  const execution = useKPIExecution({
    onExecute,
    onSave,
    isLoading
  });

  // Handle SQL validation
  const handleValidateSQL = async (sql: string, fieldKey: string) => {
    return validation.validateSQL(sql, fieldKey);
  };

  // Handle form execution
  const handleExecute = async () => {
    try {
      await execution.execute(form.data, form.validateForm);
    } catch (error) {
      // Set validation error if execution fails
      console.error('Execution failed:', error);
    }
  };

  // Handle save action - open save dialog
  const handleSave = () => {
    if (!execution.canSave) {
      return;
    }
    setIsSaveDialogOpen(true);
  };

  // Handle actual save with name from dialog
  const handleSaveWithName = async (name: string) => {
    try {
      await execution.saveKPI(form.data, name, form.validateForm);

      // Call parent save callback if provided
      if (onSave) {
        onSave();
      }
    } catch (error) {
      // Error handling is done in the hook and dialog
      throw error;
    }
  };

  return (
    <div className="space-y-6">
      {/* KPI View Type Selector */}
      <KPIBuilderHeader
        selectedView={form.data.viewType}
        onViewChange={form.handleViewTypeChange}
      />

      {/* Form Fields */}
      <KPIFormFields
        formData={form.data}
        onFormChange={form.handleFormChange}
        errors={form.errors}
        onValidateSQL={handleValidateSQL}
        isValidating={validation.isValidating}
        validationResults={validation.validationResults}
        onValidateField={form.validateField}
      />

      {/* Action Buttons */}
      <KPIBuilderActions
        onClear={form.handleClear}
        onExecute={handleExecute}
        onSave={handleSave}
        isExecuting={execution.isExecuting}
        isLoading={isLoading}
        canSave={execution.canSave && (() => {
          // Check required fields based on KPI type
          if (form.data.viewType === 'current-vs-prior') {
            return form.data.currentLabel.trim() !== '' &&
                   form.data.priorLabel.trim() !== '' &&
                   form.data.sql.trim() !== '' &&
                   form.data.priorSql?.trim() !== '';
          } else {
            return form.data.label.trim() !== '' && form.data.sql.trim() !== '';
          }
        })()}
        canExecute={(() => {
          // Step 1: Check if all required form fields are filled
          if (!form.isFormValid) {
            return false;
          }

          // Step 2: Check SQL validation based on KPI type
          if (form.data.viewType === 'current-vs-prior') {
            // Both SQL fields must be validated
            return validation.isFieldValid('sql') && validation.isFieldValid('priorSql');
          } else {
            // Only main SQL field needs validation
            return validation.isFieldValid('sql');
          }
        })()}
      />

      {/* Preview */}
      <KPIBuilderPreview
        formData={form.data}
        previewData={execution.previewData}
        isLoading={execution.isExecuting}
      />

      {/* Save Dialog */}
      <KPISaveDialog
        isOpen={isSaveDialogOpen}
        onClose={() => setIsSaveDialogOpen(false)}
        onSave={handleSaveWithName}
        kpiType={form.data.viewType}
      />
    </div>
  );
};

export default KPIBuilder;
