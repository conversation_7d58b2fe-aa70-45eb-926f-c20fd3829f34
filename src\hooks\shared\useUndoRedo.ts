import { useState, useCallback } from 'react';

interface UndoRedoState<T> {
  past: T[];
  present: T;
  future: T[];
}

export interface UseUndoRedoReturn<T> {
  state: T;
  setState: (newState: T, skipHistory?: boolean) => void;
  undo: () => void;
  redo: () => void;
  canUndo: boolean;
  canRedo: boolean;
  clearHistory: () => void;
  historyLength: number;
}

export function useUndoRedo<T>(initialState: T, maxHistorySize = 50): UseUndoRedoReturn<T> {
  const [undoRedoState, setUndoRedoState] = useState<UndoRedoState<T>>({
    past: [],
    present: initialState,
    future: []
  });

  const setState = useCallback((newState: T, skipHistory = false) => {
    setUndoRedoState(current => {
      if (skipHistory) {
        return {
          ...current,
          present: newState
        };
      }

      const newPast = [...current.past, current.present];
      
      // Limit history size
      if (newPast.length > maxHistorySize) {
        newPast.shift(); // Remove oldest entry
      }

      return {
        past: newPast,
        present: newState,
        future: []
      };
    });
  }, [maxHistorySize]);

  const undo = useCallback(() => {
    setUndoRedoState(current => {
      if (current.past.length === 0) return current;

      const previous = current.past[current.past.length - 1];
      const newPast = current.past.slice(0, current.past.length - 1);

      return {
        past: newPast,
        present: previous,
        future: [current.present, ...current.future]
      };
    });
  }, []);

  const redo = useCallback(() => {
    setUndoRedoState(current => {
      if (current.future.length === 0) return current;

      const next = current.future[0];
      const newFuture = current.future.slice(1);

      return {
        past: [...current.past, current.present],
        present: next,
        future: newFuture
      };
    });
  }, []);

  const clearHistory = useCallback(() => {
    setUndoRedoState(current => ({
      past: [],
      present: current.present,
      future: []
    }));
  }, []);

  return {
    state: undoRedoState.present,
    setState,
    undo,
    redo,
    canUndo: undoRedoState.past.length > 0,
    canRedo: undoRedoState.future.length > 0,
    clearHistory,
    historyLength: undoRedoState.past.length + undoRedoState.future.length
  };
}