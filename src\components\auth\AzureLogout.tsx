import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';

const AzureLogout = () => {
  const { logout } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    // Execute logout
    logout();
    // Redirect to home page
    navigate('/');
  }, [logout, navigate]);

  return <div>Logging out...</div>;
};

export default AzureLogout;