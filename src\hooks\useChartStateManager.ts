
import { useState, useCallback } from 'react';
import { ChartDataItem } from '@/services/api/chart/chartTypes';

interface ChartState {
  chartStyle: 'bar' | 'line' | 'pie' | 'doughnut';
  showChart: boolean;
  xAxisColumn: string;
  yAxisColumns: string[];
  xAxisTable: string;
  yAxisTables: string[];
  combinedChartData: ChartDataItem[];
  chartXAxisKey: string;
  chartYAxisKey: string;
  aggregationType: string | null;
}

export const useChartStateManager = () => {
  const [state, setState] = useState<ChartState>({
    chartStyle: 'bar',
    showChart: false,
    xAxisColumn: '',
    yAxisColumns: [],
    xAxisTable: '',
    yAxisTables: [],
    combinedChartData: [],
    chartXAxisKey: '',
    chartYAxisKey: '',
    aggregationType: null
  });

  const updateChartState = useCallback((updates: Partial<ChartState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  const resetChartState = useCallback(() => {
    setState({
      chartStyle: 'bar',
      showChart: false,
      xAxisColumn: '',
      yAxisColumns: [],
      xAxisTable: '',
      yAxisTables: [],
      combinedChartData: [],
      chartXAxisKey: '',
      chartYAxisKey: '',
      aggregationType: null
    });
  }, []);

  return {
    chartState: state,
    updateChartState,
    resetChartState,
    // Individual state accessors for convenience
    chartStyle: state.chartStyle,
    showChart: state.showChart,
    xAxisColumn: state.xAxisColumn,
    yAxisColumns: state.yAxisColumns,
    combinedChartData: state.combinedChartData
  };
};
