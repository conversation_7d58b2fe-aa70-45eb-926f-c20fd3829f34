import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useDatabaseConnection } from '@/hooks/useDatabaseConnection';

interface DatabaseConnectionFormProps {
  onConnectionCreated?: () => void;
}

export const DatabaseConnectionForm: React.FC<DatabaseConnectionFormProps> = ({ 
  onConnectionCreated 
}) => {
  const {
    dbConnection,
    connectionTested,
    connectionValid,
    isTestingConnection,
    isSubmittingDb,
    handleInputChange,
    testConnection,
    submitConnection,
    validateForm
  } = useDatabaseConnection();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const success = await submitConnection();
    if (success && onConnectionCreated) {
      onConnectionCreated();
    }
  };

  const handleTestConnection = async () => {
    await testConnection();
  };

  return (
    <div className="bg-white p-8 max-w-5xl mx-auto">
      <form onSubmit={handleSubmit} className="space-y-8">

        {/* DB Connection Name */}
        <div className="flex items-center gap-4">
          <Label className="text-sm font-medium w-40">DB Connection Name</Label>
          <Input
            value={dbConnection.connectionName}
            onChange={(e) => handleInputChange('connectionName', e.target.value)}
            placeholder="DB Connection Name"
            className="flex-1 max-w-md"
            required
          />
        </div>

        {/* Parameters Table */}
        <div className="mt-8">
          {/* Table Header */}
          <div className="grid grid-cols-3 gap-8 border-b pb-3 mb-6">
            <div className="text-sm font-medium">Parameter</div>
            <div className="text-sm font-medium">Description</div>
            <div></div>
          </div>

          {/* Host/Server Row */}
          <div className="grid grid-cols-3 gap-8 py-4 border-b items-center">
            <div className="text-sm">Host / Server</div>
            <div className="text-sm text-gray-600">IP address or domain name of the DB server (e.g., localhost, db.company.com)</div>
            <Input
              value={dbConnection.host}
              onChange={(e) => handleInputChange('host', e.target.value)}
              placeholder=""
              className="w-full"
              required
            />
          </div>

          {/* Port Row */}
          <div className="grid grid-cols-3 gap-8 py-4 border-b items-center">
            <div className="text-sm">Port</div>
            <div className="text-sm text-gray-600">Port number on which the DB is listening (default varies by DB)</div>
            <Input
              value={dbConnection.port}
              onChange={(e) => handleInputChange('port', e.target.value)}
              placeholder=""
              className="w-full"
              required
            />
          </div>

          {/* Database Name Row */}
          <div className="grid grid-cols-3 gap-8 py-4 border-b items-center">
            <div className="text-sm">Database Name</div>
            <div className="text-sm text-gray-600">The specific database to connect to on the server</div>
            <Input
              value={dbConnection.databaseName}
              onChange={(e) => handleInputChange('databaseName', e.target.value)}
              placeholder=""
              className="w-full"
              required
            />
          </div>

          {/* Username Row */}
          <div className="grid grid-cols-3 gap-8 py-4 border-b items-center">
            <div className="text-sm">Username</div>
            <div className="text-sm text-gray-600">Database username with access permissions</div>
            <Input
              value={dbConnection.username}
              onChange={(e) => handleInputChange('username', e.target.value)}
              placeholder=""
              className="w-full"
              required
            />
          </div>

          {/* Password Row */}
          <div className="grid grid-cols-3 gap-8 py-4 border-b items-center">
            <div className="text-sm">Password</div>
            <div className="text-sm text-gray-600">Associated password for authentication</div>
            <Input
              type="password"
              value={dbConnection.password}
              onChange={(e) => handleInputChange('password', e.target.value)}
              placeholder=""
              className="w-full"
              required
            />
          </div>

          {/* Driver/Dialect Row */}
          <div className="grid grid-cols-3 gap-8 py-4 items-center">
            <div className="text-sm">Driver / Dialect</div>
            <div className="text-sm text-gray-600">The connector/driver name used in client libraries</div>
            <Select
              value={dbConnection.driverDialect}
              onValueChange={(value) => handleInputChange('driverDialect', value)}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select driver/dialect" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="postgresql">PostgreSQL</SelectItem>
                <SelectItem value="mysql">MySQL</SelectItem>
                <SelectItem value="sqlserver">SQL Server</SelectItem>
                <SelectItem value="oracle">Oracle</SelectItem>
                <SelectItem value="sqlite">SQLite</SelectItem>
                <SelectItem value="snowflake">Snowflake</SelectItem>
                <SelectItem value="databricks">Databricks</SelectItem>
                <SelectItem value="salesforce">Salesforce</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Connection Status */}
        {connectionTested && (
          <div className="mt-4 p-4 rounded-lg border">
            <div className={`text-sm font-medium ${
              connectionValid ? 'text-green-600' : 'text-red-600'
            }`}>
              {connectionValid ? '✅ Connection Valid' : '❌ Connection Failed'}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end gap-3 pt-8">
          <Button
            type="button"
            onClick={handleTestConnection}
            disabled={isTestingConnection || isSubmittingDb}
            className="bg-gray-600 text-white hover:bg-gray-700 px-6"
          >
            {isTestingConnection ? 'Testing...' : 'Test Database Connection'}
          </Button>
          <Button
            type="submit"
            disabled={isSubmittingDb || isTestingConnection}
            className="bg-gray-600 text-white hover:bg-gray-700 px-6"
          >
            {isSubmittingDb ? 'Saving...' : 'Save'}
          </Button>
          <Button
            type="button"
            disabled={isSubmittingDb || isTestingConnection}
            variant="outline"
            className="px-6"
          >
            Cancel
          </Button>
        </div>
      </form>
    </div>
  );
};
