
import { ChainStage, PredictionParams } from '@/services/api/predictions/types';

export interface UsePredictionsProps {
  inputValue: string;
  dashboardType?: 1 | 2 | 3; // Add dashboard type prop to enable/disable predictions based on dashboard
  powerModeEnabled?: boolean; // New prop for controlling power mode
}

export interface UsePredictionsReturn {
  predictions: string[];
  showPredictions: boolean;
  setShowPredictions: (show: boolean) => void;
  isChainSequence: boolean;
  chainStage: ChainStage;
  lastSelectedPrediction: string;
  handlePredictionSelect: (selectedPrediction: string, setInputValue: (value: string) => void) => void;
  isPredictionsEnabled: boolean;
  enablePowerMode: () => void;
  togglePowerMode: (enabled: boolean) => void;
}
