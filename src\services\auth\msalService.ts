import { PublicClientApplication } from '@azure/msal-browser';
import { msalConfig } from './msalConfig';

// Initialize MSAL outside of component render
// const msalInstance = new PublicClientApplication(msalConfig);
// Comment out the actual initialization to prevent the crypto error
const msalInstance = null; // Use null instead of actual initialization

// Initialize once at startup
/* 
msalInstance.initialize().then(() => {
  console.log('MSAL initialized successfully');
}).catch(error => {
  console.error('MSAL initialization failed', error);
});
*/
console.log('MSAL initialization disabled');

// Add this at the top of the file to log the API URL
console.log("SSO API URL:", import.meta.env.VITE_SSO_API_URL);

// Validate token with backend
export const validateAzureToken = async (token: string): Promise<boolean> => {
  console.log('Azure token validation disabled');
  return false;
};

// Add a function to get a fresh token when needed
export const getAccessToken = async (): Promise<string | null> => {
  console.log('Getting access token disabled');
  return null;
};

// Add this function to handle different origins
export const handleRedirectAfterLogin = () => {
  return false;
};

// Create a mock msalInstance with empty methods to prevent errors
export { msalInstance };





// for production we can enbale the below code 

/*
import { PublicClientApplication } from '@azure/msal-browser';
import { msalConfig } from './msalConfig';

// Initialize MSAL outside of component render
const msalInstance = new PublicClientApplication(msalConfig);

// Initialize once at startup
msalInstance.initialize().then(() => {
  console.log('MSAL initialized successfully');
}).catch(error => {
  console.error('MSAL initialization failed', error);
});

// Add this at the top of the file to log the API URL
console.log("SSO API URL:", import.meta.env.VITE_SSO_API_URL);

// Validate token with backend
export const validateAzureToken = async (token: string): Promise<boolean> => {
  try {
    const apiUrl = `${import.meta.env.VITE_SSO_API_URL}/api/auth/azure/validate`;
    console.log("Full validation URL:", apiUrl);
    
    // Send token in the request body
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify({ token }), // Make sure token is sent in the expected format
    });

    // Handle non-200 responses properly
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Token validation failed with status ${response.status}:`, errorText);
      return false;
    }

    const data = await response.json();
    return true;
  } catch (error) {
    console.error('Error validating Azure token:', error);
    return false;
  }
};

// Add a function to get a fresh token when needed
export const getAccessToken = async (): Promise<string | null> => {
  try {
    const accounts = msalInstance.getAllAccounts();
    if (accounts.length === 0) {
      console.error("No accounts found");
      return null;
    }

    const silentRequest = {
      scopes: ["User.Read"],
      account: accounts[0]
    };

    const response = await msalInstance.acquireTokenSilent(silentRequest);
    return response.accessToken;
  } catch (error) {
    console.error("Error getting access token:", error);
    return null;
  }
};

// Add this function to handle different origins
export const handleRedirectAfterLogin = () => {
  const currentUrl = window.location.href;
  const ipBasedUrl = currentUrl.includes('***********');
  
  if (ipBasedUrl) {
    // Redirect to an allowed URL after authentication
    const allowedRedirectUrl = import.meta.env.VITE_ALLOWED_REDIRECT_URL || window.location.origin;
    window.location.href = allowedRedirectUrl;
    return true;
  }
  return false;
};

export { msalInstance };*/









