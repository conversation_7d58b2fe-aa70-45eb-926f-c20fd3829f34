import { describe, it, expect, beforeEach } from 'vitest';
import { configureStore } from '@reduxjs/toolkit';
import dadaReducer, { 
  setIsFileMode, 
  setFileSessionId, 
  setUploadedFiles, 
  clearFileSession 
} from '@/stores/dadaSlice';

describe('File Mode Management', () => {
  let store: any;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        dada: dadaReducer
      }
    });
  });

  it('should set file mode to true when files are uploaded', () => {
    // Simulate file upload
    store.dispatch(setFileSessionId('test-session-123'));
    store.dispatch(setUploadedFiles([new File(['test'], 'test.csv', { type: 'text/csv' })]));
    store.dispatch(setIsFileMode(true));

    const state = store.getState();
    expect(state.dada.isFileMode).toBe(true);
    expect(state.dada.fileSessionId).toBe('test-session-123');
    expect(state.dada.uploadedFiles).toHaveLength(1);
  });

  it('should clear file mode when clearFileSession is called', () => {
    // First set up file mode
    store.dispatch(setFileSessionId('test-session-123'));
    store.dispatch(setUploadedFiles([new File(['test'], 'test.csv', { type: 'text/csv' })]));
    store.dispatch(setIsFileMode(true));

    // Verify it's set
    let state = store.getState();
    expect(state.dada.isFileMode).toBe(true);
    expect(state.dada.fileSessionId).toBe('test-session-123');
    expect(state.dada.uploadedFiles).toHaveLength(1);

    // Clear file session
    store.dispatch(clearFileSession());

    // Verify it's cleared
    state = store.getState();
    expect(state.dada.isFileMode).toBe(false);
    expect(state.dada.fileSessionId).toBe(null);
    expect(state.dada.uploadedFiles).toHaveLength(0);
  });

  it('should clear file session when setIsFileMode is set to false', () => {
    // First set up file mode
    store.dispatch(setFileSessionId('test-session-123'));
    store.dispatch(setUploadedFiles([new File(['test'], 'test.csv', { type: 'text/csv' })]));
    store.dispatch(setIsFileMode(true));

    // Verify it's set
    let state = store.getState();
    expect(state.dada.isFileMode).toBe(true);
    expect(state.dada.fileSessionId).toBe('test-session-123');
    expect(state.dada.uploadedFiles).toHaveLength(1);

    // Set file mode to false
    store.dispatch(setIsFileMode(false));

    // Verify file session is cleared
    state = store.getState();
    expect(state.dada.isFileMode).toBe(false);
    expect(state.dada.fileSessionId).toBe(null);
    expect(state.dada.uploadedFiles).toHaveLength(0);
  });

  it('should maintain correct API selection based on file mode state', () => {
    // Test that isFileMode correctly determines API selection

    // Initially should be false (default query mode)
    let state = store.getState();
    expect(state.dada.isFileMode).toBe(false);

    // Simulate file upload (sets file mode to true)
    store.dispatch(setFileSessionId('test-session-456'));
    store.dispatch(setIsFileMode(true));

    state = store.getState();
    expect(state.dada.isFileMode).toBe(true);
    expect(state.dada.fileSessionId).toBe('test-session-456');

    // Simulate mode switch (clears file mode)
    store.dispatch(clearFileSession());

    state = store.getState();
    expect(state.dada.isFileMode).toBe(false);
    expect(state.dada.fileSessionId).toBe(null);
  });
});
