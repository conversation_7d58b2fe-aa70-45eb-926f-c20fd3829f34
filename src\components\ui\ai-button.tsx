import React from 'react';
import { Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface AIButtonProps {
  onClick: () => void;
  disabled?: boolean;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export const AIButton: React.FC<AIButtonProps> = ({ 
  onClick, 
  disabled = false, 
  className = '',
  size = 'md'
}) => {
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-5 py-3 text-lg'
  };

  return (
    <Button
      onClick={onClick}
      disabled={disabled}
      variant="outline"
      className={`
        relative overflow-hidden
        bg-white hover:bg-gray-50
        text-gray-500 hover:text-gray-600
        font-medium
        rounded-full
        border-2 border-gray-200 hover:border-gray-300
        shadow-sm hover:shadow-md
        transition-all duration-200
        hover:scale-105
        active:scale-95
        disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100
        ${sizeClasses[size]}
        ${className}
      `}
      style={{
        backgroundColor: 'white',
        color: '#9CA3AF',
        borderColor: '#E5E7EB'
      }}
    >
      {/* Content */}
      <div className="flex items-center gap-2">
        <Sparkles className="w-4 h-4 text-gray-500" />
        <span className="text-gray-500 font-medium">AI</span>
      </div>
    </Button>
  );
};

export default AIButton;
