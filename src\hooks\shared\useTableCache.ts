import { useCallback, useEffect, useRef } from 'react';
import { TableDataCache } from '@/utils/cache/TableDataCache';
import { TableWithColumns, ColumnInfo } from '@/services/api/chart/chartTypes';

// Global cache instances
const tablesCache = new TableDataCache<TableWithColumns[]>(50, 10 * 60 * 1000); // 10 minutes
const columnsCache = new TableDataCache<ColumnInfo[]>(200, 15 * 60 * 1000); // 15 minutes

export interface UseTableCacheReturn {
  getCachedTables: (connectionId: string) => TableWithColumns[] | null;
  setCachedTables: (connectionId: string, tables: TableWithColumns[]) => void;
  getCachedColumns: (connectionId: string, tableName: string) => ColumnInfo[] | null;
  setCachedColumns: (connectionId: string, tableName: string, columns: ColumnInfo[]) => void;
  invalidateConnection: (connectionId: string) => void;
  invalidateTable: (connectionId: string, tableName: string) => void;
  clearAllCaches: () => void;
}

export const useTableCache = (): UseTableCacheReturn => {
  const cleanupIntervalRef = useRef<NodeJS.Timeout>();

  // Set up periodic cleanup
  useEffect(() => {
    cleanupIntervalRef.current = setInterval(() => {
      tablesCache.cleanup();
      columnsCache.cleanup();
    }, 5 * 60 * 1000); // Cleanup every 5 minutes

    return () => {
      if (cleanupIntervalRef.current) {
        clearInterval(cleanupIntervalRef.current);
      }
    };
  }, []);

  const getCachedTables = useCallback((connectionId: string): TableWithColumns[] | null => {
    return tablesCache.get(connectionId);
  }, []);

  const setCachedTables = useCallback((connectionId: string, tables: TableWithColumns[]) => {
    tablesCache.set(connectionId, tables);
  }, []);

  const getCachedColumns = useCallback((connectionId: string, tableName: string): ColumnInfo[] | null => {
    return columnsCache.get(connectionId, tableName);
  }, []);

  const setCachedColumns = useCallback((connectionId: string, tableName: string, columns: ColumnInfo[]) => {
    columnsCache.set(connectionId, columns, tableName);
  }, []);

  const invalidateConnection = useCallback((connectionId: string) => {
    tablesCache.invalidate(connectionId);
    columnsCache.invalidate(connectionId);
  }, []);

  const invalidateTable = useCallback((connectionId: string, tableName: string) => {
    columnsCache.invalidate(connectionId, tableName);
  }, []);

  const clearAllCaches = useCallback(() => {
    tablesCache.clear();
    columnsCache.clear();
  }, []);

  return {
    getCachedTables,
    setCachedTables,
    getCachedColumns,
    setCachedColumns,
    invalidateConnection,
    invalidateTable,
    clearAllCaches
  };
};