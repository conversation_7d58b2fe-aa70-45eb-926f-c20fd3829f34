
import { useState, useEffect, useRef } from 'react';
import { fetchCharts, fetchChartById, fetchChartData, incrementChartViews } from '@/services/api/chart';
import { toast } from 'sonner';
import { SavedChart, ChartDataPoint } from '@/types/chartTypes';
import { useConnectionManager } from './useConnectionManager';

export const useChartsData = () => {
  const { getOrCreateConnection } = useConnectionManager();
  const [charts, setCharts] = useState<SavedChart[]>([]);
  const [loading, setLoading] = useState(true);
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
  const [loadingChartData, setLoadingChartData] = useState(false);
  // Track which charts have had their views incremented in this session
  const viewedCharts = useRef<Set<string>>(new Set());

  const loadCharts = async () => {
    try {
      setLoading(true);
      const response = await fetchCharts();
      console.log("Charts loaded:", response);
      
      if (response.charts && response.charts.length > 0) {
        console.log("First chart has image:", !!response.charts[0].chart_image);
      }
      
      setCharts(response.charts || []);
    } catch (error) {
      console.error('Error loading charts:', error);
      toast.error('Failed to load saved charts');
    } finally {
      setLoading(false);
    }
  };

  const loadChartById = async (chartId: string) => {
    try {
      setLoading(true);
      console.log("Fetching chart by ID:", chartId);
      
      const response = await fetchChartById(chartId);
      console.log("Full API response:", response);
      
      let chartData = null;
      
      if (response.chart) {
        chartData = response.chart;
        console.log("Found chart data in response.chart");
      } else if (response.id || response.chart_id) {
        chartData = response;
        console.log("Response itself is the chart object");
      } else {
        console.error("Unexpected API response format:", response);
        toast.error('Invalid chart data format received');
        return null;
      }
      
      if (!chartData) {
        toast.error('Chart not found');
        return null;
      }
      
      return chartData;
    } catch (error) {
      console.error('Error fetching chart by ID:', error);
      toast.error('Failed to load chart: ' + (error instanceof Error ? error.message : 'Unknown error'));
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Modified function to increment chart views on every click
  const safeIncrementChartViews = async (chartId: string): Promise<boolean> => {
    if (!chartId) return false;
    
    try {
      console.log(`Incrementing views for chart ${chartId}`);
      await incrementChartViews(chartId);
      return true;
    } catch (error) {
      console.error('Error incrementing chart views:', error);
      return false;
    }
  };

  const loadChartData = async (selectedChart: SavedChart, connectionId: string) => {
    try {
      console.log("Loading chart data for:", selectedChart.chart_name);
      
      // Priority 1: Use chart data from chart_response
      if (selectedChart.chart_response && selectedChart.chart_response.data) {
        console.log("Using chart data from chart_response:", selectedChart.chart_response.data.length, "items");
        const chartResponseData = selectedChart.chart_response.data;
        setChartData(chartResponseData);
        return chartResponseData;
      }
      
      // Priority 2: Use chart data from chart_data
      if (selectedChart.chart_data && selectedChart.chart_data.length > 0) {
        console.log("Using chart data from chart_data:", selectedChart.chart_data.length, "items");
        setChartData(selectedChart.chart_data);
        return selectedChart.chart_data;
      }
      
      // Priority 3: Only make API call if no existing data
      console.log("No existing chart data found, fetching from API");
      setLoadingChartData(true);
      
      const tables = selectedChart.tables || [];
      console.log("Fetching chart data with tables:", tables);
      
      const yAxisParams = Array.isArray(selectedChart.y_axis) 
        ? selectedChart.y_axis 
        : [{ column: selectedChart.y_axis, aggregation: null }];
      
      let groupBy;
      if (selectedChart.group_by) {
        groupBy = Array.isArray(selectedChart.group_by) 
          ? selectedChart.group_by[0] 
          : selectedChart.group_by;
      } else if (yAxisParams[0]?.aggregation) {
        groupBy = selectedChart.x_axis;
      }
      
      const data = await fetchChartData(
        connectionId, 
        tables,
        selectedChart.x_axis,
        yAxisParams,
        groupBy
      );
      
      if (data) {
        console.log("Chart data loaded from API:", data);
        setChartData(data);
        return data;
      }
      
      return [];
    } catch (error) {
      console.error('Error loading chart data:', error);
      toast.error('Failed to load chart data');
      return [];
    } finally {
      setLoadingChartData(false);
    }
  };

  const establishConnection = async (selectedChart: SavedChart, selectedDatabase: string) => {
    try {
      setLoadingChartData(true);
      
      const dbType = selectedChart.db_type || selectedDatabase;
      
      if (!dbType) {
        toast.error('No database type available for this chart');
        return null;
      }
      
      console.log("Establishing connection to database:", dbType);
      
      // Use connection manager to get or create connection
      const connectionId = await getOrCreateConnection(dbType, true);
      
      console.log("Connection established:", connectionId);
      return connectionId;
    } catch (error) {
      console.error('Error establishing connection:', error);
      toast.error('Failed to connect to database');
      return null;
    } finally {
      setLoadingChartData(false);
    }
  };

  useEffect(() => {
    loadCharts();
  }, []);

  return {
    charts,
    loading,
    chartData,
    loadingChartData,
    setChartData,
    loadCharts,
    loadChartById,
    loadChartData,
    establishConnection,
    safeIncrementChartViews // Export the new function
  };
};
