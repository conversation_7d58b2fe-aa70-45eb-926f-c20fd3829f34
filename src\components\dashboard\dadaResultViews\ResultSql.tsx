import { Copy, Check } from "lucide-react"
import { useState } from "react"
import { toast } from "sonner"
import type { QueryResultData } from "@/components/dashboard/models"

interface ResultSqlProps {
  size?: "large" | "default" | "small"
  database?: string
  queryResult?: QueryResultData | null
  isPowerQuery?: boolean
}

export const ResultSql = ({ size = "default", database = "SQL", queryResult, isPowerQuery = false }: ResultSqlProps) => {
  const [copied, setCopied] = useState(false)

  let padding = "p-3"
  let textSize = "text-sm"

  if (size === "small") {
    padding = "p-2"
    textSize = "text-xs"
  } else if (size === "large") {
    padding = "p-5"
    textSize = "text-base"
  }

  const formatSqlQuery = (query: string): string => {
    // Basic SQL formatting
    return query
      .replace(/\s+/g, ' ')  // normalize spaces
      .replace(/\s*([,])\s*/g, '$1 ')  // handle commas
      .replace(/\b(SELECT|FROM|WHERE|GROUP BY|HAVING|ORDER BY|INNER JOIN|LEFT JOIN|RIGHT JOIN|ON)\b/gi, '\n$1')
      .replace(/\n(\s*)(FROM|WHERE|GROUP BY|HAVING|ORDER BY)\b/gi, '\n$1 $2')
      .trim();
  }

  const getSqlQuery = () => {
    if (queryResult?.query) {
      return formatSqlQuery(queryResult.query);
    }
    
    if (isPowerQuery) {
      return generatePowerQuerySql();
    }
    
    return getSqlQueryPlaceholder(database);
  }

  const generatePowerQuerySql = () => {
    return `-- Power Query SQL translation
SELECT patient.*
  FROM patients AS patient
 WHERE patient.id = 1
 ORDER BY patient.admission_date DESC;`;
  }

  const sqlQuery = getSqlQuery();

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(sqlQuery)
      setCopied(true)
      toast.success("SQL query copied to clipboard")
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      toast.error("Failed to copy SQL query")
    }
  }

  return (
    <div className="relative">
      <div className={`bg-gray-900 text-gray-100 ${padding} rounded font-mono ${textSize} overflow-x-auto whitespace-pre-wrap`}>
        <button
          onClick={handleCopy}
          className="absolute top-2 right-2 p-1.5 rounded-md bg-gray-800 hover:bg-gray-700"
          aria-label="Copy SQL query"
        >
          {copied ? (
            <Check className="h-4 w-4 text-green-400" />
          ) : (
            <Copy className="h-4 w-4 text-gray-400" />
          )}
        </button>
        <pre className="whitespace-pre-wrap break-words">{sqlQuery}</pre>
        {size === "large" && (
          <div className="mt-4 pt-4 border-t border-gray-700">
            <h4 className="text-gray-300 mb-2">Query Explanation:</h4>
            <p className="text-gray-400">
              {isPowerQuery
                ? "This query retrieves patient data based on the specified patient ID. It pulls all patient information and orders records by admission date."
                : "This query retrieves data based on your input. It joins necessary tables, applies appropriate filters, and formats the results to answer your question."
              }
            </p>
          </div>
        )}
      </div>
    </div>
  )
}

function getSqlQueryPlaceholder(database: string): string {
  switch (database) {
    case "PostgreSQL":
      return `-- PostgreSQL query would appear here
SELECT *
  FROM table_name
 WHERE condition;`;
    case "Oracle":
      return `-- Oracle query would appear here
SELECT *
  FROM table_name
 WHERE condition;`;
    case "MongoDB":
      return `// MongoDB query would appear here
db.collection.find({
    field: value
});`;
    case "Pinecone":
      return `// Pinecone vector query would appear here
const results = await pineconeIndex.query({
    vector: embedding,
    topK: 10,
    filter: { field: value }
});`;
    default:
      return `-- SQL query would appear here
SELECT *
  FROM table_name
 WHERE condition;`;
  }
}
