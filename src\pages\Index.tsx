
import React, { useEffect, CSSProperties } from 'react';
import { Link } from 'react-router-dom';
import Navigation from '@/components/ui/navigation';
import Hero from '@/components/sections/hero';
import Footer from '@/components/sections/footer';

// Add this CSS class specifically for the landing page with proper TypeScript types
const landingPageStyle: CSSProperties = {
  minHeight: '100vh',
  display: 'flex',
  flexDirection: 'column',
  overflowY: 'auto', // Enable vertical scrolling
  overflowX: 'hidden'
};

const Index = () => {
  // Add this effect to toggle the landing-page class on the body
  useEffect(() => {
    // Add landing-page class to body when component mounts
    document.body.classList.add('landing-page');
    
    // Remove the class when component unmounts
    return () => {
      document.body.classList.remove('landing-page');
    };
  }, []);

  // Smooth scroll for anchor links
  useEffect(() => {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function(e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href') || '');
        if (target) {
          window.scrollTo({
            top: target.getBoundingClientRect().top + window.scrollY - 80,
            behavior: 'smooth'
          });
        }
      });
    });
    
    return () => {
      document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.removeEventListener('click', function() {});
      });
    };
  }, []);

  return (
    <div style={landingPageStyle}>
      <Navigation />
      <main className="flex-1">
        <Hero />
      </main>
      <Footer />
    </div>
  );
};

export default Index;
