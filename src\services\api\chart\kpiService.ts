import { centralApiClient } from '@/services/api/centralApiClient';

// KPI API Request Interface
export interface KPIExecuteRequest {
  connection_id: string;
  kpi_type: string;
  // For KPI Only
  sql?: string;
  // For CurrentVsPrior
  current_label?: string;
  prior_label?: string;
  current_sql?: string;
  prior_sql?: string;
  // For TargetBased
  target_value?: number;
  // Common
  additional_info: string;
}

// KPI API Response Interface
export interface KPIExecuteResponse {
  kpi_type: string;
  // For KPI Only
  value?: number;
  // For CurrentVsPrior
  current_label?: string;
  prior_label?: string;
  current_value?: number;
  prior_value?: number;
  percent_change?: number;
  // For TargetBased
  target_value?: number;
  percent_to_target?: number;
  // Common
  additional_info: string;
}

// KPI SQL Validation API Response Interface
export interface KPIValidationAPIResponse {
  status: 'success' | 'error';
  message: string;
}

// KPI Save Request Interfaces
export interface KPISaveRequestBase {
  name: string;
  additional_info: string;
}

export interface KPISaveRequestKPIOnly extends KPISaveRequestBase {
  kpi_label: string;
  sql: string;
}

export interface KPISaveRequestCurrentVsPrior extends KPISaveRequestBase {
  kpi_prior_label: string;
  kpi_current_label: string;
  kpi_current_sql: string;
  kpi_prior_sql: string;
}

export interface KPISaveRequestTargetBased extends KPISaveRequestBase {
  kpi_label: string;
  sql: string;
  kpi_target: number;
}

export type KPISaveRequest = KPISaveRequestKPIOnly | KPISaveRequestCurrentVsPrior | KPISaveRequestTargetBased;

// KPI Save Response Interface
export interface KPISaveResponse {
  success: boolean;
  message: string;
  kpi_id?: string;
}

// Helper function to get connection_id from session storage
const getConnectionIdFromSession = (): string | null => {
  try {
    // Use the same session key as chart builder
    const sessionConnection = sessionStorage.getItem('chartbuilder_session_connection');
    if (sessionConnection) {
      console.log('KPI Service: Using session connection:', sessionConnection);
      return sessionConnection;
    }
  } catch (error) {
    console.error('KPI Service: Failed to get session connection:', error);
  }
  return null;
};

// Helper function to safely parse API response
const parseKPISaveResponse = (response: unknown): KPISaveResponse => {
  console.log('KPI Service: Parsing response:', response);
  console.log('KPI Service: Response type:', typeof response);

  // Handle null/undefined response
  if (!response) {
    return {
      success: true,
      message: 'KPI saved successfully'
    };
  }

  // Handle string response
  if (typeof response === 'string') {
    return {
      success: true,
      message: response
    };
  }

  // Handle object response
  if (typeof response === 'object') {
    const responseObj = response as Record<string, any>;
    console.log('KPI Service: Response keys:', Object.keys(responseObj));

    // If response has success field, use it directly
    if ('success' in responseObj && typeof responseObj.success === 'boolean') {
      return {
        success: responseObj.success,
        message: responseObj.message || (responseObj.success ? 'KPI saved successfully' : 'Failed to save KPI'),
        kpi_id: responseObj.kpi_id || responseObj.id
      };
    }

    // If no success field, assume success and extract available fields
    return {
      success: true,
      message: responseObj.message || 'KPI saved successfully',
      kpi_id: responseObj.kpi_id || responseObj.id
    };
  }

  // Fallback for any other type
  return {
    success: true,
    message: 'KPI saved successfully'
  };
};

// Helper function to map KPI view type to API format
const mapKPITypeToAPI = (viewType: string): string => {
  switch (viewType) {
    case 'kpi-only':
      return 'KPI Only';
    case 'current-vs-prior':
      return 'KPI CurrentVsPrior';
    case 'target-based':
      return 'KPI TargetBased';
    default:
      return 'KPI Only';
  }
};

/**
 * Executes a KPI query using the /kpi/execute endpoint
 * @param kpiType The type of KPI (kpi-only, current-vs-prior, target-based)
 * @param sql The SQL query to execute
 * @param additionalInfo Additional information for the KPI
 * @param connectionId Optional connection ID (will use session storage if not provided)
 * @returns Promise with KPI execution response
 */
export const executeKPI = async (
  formData: {
    viewType: string;
    sql: string;
    additionalInfo: string;
    currentLabel?: string;
    priorLabel?: string;
    priorSql?: string;
    target?: string;
  },
  connectionId?: string
): Promise<KPIExecuteResponse> => {
  try {
    // Get connection ID from parameter or session storage
    const finalConnectionId = connectionId || getConnectionIdFromSession();

    if (!finalConnectionId) {
      throw new Error('No connection ID available. Please ensure you are connected to a database.');
    }

    // Build request body based on KPI type
    const baseRequestBody = {
      connection_id: finalConnectionId,
      kpi_type: mapKPITypeToAPI(formData.viewType),
      additional_info: formData.additionalInfo || ''
    };

    let requestBody: KPIExecuteRequest;

    if (formData.viewType === 'kpi-only') {
      // For KPI Only - only include sql field
      requestBody = {
        ...baseRequestBody,
        sql: formData.sql.trim()
      };
    } else if (formData.viewType === 'current-vs-prior') {
      // For CurrentVsPrior - include current/prior fields
      requestBody = {
        ...baseRequestBody,
        current_label: formData.currentLabel || '',
        prior_label: formData.priorLabel || '',
        current_sql: formData.sql.trim(),
        prior_sql: formData.priorSql?.trim() || ''
      };
    } else if (formData.viewType === 'target-based') {
      // For KPI TargetBased - include sql and target_value
      const targetValue = formData.target ? parseFloat(formData.target.toString()) : 0;

      requestBody = {
        ...baseRequestBody,
        sql: formData.sql.trim(),
        target_value: targetValue
      };
    } else {
      // For other types
      requestBody = {
        ...baseRequestBody,
        sql: formData.sql.trim()
      };
    }

    console.log('KPI Service: Executing KPI with request:', JSON.stringify(requestBody, null, 2));

    // Make API call
    const response = await centralApiClient.makeRequest('chart', '/kpi/execute', {
      method: 'POST',
      body: requestBody
    });

    console.log('KPI Service: Received response:', response);

    // Type assertion and validation
    const data = response as KPIExecuteResponse;

    if (!data || !data.kpi_type) {
      throw new Error('Invalid response format from KPI API');
    }

    return data;
  } catch (error) {
    console.error('KPI Service: Error executing KPI:', error);
    throw error;
  }
};

/**
 * Validates KPI SQL query using the API endpoint
 * @param sql The SQL query to validate
 * @param connectionId Optional connection ID (will use session storage if not provided)
 * @returns Promise with validation result
 */
export const validateKPISQL = async (
  sql: string,
  connectionId?: string
): Promise<{ isValid: boolean; message?: string }> => {
  try {
    // Basic client-side validation first
    const trimmedSQL = sql.trim();

    if (!trimmedSQL) {
      return {
        isValid: false,
        message: 'SQL query cannot be empty'
      };
    }

    // Get connection ID from parameter or session storage
    const finalConnectionId = connectionId || getConnectionIdFromSession();

    if (!finalConnectionId) {
      return {
        isValid: false,
        message: 'No connection ID available. Please ensure you are connected to a database.'
      };
    }

    console.log('KPI Service: Validating SQL query:', trimmedSQL);

    // Encode SQL query for URL
    const encodedSQL = encodeURIComponent(trimmedSQL);

    // Make API call to validate SQL with query parameter in URL
    const response = await centralApiClient.makeRequest('chart', `/kpi/validate-sql?sql=${encodedSQL}`, {
      method: 'POST'
    });

    console.log('KPI Service: Validation response:', response);

    // Type assertion and validation
    const data = response as KPIValidationAPIResponse;

    if (!data || !data.status) {
      throw new Error('Invalid response format from validation API');
    }

    // Return standardized format
    return {
      isValid: data.status === 'success',
      message: data.message
    };
  } catch (error) {
    console.error('KPI Service: Error validating SQL:', error);

    // Handle different types of errors
    if (error instanceof Error) {
      return {
        isValid: false,
        message: error.message
      };
    }

    return {
      isValid: false,
      message: 'Error validating SQL query. Please try again.'
    };
  }
};

/**
 * Saves a KPI using the /kpi/save endpoint
 * @param formData The KPI form data to save
 * @param name The name for the saved KPI
 * @returns Promise with KPI save response
 */
export const saveKPI = async (
  formData: {
    viewType: string;
    label: string;
    sql: string;
    additionalInfo: string;
    currentLabel?: string;
    priorLabel?: string;
    priorSql?: string;
    target?: string;
  },
  name: string
): Promise<KPISaveResponse> => {
  try {
    let requestBody: KPISaveRequest;

    if (formData.viewType === 'kpi-only') {
      // KPI Only request body
      requestBody = {
        name: name.trim(),
        kpi_label: formData.label.trim(),
        sql: formData.sql.trim(),
        additional_info: formData.additionalInfo.trim()
      } as KPISaveRequestKPIOnly;
    } else if (formData.viewType === 'current-vs-prior') {
      // Current vs Prior request body
      requestBody = {
        name: name.trim(),
        additional_info: formData.additionalInfo.trim(),
        kpi_prior_label: formData.priorLabel?.trim() || '',
        kpi_current_label: formData.currentLabel?.trim() || '',
        kpi_current_sql: formData.sql.trim(),
        kpi_prior_sql: formData.priorSql?.trim() || ''
      } as KPISaveRequestCurrentVsPrior;
    } else if (formData.viewType === 'target-based') {
      // Target Based request body
      const targetValue = formData.target ? parseFloat(formData.target.toString()) : 0;

      requestBody = {
        name: name.trim(),
        kpi_label: formData.label.trim(),
        sql: formData.sql.trim(),
        additional_info: formData.additionalInfo.trim(),
        kpi_target: targetValue
      } as KPISaveRequestTargetBased;
    } else {
      throw new Error(`Unsupported KPI type: ${formData.viewType}`);
    }

    console.log('KPI Service: Saving KPI with request:', JSON.stringify(requestBody, null, 2));

    // Make API call
    const response = await centralApiClient.makeRequest('chart', '/kpi/save', {
      method: 'POST',
      body: requestBody
    });

    // Use helper function to safely parse the response
    const data = parseKPISaveResponse(response);

    console.log('KPI Service: Processed save response:', data);
    return data;
  } catch (error) {
    console.error('KPI Service: Error saving KPI:', error);
    throw error;
  }
};
