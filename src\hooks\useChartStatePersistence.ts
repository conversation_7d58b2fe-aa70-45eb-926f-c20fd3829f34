
import { useEffect, useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from './useRedux';
import { 
  clearEditingChartData, 
  selectEditingChartData,
  setEditingChartData
} from '@/stores/chartSlice';

export const useChartStatePersistence = () => {
  const location = useLocation();
  const dispatch = useAppDispatch();
  const editingChartData = useAppSelector(selectEditingChartData);

  // Check if we have editing chart data available
  const hasEditingData = useCallback(() => {
    return editingChartData !== null;
  }, [editingChartData]);

  // Get editing chart data
  const getEditingData = useCallback(() => {
    return editingChartData;
  }, [editingChartData]);

  // Add a function to update editing data
  const updateEditingData = useCallback((updates: any) => {
    if (editingChartData) {
      // Create a new object with the updates
      const updatedData = {
        ...editingChartData,
        ...updates
      };
      dispatch(setEditingChartData(updatedData));
    }
  }, [editingChartData, dispatch]);

  // Add a function to clear specific fields in editing data
  const clearEditingDataFields = useCallback((fields: string[]) => {
    if (editingChartData) {
      // Create a copy of the editing data
      const updatedData = { ...editingChartData };
      
      // Remove the specified fields
      fields.forEach(field => {
        if (field in updatedData) {
          delete updatedData[field];
        }
      });
      
      dispatch(setEditingChartData(updatedData));
    }
  }, [editingChartData, dispatch]);

  // Clear editing data when leaving chart builder
  useEffect(() => {
    // If we're not on the chart builder page and we have editing data, clear it
    if (location.pathname !== '/chartbuilder' && editingChartData) {
      console.log('Leaving chart builder, clearing editing chart data');
      dispatch(clearEditingChartData());
    }
  }, [location.pathname, editingChartData, dispatch]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Clear editing data when component unmounts
      if (editingChartData) {
        dispatch(clearEditingChartData());
      }
    };
  }, []);

  return {
    hasEditingData,
    getEditingData,
    updateEditingData,
    clearEditingDataFields,
    editingChartData
  };
};
