import React from 'react';
import { Button } from '@/components/ui/button';
import { useDashboardBuilder } from '@/contexts/DashboardBuilderContext';
import CreateCardMenu from './CreateCardMenu';
import { X, Save } from "lucide-react";
import { toast } from 'sonner';

const DashboardToolbar: React.FC = () => {
  const {
    state,
    cardArray,
    addCard,
    resetDashboard,
    saveDashboard,
  } = useDashboardBuilder();

  // Check if there are any active charts/tables loaded into cards
  const hasActiveContent = (
    state.chartZoneChart || 
    state.tableZoneChart || 
    cardArray.some(card => card.chart !== null)
  );

  const handleAddCard = (cardType: 'chart' | 'table') => {
    addCard(cardType);
  };

  const handleCancel = () => {
    resetDashboard();
    toast.success('Dashboard cleared');
  };

  const handleSave = async () => {
    try {
      // Generate a default name with timestamp
      const defaultName = `Dashboard_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}`;
      const savedId = await saveDashboard(defaultName);
      if (savedId) {
        toast.success('Dashboard saved successfully');
      }
    } catch (error) {
      toast.error('Failed to save dashboard');
    }
  };

  return (
    <div className="mb-6 space-y-4">
      {/* Top action buttons */}
      <div className="flex items-center justify-end">
        <CreateCardMenu onAddCard={handleAddCard} />
      </div>

      {/* Description */}
      <p className="text-muted-foreground">
        Create custom dashboards by selecting charts from the sidebar.
        Drag and drop to arrange your dashboard layout.
      </p>

      {/* Action buttons - only show when there's content */}
      {hasActiveContent && (
        <div className="flex justify-end space-x-3 pt-4 border-t">
          <Button
            variant="outline"
            onClick={handleCancel}
            className='hover:text-red-500'
          >
            <X size={16} className="mr-1.5" />
            Cancel
          </Button>

          <Button
            variant="greenmind"
            onClick={handleSave}
          >
            <Save size={16} className="mr-1.5" />
            Save Dashboard
          </Button>
        </div>
      )}
    </div>
  );
};

export default DashboardToolbar;