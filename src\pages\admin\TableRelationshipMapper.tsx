import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { toast } from 'sonner';
import RelationshipForm from '@/components/admin/RelationshipForm';

interface ColumnMapping {
  leftColumn: string;
  rightColumn: string;
  joinOperator: string;
}

interface TableRelationship {
  id: string;
  name: string;
  dbConnection: string;
  leftTable: string;
  rightTable: string;
  joinType: string;
  leftColumn: string;
  rightColumn: string;
  joinOperator: string;
  rightJoinOperator: string;
  columnMappings?: ColumnMapping[];
}

interface DatabaseConnection {
  connection_id: string;
  connection_name: string;
  database_name: string;
}

interface TableInfo {
  table_name: string;
  columns: ColumnInfo[];
}

interface ColumnInfo {
  column_name: string;
  data_type: string;
}

const TableRelationshipMapper: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [relationships, setRelationships] = useState<TableRelationship[]>([]);
  const [selectedRelationship, setSelectedRelationship] = useState<TableRelationship | null>(null);
  const [isNewRelationship, setIsNewRelationship] = useState(false);
  const [dbConnections, setDbConnections] = useState<DatabaseConnection[]>([]);
  const [tables, setTables] = useState<TableInfo[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Handle connection change and load tables
  const handleConnectionChange = async (connectionId: string) => {
    try {
      console.log('🔄 Connection changed, loading tables for connection ID:', connectionId);

      // Clear existing tables while loading
      setTables([]);

      const tablesData = await fetchTablesAndColumns(connectionId);
      setTables(tablesData);

      if (tablesData.length > 0) {
        console.log(`✅ Successfully loaded ${tablesData.length} tables`);
        toast.success(`Loaded ${tablesData.length} tables for selected connection`);
      } else {
        console.log('⚠️ No tables found for this connection');
        toast.info('No tables found for selected connection');
      }
    } catch (error) {
      console.error('❌ Error loading tables:', error);
      toast.error('Failed to load tables for selected connection');
      setTables([]);
    }
  };

  // Fetch database connections
  const fetchDatabaseConnections = async (): Promise<DatabaseConnection[]> => {
    try {
      console.log('🔍 Fetching database connections...');

      const response = await fetch('http://10.100.0.22:8001/account-management/get-all-connections', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      });

      if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);

      const data = await response.json();
      console.log('🔍 Database connections API response:', data);

      // Handle different response formats
      let connectionsData: any[] = [];
      if (Array.isArray(data)) {
        connectionsData = data;
      } else if (data && Array.isArray(data.connections)) {
        connectionsData = data.connections;
      } else if (data && Array.isArray(data.data)) {
        connectionsData = data.data;
      } else if (data && typeof data === 'object') {
        // If it's a single object, wrap it in an array
        connectionsData = [data];
      }

      console.log('🔍 Raw connections data:', connectionsData);

      // Map to expected format, handling different field names
      const mappedConnections: DatabaseConnection[] = connectionsData.map((conn, index) => {
        const connectionId = conn?.connection_id || conn?.id || conn?.uuid || conn?._id || `temp-${Date.now()}-${index}`;
        const connectionName = conn?.connection_name || conn?.name || conn?.title || conn?.database_name || 'Unnamed Connection';
        const databaseName = conn?.database_name || conn?.database || conn?.db_name || connectionName;

        return {
          connection_id: String(connectionId),
          connection_name: String(connectionName),
          database_name: String(databaseName)
        };
      });

      console.log('🔍 Final mapped connections:', mappedConnections);
      return mappedConnections;
    } catch (error) {
      console.error('❌ Error fetching database connections:', error);
      toast.error('Failed to load database connections');
      return [];
    }
  };

  // Fetch tables and columns for a specific connection
  const fetchTablesAndColumns = async (connectionId: string): Promise<TableInfo[]> => {
    try {
      console.log('🔍 Fetching tables for connection ID:', connectionId);

      const apiUrl = `http://10.100.0.22:8001/account-management/get_tables_columns?connection_id=${connectionId}`;
      console.log('🚀 Request URL:', apiUrl);

      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      });

      console.log('📥 Tables API response status:', response.status);
      console.log('📥 Tables API response headers:', response.headers);

      if (!response.ok) {
        const errorData = await response.text();
        console.error('❌ Tables API error response:', errorData);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorData}`);
      }

      const data = await response.json();
      console.log('🔍 Raw tables and columns API response:', data);
      console.log('🔍 Type of response data:', typeof data);
      console.log('🔍 Is response an array?', Array.isArray(data));

      // Log the structure of the response
      if (data && typeof data === 'object') {
        console.log('🔍 Response object keys:', Object.keys(data));
      }

      // Handle the specific API response format
      let tablesData: any[] = [];

      console.log('🔍 Analyzing response structure...');

      if (data && data.status === 'success' && Array.isArray(data.table_list)) {
        console.log('📋 Found API response with table_list array');
        tablesData = data.table_list;
      } else if (Array.isArray(data)) {
        console.log('📋 Response is direct array');
        tablesData = data;
      } else if (data && Array.isArray(data.tables)) {
        console.log('📋 Response has tables array');
        tablesData = data.tables;
      } else if (data && Array.isArray(data.data)) {
        console.log('📋 Response has data array');
        tablesData = data.data;
      } else {
        console.log('📋 Unknown response format, trying to extract tables...');
        console.log('📋 Response keys:', Object.keys(data || {}));
      }

      console.log('🔍 Processed tables data:', tablesData);
      console.log('🔍 Number of tables found:', tablesData.length);

      // Map to expected format for the specific API response
      const mappedTables: TableInfo[] = tablesData.map((table, index) => {
        console.log(`🔍 Processing table ${index + 1}:`, table);

        const tableName = table?.table_name || table?.name || table?.tableName || `Table_${index + 1}`;
        console.log(`📋 Extracted table name: "${tableName}"`);

        let columns: ColumnInfo[] = [];

        // Handle the specific API format: table_columns array
        if (Array.isArray(table?.table_columns)) {
          console.log(`📋 Found ${table.table_columns.length} columns in table.table_columns`);
          columns = table.table_columns.map((columnName: string) => ({
            column_name: columnName,
            data_type: 'unknown' // API doesn't provide data types
          }));
        } else if (Array.isArray(table?.columns)) {
          console.log(`📋 Found ${table.columns.length} columns in table.columns`);
          columns = table.columns.map((col: any, colIndex: number) => {
            if (typeof col === 'string') {
              return {
                column_name: col,
                data_type: 'unknown'
              };
            } else {
              return {
                column_name: col?.column_name || col?.name || col?.columnName || `Column_${colIndex + 1}`,
                data_type: col?.data_type || col?.type || col?.dataType || 'unknown'
              };
            }
          });
        } else {
          console.log('📋 No columns found in expected format');
        }

        const result = {
          table_name: String(tableName),
          columns: columns
        };

        console.log(`✅ Final mapped table:`, result);
        return result;
      });

      console.log('✅ Final mapped tables:', mappedTables);
      return mappedTables;
    } catch (error) {
      console.error('❌ Error fetching tables and columns:', error);
      toast.error(`Failed to load tables: ${error.message}`);
      return [];
    }
  };

  const handleNewRelationship = () => {
    setSelectedRelationship({
      id: '',
      name: '',
      dbConnection: '',
      leftTable: '',
      rightTable: '',
      joinType: 'Inner Join',
      leftColumn: '',
      rightColumn: '',
      joinOperator: '=',
      rightJoinOperator: '=',
      columnMappings: [
        { leftColumn: '', rightColumn: '', joinOperator: '=' }
      ]
    });
    setIsNewRelationship(true);

    // Clear the 'new' query parameter from URL
    if (searchParams.get('new') === 'true') {
      navigate('/Admin/TableRelationshipMapper', { replace: true });
    }
  };

  // Data initialization
  useEffect(() => {
    const initializeData = async () => {
      setIsLoading(true);

      try {
        // Fetch database connections
        const connectionsData = await fetchDatabaseConnections();
        setDbConnections(connectionsData);

        // Initialize with empty tables - they will be loaded when connection is selected
        setTables([]);

        // Mock relationships for now - replace with actual API call later
        const mockRelationships: TableRelationship[] = [];
        setRelationships(mockRelationships);

        // Check if we should create a new relationship from query params
        const shouldCreateNew = searchParams.get('new') === 'true';

        if (shouldCreateNew || mockRelationships.length === 0) {
          // Create a new relationship
          handleNewRelationship();
        } else if (mockRelationships.length > 0) {
          // Auto-select first relationship
          setSelectedRelationship(mockRelationships[0]);
          setIsNewRelationship(false);
        }
      } catch (error) {
        console.error('Error initializing data:', error);
        toast.error('Failed to load data');
      } finally {
        setIsLoading(false);
      }
    };

    initializeData();
  }, [searchParams]);

  const handleSaveRelationship = (relationshipData: TableRelationship) => {
    if (isNewRelationship) {
      const newRelationship = {
        ...relationshipData,
        id: Date.now().toString()
      };
      setRelationships(prev => [...prev, newRelationship]);
      toast.success('Relationship created successfully');
    } else {
      setRelationships(prev => 
        prev.map(rel => rel.id === relationshipData.id ? relationshipData : rel)
      );
      toast.success('Relationship updated successfully');
    }
    setSelectedRelationship(null);
    setIsNewRelationship(false);
  };

  const handleCancel = () => {
    setSelectedRelationship(null);
    setIsNewRelationship(false);
  };

  const handleAIRecommendation = () => {
    toast.info('AI Recommendation feature coming soon!');
  };



  // Handle URL parameters for selecting relationships
  useEffect(() => {
    const relationshipId = searchParams.get('id');
    const isNew = searchParams.get('new');

    if (isNew === 'true') {
      handleNewRelationship();
    } else if (relationshipId && relationships.length > 0) {
      const relationship = relationships.find(r => r.id === relationshipId);
      if (relationship) {
        setSelectedRelationship(relationship);
        setIsNewRelationship(false);
      }
    } else if (relationships.length > 0 && !selectedRelationship) {
      // Auto-select first relationship if none selected
      setSelectedRelationship(relationships[0]);
      setIsNewRelationship(false);
    }
  }, [searchParams, relationships, selectedRelationship]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading table relationships...</div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Navigation Tabs */}
      <div className="border-b border-gray-200 bg-white">
        <nav className="flex space-x-8 px-6">
          <div className="py-4 px-1 border-b-2 border-blue-500 text-blue-600 font-medium text-sm">
            Table Relationship Mapper
          </div>
        </nav>
      </div>

      {/* Content Area */}
      <div className="flex-1 p-6 overflow-auto bg-white">
        {/* Header with New Relationship Button */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h2 className="text-lg font-normal text-gray-700">
              {selectedRelationship && !isNewRelationship
                ? `Edit Relationship: ${selectedRelationship.name}`
                : isNewRelationship
                ? 'Create New Relationship'
                : 'Table Relationships'
              }
            </h2>
            <p className="text-sm text-gray-500 mt-1">
              {selectedRelationship && !isNewRelationship
                ? 'Modify the relationship configuration below'
                : isNewRelationship
                ? 'Configure a new table relationship'
                : 'Select a relationship from the sidebar to edit, or create a new one'
              }
            </p>
          </div>
          <Button
            onClick={handleNewRelationship}
            className="text-white hover:opacity-90 transition-opacity"
            style={{ backgroundColor: 'rgb(0, 130, 130)' }}
          >
            <Plus className="w-4 h-4 mr-2" />
            New Relationship
          </Button>
        </div>

        {/* Form or Empty State */}
        {selectedRelationship ? (
          <RelationshipForm
            relationship={selectedRelationship}
            dbConnections={dbConnections}
            tables={tables}
            isNewRelationship={isNewRelationship}
            onSave={handleSaveRelationship}
            onCancel={handleCancel}
            onAIRecommendation={handleAIRecommendation}
            onConnectionChange={handleConnectionChange}
          />
        ) : (
          <div className="flex flex-col items-center justify-center h-64 text-gray-500">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                <Plus className="w-8 h-8 text-gray-400" />
              </div>
              <p className="text-lg font-normal text-gray-600 mb-2">No relationship selected</p>
              <p className="text-sm text-gray-500 mb-4">
                Choose a relationship from the sidebar to edit, or create a new one using the button above.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TableRelationshipMapper;
