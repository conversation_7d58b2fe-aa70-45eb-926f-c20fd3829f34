import React, { useMemo } from 'react';
import DashboardContainer from '@/components/dashboard/layout/DashboardContainer';
import DashboardMain from '@/components/dashboard/layout/DashboardMain';
import Header from '@/components/dashboard/Header';
import SharedSidebar from '@/components/shared/SharedSidebar';
import { useChartsData } from '@/hooks/useChartsData';
import { SavedChart } from '@/types/chartTypes';
import { toast } from 'sonner';
import { DndContext } from '@dnd-kit/core';
import { DashboardBuilderProvider, useDashboardBuilder } from '@/contexts/DashboardBuilderContext';

// Import new sub-components
import ChartSidebar from '@/components/dashboard/builder/ChartSidebar';
import DashboardCanvas from '@/components/dashboard/builder/DashboardCanvas';
import DashboardToolbar from '@/components/dashboard/builder/DashboardToolbar';

// Internal component that uses the context
const DashboardBuilderContent: React.FC = () => {
  const {
    state,
    setSearchQuery,
    toggleSidebar,
    toggleFolder,
    addSelectedChart,
    sensors,
    handleDragEnd,
  } = useDashboardBuilder();
  
  const {
    charts,
    loading,
    loadChartById,
  } = useChartsData();

  const filteredCharts = useMemo(() => 
    charts.filter(chart => 
      chart.chart_name.toLowerCase().includes(state.searchQuery.toLowerCase())
    ), 
    [charts, state.searchQuery]
  );
  
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };
  
  const handleAddChartToDashboard = async (chart: SavedChart) => {
    if (state.selectedCharts.find(c => c.chart_id === chart.chart_id)) {
      toast.info('Chart is already added to dashboard');
      return;
    }
    
    // Add the chart to the dashboard first to show immediate feedback
    addSelectedChart(chart);
    toast.success(`Added "${chart.chart_name}" to dashboard`);
    
    // Then load the chart data if needed
    try {
      if (chart.chart_id) {
        const chartDetails = await loadChartById(chart.chart_id);
        
        if (chartDetails) {
          // Update the chart in the selected charts array with the loaded data
          addSelectedChart(chartDetails);
        }
      }
    } catch (error) {
      console.error('Error loading chart data for dashboard:', error);
      // Don't show error toast here as the chart is already added
    }
  };

  const renderSidebarContent = () => {
    return (
      <ChartSidebar
        loading={loading}
        filteredCharts={filteredCharts}
        foldersExpanded={state.foldersExpanded}
        onToggleFolder={toggleFolder}
        onAddChartToDashboard={handleAddChartToDashboard}
      />
    );
  };

  const renderDashboardContent = () => {
    return (
      <div className="p-6 h-full">
        <div className={`${state.sidebarCollapsed ? 'max-w-none' : 'max-w-7xl'} mx-auto transition-all duration-300`}>
          {/* Dashboard Toolbar */}
          <DashboardToolbar />

          {/* Dashboard Canvas */}
          <DashboardCanvas />
        </div>
      </div>
    );
  };
  
  return (
    <DndContext sensors={sensors} onDragEnd={handleDragEnd}>
      <DashboardContainer textSize="medium">
        <Header
          toggleSidebar={toggleSidebar}
          sidebarCollapsed={state.sidebarCollapsed}
          rightSidebarVisible={false}
          dashboardName="Dashboard Builder"
          toggleRightSidebar={() => {}}
          onHeaderSearch={() => {}}
          dashboardType={2}
        />

        <DashboardMain>
          <div className="flex h-full w-full">
            <SharedSidebar
              collapsed={state.sidebarCollapsed}
              onToggle={toggleSidebar}
              searchQuery={state.searchQuery}
              onSearchChange={handleSearch}
              searchPlaceholder="Search charts..."
              loading={loading}
            >
              {renderSidebarContent()}
            </SharedSidebar>

            <div className="flex-1 overflow-y-auto">
              {renderDashboardContent()}
            </div>
          </div>
        </DashboardMain>
      </DashboardContainer>
    </DndContext>
  );
};

// Main component wrapped with provider
const DashboardBuilderPage: React.FC = () => {
  return (
    <DashboardBuilderProvider>
      <DashboardBuilderContent />
    </DashboardBuilderProvider>
  );
};

export default DashboardBuilderPage;