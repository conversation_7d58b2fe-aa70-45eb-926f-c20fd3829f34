
import { ChartDataItem, ChartSaveConfig, TabularDataItem } from './chartTypes';
import { ResponseHandlers } from './responseHandlers';
import { RequestBuilders } from './requestBuilders';
import { centralApiClient } from '@/services/api/centralApiClient';

/**
 * Fetches chart data from the chart API using the updated endpoint format
 */
export const fetchChartData = async (
  connectionId: string,
  tables: { table_name: string; columns: string[] }[],
  xAxis?: string,
  yAxis?: string[] | { column: string; table_name?: string; aggregation?: string }[],
  groupBy?: string,
  filters?: { [key: string]: string } // Add filters parameter
): Promise<ChartDataItem[]> => {
  try {
    console.log('fetchChartData called with filters:', filters);
    
    const requestBody = RequestBuilders.buildFetchDataRequest(
      connectionId, 
      tables, 
      xAxis, 
      yAxis, 
      groupBy,
      filters // Pass filters to request builder
    );
    
    console.log('Final request body for chart data fetch:', JSON.stringify(requestBody, null, 2));
    
    const data = await centralApiClient.makeRequest('chart', '/data/fetch', {
      method: 'POST',
      body: requestBody
    });
    
    return ResponseHandlers.handleChartDataResponse(data);
  } catch (error) {
    console.error('Error fetching chart data:', error);
    throw error;
  }
};

/**
 * Saves a chart configuration to the server
 */
export const saveChart = async (
  connectionId: string,
  config: ChartSaveConfig,
  chartData: {
    status: string;
    data: any[];
    metadata: {
      tables: string[];
      status: string;
    };
  },
  chartImage?: string,
  userEmail?: string
): Promise<any> => {
  try {
    console.log('saveChart called with params:', { 
      connectionId, 
      config,
      chartDataStatus: chartData.status,
      chartDataLength: chartData.data.length,
      userEmail
    });
    
    const requestBody = RequestBuilders.buildSaveChartRequest(
      connectionId, 
      config, 
      chartData, 
      chartImage,
      userEmail
    );
    
    const data = await centralApiClient.makeRequest('chart', '/chart/save', {
      method: 'POST',
      body: requestBody
    });
    
    return ResponseHandlers.handleChartSaveResponse(data);  
  } catch (error) {
    console.error('Error saving chart:', error);
    throw error;
  }
};

/**
 * Fetches all saved charts
 */
export const fetchCharts = async (): Promise<{ charts: any[] }> => {
  try {
    console.log('Fetching saved charts');
    
    const data = await centralApiClient.makeRequest('chart', '/charts', {
      method: 'GET'
    });
    
    return ResponseHandlers.handleChartsListResponse(data);
  } catch (error) {
    console.error('Error fetching charts:', error);
    throw error;
  }
};

/**
 * Fetches a specific chart by its ID
 */
export const fetchChartById = async (chartId: string): Promise<any> => {
  try {
    console.log('Fetching chart by ID:', chartId);
    
    const data = await centralApiClient.makeRequest('chart', `/charts/${chartId}`, {
      method: 'GET'
    });
    
    // Type assertion to allow property access
    const responseData = data as Record<string, any>;
    
    // If the chart data includes a connection_id, log it
    if ('connection_id' in responseData && responseData.connection_id) {
      console.log('Chart has connection_id:', responseData.connection_id);
    } else if ('chart' in responseData && responseData.chart && 'connection_id' in responseData.chart) {
      console.log('Chart has connection_id in chart object:', responseData.chart.connection_id);
    } else {
      console.log('Chart does not have a connection_id');
    }
    
    return responseData;
  } catch (error) {
    console.error('Error fetching chart by ID:', error);
    throw error;
  }
};

/**
 * Updates an existing chart configuration
 */
export const updateChart = async (
  chartId: string,
  connectionId: string,
  config: ChartSaveConfig,
  chartData: {
    status: string;
    data: any[];
    metadata: {
      tables: string[];
      status: string;
    };
  },
  chartImage?: string,
  userEmail?: string
): Promise<any> => {
  try {
    console.log('updateChart called with params:', { 
      chartId,
      connectionId, 
      config,
      chartDataStatus: chartData.status,
      chartDataLength: chartData.data.length,
      userEmail
    });
    
    const requestBody = RequestBuilders.buildUpdateChartRequest(
      chartId,
      connectionId, 
      config, 
      chartData, 
      chartImage,
      userEmail
    );
    
    const data = await centralApiClient.makeRequest('chart', `/charts/${chartId}`, {
      method: 'PUT',
      body: requestBody
    });
    
    return ResponseHandlers.handleChartSaveResponse(data);
  } catch (error) {
    console.error('Error updating chart:', error);
    throw error;
  }
};

/**
 * Increments the view count for a chart
 * @param chartId The ID of the chart to increment views for
 * @returns Promise with the response
 */
export const incrementChartViews = async (chartId: string): Promise<any> => {
  if (!chartId) {
    console.error('Cannot increment views: No chart ID provided');
    return null;
  }
  
  try {
    console.log('Incrementing view count for chart ID:', chartId);
    
    // Remove skipDeduplication: false to ensure every call goes through
    return await centralApiClient.makeRequest('chart', `/charts/${chartId}/view`, {
      method: 'POST',
      // We can use a shorter timeout for this non-critical operation
      timeout: 5000
    });
  } catch (error) {
    console.error('Error incrementing chart views:', error);
    // Don't throw the error - view increment failure shouldn't break the app
    return null;
  }
};

/**
 * Fetches tabular data for a specific chart by its ID
 */
export const fetchChartTabularData = async (chartId: string): Promise<TabularDataItem[]> => {
  try {
    console.log('Fetching tabular data for chart ID:', chartId);

    // Try the endpoint structure that matches the Swagger documentation
    const data = await centralApiClient.makeRequest('chart', `/charts/tabular_data/${chartId}`, {
      method: 'GET'
    });

    return ResponseHandlers.handleTabularDataResponse(data);
  } catch (error) {
    console.error('Error fetching chart tabular data:', error);
    throw error;
  }
};
