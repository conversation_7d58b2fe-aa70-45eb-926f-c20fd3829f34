
import { useState } from 'react';
import { useAppDispatch } from '@/hooks/useRedux';
import { useNavigate } from 'react-router-dom';
// import { setPersonalFiles, setProjectFiles } from '@/stores/fileSlice';

import { forceRefreshFiles } from '@/utils/fileUtils';

interface FileToDelete {
  fileName: string;
  fileId: string; // Only need fileId, not folderType
}

interface DeleteStatus {
  status: 'idle' | 'loading' | 'success' | 'error';
  errorCode?: number;
}

export const useDeleteDialog = (
  personalFiles: string[], 
  projectFiles: string[],
  clearFileInput?: () => void
) => {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [fileToDelete, setFileToDelete] = useState<FileToDelete | null>(null);
  const [deleteStatus, setDeleteStatus] = useState<DeleteStatus>({ status: 'idle' });
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const handleDeleteClick = (fileName: string, fileId: string) => {
    setFileToDelete({ fileName, fileId });
    setIsDeleteDialogOpen(true);
    setDeleteStatus({ status: 'idle' }); // Reset status on new delete
  };

  const handleDelete = async () => {
    if (!fileToDelete) return;

    const { fileId, fileName } = fileToDelete;
    setDeleteStatus({ status: 'loading' });

    // try {
    //   // Use the simplified deleteFile function
    //   await deleteFile(fileId);
    //   setDeleteStatus({ status: 'success' });
      
    //   // Update store after successful deletion
    //   const isPersonalFile = personalFiles.includes(fileName);
    //   if (isPersonalFile) {
    //     dispatch(setPersonalFiles(personalFiles.filter(file => file !== fileName)));
    //   } else {
    //     dispatch(setProjectFiles(projectFiles.filter(file => file !== fileName)));
    //   }

    //   // Force refresh files to update the sidebar
    //   await forceRefreshFiles();
      
    //   // Dispatch custom event to notify sidebar
    //   window.dispatchEvent(new Event('file-updated'));

    //   // Wait for success message to show before cleanup
    //   setTimeout(() => {
    //     if (clearFileInput) {
    //       clearFileInput();
    //     }
    //     setFileToDelete(null);
    //     setIsDeleteDialogOpen(false);
    //     setDeleteStatus({ status: 'idle' });
    //     navigate('/transcript');
    //   }, 4000);
    // } catch (error) {
    //   setDeleteStatus({ 
    //     status: 'error', 
    //     errorCode: error.status || 500 
    //   });
      
    //   setTimeout(() => {
    //     setDeleteStatus({ status: 'idle' });
    //   }, 3000);
    // }
  };

  return {
    isDeleteDialogOpen,
    setIsDeleteDialogOpen,
    fileToDelete,
    deleteStatus,
    handleDeleteClick,
    handleDelete
  };
};
