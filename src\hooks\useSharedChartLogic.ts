
import { useState, useCallback } from 'react';
import { useAppDispatch, useAppSelector } from './useRedux';
import { 
  selectSelectedDatabase, 
  selectConnectionId,
  setConnectionId,
  setSelectedDatabase
} from '@/stores/chartSlice';
import { connectToDatabase } from '@/services/api/chart/databaseService';
import { toast } from 'sonner';

export const useSharedChartLogic = () => {
  const dispatch = useAppDispatch();
  const selectedDatabase = useAppSelector(selectSelectedDatabase);
  const connectionId = useAppSelector(selectConnectionId);
  const [isConnecting, setIsConnecting] = useState(false);

  const establishConnection = useCallback(async (dbType: string) => {
    if (connectionId) return connectionId;
    
    try {
      setIsConnecting(true);
      console.log("Establishing connection to:", dbType);
      
      const response = await connectToDatabase({ 
        db_type: dbType,
        host: '',
        port: '',
        database: '',
        username: '',
        password: '',
        table_name: ''
      });
      
      if (response?.connection_id) {
        dispatch(setConnectionId(response.connection_id));
        dispatch(setSelectedDatabase(dbType));
        return response.connection_id;
      }
      
      throw new Error('No connection ID received');
    } catch (error) {
      console.error("Failed to establish connection:", error);
      toast.error("Failed to connect to database");
      return null;
    } finally {
      setIsConnecting(false);
    }
  }, [connectionId, dispatch]);

  return {
    selectedDatabase,
    connectionId,
    isConnecting,
    establishConnection
  };
};
