import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';


interface Marketplace {
  id: string;
  marketplace_name: string;
  marketplace_description: string;
  approval_set_id: string;
}

interface ApprovalSet {
  id: string;
  approval_set_name: string;
  can_approve_insights: boolean;
  can_approve_marketplace: boolean;
  can_approve_pattern: boolean;
  can_approve_predict: boolean;
}

interface CreateMarketplaceRequest {
  marketplace_name: string;
  marketplace_description: string;
  approval_set_id: string;
}

const Marketplace: React.FC = () => {
  const [searchParams] = useSearchParams();
  const [selectedMarketplace, setSelectedMarketplace] = useState<Marketplace | null>(null);
  const [marketplaceName, setMarketplaceName] = useState('');
  const [marketplaceDescription, setMarketplaceDescription] = useState('');
  const [selectedApprovalSet, setSelectedApprovalSet] = useState('');
  const [approvalSets, setApprovalSets] = useState<ApprovalSet[]>([]);
  const [isNewMarketplace, setIsNewMarketplace] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // Fetch approval sets from API
  const fetchApprovalSets = async (): Promise<ApprovalSet[]> => {
    try {
      const response = await fetch('http://***********:8001/account-management/get-all-approval-set', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      });

      if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);

      const data = await response.json();
      console.log('🔍 Raw Approval Sets API Response:', data);

      // Handle different response formats
      let approvalSetsData: any[] = [];
      if (Array.isArray(data)) {
        approvalSetsData = data;
      } else if (data && Array.isArray(data.approval_sets)) {
        approvalSetsData = data.approval_sets;
      } else if (data && Array.isArray(data.data)) {
        approvalSetsData = data.data;
      } else if (data && typeof data === 'object') {
        approvalSetsData = [data];
      }

      // Map to expected format, handling different field names
      const mappedApprovalSets: ApprovalSet[] = approvalSetsData.map((set, index) => {
        const id = set?.id || set?.approval_set_id || set?.uuid || set?._id || `temp-${Date.now()}-${index}`;
        const name = set?.approval_set_name || set?.name || set?.title || 'Unnamed Approval Set';

        return {
          id: String(id),
          approval_set_name: String(name),
          can_approve_insights: set?.can_approve_insights || false,
          can_approve_marketplace: set?.can_approve_marketplace || false,
          can_approve_pattern: set?.can_approve_pattern || false,
          can_approve_predict: set?.can_approve_predict || false
        };
      });

      console.log('🔍 Final mapped approval sets:', mappedApprovalSets);
      return mappedApprovalSets;
    } catch (error) {
      console.error('Error fetching approval sets:', error);
      toast.error('Failed to load approval sets');
      return [];
    }
  };

  // Data initialization
  useEffect(() => {
    const initializeData = async () => {
      setIsLoading(true);

      try {
        // Fetch approval sets
        const approvalSetsData = await fetchApprovalSets();
        setApprovalSets(approvalSetsData);

        // Check URL parameters
        const marketplaceId = searchParams.get('id');
        const isNew = searchParams.get('new') === 'true';

        if (isNew) {
          handleNewMarketplace();
        } else if (marketplaceId) {
          // Load marketplace data based on ID (mock for now)
          const mockMarketplace: Marketplace = {
            id: marketplaceId,
            marketplace_name: marketplaceId === '1' ? 'Analytics_DMP' :
                  marketplaceId === '2' ? 'DataEngineering_DMP' : 'Finance_DMP',
            marketplace_description: 'Sample marketplace description',
            approval_set_id: approvalSetsData.length > 0 ? approvalSetsData[0].id : ''
          };
          setSelectedMarketplace(mockMarketplace);
          setMarketplaceName(mockMarketplace.marketplace_name);
          setMarketplaceDescription(mockMarketplace.marketplace_description);
          setSelectedApprovalSet(mockMarketplace.approval_set_id);
          setIsNewMarketplace(false);
        }
      } catch (error) {
        console.error('Error initializing data:', error);
        toast.error('Failed to load data');
      } finally {
        setIsLoading(false);
      }
    };

    initializeData();
  }, [searchParams]);

  const handleNewMarketplace = () => {
    setSelectedMarketplace({
      id: 'new',
      marketplace_name: '',
      marketplace_description: '',
      approval_set_id: ''
    });
    setMarketplaceName('');
    setMarketplaceDescription('');
    setSelectedApprovalSet('');
    setIsNewMarketplace(true);
  };

  // Create marketplace API call
  const createMarketplace = async (data: CreateMarketplaceRequest) => {
    try {
      console.log('🚀 Creating marketplace with data:', data);

      const response = await fetch('http://***********:8001/account-management/create-marketplace', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      console.log('📥 Create marketplace response status:', response.status);

      if (!response.ok) {
        const errorData = await response.text();
        console.error('❌ Create marketplace error:', errorData);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorData}`);
      }

      const result = await response.json();
      console.log('✅ Marketplace created successfully:', result);
      return result;
    } catch (error) {
      console.error('❌ Error creating marketplace:', error);
      throw error;
    }
  };

  const handleSave = async () => {
    if (!marketplaceName.trim()) {
      toast.error('Please enter a marketplace name');
      return;
    }

    if (!marketplaceDescription.trim()) {
      toast.error('Please enter a marketplace description');
      return;
    }

    if (!selectedApprovalSet) {
      toast.error('Please select an approval set');
      return;
    }

    setIsSaving(true);

    try {
      if (isNewMarketplace) {
        // Create new marketplace
        const requestData: CreateMarketplaceRequest = {
          marketplace_name: marketplaceName,
          marketplace_description: marketplaceDescription,
          approval_set_id: selectedApprovalSet
        };

        await createMarketplace(requestData);
        toast.success('Marketplace created successfully');

        // Update local state
        if (selectedMarketplace) {
          setSelectedMarketplace({
            ...selectedMarketplace,
            marketplace_name: marketplaceName,
            marketplace_description: marketplaceDescription,
            approval_set_id: selectedApprovalSet
          });
        }
        setIsNewMarketplace(false);
      } else {
        // Update existing marketplace (local only for now)
        toast.success('Marketplace updated successfully');

        if (selectedMarketplace) {
          setSelectedMarketplace({
            ...selectedMarketplace,
            marketplace_name: marketplaceName,
            marketplace_description: marketplaceDescription,
            approval_set_id: selectedApprovalSet
          });
        }
      }
    } catch (error) {
      toast.error('Failed to save marketplace');
    } finally {
      setIsSaving(false);
    }
  };

  const handleClose = () => {
    setSelectedMarketplace(null);
    setMarketplaceName('');
    setMarketplaceDescription('');
    setSelectedApprovalSet('');
    setIsNewMarketplace(false);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading marketplace...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-lg font-normal text-gray-700">
          {selectedMarketplace && !isNewMarketplace
            ? `Edit Marketplace: ${selectedMarketplace.marketplace_name}`
            : isNewMarketplace
            ? 'Create New Marketplace'
            : 'DADA - Marketplace'
          }
        </h2>
        <p className="text-sm text-gray-500 mt-1">
          {selectedMarketplace && !isNewMarketplace
            ? 'Modify the marketplace configuration below'
            : isNewMarketplace
            ? 'Configure a new marketplace'
            : 'Select a marketplace from the sidebar to edit, or create a new one'
          }
        </p>
      </div>

      {/* Form or Empty State */}
      {selectedMarketplace ? (
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm max-w-2xl">
          {/* Form Content */}
          <div className="p-6 space-y-6">
            {/* Marketplace Name */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Marketplace Name <span className="text-red-500">*</span>
              </label>
              <Input
                value={marketplaceName}
                onChange={(e) => setMarketplaceName(e.target.value)}
                placeholder="Enter marketplace name"
                className="w-full border-gray-300 focus:border-teal-500 focus:ring-1 focus:ring-teal-500 transition-all duration-200"
              />
            </div>

            {/* Marketplace Description */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Marketplace Description <span className="text-red-500">*</span>
              </label>
              <textarea
                value={marketplaceDescription}
                onChange={(e) => setMarketplaceDescription(e.target.value)}
                placeholder="Enter marketplace description"
                rows={3}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:border-teal-500 focus:ring-1 focus:ring-teal-500 transition-all duration-200 resize-none"
              />
            </div>

            {/* Approval Set */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Approval Set <span className="text-red-500">*</span>
              </label>
              <select
                value={selectedApprovalSet}
                onChange={(e) => setSelectedApprovalSet(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:border-teal-500 focus:ring-1 focus:ring-teal-500 transition-all duration-200"
              >
                <option value="">Select approval set</option>
                {approvalSets.map((set) => (
                  <option key={set.id} value={set.id}>
                    {set.approval_set_name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Form Actions */}
          <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
            <div className="flex justify-end space-x-3">
              <Button
                onClick={handleClose}
                variant="outline"
                className="px-6 py-2"
              >
                Close
              </Button>
              <Button
                onClick={handleSave}
                disabled={isSaving}
                className="px-6 py-2 text-white hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed transition-opacity"
                style={{ backgroundColor: 'rgb(0, 130, 130)' }}
              >
                {isSaving ? 'Saving...' : 'Save'}
              </Button>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center h-64 text-gray-400">
          <div className="text-center">
            <p className="text-lg font-normal text-gray-600 mb-2">No marketplace selected</p>
            <p className="text-sm text-gray-500">
              Choose a marketplace from the sidebar to edit.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default Marketplace;
