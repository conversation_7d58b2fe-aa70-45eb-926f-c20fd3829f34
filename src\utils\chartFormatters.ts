
import { formatDistanceToNow, parseISO, format } from 'date-fns';

// Utility functions for formatting chart data
export const formatDate = (dateString: string, useRelative: boolean = false): string => {
  try {
    if (!dateString) return 'Unknown';
    
    // For relative time formatting (e.g. "2 mins ago")
    if (useRelative) {
      // Handle ISO date format
      if (dateString.includes('T') && (dateString.includes('Z') || dateString.includes('+'))) {
        return formatDistanceToNow(parseISO(dateString), { addSuffix: true });
      }
      
      // Try to parse as a regular date string
      const date = new Date(dateString);
      if (!isNaN(date.getTime())) {
        return formatDistanceToNow(date, { addSuffix: true });
      }
    }
    
    // Original formatting logic for non-relative dates
    // Handle ISO date format (e.g., "2025-06-27T12:29:13Z")
    if (dateString.includes('T') && (dateString.includes('Z') || dateString.includes('+'))) {
      const date = new Date(dateString);
      if (!isNaN(date.getTime())) {
         let h = date.getHours();
        const ampm = h >= 12 ? 'PM' : 'AM';
        h = h % 12 || 12;
        return `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear()} ${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')} ${ampm}`;
      }
    }
    
    // Handle the specific "20250626_105718" format
    if (typeof dateString === 'string' && dateString.includes('_')) {
      const [datePart, timePart] = dateString.split('_');
      
      if (datePart.length === 8 && timePart.length === 6) {
        // Format: YYYYMMDD_HHMMSS
        const year = datePart.substring(0, 4);
        const month = datePart.substring(4, 6);
        const day = datePart.substring(6, 8);
        const hour = timePart.substring(0, 2);
        const minute = timePart.substring(2, 4);
        const second = timePart.substring(4, 6);
        
        return `${month}/${day}/${year} ${hour}:${minute}:${second}`;
      }
    }
    
    // Try to parse as a regular date string
    const date = new Date(dateString);
    if (!isNaN(date.getTime())) {
      let h = date.getHours();
      const ampm = h >= 12 ? 'PM' : 'AM';
      h = h % 12 || 12;
      return `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear()} ${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')} ${ampm}`;
    }
    
    // If all else fails, return the original string
    return dateString;
  } catch (error) {
    console.error('Error formatting date:', error);
    return dateString;
  }
};

export const extractYAxisInfo = (yAxis: any): { column: string, aggregation: string | null } => {
  if (typeof yAxis === 'string') {
    return { column: yAxis, aggregation: null };
  }
  
  if (Array.isArray(yAxis)) {
    const firstItem = yAxis[0];
    if (typeof firstItem === 'string') {
      return { column: firstItem, aggregation: null };
    }
    if (firstItem && typeof firstItem === 'object' && 'column' in firstItem) {
      return { 
        column: firstItem.column, 
        aggregation: firstItem.aggregation || null 
      };
    }
  }
  
  console.error('Unknown y_axis format:', yAxis);
  return { column: '', aggregation: null };
};

export const getTableNames = (chart: any): string => {
  try {
    // If there's a single table_name property
    if (chart.table_name) {
      return chart.table_name;
    }
    
    // If there are multiple tables in an array
    if (chart.tables && Array.isArray(chart.tables)) {
      if (chart.tables.length === 0) {
        return 'Unknown';
      }
      
      // If there's only one table, return its name
      if (chart.tables.length === 1) {
        return chart.tables[0].table_name || 'Unknown';
      }
      
      // If there are multiple tables, return a comma-separated list
      return chart.tables
        .map(table => table.table_name)
        .filter(Boolean)
        .join(', ');
    }
    
    return 'Unknown';
  } catch (error) {
    console.error("Error getting table names:", error);
    return 'Unknown';
  }
};
