name: Mirror GitHub main to Azure DevOps DADA-AI-Dev

on:
  push:
    branches:
      - main # Trigger when pushing to GitHub main branch

jobs:
  sync:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0  # Get all history - VERY IMPORTANT
          ref: main

      - name: Configure Git
        run: |
          git config --global user.name "Anand-MLZ"
          git config --global user.email "<EMAIL>"

      - name: Pull latest from Azure DevOps DADA-AI-Dev
        run: |
          git remote add azure https://anything:${{ secrets.AZUREPAT }}@dev.azure.com/mindlabz/MLZ_AI_Products/_git/MLZ-DADA-AI-Frontend
          git fetch azure DADA-AI-Dev:DADA-AI-Dev  # Fetch and merge remote DADA-AI-Dev
          git merge azure/DADA-AI-Dev --allow-unrelated-histories -m "Merge Azure DevOps DADA-AI-Dev changes"
        env:
          AZUREPAT: ${{secrets.AZUREPAT}}

      - name: Push to Azure DevOps
        run: |
          git push azure main:DADA-AI-Dev  # Push GitHub main to Azure DevOps DADA-AI-Dev
        env:
          AZUREPAT: ${{secrets.AZUREPAT}}


