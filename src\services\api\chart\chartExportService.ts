
import html2canvas from 'html2canvas';
import { jsPDF } from 'jspdf';
import { toast } from 'sonner';

export class ChartExportService {
  static async copyChartToClipboard(chartName: string): Promise<boolean> {
    try {
      // Try to find the chart container that includes both details and visualization
      const chartRef = document.querySelector('.chart-export-container') || 
                       document.querySelector('.chart-page-content');
      
      if (!chartRef) {
        toast.error("Cannot copy chart", {
          description: "Chart element not found"
        });
        return false;
      }
      
      const canvas = await html2canvas(chartRef as HTMLElement);
      
      const blob = await new Promise<Blob>((resolve) => {
        canvas.toBlob((blob) => {
          resolve(blob as Blob);
        }, 'image/png');
      });
      
      const item = new ClipboardItem({ 'image/png': blob });
      await navigator.clipboard.write([item]);
      
      toast.success("Chart copied to clipboard");
      return true;
    } catch (error) {
      console.error("Error copying chart:", error);
      toast.error("Failed to copy chart", {
        description: "Your browser may not support this feature"
      });
      return false;
    }
  }

  static async exportChartAsPNG(chartName: string): Promise<boolean> {
    try {
      // Try to find the chart container that includes both details and visualization
      const chartContainer = document.querySelector('.chart-export-container') || 
                             document.querySelector('.chart-page-content');
      
      if (!chartContainer) {
        toast.error("Cannot export chart", {
          description: "Chart element not found"
        });
        return false;
      }
      
      const canvas = await html2canvas(chartContainer as HTMLElement, {
        backgroundColor: '#ffffff',
        scale: 2,
        logging: false,
        useCORS: true
      });
      
      const link = document.createElement('a');
      link.download = `${chartName}.png`;
      link.href = canvas.toDataURL('image/png');
      link.click();
      
      toast.success("Chart exported as PNG");
      return true;
    } catch (error) {
      console.error("Error exporting chart as PNG:", error);
      toast.error("Failed to export chart", {
        description: "An error occurred while exporting"
      });
      return false;
    }
  }

  static async exportChartAsPDF(chartName: string): Promise<boolean> {
    try {
      // Try to find the chart container that includes both details and visualization
      const chartContainer = document.querySelector('.chart-export-container') || 
                             document.querySelector('.chart-page-content');
      
      if (!chartContainer) {
        toast.error("Cannot export chart", {
          description: "Chart element not found"
        });
        return false;
      }
      
      const canvas = await html2canvas(chartContainer as HTMLElement, {
        backgroundColor: '#ffffff',
        scale: 2,
        logging: false,
        useCORS: true
      });
      
      const imgData = canvas.toDataURL('image/png');
      
      const pdf = new jsPDF({
        orientation: 'landscape',
        unit: 'mm'
      });
      
      const imgProps = pdf.getImageProperties(imgData);
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;
      
      pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight);
      pdf.save(`${chartName}.pdf`);
      
      toast.success("Chart exported as PDF");
      return true;
    } catch (error) {
      console.error("Error exporting chart as PDF:", error);
      toast.error("Failed to export chart", {
        description: "An error occurred while exporting"
      });
      return false;
    }
  }
}
