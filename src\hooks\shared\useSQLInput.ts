import { useState, useCallback, useEffect } from 'react';

export interface SQLValidationResult {
  isValid: boolean;
  message?: string;
}

export interface SQLInputState {
  value: string;
  validationResult: SQLValidationResult | null;
  isValidating: boolean;
  showHelperText: boolean;
  isTouched: boolean;
}

export interface UseSQLInputProps {
  initialValue?: string;
  onValidate?: (sql: string) => Promise<SQLValidationResult>;
  onChange?: (value: string) => void;
  onBlur?: () => void;
  autoValidate?: boolean;
  fieldKey?: string;
  viewType?: string;
}

export const useSQLInput = ({
  initialValue = '',
  onValidate,
  onChange,
  onBlur,
  autoValidate = false,
  fieldKey,
  viewType
}: UseSQLInputProps = {}) => {
  const [state, setState] = useState<SQLInputState>({
    value: initialValue,
    validationResult: null,
    isValidating: false,
    showHelperText: false,
    isTouched: false
  });

  // Reset helper text and validation when value changes
  useEffect(() => {
    if (state.value.trim().length === 0) {
      setState(prev => ({
        ...prev,
        showHelperText: false,
        validationResult: null
      }));
    }
  }, [state.value]);

  // Reset all state when viewType changes to prevent cross-contamination
  useEffect(() => {
    setState({
      value: initialValue,
      validationResult: null,
      isValidating: false,
      showHelperText: false,
      isTouched: false
    });
  }, [viewType, initialValue]);

  const handleChange = useCallback((newValue: string) => {
    setState(prev => ({
      ...prev,
      value: newValue,
      showHelperText: newValue.trim().length > 0 && !prev.validationResult?.isValid,
      validationResult: prev.validationResult ? null : prev.validationResult // Clear validation on change
    }));
    
    onChange?.(newValue);
  }, [onChange]);

  const handleValidate = useCallback(async () => {
    if (!onValidate || !state.value.trim()) return null;

    setState(prev => ({
      ...prev,
      isValidating: true,
      showHelperText: false
    }));

    try {
      const result = await onValidate(state.value.trim());
      setState(prev => ({
        ...prev,
        validationResult: result,
        isValidating: false
      }));
      return result;
    } catch (error) {
      const errorResult: SQLValidationResult = {
        isValid: false,
        message: error instanceof Error ? error.message : 'Validation error occurred'
      };
      setState(prev => ({
        ...prev,
        validationResult: errorResult,
        isValidating: false
      }));
      return errorResult;
    }
  }, [onValidate, state.value]);

  const handleBlur = useCallback(() => {
    setState(prev => ({
      ...prev,
      isTouched: true
    }));
    onBlur?.();
  }, [onBlur]);

  const clearState = useCallback(() => {
    setState({
      value: '',
      validationResult: null,
      isValidating: false,
      showHelperText: false,
      isTouched: false
    });
  }, []);

  const setValue = useCallback((value: string) => {
    setState(prev => ({
      ...prev,
      value
    }));
  }, []);

  return {
    ...state,
    handleChange,
    handleValidate,
    handleBlur,
    clearState,
    setValue
  };
};