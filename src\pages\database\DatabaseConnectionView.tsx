import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';

interface DatabaseConnection {
  connection_id: number;
  connection_name: string;
  database_dialect: string;
  database_name: string;
}

interface ApiConnectionsResponse {
  status: string;
  connections: DatabaseConnection[];
}

interface ConnectionDetails {
  connection_id: number;
  connection_name: string;
  db_dialect: string;
  database_name: string;
  username: string;
  password: string;
  host: string;
  port: number;
  ssl_mode: string;
  tables: Array<{
    table_name: string;
    table_type: string;
  }>;
  views: Array<{
    view_name: string;
    view_type: string;
  }>;
}

interface MetadataApiResponse {
  Table_Id: number;
  Table_Name: string;
  Table_Description: string;
  Primary_Key_Columns: Record<string, string> | null;
  Foreign_Key_Columns: Record<string, string> | null;
  Custom_Table_Instructions: string | null;
  Table_Aliases: string | null;
  Columns_List: Record<string, string>;
  Database_Dialect: string;
  Database_Name: string;
  connection_id: number;
}

interface TableData {
  Table_Id: number;
  Table_Name: string;
  Table_Description: string;
  Primary_Key_Columns: string | null;
  Foreign_Key_Columns: string | null;
  Columns_List: Record<string, string>;
}

interface ColumnData {
  columnName: string;
  columnDescription: string;
  columnAlias: string;
  columnDatatype: string;
  isPrimaryKey: boolean;
  isUniqueKey: boolean;
}

// Helper function to check if a column is a primary key
const checkIfPrimaryKey = (columnName: string, tableData: any): boolean => {
  if (!tableData.Primary_Key_Columns) {
    return false;
  }

  const primaryKeys = tableData.Primary_Key_Columns;

  // Handle different data types for Primary_Key_Columns
  if (Array.isArray(primaryKeys)) {
    return primaryKeys.includes(columnName);
  } else if (typeof primaryKeys === 'object' && primaryKeys !== null) {
    // If it's an object, check if the column name is in the values
    return Object.values(primaryKeys).includes(columnName);
  } else if (typeof primaryKeys === 'string') {
    // If it's a string, check if it matches or is comma-separated
    return primaryKeys === columnName || primaryKeys.split(',').map(k => k.trim()).includes(columnName);
  }

  return false;
};

const DatabaseConnectionView: React.FC = () => {
  const [searchParams] = useSearchParams();
  const [connections, setConnections] = useState<DatabaseConnection[]>([]);
  const [selectedConnection, setSelectedConnection] = useState<DatabaseConnection | null>(null);
  const [connectionDetails, setConnectionDetails] = useState<ConnectionDetails | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingDetails, setIsLoadingDetails] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Navigation state for switching between views
  const [activeView, setActiveView] = useState<'db-connection' | 'table-view-access'>('db-connection');

  // Handle connection selection for table access view
  const handleConnectionSelect = async (connection: DatabaseConnection) => {
    try {
      setIsLoadingDetails(true);
      console.log(`Loading tables for connection: ${connection.connection_name}`);

      // Fetch tables for the selected connection
      const API_BASE_URL = 'http://***********:8001';
      const response = await fetch(`${API_BASE_URL}/metadata/get_tables_columns`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: 'a6e3020d-984a-4394-ac73-da7ec5393314',
          connection_name: connection.connection_name,
          db_dialect: connection.database_dialect,
          database_name: connection.database_name,
          username: "username",
          password: "password",
          host: "host",
          port: 5432,
          ssl_mode: "prefer"
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.tables && Array.isArray(result.tables)) {
          const tableNames = result.tables.map((table: any) => table.table_name);
          setAvailableTables(tableNames);
          toast.success(`Loaded ${tableNames.length} tables from ${connection.connection_name}`);
        }
      } else {
        // Fallback to demo data
        setAvailableTables(['physician', 'patient', 'appointment', 'medication', 'treatment']);
        toast.info('Using demo table data');
      }
    } catch (error) {
      console.error('Error loading tables:', error);
      setAvailableTables(['physician', 'patient', 'appointment', 'medication', 'treatment']);
      toast.info('Using demo table data');
    } finally {
      setIsLoadingDetails(false);
    }
  };

  // Form states
  const [searchQuery, setSearchQuery] = useState('');
  const [metadataResponse, setMetadataResponse] = useState<MetadataApiResponse | null>(null);
  const [availableTables, setAvailableTables] = useState<string[]>([]);
  const [availableViews, setAvailableViews] = useState<string[]>([]);
  const [selectedTables, setSelectedTables] = useState<string[]>([]);
  const [selectedViews, setSelectedViews] = useState<string[]>([]);
  const [showTableViewAccess, setShowTableViewAccess] = useState(false);
  const [currentTableIndex, setCurrentTableIndex] = useState(0);
  const [currentColumnIndex, setCurrentColumnIndex] = useState(0);
  const [showSearchDropdown, setShowSearchDropdown] = useState(false);
  const [filteredTables, setFilteredTables] = useState<string[]>([]);
  const [tableMetadataCache, setTableMetadataCache] = useState<Record<string, any>>({});
  const [tableDescriptions, setTableDescriptions] = useState<Record<string, string>>({});
  const [tablesColumnsData, setTablesColumnsData] = useState<any>(null);

  // Form field states
  const [tableName, setTableName] = useState('Table Name');
  const [tableDescription, setTableDescription] = useState('Table Description');
  const [columnName, setColumnName] = useState('Column Name');
  const [columnDescription, setColumnDescription] = useState('Table Description');
  const [columnAlias, setColumnAlias] = useState('Column Name');
  const [columnDatatype, setColumnDatatype] = useState('String');
  const [isPrimaryKey, setIsPrimaryKey] = useState(false);
  const [isUniqueKey, setIsUniqueKey] = useState(false);

  const userId = 'a6e3020d-984a-4394-ac73-da7ec5393314'; // Admin user ID
  const API_BASE_URL = 'http://***********:8001';

  useEffect(() => {
    fetchUserConnections();
  }, []);

  useEffect(() => {
    const connectionId = searchParams.get('connectionId');
    if (connectionId && connections.length > 0) {
      const connection = connections.find(conn => conn.connection_id === parseInt(connectionId));
      if (connection) {
        // Clear previous connection data when switching connections
        clearConnectionData();
        setSelectedConnection(connection);
        fetchConnectionDetails(connection.connection_id);
      }
    }
  }, [searchParams, connections]);

  // Function to clear connection-specific data
  const clearConnectionData = () => {
    setSelectedTables([]);
    setSelectedViews([]);
    setAvailableTables([]);
    setAvailableViews([]);
    setFilteredTables([]);
    setShowTableViewAccess(false);
    setTableMetadataCache({});
    setTableDescriptions({}); // Clear stored table descriptions
    setTablesColumnsData(null); // Clear tables/columns API data
    setSearchQuery('');
    setShowSearchDropdown(false);
    console.log('🧹 Cleared connection data, table descriptions, and tables/columns data for new connection');
  };

  const fetchUserConnections = async () => {
    try {
      setIsLoading(true);
      console.log(`Fetching connections for user: ${userId}`);

      const response = await fetch(`${API_BASE_URL}/database/user-connections-detailed/${userId}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: ApiConnectionsResponse = await response.json();
      console.log('Fetched connections:', data);

      if (data.status === 'success' && data.connections) {
        setConnections(data.connections);
        if (data.connections.length > 0) {
          setSelectedConnection(data.connections[0]);
          await fetchConnectionDetails(data.connections[0].connection_id);
        }
      } else {
        throw new Error('Invalid API response format');
      }
    } catch (error) {
      console.error('Error fetching connections:', error);
      // toast.error('Failed to load database connections. Using demo data.');

      // Fallback to static data for demo
      const staticConnections: DatabaseConnection[] = [
        { connection_id: 1, connection_name: 'health db test connection', database_dialect: 'postgresql', database_name: 'health_db' },
        { connection_id: 2, connection_name: 'health db rahul', database_dialect: 'postgresql', database_name: 'healthcare_dm' },
        { connection_id: 3, connection_name: 'test db', database_dialect: 'postgresql', database_name: 'health_db' },
        { connection_id: 4, connection_name: 'postgres data', database_dialect: 'postgresql', database_name: 'postgres' }
      ];
      setConnections(staticConnections);
      if (staticConnections.length > 0) {
        setSelectedConnection(staticConnections[0]);
        // Set static connection details for demo
        setConnectionDetails({
          connection_id: staticConnections[0].connection_id,
          connection_name: staticConnections[0].connection_name,
          db_dialect: staticConnections[0].database_dialect,
          database_name: staticConnections[0].database_name,
          username: 'admin',
          password: '****',
          host: 'localhost',
          port: 5432,
          ssl_mode: 'prefer',
          tables: [
            { table_name: 'Table1', table_type: 'BASE TABLE' },
            { table_name: 'users', table_type: 'BASE TABLE' },
            { table_name: 'orders', table_type: 'BASE TABLE' }
          ],
          views: [
            { view_name: 'View1', view_type: 'VIEW' },
            { view_name: 'user_summary', view_type: 'VIEW' }
          ]
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const fetchConnectionDetails = async (connectionId: number) => {
    try {
      setIsLoadingDetails(true);
      console.log(`Fetching metadata for connection ID: ${connectionId}`);

      const response = await fetch(`${API_BASE_URL}/metadata/get_metadata_by_connection/${connectionId}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: MetadataApiResponse = await response.json();
      console.log('Fetched connection metadata:', data);

      // Store the metadata response
      setMetadataResponse(data);

      // Find the selected connection info
      const selectedConn = connections.find(conn => conn.connection_id === connectionId) || selectedConnection;

      // Map the metadata response to our ConnectionDetails format
      setConnectionDetails({
        connection_id: data.connection_id,
        connection_name: selectedConn?.connection_name || 'DB Connection',
        db_dialect: data.Database_Dialect,
        database_name: data.Database_Name,
        username: 'user', // These might come from a separate API call
        password: '****',
        host: 'localhost',
        port: 5432,
        ssl_mode: 'prefer',
        tables: [{
          table_name: data.Table_Name,
          table_type: 'BASE TABLE'
        }],
        views: [] // No views in this response format
      });

      // Update form fields with the metadata
      setTableName(data.Table_Name);
      setTableDescription(data.Table_Description || 'Table Description');

      // Set first column data
      const columnKeys = Object.keys(data.Columns_List || {});
      if (columnKeys.length > 0) {
        const firstColumnName = data.Columns_List[columnKeys[0]];
        setColumnName(firstColumnName);
        setColumnAlias(firstColumnName);

        // Check if it's a primary key
        const isPrimary = checkIfPrimaryKey(firstColumnName, data);
        setIsPrimaryKey(isPrimary);
      }

      toast.success(`Loaded metadata for ${data.Table_Name} table`);
    } catch (error) {
      console.error('Error fetching connection metadata:', error);
      toast.error('Failed to load connection metadata. Using demo data.');

      // Fallback to static data
      const selectedConn = connections.find(conn => conn.connection_id === connectionId) || selectedConnection;
      setConnectionDetails({
        connection_id: connectionId,
        connection_name: selectedConn?.connection_name || 'DB Connection',
        db_dialect: selectedConn?.database_dialect || 'postgresql',
        database_name: selectedConn?.database_name || 'database',
        username: 'user',
        password: '****',
        host: 'localhost',
        port: 5432,
        ssl_mode: 'prefer',
        tables: [
          { table_name: 'Table1', table_type: 'BASE TABLE' },
          { table_name: 'users', table_type: 'BASE TABLE' },
          { table_name: 'orders', table_type: 'BASE TABLE' }
        ],
        views: [
          { view_name: 'View1', view_type: 'VIEW' },
          { view_name: 'user_summary', view_type: 'VIEW' }
        ]
      });
    } finally {
      setIsLoadingDetails(false);
    }
  };

  // Handle Scan Tables View button click - Fetch real data from API
  const handleScanTablesView = async () => {
    if (!connectionDetails) {
      toast.error('Please select a database connection first');
      return;
    }

    try {
      setIsLoadingDetails(true);
      console.log(`🔍 Scanning tables for connection: ${connectionDetails.connection_name} (ID: ${connectionDetails.connection_id})`);

      // Clear previous data for this connection
      setSelectedTables([]);
      setSelectedViews([]);
      setSearchQuery('');
      setShowSearchDropdown(false);

      // Fetch tables and columns from new API endpoint
      const response = await fetch(`${API_BASE_URL}/metadata/get_tables_columns/${connectionDetails.connection_id}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const apiData = await response.json();
      console.log(`📡 API Response from get_tables_columns for connection ${connectionDetails.connection_id}:`, apiData);

      // Extract table_name from new API response format
      let tableNames: string[] = [];

      if (apiData && typeof apiData === 'object') {
        // Check if response has table_name field (new API format)
        if (apiData.table_name) {
          if (Array.isArray(apiData.table_name)) {
            tableNames = apiData.table_name.filter(Boolean);
            console.log(`📋 Found ${tableNames.length} tables in table_name array:`, tableNames);
          } else if (typeof apiData.table_name === 'string') {
            tableNames = [apiData.table_name];
            console.log(`📋 Found single table in table_name:`, apiData.table_name);
          }
        }
        // Fallback: check for tables array or other structures
        else if (apiData.tables && Array.isArray(apiData.tables)) {
          tableNames = apiData.tables.map((table: any) => table.table_name || table.Table_Name).filter(Boolean);
          console.log(`📋 Found tables in tables array:`, tableNames);
        }
        // Fallback: check for Table_Name (old format)
        else if (apiData.Table_Name) {
          tableNames = [apiData.Table_Name];
          console.log(`📋 Found single table in Table_Name:`, apiData.Table_Name);
        }
        // Fallback: try to extract from any nested structure
        else {
          const extractTableNames = (obj: any): string[] => {
            const names: string[] = [];
            if (typeof obj === 'object' && obj !== null) {
              // Check for table_name field
              if (obj.table_name && typeof obj.table_name === 'string') {
                names.push(obj.table_name);
              }
              // Check for Table_Name field (old format)
              if (obj.Table_Name && typeof obj.Table_Name === 'string') {
                names.push(obj.Table_Name);
              }
              Object.values(obj).forEach(value => {
                if (Array.isArray(value)) {
                  value.forEach(item => names.push(...extractTableNames(item)));
                } else if (typeof value === 'object') {
                  names.push(...extractTableNames(value));
                }
              });
            }
            return names;
          };
          tableNames = extractTableNames(apiData);
          console.log(`📋 Extracted tables from nested structure:`, tableNames);
        }
      }

      // Remove duplicates and filter out empty values
      const uniqueTableNames = [...new Set(tableNames)].filter(name => name && name.trim() !== '');

      console.log('📋 Extracted Table Names from get_tables_columns API:', uniqueTableNames);

      // Store the full API response for later use
      setTablesColumnsData(apiData);

      if (uniqueTableNames.length === 0) {
        // Fallback to demo data if no tables found
        console.log('⚠️ No tables found in API response, using demo data');
        const fallbackTables = ['Table1', 'Table2', 'Table3', 'users', 'orders', 'products'];
        setAvailableTables(fallbackTables);
        setFilteredTables(fallbackTables);
        toast.warning(`No tables found in get_tables_columns API response. Using demo data: ${fallbackTables.length} tables`);
      } else {
        setAvailableTables(uniqueTableNames);
        setFilteredTables(uniqueTableNames);
        toast.success(`✅ Found ${uniqueTableNames.length} tables from get_tables_columns API`);
        console.log(`✅ Successfully loaded ${uniqueTableNames.length} tables for autocomplete`);
      }

      setShowTableViewAccess(true);

    } catch (error) {
      console.error('Error scanning tables from API:', error);

      // Fallback to demo data on API error
      const fallbackTables = ['Table1', 'Table2', 'Table3', 'users', 'orders', 'products', 'customers'];
      setAvailableTables(fallbackTables);
      setFilteredTables(fallbackTables);
      setShowTableViewAccess(true);

      toast.error('Failed to fetch tables from API. Using demo data.');
    } finally {
      setIsLoadingDetails(false);
    }
  };

  // Handle search input change with autocomplete
  const handleSearchChange = (value: string) => {
    setSearchQuery(value);

    if (value.trim() === '') {
      setShowSearchDropdown(false);
      setFilteredTables([]);
      return;
    }

    // Filter tables based on search query
    const filtered = availableTables.filter(table =>
      table.toLowerCase().includes(value.toLowerCase())
    );

    setFilteredTables(filtered);
    setShowSearchDropdown(filtered.length > 0);
  };

  // Handle table selection from autocomplete dropdown
  const handleTableSelectFromSearch = async (tableName: string) => {
    // Clear search query and hide dropdown for next search
    setSearchQuery('');
    setShowSearchDropdown(false);

    // Add the selected table to the Table View Access section
    if (!selectedTables.includes(tableName)) {
      setSelectedTables(prev => [...prev, tableName]);
      console.log(`✅ Added table to selection: ${tableName}`);
      toast.success(`Added table: ${tableName}`);
    } else {
      toast.info(`Table ${tableName} is already selected`);
      return; // Don't fetch metadata again if already selected
    }

    try {
      // Fetch detailed metadata for the selected table from API
      setIsLoadingDetails(true);
      console.log(`Fetching metadata for selected table: ${tableName}`);

      const response = await fetch(`${API_BASE_URL}/metadata/get_metadata_by_connection/${connectionDetails?.connection_id}`);

      if (response.ok) {
        const apiData = await response.json();
        console.log('API metadata for selected table:', apiData);

        // Find the specific table data from API response
        let tableData = null;

        if (Array.isArray(apiData)) {
          tableData = apiData.find((table: any) => table.Table_Name === tableName);
        } else if (apiData.Table_Name === tableName) {
          tableData = apiData;
        }

        if (tableData) {
          // Update form fields with real API data
          setTableName(tableData.Table_Name || tableName);

          // Patch Table_Description from API response
          console.log('Full tableData object:', tableData);
          console.log('Table_Description field:', tableData.Table_Description);
          console.log('Type of Table_Description:', typeof tableData.Table_Description);

          const apiTableDescription = tableData.Table_Description;
          if (apiTableDescription !== null && apiTableDescription !== undefined && String(apiTableDescription).trim() !== '') {
            const descriptionText = String(apiTableDescription);
            setTableDescription(descriptionText);
            console.log(`✅ Patched Table_Description: ${descriptionText}`);
            console.log(`✅ Current tableDescription state after setting:`, descriptionText);

            // Force a test update to verify the state is working
            setTimeout(() => {
              console.log('Checking tableDescription state after timeout:', descriptionText);
            }, 100);
          } else {
            setTableDescription(`No description available for ${tableName}`);
            console.log(`❌ No Table_Description found in API for ${tableName}. Value was:`, apiTableDescription);
          }

          // Update column information if available
          if (tableData.Columns_List && typeof tableData.Columns_List === 'object') {
            const columns = Object.keys(tableData.Columns_List);
            if (columns.length > 0) {
              setColumnName(columns[0]);
              setColumnAlias(columns[0]);
              setColumnDescription(tableData.Columns_List[columns[0]] || 'Column description');
            }
          }

          // Set primary key information if available
          if (tableData.Primary_Key_Columns) {
            const firstColumn = tableData.Columns_List ? Object.keys(tableData.Columns_List)[0] : '';
            setIsPrimaryKey(checkIfPrimaryKey(firstColumn, tableData));
          }

          toast.success(`Loaded metadata for table: ${tableName} with description`);
        } else {
          // Fallback if specific table not found
          setTableName(tableName);
          setTableDescription(`No description available for ${tableName}`);
          toast.warning(`Table ${tableName} selected, but detailed metadata not found`);
        }
      } else {
        throw new Error('Failed to fetch metadata');
      }
    } catch (error) {
      console.error('Error fetching table metadata:', error);
      // Fallback to basic information
      setTableName(tableName);
      setTableDescription(`Table Description for ${tableName}`);
      toast.error('Failed to load detailed metadata. Using basic information.');
    } finally {
      setIsLoadingDetails(false);
    }
  };

  // Handle table selection (Select All or individual Select) and load metadata
  const handleTableSelection = async (tableName: string, action: 'select' | 'selectAll') => {
    if (action === 'selectAll') {
      // Add table to selected if not already selected
      if (!selectedTables.includes(tableName)) {
        setSelectedTables(prev => [...prev, tableName]);
      }
    } else if (action === 'select') {
      // Toggle table selection
      if (selectedTables.includes(tableName)) {
        setSelectedTables(prev => prev.filter(t => t !== tableName));
      } else {
        setSelectedTables(prev => [...prev, tableName]);
      }
    }

    // Load metadata for the selected table to update form fields
    await loadTableMetadata(tableName);
    console.log(`Table ${tableName} action: ${action}`);
  };

  // Function to load metadata for a specific table and update form
  const loadTableMetadata = async (tableName: string) => {
    try {
      setIsLoadingDetails(true);
      console.log(`🔄 Loading metadata for table: ${tableName}`);

      // Check if we have cached data first
      const cachedData = tableMetadataCache[tableName];
      if (cachedData) {
        console.log(`📋 Using cached data for ${tableName}`);
        updateFormWithTableData(cachedData, tableName);
        return;
      }

      // Fetch from API if not cached
      const response = await fetch(`${API_BASE_URL}/metadata/get_metadata_by_connection/${connectionDetails?.connection_id}`);

      if (response.ok) {
        const apiData = await response.json();
        console.log('📡 API metadata response:', apiData);

        // Find the specific table data - Handle new API format
        let tableData = null;
        if (Array.isArray(apiData)) {
          // Multiple tables response
          tableData = apiData.find((table: any) => table.Table_Name === tableName);
        } else if (apiData.Table_Name === tableName) {
          // Single table response (new format)
          tableData = apiData;
          console.log(`📋 Found matching table data for ${tableName}`);
        } else if (apiData.Table_Name) {
          // Single table response but different table name
          console.log(`⚠️ API returned ${apiData.Table_Name} but looking for ${tableName}`);
          tableData = apiData; // Use it anyway for now
        }

        if (tableData) {
          // Cache the data for future use
          setTableMetadataCache(prev => ({
            ...prev,
            [tableName]: tableData
          }));

          // Update form with the data
          updateFormWithTableData(tableData, tableName);
          console.log(`✅ Successfully loaded metadata for ${tableName}`);
        } else {
          console.log(`❌ No metadata found for table: ${tableName}`);
          // Try to fetch metadata specifically for this table
          await fetchSpecificTableMetadata(tableName);
        }
      }
    } catch (error) {
      console.error('💥 Error loading table metadata:', error);
      setTableName(tableName);
      setTableDescription(`Error loading description for ${tableName}`);
    } finally {
      setIsLoadingDetails(false);
    }
  };

  // Function to fetch metadata for a specific table when not found in general response
  const fetchSpecificTableMetadata = async (tableName: string) => {
    try {
      console.log(`🔍 Fetching specific metadata for table: ${tableName}`);

      // Try different API endpoints or parameters to get specific table data
      const response = await fetch(`${API_BASE_URL}/metadata/get_metadata_by_connection/${connectionDetails?.connection_id}`);

      if (response.ok) {
        const apiData = await response.json();
        console.log(`📡 Specific API response for ${tableName}:`, apiData);

        // Check if this response contains our table
        if (apiData.Table_Name === tableName) {
          updateFormWithTableData(apiData, tableName);
          return;
        }
      }

      // If still not found, set fallback values
      setTableName(tableName);
      setTableDescription(`No description available for ${tableName}`);
      console.log(`⚠️ Could not find specific metadata for ${tableName}`);

    } catch (error) {
      console.error(`💥 Error fetching specific metadata for ${tableName}:`, error);
      setTableName(tableName);
      setTableDescription(`Error loading description for ${tableName}`);
    }
  };

  // Helper function to update form fields with table data
  const updateFormWithTableData = (tableData: any, tableName: string) => {
    console.log(`🔧 Updating form for ${tableName} with API data:`, tableData);

    // Update table name
    setTableName(tableData.Table_Name || tableName);

    // Update table description from API and store it
    const apiTableDescription = tableData.Table_Description;
    console.log(`📝 Table_Description from API for ${tableName}:`, apiTableDescription);
    console.log(`📝 Type of Table_Description:`, typeof apiTableDescription);

    if (apiTableDescription !== null && apiTableDescription !== undefined && String(apiTableDescription).trim() !== '') {
      const descriptionText = String(apiTableDescription);

      // Store the description for this specific table
      setTableDescriptions(prev => ({
        ...prev,
        [tableName]: descriptionText
      }));

      // Update the form field
      setTableDescription(descriptionText);
      console.log(`✅ Stored and updated Table_Description for ${tableName}: ${descriptionText}`);
    } else {
      const fallbackDescription = `No description available for ${tableName}`;

      // Store the fallback description
      setTableDescriptions(prev => ({
        ...prev,
        [tableName]: fallbackDescription
      }));

      setTableDescription(fallbackDescription);
      console.log(`❌ No Table_Description found for ${tableName}. Using fallback. Value was:`, apiTableDescription);
    }

    // Update column information - Handle indexed object format
    if (tableData.Columns_List && typeof tableData.Columns_List === 'object') {
      const columnValues = Object.values(tableData.Columns_List);
      console.log(`📋 Columns from API:`, columnValues);

      if (columnValues.length > 0) {
        const firstColumnName = String(columnValues[0]);
        setColumnName(firstColumnName);
        setColumnAlias(firstColumnName);
        setColumnDescription(`Column: ${firstColumnName}`);
        setCurrentColumnIndex(0);
        console.log(`🔧 Set first column: ${firstColumnName}`);
      }
    }

    // Update primary key information - Handle indexed object format
    if (tableData.Primary_Key_Columns) {
      const columnValues = tableData.Columns_List ? Object.values(tableData.Columns_List) : [];

      if (columnValues.length > 0) {
        const firstColumn = String(columnValues[0]);
        const isPrimary = checkIfPrimaryKey(firstColumn, tableData);
        setIsPrimaryKey(isPrimary);
        console.log(`🔑 Primary key check for ${firstColumn}: ${isPrimary}`);
      }
    }
  };

  // Handle view selection
  const handleViewSelection = (viewName: string, action: 'select' | 'selectAll') => {
    if (action === 'selectAll') {
      if (!selectedViews.includes(viewName)) {
        setSelectedViews(prev => [...prev, viewName]);
      }
    } else if (action === 'select') {
      if (selectedViews.includes(viewName)) {
        setSelectedViews(prev => prev.filter(v => v !== viewName));
      } else {
        setSelectedViews(prev => [...prev, viewName]);
      }
    }
  };

  // Start column metadata analysis with API data
  const handleStartColumnMetadata = async () => {
    if (selectedTables.length === 0 && selectedViews.length === 0) {
      toast.error('Please select at least one table or view');
      return;
    }

    try {
      setIsLoadingDetails(true);
      console.log('🚀 Starting metadata analysis for:', { selectedTables, selectedViews });

      // Load metadata for the first selected table from API
      const firstTable = selectedTables[0] || selectedViews[0];
      console.log(`🎯 Loading metadata for first table: ${firstTable}`);

      // Fetch detailed metadata from API
      const response = await fetch(`${API_BASE_URL}/metadata/get_metadata_by_connection/${connectionDetails?.connection_id}`);

      if (response.ok) {
        const apiData = await response.json();
        console.log('API metadata for start column analysis:', apiData);

        // Cache metadata - Handle new single table API format
        const metadataCache: Record<string, any> = {};
        if (Array.isArray(apiData)) {
          // Multiple tables response
          apiData.forEach((table: any) => {
            if (table.Table_Name) {
              metadataCache[table.Table_Name] = table;
            }
          });
        } else if (apiData.Table_Name) {
          // Single table response (new format)
          metadataCache[apiData.Table_Name] = apiData;
          console.log(`📋 Cached single table: ${apiData.Table_Name}`);
        }
        setTableMetadataCache(metadataCache);
        console.log('Cached metadata for tables:', Object.keys(metadataCache));

        // Find the specific table data for the first table
        let tableData = metadataCache[firstTable];

        // If not found and we have a single table response, use it
        if (!tableData && apiData.Table_Name && Object.keys(metadataCache).length === 1) {
          tableData = apiData;
          console.log(`📋 Using single table data for ${firstTable}`);
        }

        if (tableData) {
          // Use the helper function to update form data
          updateFormWithTableData(tableData, firstTable);
          console.log(`✅ Start Column - Successfully loaded metadata for ${firstTable}`);
        } else {
          console.log(`❌ Start Column - No metadata found for ${firstTable}, trying specific fetch`);
          // Try to fetch specific metadata for this table
          await fetchSpecificTableMetadata(firstTable);
        }
      } else {
        throw new Error('Failed to fetch metadata from API');
      }

      setCurrentTableIndex(0);
      setCurrentColumnIndex(0);

      toast.success(`Loaded metadata for ${selectedTables.length + selectedViews.length} items from API`);
    } catch (error) {
      console.error('Error loading metadata from API:', error);

      // Fallback to basic data
      const firstTable = selectedTables[0] || selectedViews[0];
      setTableName(firstTable);
      setTableDescription(`No description available for ${firstTable}`);
      setColumnName('Column Name');
      setColumnDescription('No description available');
      setColumnAlias('Column Name');
      setCurrentTableIndex(0);
      setCurrentColumnIndex(0);

      toast.error('Failed to load metadata from API. Using basic information.');
    } finally {
      setIsLoadingDetails(false);
    }
  };

  // Navigate between tables with API data
  const handleTableNavigation = async (direction: 'prev' | 'next') => {
    const totalTables = selectedTables.length + selectedViews.length;
    if (totalTables === 0) return;

    let newIndex = currentTableIndex;
    if (direction === 'next' && currentTableIndex < totalTables - 1) {
      newIndex = currentTableIndex + 1;
    } else if (direction === 'prev' && currentTableIndex > 0) {
      newIndex = currentTableIndex - 1;
    }

    setCurrentTableIndex(newIndex);

    // Update table name and description based on new index
    const allSelected = [...selectedTables, ...selectedViews];
    const currentTable = allSelected[newIndex];

    if (currentTable) {
      console.log(`Navigating to table ${newIndex + 1}/${totalTables}: ${currentTable}`);

      // Use cached metadata if available, otherwise fetch from API
      const cachedTableData = tableMetadataCache[currentTable];

      if (cachedTableData) {
        // Use cached data for faster pagination
        console.log(`Using cached metadata for ${currentTable}`);

        setTableName(cachedTableData.Table_Name || currentTable);

        // Use stored description first, then fall back to cached API data
        const storedDescription = tableDescriptions[currentTable];
        if (storedDescription) {
          setTableDescription(storedDescription);
          console.log(`✅ Pagination - Using stored description for ${currentTable}: ${storedDescription}`);
        } else {
          // Fall back to cached API data
          console.log('Pagination - Cached tableData:', cachedTableData);
          const apiTableDescription = cachedTableData.Table_Description;
          console.log('Pagination - Table_Description:', apiTableDescription);

          if (apiTableDescription !== null && apiTableDescription !== undefined && String(apiTableDescription).trim() !== '') {
            const descriptionText = String(apiTableDescription);
            setTableDescription(descriptionText);

            // Store it for future use
            setTableDescriptions(prev => ({
              ...prev,
              [currentTable]: descriptionText
            }));

            console.log(`✅ Pagination - Using cached Table_Description and storing: ${descriptionText}`);
          } else {
            const fallbackDescription = `No description available for ${currentTable}`;
            setTableDescription(fallbackDescription);

            // Store the fallback
            setTableDescriptions(prev => ({
              ...prev,
              [currentTable]: fallbackDescription
            }));

            console.log(`❌ Pagination - No cached Table_Description for ${currentTable}. Value:`, apiTableDescription);
          }
        }

        // Update column information for the current table
        if (cachedTableData.Columns_List && typeof cachedTableData.Columns_List === 'object') {
          const columns = Object.keys(cachedTableData.Columns_List);
          if (columns.length > 0) {
            setColumnName(columns[0]);
            setColumnAlias(columns[0]);
            setColumnDescription(cachedTableData.Columns_List[columns[0]] || 'No column description');

            // Reset column index when switching tables
            setCurrentColumnIndex(0);
          }
        }

        // Set primary key information
        if (cachedTableData.Primary_Key_Columns) {
          const firstColumn = cachedTableData.Columns_List ? Object.keys(cachedTableData.Columns_List)[0] : '';
          setIsPrimaryKey(checkIfPrimaryKey(firstColumn, cachedTableData));
        }

      } else {
        // Fallback: fetch from API if not cached
        try {
          setIsLoadingDetails(true);
          console.log(`Fetching metadata from API for ${currentTable}`);

          const response = await fetch(`${API_BASE_URL}/metadata/get_metadata_by_connection/${connectionDetails?.connection_id}`);

          if (response.ok) {
            const apiData = await response.json();

            let tableData = null;
            if (Array.isArray(apiData)) {
              tableData = apiData.find((table: any) => table.Table_Name === currentTable);
            } else if (apiData.Table_Name === currentTable) {
              tableData = apiData;
            }

            if (tableData) {
              setTableName(tableData.Table_Name || currentTable);

              console.log('Pagination API Fetch - Full tableData:', tableData);
              const apiTableDescription = tableData.Table_Description;
              console.log('Pagination API Fetch - Table_Description:', apiTableDescription);

              if (apiTableDescription !== null && apiTableDescription !== undefined && String(apiTableDescription).trim() !== '') {
                setTableDescription(String(apiTableDescription));
                console.log(`✅ Pagination API - Fetched Table_Description: ${apiTableDescription}`);
              } else {
                setTableDescription(`No description available for ${currentTable}`);
                console.log(`❌ Pagination API - No Table_Description for ${currentTable}. Value:`, apiTableDescription);
              }

              // Cache the fetched data for future use
              setTableMetadataCache(prev => ({
                ...prev,
                [currentTable]: tableData
              }));

            } else {
              setTableName(currentTable);
              setTableDescription(`No description available for ${currentTable}`);
            }
          }
        } catch (error) {
          console.error('Error fetching table metadata:', error);
          setTableName(currentTable);
          setTableDescription(`No description available for ${currentTable}`);
        } finally {
          setIsLoadingDetails(false);
        }
      }
    }
  };

  // Navigate between columns
  const handleColumnNavigation = (direction: 'prev' | 'next') => {
    // For demo, assume each table has multiple columns
    const totalColumns = 21; // As shown in the image

    let newIndex = currentColumnIndex;
    if (direction === 'next' && currentColumnIndex < totalColumns - 1) {
      newIndex = currentColumnIndex + 1;
    } else if (direction === 'prev' && currentColumnIndex > 0) {
      newIndex = currentColumnIndex - 1;
    }

    setCurrentColumnIndex(newIndex);

    // Update column info based on index
    const columnNames = ['passengerid', 'survived', 'pclass', 'name', 'sex', 'age', 'sibsp', 'parch', 'ticket', 'fare', 'cabin', 'embarked'];
    const currentColumn = columnNames[newIndex % columnNames.length];
    setColumnName(currentColumn);
    setColumnAlias(currentColumn);
    setIsPrimaryKey(newIndex === 0); // First column is primary key
  };



  const testConnection = async () => {
    if (!connectionDetails) {
      toast.error('No connection selected');
      return;
    }

    try {
      setIsTesting(true);
      console.log('Testing connection:', connectionDetails.connection_name);

      const payload = {
        user_id: userId,
        connection_name: connectionDetails.connection_name,
        db_dialect: connectionDetails.db_dialect,
        database_name: connectionDetails.database_name,
        username: connectionDetails.username,
        password: connectionDetails.password,
        host: connectionDetails.host,
        port: connectionDetails.port,
        ssl_mode: connectionDetails.ssl_mode
      };

      console.log('Test connection payload:', payload);

      const response = await fetch(`${API_BASE_URL}/database/test-connection`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('Test connection result:', result);
      toast.success(`Connection test successful for ${connectionDetails.connection_name}!`);
    } catch (error) {
      console.error('Error testing connection:', error);
      toast.error('Failed to test database connection');
    } finally {
      setIsTesting(false);
    }
  };

  const saveConnection = async () => {
    if (!connectionDetails) {
      toast.error('No connection details to save');
      return;
    }

    try {
      setIsSaving(true);
      const payload = {
        user_id: userId,
        connection_name: connectionDetails.connection_name,
        db_dialect: connectionDetails.db_dialect,
        database_name: connectionDetails.database_name,
        username: connectionDetails.username,
        password: connectionDetails.password,
        host: connectionDetails.host,
        port: connectionDetails.port,
        ssl_mode: connectionDetails.ssl_mode
      };

      console.log('Saving connection with payload:', payload);

      const response = await fetch(`${API_BASE_URL}/database/add-connection`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('Save connection result:', result);
      toast.success('Database connection saved successfully!');
      fetchUserConnections(); // Refresh the connections list
    } catch (error) {
      console.error('Error saving connection:', error);
      toast.error('Failed to save database connection');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="h-screen bg-gray-100">
      {/* Navigation Buttons */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex justify-center gap-4">
          <Button
            onClick={() => setActiveView('db-connection')}
            className={`px-6 py-2 border ${
              activeView === 'db-connection'
                ? 'bg-blue-600 text-white border-blue-600'
                : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
            }`}
          >
            DB Connection
          </Button>
          <Button
            onClick={() => setActiveView('table-view-access')}
            className={`px-6 py-2 border ${
              activeView === 'table-view-access'
                ? 'bg-blue-600 text-white border-blue-600'
                : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
            }`}
          >
            Table/ View Access
          </Button>
        </div>
      </div>

      {/* Main Content - Full Width */}
      <div className="p-4 bg-gray-100">
        {/* Main Heading */}
        <div className="mb-6 text-center">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            {activeView === 'db-connection' ? 'Database Connection Management' : 'Table/ View Access Management'}
          </h1>
          <p className="text-gray-600">
            {activeView === 'db-connection'
              ? 'Manage database connections and metadata'
              : 'Configure table and view access permissions'
            }
          </p>
        </div>

        {/* DB Connection View */}
        {activeView === 'db-connection' && (
          <>
            {/* Exact UI Layout from Image */}
            <div className="max-w-4xl mx-auto space-y-3 bg-white">

          {/* DB Connection Name Row */}
          <div className="flex items-center gap-3">
            <label className="text-sm font-medium w-32 text-blue-600">DB Connection Name</label>
            <Input
              value={connectionDetails?.connection_name || 'DB Connection Name'}
              readOnly
              className="flex-1 max-w-md border border-gray-400 bg-white text-sm"
            />
            <Button
              variant="outline"
              size="sm"
              className="px-3 py-1 bg-gray-500 text-white border-gray-500 hover:bg-gray-600"
            >
              🗑️
            </Button>
          </div>

          {/* Scan Tables View Button */}
          <div className="flex justify-center py-2">
            <Button
              onClick={handleScanTablesView}
              disabled={!connectionDetails}
              className="bg-gray-600 text-white hover:bg-gray-700 px-8 py-2 text-sm font-medium"
            >
              Scan Tables View
            </Button>
          </div>

          {/* Search Table View Row with Autocomplete */}
          <div className="flex items-center gap-3 relative">
            <label className="text-sm font-medium w-32">Search Table View</label>
            <div className="relative flex-1 max-w-md">
              <Input
                value={searchQuery}
                onChange={(e) => handleSearchChange(e.target.value)}
                placeholder=""
                className="w-full border border-gray-400 text-sm"
                disabled={!showTableViewAccess}
                onFocus={() => {
                  if (searchQuery && filteredTables.length > 0) {
                    setShowSearchDropdown(true);
                  }
                }}
                onBlur={() => {
                  // Delay hiding dropdown to allow clicking on options
                  setTimeout(() => setShowSearchDropdown(false), 200);
                }}
              />

              {/* Autocomplete Dropdown */}
              {showSearchDropdown && filteredTables.length > 0 && (
                <div className="absolute top-full left-0 right-0 bg-white border border-gray-400 border-t-0 max-h-40 overflow-y-auto z-10 shadow-lg">
                  {filteredTables.map((table, index) => (
                    <div
                      key={index}
                      onClick={() => handleTableSelectFromSearch(table)}
                      className="px-3 py-2 hover:bg-blue-100 cursor-pointer text-sm border-b border-gray-200 last:border-b-0"
                    >
                      {table}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Table View Access Section - Exact Image Match */}
          <div className="border border-gray-400 bg-white">
            <div className="text-center text-sm font-medium bg-gray-200 py-2 border-b border-gray-400">
              Table View Access
            </div>

            <div className="p-4 space-y-4">
              <div className="bg-gray-100 p-2 text-sm font-medium border border-gray-300">
                {selectedConnection?.connection_name || connectionDetails?.connection_name || 'No Connection Selected'}
              </div>

              {isLoadingDetails ? (
                <div className="text-sm text-gray-500">Loading tables and views...</div>
              ) : showTableViewAccess ? (
                <div className="space-y-3">
                  {/* Show only selected tables from search with checkboxes */}
                  {selectedTables.length > 0 ? (
                    selectedTables.map((table, index) => (
                      <div key={`selected-table-${index}`} className="flex items-center justify-between py-1">
                        <div className="flex items-center gap-2">
                          <input
                            type="checkbox"
                            checked={true} // Always checked since these are selected tables
                            onChange={(e) => {
                              if (!e.target.checked) {
                                // Remove table from selection if unchecked
                                setSelectedTables(prev => prev.filter(t => t !== table));
                                console.log(`Removed table from selection: ${table}`);
                              }
                            }}
                            className="w-4 h-4 text-blue-600"
                          />
                          <span className="text-sm text-red-600 font-medium">{table}</span>
                        </div>
                        <Select
                          defaultValue="select"
                          onValueChange={(value) => handleTableSelection(table, value as 'select' | 'selectAll')}
                        >
                          <SelectTrigger className="w-24 h-7 text-xs border border-gray-400">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="selectAll">Select All</SelectItem>
                            <SelectItem value="select">Select</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    ))
                  ) : (
                    <div className="text-sm text-gray-500">
                      Use the search above to find and select tables
                    </div>
                  )}

                  {/* Views (if any selected) */}
                  {selectedViews.map((view, index) => (
                    <div key={`selected-view-${index}`} className="flex items-center justify-between">
                      <span className="text-sm text-blue-600 font-medium">{view}</span>
                      <Select
                        defaultValue="select"
                        onValueChange={(value) => handleViewSelection(view, value as 'select' | 'selectAll')}
                      >
                        <SelectTrigger className="w-24 h-7 text-xs border border-gray-400">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="selectAll">Select All</SelectItem>
                          <SelectItem value="select">Select</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  ))}

                  {/* Selection summary */}
                  {(selectedTables.length > 0 || selectedViews.length > 0) && (
                    <div className="mt-4 p-2 bg-blue-50 border border-blue-200 text-sm">
                      <strong>Selected:</strong> {selectedTables.length} tables, {selectedViews.length} views
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-sm text-gray-500">
                  Click "Scan Tables View" to load available tables and views
                </div>
              )}
            </div>

            <div className="p-4 pt-0">
              <Button
                className="w-full bg-gray-600 text-white hover:bg-gray-700 text-sm py-2 font-medium"
                onClick={handleStartColumnMetadata}
                disabled={selectedTables.length === 0 && selectedViews.length === 0}
              >
                Start column metadata
              </Button>
            </div>
          </div>

          {/* Tables Section - Exact Image Match */}
          <div className="border border-gray-400 bg-white mt-4">
            <div className="text-center text-sm font-medium bg-gray-200 py-2 border-b border-gray-400">
              Tables
            </div>
            <div className="p-4 space-y-4">
              <div className="flex items-center gap-3">
                <label className="text-sm font-medium w-24">Table Name:</label>
                <Input
                  value={tableName}
                  onChange={(e) => setTableName(e.target.value)}
                  className="flex-1 max-w-sm border border-gray-400 text-sm"
                />
                <Button variant="outline" size="sm" className="px-2 py-1 border border-gray-400">
                  ⚙️
                </Button>
              </div>

              <div className="flex items-center gap-3">
                <label className="text-sm font-medium w-24">Table Description:</label>
                <Input
                  value={tableDescription}
                  onChange={(e) => {
                    console.log('Input onChange triggered with value:', e.target.value);
                    setTableDescription(e.target.value);
                  }}
                  className="flex-1 border border-gray-400 text-sm"
                  placeholder="Table description will appear here..."
                  onFocus={() => console.log('Table Description Input focused, current value:', tableDescription)}
                />
                {/* Test button to verify UI updates */}
                <Button
                  onClick={() => {
                    const testDesc = "TEST: Contains basic patient information including Patient_Id, Patient_First_Name, Patient_Last_Name, Active_Record status, and ETL_DT. The table tracks patients and their record statuses over time, as well as loading/extract dates.";
                    setTableDescription(testDesc);
                    console.log('Test button clicked - set description to:', testDesc);
                  }}
                  className="px-2 py-1 text-xs bg-blue-500 text-white"
                >
                  Test
                </Button>
              </div>

              {/* Pagination Controls for Tables */}
              <div className="flex items-center justify-end gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="px-3 py-1 text-xs border border-gray-400"
                  onClick={() => handleTableNavigation('prev')}
                  disabled={currentTableIndex === 0}
                >
                  ←
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="px-3 py-1 text-xs border border-gray-400"
                  onClick={() => handleTableNavigation('next')}
                  disabled={currentTableIndex >= (selectedTables.length + selectedViews.length) - 1}
                >
                  →
                </Button>
                <span className="text-xs text-gray-600 ml-2">
                  {currentTableIndex + 1}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  className="px-3 py-1 text-xs border border-gray-400"
                >
                  ←
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="px-3 py-1 text-xs border border-gray-400"
                >
                  →
                </Button>
                <span className="text-xs text-gray-600 ml-2">999</span>
              </div>
            </div>
          </div>

          {/* Columns Section - Exact Image Match */}
          <div className="border border-gray-400 bg-white mt-4">
            <div className="text-center text-sm font-medium bg-gray-200 py-2 border-b border-gray-400">
              Columns
            </div>
            <div className="p-4">
              {/* First Row - Column Name and Checkboxes */}
              <div className="flex items-center gap-6 mb-4">
                <div className="flex items-center gap-2">
                  <label className="text-sm font-medium w-24">Column Name:</label>
                  <Input
                    value={columnName}
                    onChange={(e) => setColumnName(e.target.value)}
                    className="w-48 h-7 text-sm border border-gray-400"
                  />
                </div>

                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={isPrimaryKey}
                    onChange={(e) => setIsPrimaryKey(e.target.checked)}
                    className="w-4 h-4"
                  />
                  <label className="text-sm">Primary Key</label>
                </div>

                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={isUniqueKey}
                    onChange={(e) => setIsUniqueKey(e.target.checked)}
                    className="w-4 h-4"
                  />
                  <label className="text-sm">Unique Key</label>
                </div>
              </div>

              {/* Second Row - Column Description */}
              <div className="flex items-center gap-2 mb-4">
                <label className="text-sm font-medium w-24">Column Description:</label>
                <Input
                  value={columnDescription}
                  onChange={(e) => setColumnDescription(e.target.value)}
                  className="flex-1 h-7 text-sm border border-gray-400"
                />
              </div>

              {/* Third Row - Column Alias and Datatype with Pagination */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-6">
                  <div className="flex items-center gap-2">
                    <label className="text-sm font-medium w-24">Column Alias:</label>
                    <Input
                      value={columnAlias}
                      onChange={(e) => setColumnAlias(e.target.value)}
                      className="w-48 h-7 text-sm border border-gray-400"
                    />
                  </div>

                  <div className="flex items-center gap-2">
                    <label className="text-sm font-medium">Column Datatype:</label>
                    <Select value={columnDatatype} onValueChange={setColumnDatatype}>
                      <SelectTrigger className="w-32 h-7 text-sm border border-gray-400">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="String">String</SelectItem>
                        <SelectItem value="Integer">Integer</SelectItem>
                        <SelectItem value="Float">Float</SelectItem>
                        <SelectItem value="Boolean">Boolean</SelectItem>
                        <SelectItem value="Date">Date</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Pagination Controls */}
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="px-3 py-1 text-xs border border-gray-400"
                    onClick={() => handleColumnNavigation('prev')}
                    disabled={currentColumnIndex === 0}
                  >
                    ←
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="px-3 py-1 text-xs border border-gray-400"
                    onClick={() => handleColumnNavigation('next')}
                    disabled={currentColumnIndex >= 20}
                  >
                    →
                  </Button>
                  <span className="text-xs text-gray-600 bg-white border border-gray-400 px-2 py-1 min-w-[2rem] text-center">
                    10
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    className="px-3 py-1 text-xs border border-gray-400"
                  >
                    ←
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="px-3 py-1 text-xs border border-gray-400"
                  >
                    →
                  </Button>
                  <span className="text-xs text-gray-600 ml-1">999</span>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-2 pt-4">
            <Button
              className="bg-gray-600 text-white hover:bg-gray-700 px-6 py-1 text-sm"
              onClick={saveConnection}
              disabled={isSaving}
            >
              Save
            </Button>
            <Button variant="outline" className="px-6 py-1 text-sm border-gray-300">
              Cancel
            </Button>
          </div>
        </div>
          </>
        )}

        {/* Table/ View Access View */}
        {activeView === 'table-view-access' && (
          <div className="max-w-4xl mx-auto space-y-6 bg-white p-6 rounded-lg shadow-sm">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-semibold text-gray-800 mb-2">Table/ View Access Management</h2>
              <p className="text-gray-600">Configure access permissions for database tables and views</p>
            </div>

            {/* Connection Selection for Table Access */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">Select Database Connection</label>
              <Select
                value={selectedConnection?.connection_id.toString() || ''}
                onValueChange={(value) => {
                  const connection = connections.find(c => c.connection_id.toString() === value);
                  if (connection) {
                    setSelectedConnection(connection);
                    handleConnectionSelect(connection);
                  }
                }}
              >
                <SelectTrigger className="w-full max-w-md">
                  <SelectValue placeholder="Choose a database connection" />
                </SelectTrigger>
                <SelectContent>
                  {connections.map((connection) => (
                    <SelectItem key={connection.connection_id} value={connection.connection_id.toString()}>
                      {connection.connection_name} ({connection.database_dialect})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Table/View Access Configuration */}
            {selectedConnection && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Available Tables */}
                  <div className="border border-gray-300 rounded-lg">
                    <div className="bg-gray-100 border-b border-gray-300 px-4 py-3">
                      <h3 className="font-medium text-gray-800">Available Tables</h3>
                    </div>
                    <div className="p-4 max-h-64 overflow-y-auto">
                      {availableTables.length > 0 ? (
                        <div className="space-y-2">
                          {availableTables.map((table, index) => (
                            <div key={index} className="flex items-center justify-between p-2 hover:bg-gray-50 rounded">
                              <span className="text-sm">{table}</span>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  if (!selectedTables.includes(table)) {
                                    setSelectedTables([...selectedTables, table]);
                                  }
                                }}
                                disabled={selectedTables.includes(table)}
                              >
                                {selectedTables.includes(table) ? 'Added' : 'Add'}
                              </Button>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-gray-500 text-sm">No tables available</p>
                      )}
                    </div>
                  </div>

                  {/* Selected Tables */}
                  <div className="border border-gray-300 rounded-lg">
                    <div className="bg-gray-100 border-b border-gray-300 px-4 py-3">
                      <h3 className="font-medium text-gray-800">Selected Tables</h3>
                    </div>
                    <div className="p-4 max-h-64 overflow-y-auto">
                      {selectedTables.length > 0 ? (
                        <div className="space-y-2">
                          {selectedTables.map((table, index) => (
                            <div key={index} className="flex items-center justify-between p-2 hover:bg-gray-50 rounded">
                              <span className="text-sm">{table}</span>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  setSelectedTables(selectedTables.filter(t => t !== table));
                                }}
                              >
                                Remove
                              </Button>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-gray-500 text-sm">No tables selected</p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Action Buttons for Table Access */}
                <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
                  <Button
                    onClick={() => {
                      toast.success('Table access permissions saved successfully!');
                    }}
                    className="bg-blue-600 text-white hover:bg-blue-700 px-6 py-2"
                  >
                    Save Access Settings
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSelectedTables([]);
                      setSelectedViews([]);
                    }}
                    className="px-6 py-2"
                  >
                    Reset
                  </Button>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default DatabaseConnectionView;


