
import React from 'react';
import { X, ArrowLeft, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAppDispatch } from '@/hooks/useRedux';
import { nextStep, previousStep } from '@/stores/datasetSlice';

interface StepHeaderProps {
  title: string;
  onClose: () => void;
}

const StepHeader: React.FC<StepHeaderProps> = ({
  title,
  onClose,
}) => {
  return (
    <>
      <div className="flex justify-between mb-4">
        <div className="text-xl font-bold">{title}</div>
        <button 
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600"
        >
          <X size={20} />
        </button>
      </div>
      
      {/* The content of the step will be rendered between the StepHeader and the bottom buttons */}
    </>
  );
};

interface NavigationButtonsProps {
  showPrevious?: boolean;
  showNext?: boolean;
  disableNext?: boolean;
  prevButtonLabel?: string;
  nextButtonLabel?: string;
}

const NavigationButtons: React.FC<NavigationButtonsProps> = ({ 
  showPrevious = true, 
  showNext = true, 
  disableNext = false, 
  prevButtonLabel = 'Previous', 
  nextButtonLabel = 'Next' 
}) => {
  const dispatch = useAppDispatch();
  
  const handleNext = () => {
    dispatch(nextStep());
  };

  const handlePrevious = () => {
    dispatch(previousStep());
  };
  
  return (
    <div className="flex justify-between mt-6">
      {showPrevious ? (
        <Button 
          variant="outline" 
          onClick={handlePrevious}
        >
          <ArrowLeft size={16} className="mr-2" /> {prevButtonLabel}
        </Button>
      ) : <div></div>}
      
      {showNext && (
        <Button 
          disabled={disableNext}
          onClick={handleNext}
        >
          {nextButtonLabel} <ArrowRight size={16} className="ml-2" />
        </Button>
      )}
    </div>
  );
};

export { NavigationButtons };
export default StepHeader;
