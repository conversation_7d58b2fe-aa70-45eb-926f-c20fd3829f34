
import React, { useState, useEffect } from 'react';
import { Database, ChevronDown, Loader2, PanelLeftClose } from 'lucide-react';
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { useAppSelector, useAppDispatch } from '@/hooks/useRedux';
import { setSelectedDatabase, setSelectedConnectionId, setConnections, setIsLoading, setError } from '@/stores/dadaSlice';
import { fetchUserConnections } from '@/services/api/dadaAI/connectionService';
import { toast } from 'sonner';

// Define the DatabaseConnection interface here since the types file is missing
interface DatabaseConnection {
  connection_id: number;
  connection_name: string;
  database_dialect: string;
  database_name: string;
}

interface DadaConnectionDropdownProps {
  onToggleSidebar: () => void;
}

const DadaConnectionDropdown: React.FC<DadaConnectionDropdownProps> = ({ 
  onToggleSidebar 
}) => {
  const dispatch = useAppDispatch();
  const { selectedDatabase, selectedConnectionId, connections, isLoading } = useAppSelector(state => state.dada);

  // Fetch connections on component mount
  useEffect(() => {
    const loadConnections = async () => {
      dispatch(setIsLoading(true));
      dispatch(setError(null));
      
      try {
        console.log('DADA: Loading database connections...');
        const userConnections = await fetchUserConnections();
        dispatch(setConnections(userConnections));
        
        // Auto-select the first connection if none is selected
        if (userConnections.length > 0 && !selectedConnectionId) {
          const firstConnection = userConnections[0];
          dispatch(setSelectedDatabase(firstConnection.database_name));
          dispatch(setSelectedConnectionId(firstConnection.connection_id));
          
          console.log(`DADA: Auto-selected connection: ${firstConnection.database_name} (ID: ${firstConnection.connection_id})`);
          
          toast.success(`Connected to ${firstConnection.database_name}`, {
            description: `Using connection: ${firstConnection.connection_name}`
          });
        }
      } catch (error) {
        console.error('DADA: Failed to load connections:', error);
        dispatch(setError('Failed to load database connections'));
        toast.error('Failed to load database connections');
      } finally {
        dispatch(setIsLoading(false));
      }
    };
    
    loadConnections();
  }, [dispatch, selectedConnectionId]);

  const handleDatabaseSelect = (connection: DatabaseConnection) => {
    console.log("DADA: Selected connection:", connection);
    
    try {
      // Store the selected database name and connection ID in dadaSlice
      dispatch(setSelectedDatabase(connection.database_name));
      dispatch(setSelectedConnectionId(connection.connection_id));
      
      // Log the connection ID to verify it's being set correctly
      console.log(`DADA: Set connection ID to: ${connection.connection_id}`);
      
      toast.success(`Connected to ${connection.database_name}`, {
        description: `Using connection: ${connection.connection_name}`
      });
    } catch (error) {
      console.error('DADA: Error selecting database:', error);
      toast.error(`Failed to connect to ${connection.database_name}`);
    }
  };

  return (
    <div className="mb-2 flex items-center gap-2 group">
      <DropdownMenu.Root>
        <DropdownMenu.Trigger asChild>
          <button
            className="flex-1 flex items-center text-xs text-white bg-blue-500 hover:bg-blue-600 rounded px-2 py-1 w-full justify-between"
            disabled={isLoading}
            data-testid="dada-database-dropdown-trigger"
          >
            <div className="flex items-center">
              <Database size={12} className="mr-1" />
              {isLoading ? (
                <span className="flex items-center">
                  <Loader2 size={12} className="mr-1 animate-spin" />
                  Loading...
                </span>
              ) : (
                <span>{selectedDatabase || "Select Database"}</span>
              )}
            </div>
            <ChevronDown size={12} className="ml-1" />
          </button>
        </DropdownMenu.Trigger>

        <DropdownMenu.Content
          className="mt-1 w-48 bg-white border border-gray-200 rounded shadow-lg z-10"
          sideOffset={5}
        >
          {isLoading ? (
            <div className="flex items-center justify-center p-2">
              <Loader2 size={14} className="animate-spin text-blue-500 mr-2" />
              <span className="text-xs text-gray-500">Loading...</span>
            </div>
          ) : connections.length > 0 ? (
            connections.map((connection) => (
              <DropdownMenu.Item
                key={connection.connection_id}
                onSelect={() => handleDatabaseSelect(connection)}
                className={`block w-full text-left px-3 py-1.5 text-xs cursor-pointer ${
                  selectedDatabase === connection.database_name ? "bg-gray-100 font-medium" : "hover:bg-gray-50"
                }`}
                data-testid={`database-option-${connection.database_name}`}
              >
                <div className="flex flex-col">
                  <span className="font-medium">{connection.database_name}</span>
                  <span className="text-gray-500 text-xs">{connection.connection_name}</span>
                </div>
              </DropdownMenu.Item>
            ))
          ) : (
            <div className="p-2 text-xs text-gray-500 text-center">
              No connections available
            </div>
          )}
        </DropdownMenu.Content>
      </DropdownMenu.Root>
      
      <button 
        onClick={onToggleSidebar}
        className="flex-shrink-0 w-8 h-8 rounded-md flex items-center justify-center hover:bg-gray-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
        aria-label="Toggle sidebar"
        data-testid="toggle-sidebar-button"
      >
        <PanelLeftClose size={16} />
      </button>
    </div>
  );
};

export default DadaConnectionDropdown;
