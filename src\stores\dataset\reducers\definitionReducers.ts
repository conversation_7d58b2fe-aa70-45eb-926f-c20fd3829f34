
import { PayloadAction } from '@reduxjs/toolkit';
import { DatasetState } from '../types';

export const definitionReducers = {
  updateDatasetDefinition: (state: DatasetState, action: PayloadAction<{
    name?: string;
    description?: string;
    connection?: string;
    schema?: string;
  }>) => {
    const { name, description, connection, schema } = action.payload;
    if (name !== undefined) state.datasetName = name;
    if (description !== undefined) state.datasetDescription = description;
    if (connection !== undefined) state.destinationConnection = connection;
    if (schema !== undefined) state.schemaName = schema;
  },
};
