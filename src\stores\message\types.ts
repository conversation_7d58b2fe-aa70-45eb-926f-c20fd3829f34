
import { QueryResultData } from '@/components/dashboard/models';

// Define the MeetingData interface
export interface MeetingData {
  meetingIds: string[];
  questions?: {
    question: string;
    answer: string;
  }[];
}

// Define types
export interface Message {
  type: 'query' | 'response';
  content: string;
  minimized: boolean;
  queryResult?: QueryResultData;
  meetingData?: MeetingData; // Add meetingData property
}

export interface MessageState {
  messages: Message[];
  filteredMessages: Message[];
  inputValue: string;
  chatbotInputValue: string;
  transcriptInputValue: string;
  dadaInputValue: string;
  isLoading: boolean;
  searchQuery: string;
  headerSearchQuery: string;
  uploadedFile: any;
}

// Initial state
export const initialState: MessageState = {
  messages: [],
  filteredMessages: [],
  inputValue: '',
  chatbotInputValue: '',
  transcriptInputValue: '',
  dadaInputValue: '',
  isLoading: false,
  searchQuery: '',
  headerSearchQuery: '',
  uploadedFile: null,
};
