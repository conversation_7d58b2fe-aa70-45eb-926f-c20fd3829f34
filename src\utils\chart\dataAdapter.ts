import { SavedChart, ChartDataPoint } from '@/types/chartTypes';
import { ChartDataItem } from '@/services/api/chart/chartTypes';
import type { QueryResultData } from '@/components/dashboard/models';

export interface UniversalChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor: string | string[];
    borderColor: string | string[];
    borderWidth: number;
    [key: string]: any;
  }[];
}

export interface ProcessedChartInfo {
  data: UniversalChartData;
  chartType: string;
  xAxisLabel: string;
  yAxisLabel: string;
  title?: string;
  canVisualize?: boolean;
  reasoning?: string;
}

/**
 * Universal data adapter that converts various data formats to a unified chart data structure
 */
export class UniversalDataAdapter {
  
  /**
   * Convert SavedChart data to universal format
   */
  static fromSavedChart(chart: SavedChart, chartData: ChartDataPoint[]): ProcessedChartInfo {
    console.log('[DataAdapter] fromSavedChart called:', {
      chart: chart,
      chartData_length: chartData?.length || 0,
      chartData_sample: chartData?.[0],
      chart_x_axis: chart?.x_axis,
      chart_y_axis: chart?.y_axis,
      full_chartData: chartData
    });

    if (!chartData || chartData.length === 0 || !chart) {
      console.log('[DataAdapter] fromSavedChart returning empty data - missing chart or chartData');
      return this.getEmptyData();
    }

    // Extract x-axis values
    const xAxisValues = Array.from(new Set(chartData.map(item => item[chart.x_axis])));
    
    // Extract y-axis column name
    const yAxisColumn = this.extractYAxisColumn(chart.y_axis);
    
    // Find the data key in the chart data
    const dataKey = this.findDataKey(chartData[0], yAxisColumn);

    console.log('[DataAdapter] fromSavedChart processing:', {
      xAxisValues: xAxisValues,
      yAxisColumn: yAxisColumn,
      dataKey: dataKey,
      firstDataItem: chartData[0]
    });

    // Prepare dataset
    const datasets = [{
      label: yAxisColumn,
      data: xAxisValues.map(xValue => {
        const items = chartData.filter(item => item[chart.x_axis] === xValue);

        console.log(`[DataAdapter] Processing xValue: ${xValue}, found ${items.length} items:`, items);

        if (items.length === 0) return 0;

        // Handle aggregated data
        if (this.isAggregatedKey(dataKey)) {
          const value = items[0][dataKey];
          const numericValue = typeof value === 'number' ? value : parseFloat(String(value)) || 0;
          console.log(`[DataAdapter] Aggregated value for ${xValue}: ${value} -> ${numericValue}`);
          return numericValue;
        }

        // Sum non-aggregated data
        const sumValue = items.reduce((sum, item) => {
          const value = parseFloat(String(item[dataKey]));
          return sum + (isNaN(value) ? 0 : value);
        }, 0);
        console.log(`[DataAdapter] Sum value for ${xValue}: ${sumValue}`);
        return sumValue;
      }),
      backgroundColor: [],
      borderColor: [],
      borderWidth: 1
    }];

    console.log('[DataAdapter] Final dataset data:', datasets[0].data);

    const result = {
      data: {
        labels: xAxisValues.map(String),
        datasets
      },
      chartType: chart.chart_type,
      xAxisLabel: chart.x_axis,
      yAxisLabel: yAxisColumn,
      title: chart.chart_name
    };

    console.log('[DataAdapter] fromSavedChart result:', {
      result,
      hasData: datasets[0]?.data?.length > 0,
      datasetData: datasets[0]?.data,
      labels: result.data.labels,
      datasets: result.data.datasets,
      datasetsLength: result.data.datasets.length
    });

    return result;
  }

  /**
   * Convert ChartDataItem array to universal format
   */
  static fromChartDataItems(
    data: ChartDataItem[],
    xAxis: string,
    yAxis: string[],
    chartType: string = 'bar'
  ): ProcessedChartInfo {
    console.log('[DataAdapter] fromChartDataItems called:', {
      data_length: data?.length || 0,
      data_sample: data?.[0],
      xAxis,
      yAxis,
      chartType,
      available_keys: data?.[0] ? Object.keys(data[0]) : []
    });

    if (!data || data.length === 0) {
      console.log('[DataAdapter] fromChartDataItems returning empty data - no data');
      return this.getEmptyData();
    }

    const xValues = Array.from(new Set(data.map(item => item[xAxis])));
    console.log('[DataAdapter] xValues extracted:', xValues);

    // Use provided yAxis directly for DADA AI data, but also check for aggregated keys
    let actualYAxisKeys = yAxis.length > 0 ? [...yAxis] : [];

    // If no yAxis provided or yAxis keys don't exist in data, find available keys
    if (actualYAxisKeys.length === 0 || !actualYAxisKeys.some(key => data[0].hasOwnProperty(key))) {
      const availableKeys = Object.keys(data[0]);
      console.log('[DataAdapter] Available keys in data:', availableKeys);

      // Look for aggregated keys first (sum_, count_, avg_, etc.)
      const aggregatedKeys = this.findAggregatedKeys(data[0]);
      if (aggregatedKeys.length > 0) {
        actualYAxisKeys = aggregatedKeys;
        console.log('[DataAdapter] Using aggregated keys:', actualYAxisKeys);
      } else {
        // Fallback to numeric keys
        const numericKeys = availableKeys.filter(key => {
          const value = data[0][key];
          return key !== xAxis && (typeof value === 'number' || !isNaN(Number(value)));
        });
        actualYAxisKeys = numericKeys.slice(0, 1);
        console.log('[DataAdapter] Using numeric keys:', actualYAxisKeys);
      }
    }

    console.log('[DataAdapter] Final yAxis keys to use:', actualYAxisKeys);

    const datasets = actualYAxisKeys.map((yKey) => {
      console.log('[DataAdapter] Processing yKey:', yKey);

      const dataPoints = xValues.map(xValue => {
        const matchingItems = data.filter(item => item[xAxis] === xValue);

        if (matchingItems.length === 0) {
          console.log('[DataAdapter] No matching items for xValue:', xValue);
          return 0;
        }

        // PRESERVE ACTUAL NUMBERS FROM DADA AI - NO NORMALIZATION
        const rawValue = matchingItems[0][yKey];
        console.log('[DataAdapter] Raw value for', xValue, yKey, ':', rawValue, 'type:', typeof rawValue);

        let numericValue;
        if (typeof rawValue === 'number') {
          numericValue = rawValue;
        } else {
          numericValue = parseFloat(String(rawValue));
          if (isNaN(numericValue)) {
            console.log('[DataAdapter] Failed to parse value:', rawValue, 'defaulting to 0');
            numericValue = 0;
          }
        }

        console.log('[DataAdapter] Final numeric value for', xValue, yKey, ':', numericValue);
        return numericValue;
      });

      console.log('[DataAdapter] Dataset for', yKey, '- dataPoints:', dataPoints);

      return {
        label: yKey,
        data: dataPoints,
        backgroundColor: [],
        borderColor: [],
        borderWidth: 1
      };
    });

    // CRITICAL FIX: For pie/doughnut charts - special handling
    const isRadialChart = chartType === 'pie' || chartType === 'doughnut';
    
    if (isRadialChart) {
      // For pie charts: labels are categories (Male, Female, Other), data is the values
      // Dataset label should be the metric name
      return {
        data: {
          labels: xValues.map(String), // Categories as labels
          datasets: [{
            label: actualYAxisKeys[0], // Metric name as dataset label
            data: datasets[0].data, // Actual values
            backgroundColor: [],
            borderColor: [],
            borderWidth: 1
          }]
        },
        chartType,
        xAxisLabel: xAxis,
        yAxisLabel: actualYAxisKeys[0],
        title: `${actualYAxisKeys[0]} by ${xAxis}`
      };
    }
    
    // For non-radial charts, use standard structure
    const result = {
      data: {
        labels: xValues.map(String),
        datasets
      },
      chartType,
      xAxisLabel: xAxis,
      yAxisLabel: actualYAxisKeys.join(' & '),
      title: `${actualYAxisKeys.join(' & ')} by ${xAxis}`
    };

    console.log('[DataAdapter] fromChartDataItems final result:', result);
    return result;
  }

  /**
   * Convert QueryResultData to universal format
   */
  static fromQueryResult(queryResult: QueryResultData): ProcessedChartInfo {
    // Check if DADA AI says data can be visualized
    if (queryResult?.visualization && !queryResult.visualization.can_visualize) {
      return {
        ...this.getEmptyData(),
        title: 'Cannot Visualize Data',
        canVisualize: false,
        reasoning: queryResult.visualization.reasoning
      };
    }

    // PRIORITY 1: Use DADA AI sql_result format (actual values from database)
    let chartData: ChartDataItem[];
    if (queryResult?.sql_result && Array.isArray(queryResult.sql_result) && queryResult.sql_result.length > 0) {
      console.log('[DataAdapter] Using sql_result data:', {
        length: queryResult.sql_result.length,
        firstRow: queryResult.sql_result[0],
        keys: Object.keys(queryResult.sql_result[0] || {})
      });
      chartData = queryResult.sql_result;
    } else if (queryResult?.tableData?.rows && queryResult.tableData.rows.length > 0) {
      console.log('[DataAdapter] Using tableData.rows:', {
        length: queryResult.tableData.rows.length,
        firstRow: queryResult.tableData.rows[0]
      });
      chartData = this.convertQueryResultToChartData(queryResult);
    } else {
      console.log('[DataAdapter] No valid data found:', {
        has_sql_result: !!queryResult?.sql_result,
        sql_result_length: queryResult?.sql_result?.length || 0,
        has_tableData: !!queryResult?.tableData,
        tableData_rows_length: queryResult?.tableData?.rows?.length || 0
      });
      return this.getEmptyData();
    }

    // PRIORITY 2: Use DADA AI visualization configuration
    const visualization = queryResult.visualization;
    let xAxis: string, yAxis: string, chartType: string, title: string;

    if (visualization && visualization.can_visualize) {
      // Use DADA AI provided configuration directly
      xAxis = visualization.x_axis_key_name || Object.keys(chartData[0])[0];
      yAxis = visualization.y_axis_key_name || Object.keys(chartData[0])[1];
      // CRITICAL: Use exact chart type from DADA AI
      chartType = visualization.best_chart_type || 'bar';
      title = visualization.chart_title || `${yAxis} by ${xAxis}`;
    } else {
      // Fallback to inference
      const columns = queryResult?.tableData?.columns || Object.keys(chartData[0]);
      const axes = this.inferAxesFromData(chartData, columns);
      xAxis = axes.xAxis;
      yAxis = axes.yAxis;
      chartType = this.inferChartType(chartData);
      title = `${yAxis} by ${xAxis}`;
    }

    // Process the data using the determined axes
    const result = this.fromChartDataItems(chartData, xAxis, [yAxis], chartType);
    result.title = title;
    result.canVisualize = true;
    
    return result;
  }

  /**
   * Helper methods
   */
  private static getEmptyData(): ProcessedChartInfo {
    return {
      data: {
        labels: [],
        datasets: []
      },
      chartType: 'bar',
      xAxisLabel: '',
      yAxisLabel: '',
      title: 'No Data Available',
      canVisualize: false
    };
  }

  private static extractYAxisColumn(yAxis: string | string[] | any): string {
    if (typeof yAxis === 'string') {
      return yAxis;
    }
    if (Array.isArray(yAxis) && yAxis.length > 0) {
      const firstItem = yAxis[0];
      return typeof firstItem === 'object' ? firstItem.column : firstItem;
    }
    return 'value';
  }

  private static findDataKey(dataPoint: any, yAxisColumn: string): string {
    const availableKeys = Object.keys(dataPoint);
    console.log('[DataAdapter] findDataKey searching for:', {
      yAxisColumn,
      availableKeys,
      dataPoint
    });

    // Try multiple matching strategies
    const strategies = [
      // 1. Exact match
      (key: string) => key === yAxisColumn,
      // 2. Contains the column name
      (key: string) => key.includes(yAxisColumn),
      // 3. Aggregated patterns
      (key: string) => key.startsWith('count_') && key.includes(yAxisColumn),
      (key: string) => key.startsWith('sum_') && key.includes(yAxisColumn),
      (key: string) => key.startsWith('avg_') && key.includes(yAxisColumn),
      (key: string) => key.startsWith('min_') && key.includes(yAxisColumn),
      (key: string) => key.startsWith('max_') && key.includes(yAxisColumn),
      // 4. Any aggregated key (fallback)
      (key: string) => key.startsWith('count_') || key.startsWith('sum_') || key.startsWith('avg_')
    ];

    for (const strategy of strategies) {
      const foundKey = availableKeys.find(strategy);
      if (foundKey) {
        console.log('[DataAdapter] findDataKey found:', foundKey);
        return foundKey;
      }
    }

    console.log('[DataAdapter] findDataKey fallback to:', yAxisColumn);
    return yAxisColumn;
  }

  private static isAggregatedKey(key: string): boolean {
    const isAggregated = key.startsWith('count_') || key.startsWith('sum_') ||
           key.startsWith('avg_') || key.startsWith('min_') ||
           key.startsWith('max_');
    console.log('[DataAdapter] isAggregatedKey check:', key, '->', isAggregated);
    return isAggregated;
  }

  private static isDataAggregated(dataPoint: any, yAxis: string[]): boolean {
    return yAxis.some(col => {
      const aggregatedKeys = Object.keys(dataPoint).filter(key => this.isAggregatedKey(key));
      return aggregatedKeys.includes(col) || aggregatedKeys.some(k => k.includes(col));
    });
  }

  private static findAggregatedKeys(dataPoint: any): string[] {
    return Object.keys(dataPoint).filter(key => this.isAggregatedKey(key));
  }

  private static convertQueryResultToChartData(queryResult: QueryResultData): ChartDataItem[] {
    const { columns, rows } = queryResult.tableData;
    
    return rows.map(row => {
      const item: ChartDataItem = {};
      columns.forEach((col, index) => {
        item[col] = row[index];
      });
      return item;
    });
  }

  private static inferAxesFromData(data: ChartDataItem[], columns: string[]): { xAxis: string; yAxis: string } {
    // Simple heuristic: first column as x-axis, first numeric column as y-axis
    const xAxis = columns[0] || Object.keys(data[0])[0];
    
    // Find first numeric column
    const yAxis = columns.find(col => {
      const sampleValue = data[0]?.[col];
      return typeof sampleValue === 'number' || !isNaN(Number(sampleValue));
    }) || columns[1] || Object.keys(data[0])[1];

    return { xAxis, yAxis };
  }

  private static inferChartType(data: ChartDataItem[]): string {
    // Simple heuristic based on data characteristics
    if (data.length <= 10) {
      return 'pie';
    }
    return 'bar';
  }
}