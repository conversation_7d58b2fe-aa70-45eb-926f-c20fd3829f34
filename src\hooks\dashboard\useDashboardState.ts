import { useReducer, useCallback } from 'react';
import { SavedChart, ChartDataPoint } from '@/types/chartTypes';
import { TabularDataItem } from '@/services/api/chart/chartTypes';

// Interface for individual card state
export interface CardState {
  id: string;
  type: 'chart' | 'table';
  chart: SavedChart | null;
  data: ChartDataPoint[] | TabularDataItem[] | null;
  loading: boolean;
  createdAt: number;
  position?: { x: number; y: number };
}

// Dashboard state interface
export interface DashboardState {
  cards: Map<string, CardState>;
  cardOrder: string[];
  cardsByType: {
    chart: Set<string>;
    table: Set<string>;
  };
  chartDataCache: Map<string, any>;
  selectedCharts: SavedChart[];
  searchQuery: string;
  sidebarCollapsed: boolean;
  foldersExpanded: Record<string, boolean>;
  // Legacy zone support
  chartZoneChart: SavedChart | null;
  tableZoneChart: SavedChart | null;
  zoneChartData: any[] | null;
  tabularData: TabularDataItem[] | null;
  loadingZoneData: boolean;
  loadingTabularData: boolean;
}

// Action types
type DashboardAction =
  | { type: 'ADD_CARD'; payload: CardState }
  | { type: 'REMOVE_CARD'; payload: string }
  | { type: 'UPDATE_CARD'; payload: { id: string; updates: Partial<CardState> } }
  | { type: 'SET_SEARCH_QUERY'; payload: string }
  | { type: 'TOGGLE_SIDEBAR' }
  | { type: 'TOGGLE_FOLDER'; payload: string }
  | { type: 'ADD_SELECTED_CHART'; payload: SavedChart }
  | { type: 'REMOVE_SELECTED_CHART'; payload: string }
  | { type: 'SET_CHART_ZONE'; payload: SavedChart | null }
  | { type: 'SET_TABLE_ZONE'; payload: SavedChart | null }
  | { type: 'SET_ZONE_CHART_DATA'; payload: any[] | null }
  | { type: 'SET_TABULAR_DATA'; payload: TabularDataItem[] | null }
  | { type: 'SET_LOADING_ZONE_DATA'; payload: boolean }
  | { type: 'SET_LOADING_TABULAR_DATA'; payload: boolean }
  | { type: 'REORDER_CARDS'; payload: string[] }
  | { type: 'CACHE_CHART_DATA'; payload: { chartId: string; data: any } }
  | { type: 'CLEAR_CACHE' }
  | { type: 'RESET_DASHBOARD' };

// Initial state
const initialState: DashboardState = {
  cards: new Map(),
  cardOrder: [],
  cardsByType: {
    chart: new Set(),
    table: new Set(),
  },
  chartDataCache: new Map(),
  selectedCharts: [],
  searchQuery: '',
  sidebarCollapsed: false,
  foldersExpanded: {
    'availableCharts': true
  },
  chartZoneChart: null,
  tableZoneChart: null,
  zoneChartData: null,
  tabularData: null,
  loadingZoneData: false,
  loadingTabularData: false,
};

// Reducer function
function dashboardReducer(state: DashboardState, action: DashboardAction): DashboardState {
  switch (action.type) {
    case 'ADD_CARD': {
      const newCards = new Map(state.cards);
      const newCardsByType = {
        chart: new Set(state.cardsByType.chart),
        table: new Set(state.cardsByType.table),
      };
      
      newCards.set(action.payload.id, action.payload);
      newCardsByType[action.payload.type].add(action.payload.id);
      
      return {
        ...state,
        cards: newCards,
        cardOrder: [...state.cardOrder, action.payload.id],
        cardsByType: newCardsByType,
      };
    }
    
    case 'REMOVE_CARD': {
      const newCards = new Map(state.cards);
      const card = newCards.get(action.payload);
      const newCardsByType = {
        chart: new Set(state.cardsByType.chart),
        table: new Set(state.cardsByType.table),
      };
      
      if (card) {
        newCards.delete(action.payload);
        newCardsByType[card.type].delete(action.payload);
      }
      
      return {
        ...state,
        cards: newCards,
        cardOrder: state.cardOrder.filter(id => id !== action.payload),
        cardsByType: newCardsByType,
      };
    }
    
    case 'UPDATE_CARD': {
      const newCards = new Map(state.cards);
      const existingCard = newCards.get(action.payload.id);
      
      if (existingCard) {
        newCards.set(action.payload.id, { ...existingCard, ...action.payload.updates });
      }
      
      return {
        ...state,
        cards: newCards,
      };
    }
    
    case 'SET_SEARCH_QUERY':
      return { ...state, searchQuery: action.payload };
    
    case 'TOGGLE_SIDEBAR':
      return { ...state, sidebarCollapsed: !state.sidebarCollapsed };
    
    case 'TOGGLE_FOLDER':
      return {
        ...state,
        foldersExpanded: {
          ...state.foldersExpanded,
          [action.payload]: !state.foldersExpanded[action.payload],
        },
      };
    
    case 'ADD_SELECTED_CHART':
      if (state.selectedCharts.find(c => c.chart_id === action.payload.chart_id)) {
        return state;
      }
      return {
        ...state,
        selectedCharts: [...state.selectedCharts, action.payload],
      };
    
    case 'REMOVE_SELECTED_CHART':
      return {
        ...state,
        selectedCharts: state.selectedCharts.filter(c => c.chart_id !== action.payload),
      };
    
    case 'SET_CHART_ZONE':
      return { ...state, chartZoneChart: action.payload };
    
    case 'SET_TABLE_ZONE':
      return { ...state, tableZoneChart: action.payload };
    
    case 'SET_ZONE_CHART_DATA':
      return { ...state, zoneChartData: action.payload };
    
    case 'SET_TABULAR_DATA':
      return { ...state, tabularData: action.payload };
    
    case 'SET_LOADING_ZONE_DATA':
      return { ...state, loadingZoneData: action.payload };
    
    case 'SET_LOADING_TABULAR_DATA':
      return { ...state, loadingTabularData: action.payload };
    
    case 'REORDER_CARDS':
      return { ...state, cardOrder: action.payload };
    
    case 'CACHE_CHART_DATA': {
      const newCache = new Map(state.chartDataCache);
      newCache.set(action.payload.chartId, action.payload.data);
      return { ...state, chartDataCache: newCache };
    }
    
    case 'CLEAR_CACHE':
      return { ...state, chartDataCache: new Map() };
    
    case 'RESET_DASHBOARD':
      return initialState;
    
    default:
      return state;
  }
}

export const useDashboardState = () => {
  const [state, dispatch] = useReducer(dashboardReducer, initialState);

  // Card management
  const addCard = useCallback((type: 'chart' | 'table') => {
    const newCard: CardState = {
      id: `${type}-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      type,
      chart: null,
      data: null,
      loading: false,
      createdAt: Date.now(),
    };
    dispatch({ type: 'ADD_CARD', payload: newCard });
    return newCard.id;
  }, []);

  const removeCard = useCallback((cardId: string) => {
    dispatch({ type: 'REMOVE_CARD', payload: cardId });
  }, []);

  const updateCard = useCallback((cardId: string, updates: Partial<CardState>) => {
    dispatch({ type: 'UPDATE_CARD', payload: { id: cardId, updates } });
  }, []);

  const reorderCards = useCallback((newOrder: string[]) => {
    dispatch({ type: 'REORDER_CARDS', payload: newOrder });
  }, []);

  // UI state management
  const setSearchQuery = useCallback((query: string) => {
    dispatch({ type: 'SET_SEARCH_QUERY', payload: query });
  }, []);

  const toggleSidebar = useCallback(() => {
    dispatch({ type: 'TOGGLE_SIDEBAR' });
  }, []);

  const toggleFolder = useCallback((folderName: string) => {
    dispatch({ type: 'TOGGLE_FOLDER', payload: folderName });
  }, []);

  // Chart management
  const addSelectedChart = useCallback((chart: SavedChart) => {
    dispatch({ type: 'ADD_SELECTED_CHART', payload: chart });
  }, []);

  const removeSelectedChart = useCallback((chartId: string) => {
    dispatch({ type: 'REMOVE_SELECTED_CHART', payload: chartId });
  }, []);

  // Zone management (legacy support)
  const setChartZone = useCallback((chart: SavedChart | null) => {
    dispatch({ type: 'SET_CHART_ZONE', payload: chart });
  }, []);

  const setTableZone = useCallback((chart: SavedChart | null) => {
    dispatch({ type: 'SET_TABLE_ZONE', payload: chart });
  }, []);

  const setZoneChartData = useCallback((data: any[] | null) => {
    dispatch({ type: 'SET_ZONE_CHART_DATA', payload: data });
  }, []);

  const setTabularData = useCallback((data: TabularDataItem[] | null) => {
    dispatch({ type: 'SET_TABULAR_DATA', payload: data });
  }, []);

  const setLoadingZoneData = useCallback((loading: boolean) => {
    dispatch({ type: 'SET_LOADING_ZONE_DATA', payload: loading });
  }, []);

  const setLoadingTabularData = useCallback((loading: boolean) => {
    dispatch({ type: 'SET_LOADING_TABULAR_DATA', payload: loading });
  }, []);

  // Cache management
  const cacheChartData = useCallback((chartId: string, data: any) => {
    dispatch({ type: 'CACHE_CHART_DATA', payload: { chartId, data } });
  }, []);

  const getCachedData = useCallback((chartId: string) => {
    return state.chartDataCache.get(chartId);
  }, [state.chartDataCache]);

  const clearCache = useCallback(() => {
    dispatch({ type: 'CLEAR_CACHE' });
  }, []);

  // Reset dashboard
  const resetDashboard = useCallback(() => {
    dispatch({ type: 'RESET_DASHBOARD' });
  }, []);

  // Computed values
  const cardArray = Array.from(state.cards.values()).sort((a, b) => {
    const aIndex = state.cardOrder.indexOf(a.id);
    const bIndex = state.cardOrder.indexOf(b.id);
    return aIndex - bIndex;
  });

  const chartCards = cardArray.filter(card => card.type === 'chart');
  const tableCards = cardArray.filter(card => card.type === 'table');

  return {
    // State
    state,
    cardArray,
    chartCards,
    tableCards,
    
    // Card management
    addCard,
    removeCard,
    updateCard,
    reorderCards,
    
    // UI state management
    setSearchQuery,
    toggleSidebar,
    toggleFolder,
    
    // Chart management
    addSelectedChart,
    removeSelectedChart,
    
    // Zone management (legacy)
    setChartZone,
    setTableZone,
    setZoneChartData,
    setTabularData,
    setLoadingZoneData,
    setLoadingTabularData,
    
    // Cache management
    cacheChartData,
    getCachedData,
    clearCache,
    
    // Reset
    resetDashboard,
  };
};