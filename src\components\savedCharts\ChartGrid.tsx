
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, Loader2 } from 'lucide-react';
import { SavedChart } from '@/types/chartTypes';
import { formatDate } from '@/utils/chartFormatters';
import ChartThumbnail from '@/components/charts/ChartThumbnail';
import EmptyState from './EmptyState';
import VerifiedIcon from '@/components/ui/VerifiedIcon';

interface ChartGridProps {
  charts: SavedChart[];
  loading: boolean;
  onSelectChart: (chart: SavedChart) => void;
}

const ChartGrid: React.FC<ChartGridProps> = ({ 
  charts, 
  loading, 
  onSelectChart 
}) => {
  const navigate = useNavigate();

  const handleCreateNewChart = () => {
    navigate('/chartbuilder');
  };

  // No need to handle view increment here - we'll do it in the parent component

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 text-blue-500 animate-spin" />
        <span className="ml-2 text-gray-600">Loading charts...</span>
      </div>
    );
  }

  if (charts.length === 0) {
    return <EmptyState onCreateNewChart={handleCreateNewChart} />;
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 p-6">
      {charts.map((chart) => (
        <div 
          key={chart.chart_id || chart.chart_name}
          className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-200 cursor-pointer border border-gray-200 relative"
          onClick={() => onSelectChart(chart)}
        >
          {/* Verified Icon */}
          {chart.verified && (
            <div className="absolute top-2 right-2 z-10">
              <VerifiedIcon />
            </div>
          )}
          <div className="absolute top-2 right-2 z-10">
            <VerifiedIcon />
          </div>
          
          {/* Chart Preview Area */}
          <div className="h-48 bg-gray-50 flex items-center justify-center p-2">
            <ChartThumbnail chart={chart} />
          </div>
          
          {/* Chart Info */}
          <div className="p-4 border-t">
            <h3 className="font-medium text-gray-800 mb-1 truncate text-sm">
              {chart.chart_name}
            </h3>
            <div className="flex items-center text-xs text-gray-500 justify-between">
              <span className="py-1 text-black">Published by</span>
               <span className="py-1 text-black">{chart.published_by}</span>
            </div>
            <div className="flex items-center text-xs text-gray-500 justify-between">
              <span className="py-1 text-black">{chart.views || 0} views </span>
              <span className="py-1 text-black">{formatDate(chart.created_at, true)}</span>
              
            </div>
          </div>
        </div>
      ))}
      
      {/* Add New Chart Card */}
      <div 
        className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-200 cursor-pointer border-2 border-dashed border-gray-300"
        onClick={handleCreateNewChart}
      >
        <div className="h-48 flex flex-col items-center justify-center p-6">
          <div className="w-16 h-16 rounded-full bg-blue-50 flex items-center justify-center mb-4">
            <Plus size={32} className="text-blue-500" />
          </div>
          <div className="text-gray-600 font-medium">Create New Chart</div>
          <div className="text-xs text-gray-400 mt-1">Start building a new visualization</div>
        </div>
      </div>
    </div>
  );
};

export default ChartGrid;
