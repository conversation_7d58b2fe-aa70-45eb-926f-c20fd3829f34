import React, { useState } from 'react';
import { TrendingUp, TrendingDown, Minus, Target, BarChart3, Info, X } from 'lucide-react';
import { KPIPreviewProps } from './types/kpiTypes';
import CircularProgress from '../../ui/circular-progress';

const KPIPreview: React.FC<KPIPreviewProps> = ({
  formData,
  previewData,
  isLoading = false
}) => {
  const [showInfoDialog, setShowInfoDialog] = useState(false);
  const formatNumber = (value: number | string): string => {
    if (typeof value === 'string') return value;
    return new Intl.NumberFormat().format(value);
  };

  const getTrendIcon = (trend?: 'up' | 'down' | 'neutral') => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-green-500" />;
      case 'down':
        return <TrendingDown className="w-4 h-4 text-red-500" />;
      default:
        return <Minus className="w-4 h-4 text-gray-500" />;
    }
  };

  const getChangeColor = (changePercentage?: number) => {
    if (!changePercentage) return 'text-gray-500';
    return changePercentage >= 0 ? 'text-green-600' : 'text-red-600';
  };



  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow p-6 h-[300px] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
          <p className="text-gray-500">Loading KPI preview...</p>
        </div>
      </div>
    );
  }

  const renderKPIOnly = () => (
    <div className="h-[300px] flex flex-col">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-800">
          {formData.label || 'KPI Label'}
        </h3>
        {previewData?.additionalInfo && (
          <button
            onClick={() => setShowInfoDialog(true)}
            className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
            title="Additional Information"
          >
            <Info className="w-4 h-4" />
          </button>
        )}
      </div>

      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="text-4xl font-bold text-blue-600 mb-2">
            {previewData?.currentValue ? formatNumber(previewData.currentValue) : '---'}
          </div>
          <div className="text-sm text-gray-500">Current Value</div>
        </div>
      </div>
    </div>
  );

  const renderCurrentVsPrior = () => (
    <div className="h-[300px] flex flex-col">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-800">
          {formData.label || 'KPI Label'}
        </h3>
        {previewData?.additionalInfo && (
          <button
            onClick={() => setShowInfoDialog(true)}
            className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
            title="Additional Information"
          >
            <Info className="w-4 h-4" />
          </button>
        )}
      </div>

      <div className="flex-1 flex items-center justify-center">
        <div className="grid grid-cols-2 gap-8 w-full">
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600 mb-1">
              {previewData?.currentValue ? formatNumber(previewData.currentValue) : '---'}
            </div>
            <div className="text-sm text-gray-500">
              {formData.currentLabel || 'Current'}
            </div>
          </div>

          <div className="text-center">
            <div className="text-3xl font-semibold text-gray-600 mb-1">
              {previewData?.priorValue ? formatNumber(previewData.priorValue) : '---'}
            </div>
            <div className="text-sm text-gray-500">
              {formData.priorLabel || 'Prior'}
            </div>
          </div>
        </div>
      </div>

      {previewData?.changePercentage !== undefined && (
        <div className="flex items-center justify-center mb-4">
          <div className={`flex items-center space-x-1 ${getChangeColor(previewData.changePercentage)}`}>
            {getTrendIcon(previewData.trend)}
            <span className="font-medium">
              {previewData.changePercentage > 0 ? '+' : ''}{previewData.changePercentage.toFixed(1)}%
            </span>
          </div>
        </div>
      )}
    </div>
  );

  const renderTargetBased = () => (
    <div className="h-[300px] flex flex-col overflow-hidden">
      {/* Header with KPI Label and Info Icon */}
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-semibold text-gray-800">
          {formData.label || 'KPI Label'}
        </h3>
        {previewData?.additionalInfo && (
          <button
            onClick={() => setShowInfoDialog(true)}
            className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
            title="Additional Information"
          >
            <Info className="w-4 h-4" />
          </button>
        )}
      </div>

      <div className="flex-1 flex flex-col justify-center items-center min-h-0">
        {/* Current Value - Large and Prominent */}
        <div className="text-center mb-3">
          <h2 className="text-sm text-gray-500 mb-1">Current Value</h2>
          <div className="text-2xl font-bold text-blue-600">
            {previewData?.currentValue ? formatNumber(previewData.currentValue) : '---'}
          </div>
        </div>

        {/* Circular Progress */}
        {previewData?.achievementPercentage !== undefined && (
          <div className="flex flex-col items-center">
            {/* Circular Progress Component */}
            <div className="mb-2">
              <CircularProgress
                percentage={previewData.achievementPercentage}
                size={100}
                strokeWidth={8}
                color={previewData.achievementPercentage >= 100 ? '#10b981' : '#8b5cf6'}
                showPercentage={true}
              />
            </div>

            {/* Target Value below the circle */}
            <div className="text-center">
              <span className="text-xs text-gray-500 block">Target Value</span>
              <div className="text-base font-bold text-blue-700">
                {previewData?.target ? formatNumber(previewData.target) : '---'}
              </div>
            </div>
          </div>
        )}
      </div>

    </div>
  );

  return (
    <>
      {(() => {
        switch (formData.viewType) {
          case 'current-vs-prior':
            return renderCurrentVsPrior();
          case 'target-based':
            return renderTargetBased();
          default:
            return renderKPIOnly();
        }
      })()}

      {/* Info Dialog - Available for all KPI types */}
      {showInfoDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-semibold text-gray-800">Additional Information</h4>
              <button
                onClick={() => setShowInfoDialog(false)}
                className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            <p className="text-gray-600">
              {previewData?.additionalInfo || 'No additional information available.'}
            </p>
          </div>
        </div>
      )}
    </>
  );
};

export default KPIPreview;
