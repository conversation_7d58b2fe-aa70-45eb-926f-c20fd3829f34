import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { createDatabaseConnection, testDatabaseConnection } from '@/services/api/admin/databaseConnectionService';

interface FormErrors {
  connectionName?: string;
  host?: string;
  port?: string;
  databaseName?: string;
  username?: string;
  password?: string;
  driverDialect?: string;
}

const DatabaseConnectionForm: React.FC = () => {
  const navigate = useNavigate();
  
  // Form state
  const [dbConnection, setDbConnection] = useState({
    connectionName: '',
    host: '',
    port: '5432',
    databaseName: '',
    username: '',
    password: '',
    driverDialect: 'postgresql'
  });

  // Form validation and UI state
  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [isSubmittingDb, setIsSubmittingDb] = useState(false);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [connectionTested, setConnectionTested] = useState(false);
  const [connectionValid, setConnectionValid] = useState(false);

  // Validate form when values change
  useEffect(() => {
    if (Object.keys(touched).length > 0) {
      validateForm();
    }
  }, [dbConnection, touched]);

  // Handle input changes
  const handleDbChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setDbConnection(prev => ({ ...prev, [name]: value }));
    setTouched(prev => ({ ...prev, [name]: true }));
  };

  // Handle select changes
  const handleDbSelectChange = (name: string, value: string) => {
    setDbConnection(prev => ({ ...prev, [name]: value }));
    setTouched(prev => ({ ...prev, [name]: true }));
  };

  // Handle input blur for validation
  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const { name } = e.target;
    setTouched(prev => ({ ...prev, [name]: true }));
  };

  // Validate the form
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    
    // Connection name validation
    if (!dbConnection.connectionName.trim()) {
      newErrors.connectionName = 'Connection name is required';
    }
    
    // Host validation
    if (!dbConnection.host.trim()) {
      newErrors.host = 'Host is required';
    }
    
    // Port validation
    if (!dbConnection.port.trim()) {
      newErrors.port = 'Port is required';
    } else if (!/^\d+$/.test(dbConnection.port) || parseInt(dbConnection.port) <= 0 || parseInt(dbConnection.port) > 65535) {
      newErrors.port = 'Port must be a valid number between 1 and 65535';
    }
    
    // Database name validation
    if (!dbConnection.databaseName.trim()) {
      newErrors.databaseName = 'Database name is required';
    }
    
    // Username validation
    if (!dbConnection.username.trim()) {
      newErrors.username = 'Username is required';
    }
    
    // Password validation
    if (!dbConnection.password.trim()) {
      newErrors.password = 'Password is required';
    }
    
    // Driver/Dialect validation
    if (!dbConnection.driverDialect) {
      newErrors.driverDialect = 'Database type is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Mark all fields as touched to show all validation errors
  const touchAllFields = () => {
    setTouched({
      connectionName: true,
      host: true,
      port: true,
      databaseName: true,
      username: true,
      password: true,
      driverDialect: true
    });
  };

  // Test database connection
  const handleTestDbConnection = async () => {
    // Touch all fields to show validation errors
    touchAllFields();
    
    // Validate form
    const isValid = validateForm();
    if (!isValid) {
      toast.error('Please fill in all required fields correctly');
      return;
    }
    
    setIsTestingConnection(true);
    setConnectionTested(false);
    setConnectionValid(false);
    
    try {
      const payload = {
        connection_name: dbConnection.connectionName,
        db_dialect: dbConnection.driverDialect,
        database_name: dbConnection.databaseName,
        username: dbConnection.username,
        password: dbConnection.password,
        host: dbConnection.host,
        port: parseInt(dbConnection.port),
        user_id: 0,
        ssl_mode: "prefer"
      };

      const result = await testDatabaseConnection(payload);
      
      if (result.status === 'success') {
        toast.success('Connection test successful!');
        setConnectionTested(true);
        setConnectionValid(true);
      } else {
        toast.error(result.message || 'Failed to connect to database');
        setConnectionTested(true);
        setConnectionValid(false);
      }
    } catch (error) {
      console.error('Error testing connection:', error);
      toast.error('Failed to connect to database');
      setConnectionTested(true);
      setConnectionValid(false);
    } finally {
      setIsTestingConnection(false);
    }
  };

  // Save database connection
  const handleSubmitDbConnection = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Touch all fields to show validation errors
    touchAllFields();
    
    // Validate form
    const isValid = validateForm();
    if (!isValid) {
      toast.error('Please fill in all required fields correctly');
      return;
    }
    
    // Check if connection was tested successfully
    if (!connectionTested || !connectionValid) {
      toast.error('Please test the connection successfully before saving');
      return;
    }
    
    setIsSubmittingDb(true);
    
    try {
      const payload = {
        connection_name: dbConnection.connectionName,
        db_dialect: dbConnection.driverDialect,
        database_name: dbConnection.databaseName,
        username: dbConnection.username,
        password: dbConnection.password,
        host: dbConnection.host,
        port: parseInt(dbConnection.port),
        user_id: 0,
        ssl_mode: "prefer"
      };

      const result = await createDatabaseConnection(payload);
      console.log('Database connection created:', result);
      
      // Reset form
      setDbConnection({
        connectionName: '',
        host: '',
        port: '5432',
        databaseName: '',
        username: '',
        password: '',
        driverDialect: 'postgresql'
      });
      
      // Reset validation state
      setTouched({});
      setErrors({});
      setConnectionTested(false);
      setConnectionValid(false);

      toast.success('Database connection created successfully!');
      
      // Navigate to connections list
      navigate('/database');
    } catch (error) {
      console.error('Error saving database connection:', error);
      toast.error('Failed to save database connection');
    } finally {
      setIsSubmittingDb(false);
    }
  };

  // Handle cancel button
  const handleCancel = () => {
    navigate('/database');
  };

  return (
    <div className="bg-white p-8 max-w-5xl mx-auto">
      <form onSubmit={handleSubmitDbConnection} className="space-y-8">
        
        {/* DB Connection Name */}
        <div className="flex items-start gap-4">
          <Label className="text-sm font-medium w-40 pt-2">DB Connection Name</Label>
          <div className="flex-1 max-w-md">
            <Input
              name="connectionName"
              value={dbConnection.connectionName}
              onChange={handleDbChange}
              onBlur={handleBlur}
              placeholder="DB Connection Name"
              className={`w-full ${touched.connectionName && errors.connectionName ? 'border-red-500' : ''}`}
            />
            {touched.connectionName && errors.connectionName && (
              <p className="text-red-500 text-xs mt-1">{errors.connectionName}</p>
            )}
          </div>
        </div>

        {/* Parameters Table */}
        <div className="mt-8">
          {/* Table Header */}
          <div className="grid grid-cols-3 gap-8 border-b pb-3 mb-6">
            <div className="text-sm font-medium">Parameter</div>
            <div className="text-sm font-medium">Description</div>
            <div></div>
          </div>

          {/* Host/Server Row */}
          <div className="grid grid-cols-3 gap-8 py-4 border-b items-start">
            <div className="text-sm pt-2">Host / Server</div>
            <div className="text-sm text-gray-600 pt-2">IP address or domain name of the DB server (e.g., localhost, db.company.com)</div>
            <div>
              <Input
                name="host"
                value={dbConnection.host}
                onChange={handleDbChange}
                onBlur={handleBlur}
                placeholder=""
                className={`w-full ${touched.host && errors.host ? 'border-red-500' : ''}`}
              />
              {touched.host && errors.host && (
                <p className="text-red-500 text-xs mt-1">{errors.host}</p>
              )}
            </div>
          </div>

          {/* Port Row */}
          <div className="grid grid-cols-3 gap-8 py-4 border-b items-start">
            <div className="text-sm pt-2">Port</div>
            <div className="text-sm text-gray-600 pt-2">Port number on which the DB is listening (default varies by DB)</div>
            <div>
              <Input
                name="port"
                value={dbConnection.port}
                onChange={handleDbChange}
                onBlur={handleBlur}
                placeholder=""
                className={`w-full ${touched.port && errors.port ? 'border-red-500' : ''}`}
              />
              {touched.port && errors.port && (
                <p className="text-red-500 text-xs mt-1">{errors.port}</p>
              )}
            </div>
          </div>

          {/* Database Name Row */}
          <div className="grid grid-cols-3 gap-8 py-4 border-b items-start">
            <div className="text-sm pt-2">Database Name</div>
            <div className="text-sm text-gray-600 pt-2">The specific database to connect to on the server</div>
            <div>
              <Input
                name="databaseName"
                value={dbConnection.databaseName}
                onChange={handleDbChange}
                onBlur={handleBlur}
                placeholder=""
                className={`w-full ${touched.databaseName && errors.databaseName ? 'border-red-500' : ''}`}
              />
              {touched.databaseName && errors.databaseName && (
                <p className="text-red-500 text-xs mt-1">{errors.databaseName}</p>
              )}
            </div>
          </div>

          {/* Username Row */}
          <div className="grid grid-cols-3 gap-8 py-4 border-b items-start">
            <div className="text-sm pt-2">Username</div>
            <div className="text-sm text-gray-600 pt-2">Database username with access permissions</div>
            <div>
              <Input
                name="username"
                value={dbConnection.username}
                onChange={handleDbChange}
                onBlur={handleBlur}
                placeholder=""
                className={`w-full ${touched.username && errors.username ? 'border-red-500' : ''}`}
              />
              {touched.username && errors.username && (
                <p className="text-red-500 text-xs mt-1">{errors.username}</p>
              )}
            </div>
          </div>

          {/* Password Row */}
          <div className="grid grid-cols-3 gap-8 py-4 border-b items-start">
            <div className="text-sm pt-2">Password</div>
            <div className="text-sm text-gray-600 pt-2">Associated password for authentication</div>
            <div>
              <Input
                name="password"
                type="password"
                value={dbConnection.password}
                onChange={handleDbChange}
                onBlur={handleBlur}
                placeholder=""
                className={`w-full ${touched.password && errors.password ? 'border-red-500' : ''}`}
              />
              {touched.password && errors.password && (
                <p className="text-red-500 text-xs mt-1">{errors.password}</p>
              )}
            </div>
          </div>

          {/* Driver/Dialect Row */}
          <div className="grid grid-cols-3 gap-8 py-4 items-start">
            <div className="text-sm pt-2">Driver / Dialect</div>
            <div className="text-sm text-gray-600 pt-2">The connector/driver name used in client libraries</div>
            <div>
              <Select 
                value={dbConnection.driverDialect} 
                onValueChange={(value) => handleDbSelectChange('driverDialect', value)}
              >
                <SelectTrigger className={`w-full ${touched.driverDialect && errors.driverDialect ? 'border-red-500' : ''}`}>
                  <SelectValue placeholder="Select driver/dialect" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="postgresql">PostgreSQL</SelectItem>
                  <SelectItem value="mysql">MySQL</SelectItem>
                  <SelectItem value="sqlserver">SQL Server</SelectItem>
                  <SelectItem value="oracle">Oracle</SelectItem>
                  <SelectItem value="sqlite">SQLite</SelectItem>
                  <SelectItem value="snowflake">Snowflake</SelectItem>
                  <SelectItem value="databricks">Databricks</SelectItem>
                  <SelectItem value="salesforce">Salesforce</SelectItem>
                </SelectContent>
              </Select>
              {touched.driverDialect && errors.driverDialect && (
                <p className="text-red-500 text-xs mt-1">{errors.driverDialect}</p>
              )}
            </div>
          </div>
        </div>

        {/* Connection Status Indicator */}
        {connectionTested && (
          <div className={`p-3 rounded-md ${connectionValid ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'}`}>
            {connectionValid 
              ? 'Connection test successful! You can now save this connection.' 
              : 'Connection test failed. Please check your connection details and try again.'}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end gap-3 pt-8">
          <Button
            type="button"
            onClick={handleTestDbConnection}
            disabled={isTestingConnection || isSubmittingDb}
            className={`px-6 ${isTestingConnection ? 'bg-gray-400' : 'bg-gray-600 hover:bg-gray-700'} text-white`}
          >
            {isTestingConnection ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Testing...
              </span>
            ) : 'Test Database Connection'}
          </Button>
          <Button
            type="submit"
            disabled={isSubmittingDb || isTestingConnection || !connectionValid}
            className={`px-6 ${isSubmittingDb || !connectionValid ? 'bg-gray-400' : 'bg-gray-600 hover:bg-gray-700'} text-white`}
          >
            {isSubmittingDb ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Saving...
              </span>
            ) : 'Save'}
          </Button>
          <Button
            type="button"
            onClick={handleCancel}
            disabled={isSubmittingDb || isTestingConnection}
            variant="outline"
            className="px-6"
          >
            Cancel
          </Button>
        </div>
      </form>
    </div>
  );
};

export default DatabaseConnectionForm;