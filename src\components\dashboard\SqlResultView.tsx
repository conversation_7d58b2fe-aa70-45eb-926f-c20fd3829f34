import { useState, useEffect } from "react"
import { MessageSquare, Expand, Maximize, Minimize, Trash, X, Loader2 } from "lucide-react"
import type { Message } from "./models"
import * as Dialog from "@radix-ui/react-dialog"
import { AnswerContent } from "./dadaResultViews/AnswerContent"
import { TabNavigation } from "./dadaResultViews/TabNavigation"
import { TabContent } from "./dadaResultViews/TabContent"
import { ActionButtons } from "./dadaResultViews/ActionButtons"


interface SqlResultViewProps {
  message: Message
  index: number
  toggleMinimizeMessage: (index: number) => void
  handleDeleteMessage: (index: number) => void
  messages: Message[] // added messages
}

const SqlResultView = ({ message, index, toggleMinimizeMessage, handleDeleteMessage, messages }: SqlResultViewProps) => {
  const [activeTab, setActiveTab] = useState("table")
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [selectedDatabase, setSelectedDatabase] = useState("SQL")
  const [queryResult, setQueryResult] = useState(message.queryResult || null)

  // Function to remove @ symbols from content
  const formatContent = (content: string) => {
    return content.replace(/^@+\s*/, '');
  };

  // Check if this is a power query (starts with @)
  const isPowerQuery = () => {
    if (message.type === "query") {
      return message.content && message.content.trim().startsWith('@');
    }
    
    const previousMessage = messages[index - 1];
    return previousMessage && previousMessage.content && previousMessage.content.trim().startsWith('@');
  };

  // Reset to table tab if SQL tab is selected for a power query
  useEffect(() => {
    if (isPowerQuery() && activeTab === "sql") {
      setActiveTab("table");
    }
  }, [activeTab]);

  const handleQueryRegenerated = (newResult: any) => {
    setQueryResult(newResult)
  }

  const handleDatabaseChange = (database: string) => {
    setSelectedDatabase(database)
  }

  // Find the corresponding response message for a query
  const getResponseMessage = (queryIndex: number) => {
    if (queryIndex + 1 < messages.length && messages[queryIndex + 1].type === "response") {
      return messages[queryIndex + 1]
    }
    return null
  }

  if (message.type === "query") {
    const responseMessage = getResponseMessage(index)
    const isPower = isPowerQuery();

    return (
      <div className="m-0 border border-gray-200 rounded-md overflow-hidden">
        <div className="p-2 border-b border-gray-200 flex justify-between">
          <div className="flex items-center gap-2">
            <MessageSquare size={18} className="text-blue-500" />
            <p className="text-sm">{formatContent(message.content)}</p>
          </div>
          <div className="flex items-center space-x-1">
            <Dialog.Root open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <Dialog.Trigger asChild>
                <button className="text-gray-400 hover:text-gray-600" aria-label="Expand to full view">
                  <Expand size={16} />
                </button>
              </Dialog.Trigger>
              <Dialog.Portal>
                <Dialog.Overlay className="fixed inset-0 bg-black/50 z-50" />
                <Dialog.Content className="fixed inset-0 z-50 flex items-center justify-center">
                  <div className="bg-white rounded-lg shadow-lg w-[90vw] h-[90vh] max-w-6xl overflow-auto p-6">
                    <div className="flex justify-between items-center mb-4">
                      <div className="flex items-center gap-2">
                        <MessageSquare size={20} className="text-blue-500" />
                        <Dialog.Title className="text-xl font-semibold">{formatContent(message.content)}</Dialog.Title>
                      </div>
                      <Dialog.Close asChild>
                        <button className="rounded-full p-1 hover:bg-gray-100">
                          <X size={20} />
                        </button>
                      </Dialog.Close>
                    </div>
                    <Dialog.Description className="sr-only">View query details and results</Dialog.Description>
                    <div className="mt-6">
                    {responseMessage && (
                        <div>
                          {/* <h3 className="text-lg font-medium mb-4">Answer</h3> */}
                          <AnswerContent
                            size="large"
                            queryResult={responseMessage.queryResult || null}
                            isLoading={false}
                            error={null}
                            originalQuery={message.content}
                            isPowerQuery={isPower}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </Dialog.Content>
              </Dialog.Portal>
            </Dialog.Root>
            <button
              onClick={() => toggleMinimizeMessage(index + 1)}
              className="text-gray-400 hover:text-gray-600"
              aria-label={message.minimized ? "Maximize" : "Minimize"}
            >
              {message.minimized ? <Maximize size={16} /> : <Minimize size={16} />}
            </button>
            <button
              onClick={() => handleDeleteMessage(index)}
              className="text-gray-400 hover:text-red-500"
              aria-label="Delete"
            >
              <Trash size={16} />
            </button>
          </div>
        </div>
      </div>
    )
  } else {
    // Check if the previous message is a power query
    const isPower = isPowerQuery();
    
    return (
      <div className="m-0 border border-gray-200 rounded-md overflow-hidden">
        {!message.minimized && (
          <div>
            <div className="tabs-container border-b border-gray-200">
              <TabNavigation 
                activeTab={activeTab} 
                setActiveTab={setActiveTab} 
                size="small" 
                isPowerQuery={isPower}
              />
            </div>

            <TabContent
              activeTab={activeTab}
              size="small"
              database={selectedDatabase}
              queryResult={queryResult || message.queryResult || null}
              isPowerQuery={isPower}
            />
            <ActionButtons 
              size="small" 
              queryResult={queryResult || message.queryResult || null}
              originalQuery={messages[index - 1]?.content}
              onQueryRegenerated={handleQueryRegenerated}
            />
          </div>
        )}
        {message.minimized && (
          <div className="flex items-center justify-between px-2 py-1 bg-gray-50 rounded">
            <span className="text-xs text-gray-500">[Answer minimized]</span>
            <button
              onClick={() => toggleMinimizeMessage(index)}
              className="p-1 text-gray-400 hover:text-gray-600"
              aria-label="Maximize"
            >
              <Maximize size={14} />
            </button>
          </div>
        )}
      </div>
    )
  }
}

export default SqlResultView
