
import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { clearTableData } from '@/stores/chartSlice';
import { useChartDataManager } from '@/hooks/useChartDataManager';
import { useChartSaveHandler } from '@/hooks/useChartSaveHandler';
import { useChartStatePersistence } from '@/hooks/useChartStatePersistence';
import { toast } from 'sonner';
import ChartConfigurationPanel from './chartboard/ChartConfigurationPanel';
import ChartPreviewPanel from './chartboard/ChartPreviewPanel';
import SaveChartDialog from './chartboard/SaveChartDialog';

interface ChartboardViewProps {
  onViewTypeChange?: (viewType: 'chart' | 'kpi' | 'datagrid') => void;
}

const ChartboardView: React.FC<ChartboardViewProps> = ({ onViewTypeChange }) => {
  const { chartId } = useParams<{ chartId?: string }>();
  const dispatch = useDispatch();
  const isEditMode = Boolean(chartId);
  const { editingChartData } = useChartStatePersistence();
  const [chartName, setChartName] = useState('');
  const [selectedViewType, setSelectedViewType] = useState<'chart' | 'kpi' | 'datagrid'>('chart');

  // Always call the data manager hook (to follow Rules of Hooks)
  // but we'll control its behavior based on view type
  const {
    chartStyle,
    xAxisColumn,
    yAxisColumns,
    xAxisTable,
    yAxisTables,
    aggregationType,
    showChart,
    combinedChartData,
    chartXAxisKey,
    chartYAxisKey,
    isLoading,
    selectedDatabase,
    connectionId,
    updateChartState,
    resetChartState,
    executeChart,
    filterConditions
  } = useChartDataManager(isEditMode, chartId, selectedViewType);

  // Notify parent component when view type changes and clear chart state
  useEffect(() => {
    if (onViewTypeChange) {
      onViewTypeChange(selectedViewType);
    }

    // Clear chart state when switching away from chart view to prevent data persistence
    if (selectedViewType !== 'chart') {
      resetChartState();
      // Also explicitly clear Redux table data to prevent DataGrid from showing chart data
      dispatch(clearTableData());
      console.log('Cleared chart state and table data when switching to:', selectedViewType);
    }
  }, [selectedViewType, onViewTypeChange, resetChartState, dispatch]);

  // Use the save handler hook
  const {
    showSaveDialog,
    setShowSaveDialog,
    isSaving,
    handleSave
  } = useChartSaveHandler(isEditMode, chartId);

  // Extract chart name from editing data when in edit mode
  useEffect(() => {
    if (isEditMode && editingChartData) {
      const extractedChartName = 
        editingChartData.originalChart?.chart_name ||
        editingChartData.chart_name ||
        editingChartData.chartData?.chart_name ||
        '';
      
      if (extractedChartName) {
        setChartName(extractedChartName);
      }
    }
  }, [isEditMode, editingChartData]);

  const handleSaveButtonClick = () => {
    if (!connectionId || !xAxisTable || !xAxisColumn || yAxisColumns.length === 0) {
      toast.error('Cannot save chart', {
        description: 'Please select data and execute the chart first'
      });
      return;
    }
    setShowSaveDialog(true);
  };

  const handleSaveChart = (name: string) => {
    handleSave(
      name,
      chartStyle,
      xAxisColumn,
      yAxisColumns,
      xAxisTable,
      yAxisTables,
      selectedDatabase,
      connectionId,
      combinedChartData,
      aggregationType,
      filterConditions
    );
  };

  return (
    <div className="p-4">
      <ChartConfigurationPanel
        chartStyle={chartStyle}
        xAxisColumn={xAxisColumn}
        yAxisColumns={yAxisColumns}
        xAxisTable={xAxisTable}
        yAxisTables={yAxisTables}
        aggregationType={aggregationType}
        showChart={showChart}
        isLoading={isLoading}
        updateChartState={updateChartState}
        resetChartState={resetChartState}
        onExecute={executeChart}
        onSave={handleSaveButtonClick}
        initialFilterConditions={filterConditions}
        selectedViewType={selectedViewType}
        onViewTypeChange={setSelectedViewType}
      />
      
      <ChartPreviewPanel
        showChart={showChart}
        chartStyle={chartStyle}
        combinedChartData={combinedChartData}
        xAxisColumn={xAxisColumn}
        yAxisColumns={yAxisColumns}
        chartXAxisKey={chartXAxisKey}
        chartYAxisKey={chartYAxisKey}
        selectedDatabase={selectedDatabase}
        onSave={handleSaveButtonClick}
        selectedViewType={selectedViewType}
        onExecute={() => executeChart(filterConditions)}
        onValidateSQL={async (sql: string) => {
          // Mock SQL validation - in a real implementation, this would call an API
          console.log('Validating SQL:', sql);
          return true;
        }}
        isLoading={isLoading}
      />
      
      <SaveChartDialog
        isOpen={showSaveDialog}
        onClose={() => setShowSaveDialog(false)}
        onSave={handleSaveChart}
        isSaving={isSaving}
        initialChartName={chartName}
        isEditMode={isEditMode}
        selectedDatabase={selectedDatabase}
      />
    </div>
  );
};

export default ChartboardView;
