import { centralApiClient } from '@/services/api/centralApiClient';

// Define the ServicePrincipal interface
export interface ServicePrincipal {
  id: string;
  name: string;
  source: 'Azure' | 'DADA';
  applicationId: string;
  createdAt?: string;
  updatedAt?: string;
}

// Define the payload for creating a service principal
export interface CreateServicePrincipalPayload {
  name: string;
  source: 'Azure' | 'DADA';
  applicationId: string;
}

// Function to create a new service principal
export const createServicePrincipal = async (data: CreateServicePrincipalPayload): Promise<ServicePrincipal> => {
  try {
    console.log('Creating service principal with data:', data);

    const responseData = await centralApiClient.makeRequest<any>('dada', '/service-principals/', {
      method: 'POST',
      body: data
    });

    console.log('Service principal created successfully:', responseData);
    
    // Transform the response to match our ServicePrincipal interface
    const transformedServicePrincipal: ServicePrincipal = {
      id: responseData.id || Date.now().toString(),
      name: responseData.name || data.name,
      source: (responseData.source || data.source) as 'Azure' | 'DADA',
      applicationId: responseData.applicationId || responseData.application_id || data.applicationId,
      createdAt: responseData.created_at || responseData.createdAt || new Date().toISOString(),
      updatedAt: responseData.updated_at || responseData.updatedAt || new Date().toISOString()
    };

    return transformedServicePrincipal;
  } catch (error) {
    console.error('Error creating service principal:', error);
    throw error;
  }
};

// Function to get all service principals
export const getServicePrincipals = async (): Promise<ServicePrincipal[]> => {
  try {
    console.log('Fetching all service principals');

    const responseData = await centralApiClient.makeRequest<any>('dada', '/service-principals/', {
      method: 'GET'
    });

    console.log('Service principals fetched successfully:', responseData);

    // Handle different response formats
    let servicePrincipals: any[] = [];
    if (Array.isArray(responseData)) {
      servicePrincipals = responseData;
    } else if (responseData && responseData.servicePrincipals && Array.isArray(responseData.servicePrincipals)) {
      servicePrincipals = responseData.servicePrincipals;
    } else if (responseData && responseData.data && Array.isArray(responseData.data)) {
      servicePrincipals = responseData.data;
    }

    // Transform the response to match our ServicePrincipal interface
    return servicePrincipals.map((sp: any): ServicePrincipal => ({
      id: sp.id || sp.service_principal_id || sp.servicePrincipalId,
      name: sp.name,
      source: (sp.source || 'Azure') as 'Azure' | 'DADA',
      applicationId: sp.applicationId || sp.application_id || sp.appId,
      createdAt: sp.created_at || sp.createdAt,
      updatedAt: sp.updated_at || sp.updatedAt
    }));
  } catch (error) {
    console.error('Error fetching service principals:', error);
    // Return empty array if the endpoint doesn't exist or fails
    return [];
  }
};

// Function to get a service principal by ID
export const getServicePrincipalById = async (id: string): Promise<ServicePrincipal> => {
  try {
    console.log('Fetching service principal with ID:', id);

    const responseData = await centralApiClient.makeRequest<any>('dada', `/service-principals/${id}`, {
      method: 'GET'
    });

    console.log('Service principal fetched successfully:', responseData);

    // Transform the response to match our ServicePrincipal interface
    return {
      id: responseData.id || id,
      name: responseData.name,
      source: (responseData.source || 'Azure') as 'Azure' | 'DADA',
      applicationId: responseData.applicationId || responseData.application_id || responseData.appId,
      createdAt: responseData.created_at || responseData.createdAt,
      updatedAt: responseData.updated_at || responseData.updatedAt
    };
  } catch (error) {
    console.error('Error fetching service principal:', error);
    throw error;
  }
};

// Function to update a service principal
export const updateServicePrincipal = async (id: string, data: Partial<CreateServicePrincipalPayload>): Promise<ServicePrincipal> => {
  try {
    console.log('Updating service principal:', id, data);

    const responseData = await centralApiClient.makeRequest<any>('dada', `/service-principals/${id}`, {
      method: 'PUT',
      body: data
    });

    console.log('Service principal updated successfully:', responseData);

    // Transform the response to match our ServicePrincipal interface
    return {
      id: responseData.id || id,
      name: responseData.name,
      source: (responseData.source || 'Azure') as 'Azure' | 'DADA',
      applicationId: responseData.applicationId || responseData.application_id || responseData.appId,
      createdAt: responseData.created_at || responseData.createdAt,
      updatedAt: responseData.updated_at || responseData.updatedAt
    };
  } catch (error) {
    console.error('Error updating service principal:', error);
    throw error;
  }
};

// Function to delete a service principal
export const deleteServicePrincipal = async (id: string): Promise<void> => {
  try {
    console.log('Deleting service principal with ID:', id);

    await centralApiClient.makeRequest<any>('dada', `/service-principals/${id}`, {
      method: 'DELETE'
    });

    console.log('Service principal deleted successfully:', id);
  } catch (error) {
    console.error('Error deleting service principal:', error);
    throw error;
  }
};

// Function to delete multiple service principals
export const deleteMultipleServicePrincipals = async (ids: string[]): Promise<void> => {
  try {
    console.log('Deleting multiple service principals with IDs:', ids);

    // Delete service principals one by one since the API doesn't support bulk delete
    const deletePromises = ids.map(id => deleteServicePrincipal(id));
    await Promise.all(deletePromises);

    console.log('Multiple service principals deleted successfully');
  } catch (error) {
    console.error('Error deleting multiple service principals:', error);
    throw error;
  }
};
