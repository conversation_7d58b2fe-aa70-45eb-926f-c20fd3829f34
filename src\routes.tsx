import { createBrowserRouter, Navigate } from 'react-router-dom';
import App from './App';
import Index from './pages/Index';
import Dashboard from './pages/Dashboard';
import NotFound from './pages/NotFound';
import SignIn from './pages/SignIn';
import SignUp from './pages/SignUp';
import ChartboardPage from './pages/Chartboard';
import SavedChartsPage from '@/pages/SavedCharts';
import DashboardBuilderPage from '@/pages/DashboardBuilder';
import AdminLayout from '@/components/admin/AdminLayout';
import AdminDashboard from '@/pages/admin/AdminDashboard';
import EditDatabaseConnection from '@/pages/admin/EditDatabaseConnection';
import MLZDataScanner from '@/pages/admin/MLZDataScanner';
import Users from '@/pages/admin/Users';
import TableRelationshipMapper from '@/pages/admin/TableRelationshipMapper';
import ApprovalSet from '@/pages/admin/ApprovalSet';
import Workspace from '@/pages/admin/Workspace';
import Marketplace from '@/pages/admin/Marketplace';
import PermissionSet from '@/pages/admin/PermissionSet';
import ServicePrincipal from '@/pages/admin/ServicePrincipal';
import Groups from '@/pages/admin/Groups';
import DataviewBuilder from '@/pages/admin/DataviewBuilder';

import DatabaseManagement from '@/pages/database/DatabaseManagement';
import DatabaseDemo from '@/pages/database/DatabaseDemo';
import DatabaseConnectionView from '@/pages/database/DatabaseConnectionView';

import AzureLogout from './components/auth/AzureLogout';
import ProjectDetails from './components/admin/ProjectDetails';

import { ProtectedRoute } from './components/auth/ProtectedRoute';
import AdminMenuSimple from './pages/admin/AdminMenuSimple';

// Define your routes
const router = createBrowserRouter([
  {
    path: '/',
    element: <App />,
    children: [
      { index: true, element: <Index /> },
      { path: 'signin', element: <SignIn /> },
      { path: 'signup', element: <SignUp /> },
      
      // Protected Routes
      { 
        path: 'chatbot', 
        element: <ProtectedRoute requiredFeature="chatbot"><Dashboard initialDashboard={1} /></ProtectedRoute> 
      },
      
      // Removed transcript routes
      
      {
        path: 'dada',
        element: <ProtectedRoute requiredFeature="dada"><Dashboard initialDashboard={3} /></ProtectedRoute>
      },
      
      // Add the chartboard route here
      {
        path: 'chartbuilder',
        element: <ProtectedRoute requiredFeature="chartbuilder"><ChartboardPage /></ProtectedRoute>
      },

      // Dashboard Builder route
      {
        path: 'dashboard-builder',
        element: <ProtectedRoute requiredFeature="chartbuilder"><DashboardBuilderPage /></ProtectedRoute>
      },
      
      // Add the saved charts route here
      {
        path: 'saved-charts',
        element: <ProtectedRoute requiredFeature="chartbuilder"><SavedChartsPage /></ProtectedRoute>
      },
      
      // Add a new route for viewing a specific chart by ID
      {
        path: 'saved-charts/:chartId',
        element: <ProtectedRoute requiredFeature="chartbuilder"><SavedChartsPage /></ProtectedRoute>
      },
      
      // Add the chartbuilder edit route
      {
        path: 'chartbuilder/edit/:chartId',
        element: <ProtectedRoute requiredFeature="chartbuilder"><ChartboardPage /></ProtectedRoute>
      },
      
      // Legacy route - redirect
      { path: 'dashboard', element: <Navigate to="/chatbot" replace /> },
            { path: 'database-demo', element: <DatabaseDemo /> },

      // Admin Routes
      {
        path: 'Admin',
        element: <AdminLayout />,
          children: [
          { index: true, element: <AdminDashboard /> },
          { path: 'dashboard', element: <AdminDashboard /> },
          { path: 'menu', element: <AdminMenuSimple /> },
          { path: 'edit-connection/:connectionId', element: <EditDatabaseConnection /> },
          { path: 'mlz-scanner', element: <MLZDataScanner /> },
          { path: 'project/:projectId', element: <ProjectDetails /> },
          { path: 'database', element: <DatabaseConnectionView /> },
          { path: 'users', element: <Users /> },
          { path: 'groups', element: <Groups /> },
          { path: 'TableRelationshipMapper', element: <TableRelationshipMapper /> },
          { path: 'approval-set', element: <ApprovalSet /> },
          { path: 'permission-set', element: <PermissionSet /> },
          { path: 'service-principal', element: <ServicePrincipal /> },
          { path: 'workspace', element: <Workspace /> },
          { path: 'marketplace', element: <Marketplace /> },
          { path: 'dataview-builder', element: <DataviewBuilder /> }
        ]
      },
      { path: 'azure-logout', element: <AzureLogout /> },
      
      // 404 route
      { path: '*', element: <NotFound /> }
    ]
  }
]);

export default router;
