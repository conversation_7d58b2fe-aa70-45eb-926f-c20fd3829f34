
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useAppSelector } from '@/hooks/useRedux';
import StepHeader from './StepHeader';
import { NavigationButtons } from './StepHeader';

interface GenerateStepProps {
  onClose: () => void;
}

const GenerateStep: React.FC<GenerateStepProps> = ({ onClose }) => {
  const { 
    selectedConnections, 
    selectedTables, 
    selectedColumns,
    derivedColumns,
    filterConditions,
    datasetName,
    datasetDescription,
    destinationConnection,
    schemaName
  } = useAppSelector(state => state.dataset);

  const allColumns = [...selectedColumns, ...derivedColumns];

  // Type guard to check if a column is a derived column
  const isDerivedColumn = (column: any): column is {
    name: string;
    formula: string;
    naturalLanguage: string;
    description: string;
    dataType: string;
  } => {
    return 'formula' in column;
  };

  // Generate mock SQL for the dataset
  const generateSql = () => {
    const columnsSql = allColumns.map(col => {
      if (isDerivedColumn(col)) {
        return `${col.formula} as ${col.name}`;
      } else {
        return `${col.tableOrigin}.${col.name} as ${col.name}`;
      }
    }).join(',\n  ');

    const tablesSql = selectedTables.map(table => {
      const [conn, tableName] = table.split('_');
      return `${conn}.${tableName}`;
    }).join(',\n  ');

    const whereClause = filterConditions.length > 0 
      ? '\nWHERE ' + filterConditions.map(f => `${f.columnName} ${f.condition} ${f.value}`).join('\n  AND ')
      : '';

    return `CREATE OR REPLACE TABLE ${destinationConnection}.${schemaName}.${datasetName} AS
SELECT
  ${columnsSql}
FROM
  ${tablesSql}${whereClause}`;
  };

  return (
    <div className="p-4">
      <StepHeader 
        title="Generate Dataset" 
        onClose={onClose}
      />
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <Card>
          <CardHeader>
            <CardTitle>Dataset Overview</CardTitle>
            <CardDescription>Summary of the dataset definition</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium">Dataset Name</h3>
                <p className="text-sm">{datasetName || 'Not defined'}</p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium">Description</h3>
                <p>{datasetDescription || 'No description provided'}</p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium">Destination</h3>
                <p>{destinationConnection}.{schemaName}</p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium">Data Sources</h3>
                <div className="flex flex-wrap gap-2 mt-1">
                  {selectedConnections.map(conn => (
                    <Badge key={conn} variant="secondary">{conn}</Badge>
                  ))}
                </div>
              </div>
              
              <div>
                <h3 className="text-sm font-medium">Tables</h3>
                <div className="flex flex-wrap gap-2 mt-1">
                  {selectedTables.map(table => {
                    const [_, tableName] = table.split('_');
                    return <Badge key={table} variant="outline">{tableName}</Badge>;
                  })}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Schema Definition</CardTitle>
            <CardDescription>Columns included in this dataset</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Standard Columns ({selectedColumns.length})</h3>
              <ul className="list-disc pl-5 text-sm space-y-1">
                {selectedColumns.map(col => (
                  <li key={`${col.tableOrigin}_${col.name}`}>
                    <span className="font-medium">{col.name}</span>
                    <span className="text-gray-500"> ({col.dataType})</span>
                    {col.description && <p className="text-xs text-gray-500">{col.description}</p>}
                  </li>
                ))}
              </ul>
              
              <h3 className="text-sm font-medium mt-4">Derived Columns ({derivedColumns.length})</h3>
              <ul className="list-disc pl-5 text-sm space-y-1">
                {derivedColumns.map(col => (
                  <li key={col.name}>
                    <span className="font-medium">{col.name}</span>
                    <span className="text-gray-500"> ({col.dataType || 'String'})</span>
                    <p className="text-xs text-gray-600">{col.formula}</p>
                    {col.description && <p className="text-xs text-gray-500">{col.description}</p>}
                  </li>
                ))}
              </ul>
              
              <h3 className="text-sm font-medium mt-4">Filter Conditions ({filterConditions.length})</h3>
              <ul className="list-disc pl-5 text-sm space-y-1">
                {filterConditions.map((filter, idx) => (
                  <li key={idx}>
                    {filter.columnName} {filter.condition} {filter.value}
                    {filter.naturalLanguage && <p className="text-xs text-gray-500">{filter.naturalLanguage}</p>}
                  </li>
                ))}
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
      
      <Alert className="mb-6">
        <AlertTitle>Generated SQL</AlertTitle>
        <AlertDescription>
          <pre className="whitespace-pre-wrap text-sm p-4 bg-gray-100 rounded-md overflow-auto max-h-60">
            {generateSql()}
          </pre>
        </AlertDescription>
      </Alert>
      
      {/* Add explicit navigation buttons */}
      <NavigationButtons
        showPrevious={true}
        showNext={true}
        prevButtonLabel="Previous"
        nextButtonLabel="Next"
      />
    </div>
  );
};

export default GenerateStep;
