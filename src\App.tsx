
import { useEffect } from 'react';
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Outlet } from "react-router-dom";
import { Provider } from "react-redux";
import { store } from "@/stores/store";
import { AuthProvider } from "./contexts/AuthContext";
import ErrorDialog from "./components/ui/ErrorDialog";
import ErrorBoundary from "./components/ui/ErrorBoundary";
import { useNetworkStatus } from "./hooks/useNetworkStatus";
import { initConnectionMonitoring } from "./utils/connectionUtils";

const queryClient = new QueryClient();

// Network status component
const NetworkStatusMonitor = () => {
  useNetworkStatus();
  
  useEffect(() => {
    // Initialize connection monitoring
    const cleanup = initConnectionMonitoring();
    return cleanup;
  }, []);
  
  return null;
};

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <Provider store={store}>
        <AuthProvider>
          <TooltipProvider>
            <ErrorBoundary>
              <Outlet />
              <ErrorDialog />
              <Toaster />
              <Sonner position="top-right" />
            </ErrorBoundary>
          </TooltipProvider>
        </AuthProvider>
      </Provider>
    </QueryClientProvider>
  );
}

export default App;


