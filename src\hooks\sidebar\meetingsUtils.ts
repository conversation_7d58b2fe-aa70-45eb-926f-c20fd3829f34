
import { Meeting } from '@/components/dashboard/sidebar/types';

export const getInitialMeetings = (): Meeting[] => [
  {
    id: '1',
    name: 'Meeting1',
    items: [
      { name: 'Transcript', type: 'transcript' },
      { name: 'Summary', type: 'summary' },
      { name: 'Action Items', type: 'action' },
      { name: 'Open Questions', type: 'question' }
    ],
    expanded: true
  },
  {
    id: '2',
    name: 'Meeting2',
    items: [
      { name: 'Transcript', type: 'transcript' },
      { name: 'Summary', type: 'summary' },
      { name: 'Action Items', type: 'action' },
      { name: 'Open Questions', type: 'question' }
    ],
    expanded: false
  }
];

export const toggleMeeting = (meetings: Meeting[], itemId: string): Meeting[] => {
  return meetings.map(meeting => 
    meeting.id === itemId 
      ? { ...meeting, expanded: !meeting.expanded } 
      : meeting
  );
};

export const addNewMeeting = (meetings: Meeting[]): Meeting[] => {
  const newMeetingId = (meetings.length + 1).toString();
  return [
    ...meetings,
    {
      id: newMeetingId,
      name: `Meeting${newMeetingId}`,
      items: [
        { name: 'Transcript', type: 'transcript' },
        { name: 'Summary', type: 'summary' },
        { name: 'Action Items', type: 'action' },
        { name: 'Open Questions', type: 'question' }
      ],
      expanded: false
    }
  ];
};

export const filterMeetings = (meetings: Meeting[], query: string): Meeting[] => {
  if (!query.trim()) return meetings;
  
  const filterQuery = query.toLowerCase();
  
  return meetings.map(meeting => {
    const meetingMatches = meeting.name.toLowerCase().includes(filterQuery);
    
    const filteredItems = meeting.items.filter(item => 
      item.name.toLowerCase().includes(filterQuery)
    );
    
    if (meetingMatches || filteredItems.length > 0) {
      return {
        ...meeting,
        items: filteredItems,
        expanded: true
      };
    }
    
    return null;
  }).filter(Boolean) as Meeting[];
};
