import React, { useState, memo, useCallback, useMemo } from 'react';
import { ResizableBox, ResizeHandle } from 'react-resizable';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Maximize2, Minimize2, X } from 'lucide-react';
import 'react-resizable/css/styles.css';

interface ResizableCardProps {
  children: React.ReactNode | ((size: { width: number; height: number }) => React.ReactNode);
  initialWidth?: number;
  initialHeight?: number;
  minWidth?: number;
  minHeight?: number;
  maxWidth?: number;
  maxHeight?: number;
  className?: string;
  onResize?: (width: number, height: number) => void;
  hasContent?: boolean; // New prop to control resize icon visibility
  onRemove?: () => void; // New prop for remove functionality
  showCloseIcon?: boolean; // New prop to control close icon visibility for empty cards
}

export const ResizableCard: React.FC<ResizableCardProps> = memo(({
  children,
  initialWidth = 400,
  initialHeight = 300,
  minWidth = 300,
  minHeight = 200,
  maxWidth = 1200,
  maxHeight = 600,
  className = '',
  onResize,
  hasContent = false,
  onRemove,
  showCloseIcon = false
}) => {
  const [isResizing, setIsResizing] = useState(false);
  const [size, setSize] = useState({
    width: initialWidth,
    height: initialHeight,
  });

  // Memoize event handlers to prevent unnecessary re-renders
  const handleResize = useCallback((_event: any, { size: newSize }: any) => {
    setSize(newSize);
    onResize?.(newSize.width, newSize.height);
  }, [onResize]);

  const toggleResizeMode = useCallback(() => {
    setIsResizing(!isResizing);
  }, [isResizing]);

  const handleRemove = useCallback(() => {
    onRemove?.();
  }, [onRemove]);

  // Memoize inline styles to prevent object recreation
  const containerStyle = useMemo(() => ({
    minWidth: `${minWidth}px`,
    maxWidth: `${maxWidth}px`,
    flexBasis: 'calc(33.333% - 1rem)',
    flexGrow: 0,
    flexShrink: 0,
    zIndex: isResizing ? 1000 : 'auto'
  }), [minWidth, maxWidth, isResizing]);

  const resizeHandles = useMemo((): ResizeHandle[] => 
    isResizing ? ['se', 'e', 's', 'sw', 'w'] : [],
    [isResizing]
  );

  return (
    <div
      className="relative"
      style={containerStyle}
    >
      <ResizableBox
        width={size.width}
        height={size.height}
        minConstraints={[minWidth, minHeight]}
        maxConstraints={[maxWidth, maxHeight]}
        onResize={handleResize}
        resizeHandles={resizeHandles}
        className={`relative ${isResizing ? 'resizing-card' : ''} ${className}`}
      >
        <Card className="w-full h-full relative overflow-hidden p-0 border-0">
          {/* Action Buttons */}
          {(hasContent || showCloseIcon) && (
            <div className="absolute top-2 right-2 z-10 flex gap-1">
              {/* Resize Toggle Button - Only show when content is present */}
              {hasContent && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={toggleResizeMode}
                  className={`p-1 h-6 w-6 ${
                    isResizing
                      ? 'bg-blue-100 text-blue-600 hover:bg-blue-200'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                  title={isResizing ? 'Disable resize' : 'Enable resize'}
                >
                  {isResizing ? (
                    <Minimize2 className="h-3 w-3" />
                  ) : (
                    <Maximize2 className="h-3 w-3" />
                  )}
                </Button>
              )}

              {/* Remove Button - Show for content removal or empty card removal */}
              {onRemove && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleRemove}
                  className="p-1 h-6 w-6 bg-gray-100 text-gray-600 hover:bg-red-100 hover:text-red-600"
                  title={hasContent ? "Remove content" : "Remove card"}
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>
          )}

          {/* Card Content */}
          <div className="w-full h-full flex flex-col">
            {typeof children === 'function' ? children(size) : children}
          </div>

          {/* Resize Mode Overlay */}
          {isResizing && (
            <div className="absolute inset-0 border-2 border-blue-400 border-dashed pointer-events-none">
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-blue-500 text-white px-2 py-1 rounded text-xs">
                Resize Mode
              </div>
            </div>
          )}
        </Card>
      </ResizableBox>

      {/* Custom Resize Handle Styles */}
      <style>{`
        .react-resizable-handle {
          background-color: #3b82f6 !important;
          border: 2px solid #ffffff !important;
          border-radius: 3px !important;
          opacity: 0.8;
          transition: opacity 0.2s ease;
        }

        .react-resizable-handle:hover {
          opacity: 1;
        }

        .react-resizable-handle-se {
          bottom: -5px !important;
          right: -5px !important;
          width: 12px !important;
          height: 12px !important;
          cursor: se-resize !important;
        }

        .react-resizable-handle-e {
          right: -5px !important;
          top: 50% !important;
          transform: translateY(-50%) !important;
          width: 8px !important;
          height: 24px !important;
          cursor: e-resize !important;
        }

        .react-resizable-handle-s {
          bottom: -5px !important;
          left: 50% !important;
          transform: translateX(-50%) !important;
          width: 24px !important;
          height: 8px !important;
          cursor: s-resize !important;
        }

        .react-resizable-handle-sw {
          bottom: -5px !important;
          left: -5px !important;
          width: 12px !important;
          height: 12px !important;
          cursor: sw-resize !important;
        }

        .react-resizable-handle-w {
          left: -5px !important;
          top: 50% !important;
          transform: translateY(-50%) !important;
          width: 8px !important;
          height: 24px !important;
          cursor: w-resize !important;
        }
      `}</style>
    </div>
  );
});

ResizableCard.displayName = 'ResizableCard';
