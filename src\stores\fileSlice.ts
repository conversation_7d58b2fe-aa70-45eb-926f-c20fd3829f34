
// import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// // Add TrackerFile interface
// interface TrackerFile {
//   fileName: string;
//   projectId: string;
//   projectName: string;
// }

// interface TranscriptItem {
//   text: string;
//   startTime: string;
//   endTime: string;
//   timestamp: string;
// }

// interface SummaryData {
//   summary: string;
//   actionItems: { action: string; dueDate: string; responsible: string }[];
//   importantPoints: string[];
//   openQuestions: { question: string }[];
//   outline: OutlineSection[];
// }

// interface OutlineSection {
//   title: string;
//   points: string[];
// }



// interface CacheTimestamps {
//   personalFiles: number;
//   projectFiles: number;
//   trackerProjects: number;
// }

// interface FileState {
//   personalFiles: { file_id: string; filename: string }[];
//   projectFiles: { file_id: string; filename: string }[];
//   trackerFiles: TrackerFile[];
//   trackerProjects: { id: string; name: string; fileCount: number }[];
//   isPersonalExpanded: boolean;
//   isProjectExpanded: boolean;
//   isTrackerExpanded: boolean;
//   currentTranscript: {
//     data: any;
//     fileName: string;
//     folderType: 'personal' | 'project';
//     isExisting: boolean;
//   } | null;
//   lastFetched: CacheTimestamps;
//   fetchStatus: {
//     personalFiles: 'idle' | 'loading' | 'succeeded' | 'failed';
//     projectFiles: 'idle' | 'loading' | 'succeeded' | 'failed';
//     trackerProjects: 'idle' | 'loading' | 'succeeded' | 'failed';
//   };
// }

// const CACHE_TTL = 60 * 60 * 1000; // 1 hour cache TTL

// const initialState: FileState = {
//   personalFiles: [],
//   projectFiles: [],
//   trackerFiles: [],
//   trackerProjects: [],
//   isPersonalExpanded: true,
//   isProjectExpanded: true,
//   isTrackerExpanded: true,
//   currentTranscript: null,
//   lastFetched: {
//     personalFiles: 0,
//     projectFiles: 0,
//     trackerProjects: 0
//   },
//   fetchStatus: {
//     personalFiles: 'idle',
//     projectFiles: 'idle',
//     trackerProjects: 'idle'
//   }
// };

// const fileSlice = createSlice({
//   name: 'file',
//   initialState,
//   reducers: {
//     setPersonalFiles: (state, action: PayloadAction<string[] | { file_id: string; filename: string }[]>) => {
//       // Convert string[] to the expected format if needed
//       if (action.payload.length > 0 && typeof action.payload[0] === 'string') {
//         state.personalFiles = (action.payload as string[]).map(filename => ({
//           file_id: filename, // Use filename as file_id for backward compatibility
//           filename: filename
//         }));
//       } else {
//         state.personalFiles = action.payload as { file_id: string; filename: string }[];
//       }
//       state.lastFetched.personalFiles = Date.now();
//       state.fetchStatus.personalFiles = 'succeeded';
//     },
//     setProjectFiles: (state, action: PayloadAction<string[] | { file_id: string; filename: string }[]>) => {
//       // Convert string[] to the expected format if needed
//       if (action.payload.length > 0 && typeof action.payload[0] === 'string') {
//         state.projectFiles = (action.payload as string[]).map(filename => ({
//           file_id: filename, // Use filename as file_id for backward compatibility
//           filename: filename
//         }));
//       } else {
//         state.projectFiles = action.payload as { file_id: string; filename: string }[];
//       }
//       state.lastFetched.projectFiles = Date.now();
//       state.fetchStatus.projectFiles = 'succeeded';
//     },
//     setTrackerProjects: (state, action: PayloadAction<Array<{id: string; name: string; fileCount: number}>>) => {
//       state.trackerProjects = action.payload;
//       state.lastFetched.trackerProjects = Date.now();
//       state.fetchStatus.trackerProjects = 'succeeded';
//     },
//     setFetchStatus: (state, action: PayloadAction<{
//       type: 'personalFiles' | 'projectFiles' | 'trackerProjects';
//       status: 'idle' | 'loading' | 'succeeded' | 'failed';
//     }>) => {
//       state.fetchStatus[action.payload.type] = action.payload.status;
//     },
//     togglePersonalExpanded: (state) => {
//       state.isPersonalExpanded = !state.isPersonalExpanded;
//     },
//     toggleProjectExpanded: (state) => {
//       state.isProjectExpanded = !state.isProjectExpanded;
//     },
//     // setCurrentTranscript: (state, action: PayloadAction<{...}>) => { ... },
//     // clearCurrentTranscript: (state) => { ... },
//     addToPersonal: (state, action: PayloadAction<{ file_id: string; filename: string }>) => {
//       const fileExists = state.personalFiles.some(file => file.file_id === action.payload.file_id);
//       if (!fileExists) {
//         state.personalFiles.push(action.payload);
//       }
//     },
//     addToProject: (state, action: PayloadAction<{ file_id: string; filename: string }>) => {
//       const fileExists = state.projectFiles.some(file => file.file_id === action.payload.file_id);
//       if (!fileExists) {
//         state.projectFiles.push(action.payload);
//       }
//     },
//     // Add reducer for tracker files
//     addToTracker: (state, action: PayloadAction<TrackerFile>) => {
//       const exists = state.trackerFiles.some(
//         file => file.projectId === action.payload.projectId && file.fileName === action.payload.fileName
//       );
      
//       if (!exists) {
//         state.trackerFiles.push(action.payload);
//       }

//       // Update the project's file count in trackerProjects
//       const projectIndex = state.trackerProjects.findIndex(
//         project => project.id === action.payload.projectId || project.name === action.payload.projectName
//       );

//       if (projectIndex >= 0) {
//         state.trackerProjects[projectIndex].fileCount++;
//       } else {
//         state.trackerProjects.push({
//           id: action.payload.projectId,
//           name: action.payload.projectName,
//           fileCount: 1
//         });
//       }
//     },
//     removeFromTracker: (state, action: PayloadAction<{ projectId: string, fileName: string }>) => {
//       state.trackerFiles = state.trackerFiles.filter(
//         file => !(file.projectId === action.payload.projectId && file.fileName === action.payload.fileName)
//       );
      
//       // Update file count
//       const projectIndex = state.trackerProjects.findIndex(
//         project => project.id === action.payload.projectId
//       );

//       if (projectIndex >= 0) {
//         state.trackerProjects[projectIndex].fileCount = Math.max(0, state.trackerProjects[projectIndex].fileCount - 1);
//       }
//     },
//     toggleTrackerExpanded: (state) => {
//       state.isTrackerExpanded = !state.isTrackerExpanded;
//     },
//     invalidateCache: (state, action: PayloadAction<'personalFiles' | 'projectFiles' | 'trackerProjects' | 'all'>) => {
//       if (action.payload === 'all') {
//         state.lastFetched = initialState.lastFetched;
//       } else {
//         state.lastFetched[action.payload] = 0;
//       }
//     },
//     // Optional: Manually clear cache after 24 hours if needed
//     clearStaleCache: (state) => {
//       const now = Date.now();
//       const dayInMs = 24 * 60 * 60 * 1000;
      
//       if (now - state.lastFetched.personalFiles > dayInMs) {
//         state.lastFetched.personalFiles = 0;
//       }
      
//       if (now - state.lastFetched.projectFiles > dayInMs) {
//         state.lastFetched.projectFiles = 0;
//       }
      
//       if (now - state.lastFetched.trackerProjects > dayInMs) {
//         state.lastFetched.trackerProjects = 0;
//       }
//     }
//   },
// });

// export const {
//   setPersonalFiles,
//   setProjectFiles,
//   setTrackerProjects,
//   setFetchStatus,
//   togglePersonalExpanded,
//   toggleProjectExpanded,
//   toggleTrackerExpanded,
//   // setCurrentTranscript,
//   // clearCurrentTranscript,
//   addToPersonal,
//   addToProject,
//   addToTracker,
//   removeFromTracker,
//   invalidateCache,
//   clearStaleCache
// } = fileSlice.actions;

// // Create a selector for checking if data is stale
// export const selectIsCacheValid = (state: { file: FileState }, cacheType: 'personalFiles' | 'projectFiles' | 'trackerProjects') => {
//   const lastFetched = state.file.lastFetched[cacheType];
//   return Date.now() - lastFetched < CACHE_TTL;
// };

// // Normalize transcript data to ensure consistent structure
// // const normalizeTranscriptData = (data: any): TranscriptResponse => { ... };

// // Single export default at the end of the file
// export default fileSlice.reducer;

