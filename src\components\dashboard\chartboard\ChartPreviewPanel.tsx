
import React from 'react';
import { ChartDataItem } from '@/services/api/chart/chartTypes';
import ChartDisplay from './ChartDisplay';
import { NoDataDisplay } from '../dadaResultViews/chart/NoDataDisplay';
import KPIBuilder from '../kpi/KPIBuilder';



interface ChartPreviewPanelProps {
  showChart: boolean;
  chartStyle: 'bar' | 'line' | 'pie' | 'doughnut';
  combinedChartData: ChartDataItem[];
  xAxisColumn: string;
  yAxisColumns: string[];
  chartXAxisKey: string;
  chartYAxisKey: string;
  selectedDatabase: string;
  onSave: () => void;
  selectedViewType?: 'chart' | 'kpi' | 'datagrid';
  onExecute?: () => void;
  onValidateSQL?: (sql: string) => Promise<boolean>;
  isLoading?: boolean;
}

const ChartPreviewPanel: React.FC<ChartPreviewPanelProps> = ({
  showChart,
  chartStyle,
  combinedChartData,
  xAxisColumn,
  yAxisColumns,
  chartXAxisKey,
  chartYAxisKey,
  selectedDatabase,
  onSave,
  selectedViewType = 'chart',
  onExecute,
  onValidateSQL,
  isLoading = false
}) => {
  // Always show KPI Builder when KPI view is selected, regardless of showChart state
  if (selectedViewType === 'kpi') {
    return (
      <div className="bg-white rounded-lg shadow p-4">
        <KPIBuilder
          onSave={onSave}
          isLoading={isLoading}
        />
      </div>
    );
  }

  // For DataGrid view, don't show the preview panel when no chart is displayed
  // The DataGridBuilder handles its own empty state
  if (!showChart && selectedViewType === 'datagrid') {
    return null;
  }

  if (!showChart) {
    const getNoDataMessage = () => {
      return {
        message: "No Chart Displayed",
        subMessage: "Select your data columns and filters, then click the Execute button to generate a chart."
      };
    };

    const { message, subMessage } = getNoDataMessage();

    return (
      <div className="bg-white rounded-lg shadow p-4">
        <NoDataDisplay
          height="h-[350px]"
          textSize="text-base"
          message={message}
          subMessage={subMessage}
        />
      </div>
    );
  }

  if (selectedViewType === 'datagrid') {
    return (
      <div className="bg-white rounded-lg shadow p-4">
        <div className="h-[350px] overflow-auto">
          {combinedChartData.length > 0 ? (
            <table className="w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-50">
                  {xAxisColumn && (
                    <th className="border border-gray-300 px-4 py-2 text-left font-medium text-gray-700">
                      {xAxisColumn}
                    </th>
                  )}
                  {yAxisColumns.map((col, index) => (
                    <th key={index} className="border border-gray-300 px-4 py-2 text-left font-medium text-gray-700">
                      {col}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {combinedChartData.slice(0, 10).map((row, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    {xAxisColumn && (
                      <td className="border border-gray-300 px-4 py-2 text-sm text-gray-900">
                        {row[chartXAxisKey]}
                      </td>
                    )}
                    {yAxisColumns.map((col, colIndex) => (
                      <td key={colIndex} className="border border-gray-300 px-4 py-2 text-sm text-gray-900">
                        {row[chartYAxisKey]}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <div className="flex items-center justify-center h-full text-gray-500">
              No data available
            </div>
          )}
        </div>
      </div>
    );
  }

  // Default chart view
  return (
    <ChartDisplay
      chartStyle={chartStyle}
      combinedChartData={combinedChartData}
      xAxisColumn={xAxisColumn}
      yAxisColumns={yAxisColumns}
      availableColumns={[]}
      chartXAxisKey={chartXAxisKey}
      chartYAxisKey={chartYAxisKey}
      handleSaveButtonClick={onSave}
      selectedDatabase={selectedDatabase}
    />
  );
};

export default ChartPreviewPanel;
