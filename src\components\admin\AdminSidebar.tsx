import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  ChevronDown,
  ChevronUp,
  Search,
  PanelLeftClose,
  PanelLeftOpen,
  Loader2,
  Database,
  ArrowLeft,
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { fetchUserConnections, DatabaseConnection } from '@/services/api/dadaAI/connectionService';

// Define interface for approval sets
interface ApprovalSet {
  id: string;
  approval_set_name: string;
  can_approve_insights: boolean;
  can_approve_marketplace: boolean;
  can_approve_pattern: boolean;
  can_approve_predict: boolean;
  approvers?: any[];
}

interface AdminSidebarProps {
  collapsed?: boolean;
  onToggle?: () => void;
}

const AdminSidebar: React.FC<AdminSidebarProps> = ({ collapsed: propCollapsed, onToggle }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isHovering, setIsHovering] = useState(false);

  // Database connections state
  const [dbConnections, setDbConnections] = useState<DatabaseConnection[]>([]);
  const [isDbLoading, setIsDbLoading] = useState(true);
  const [isDbExpanded, setIsDbExpanded] = useState(true);

  // Table Relationships specific state
  const [tableRelationships, setTableRelationships] = useState<any[]>([]);
  const [isRelationshipsLoading, setIsRelationshipsLoading] = useState(false);
  const [isRelationshipsExpanded, setIsRelationshipsExpanded] = useState(true);

  // Approval Sets specific state
  const [approvalSets, setApprovalSets] = useState<ApprovalSet[]>([]);
  const [isLoadingApprovalSets, setIsLoadingApprovalSets] = useState(false);
  const [isApprovalSetsExpanded, setIsApprovalSetsExpanded] = useState(true);

  // Permission Sets specific state
  const [permissionSets, setPermissionSets] = useState<any[]>([]);
  const [isLoadingPermissionSets, setIsLoadingPermissionSets] = useState(false);
  const [isPermissionSetsExpanded, setIsPermissionSetsExpanded] = useState(true);

  // Workspace specific state
  const [workspaces, setWorkspaces] = useState<any[]>([]);
  const [isLoadingWorkspaces, setIsLoadingWorkspaces] = useState(false);
  const [isWorkspacesExpanded, setIsWorkspacesExpanded] = useState(true);

  // Marketplace specific state
  const [marketplaces, setMarketplaces] = useState<any[]>([]);
  const [isLoadingMarketplaces, setIsLoadingMarketplaces] = useState(false);
  const [isMarketplacesExpanded, setIsMarketplacesExpanded] = useState(true);

  // Check current admin page
  const isDatabaseConnectionPage = location.pathname.includes('/Admin/database') || location.pathname.includes('/Admin/menu') || location.pathname.includes('/Admin/edit-connection');
  const isMLZScannerPage = location.pathname.includes('/Admin/mlz-scanner');
  const isTableViewAccessPage = location.pathname.includes('/Admin/table-view-access') || location.pathname.includes('/Admin/table-access');
  const isTableRelationshipPage = location.pathname.includes('/Admin/TableRelationshipMapper');
  const isApprovalSetPage = location.pathname.includes('/Admin/approval-set');
  const isPermissionSetPage = location.pathname.includes('/Admin/permission-set');
  const isWorkspacePage = location.pathname.includes('/Admin/workspace');
  const isMarketplacePage = location.pathname.includes('/Admin/marketplace');
  const isAdminDashboard =
    location.pathname === '/Admin' || location.pathname === '/Admin/' || location.pathname.includes('/Admin/dashboard');

  // Use the prop value if provided, otherwise use internal state
  const effectiveCollapsed = propCollapsed !== undefined ? propCollapsed : isCollapsed;

  const toggleSidebar = () => {
    if (onToggle) {
      onToggle();
    } else {
      setIsCollapsed(!isCollapsed);
    }
  };

  // Helper function to get approval set status
  const getApprovalSetStatus = (approvalSet: ApprovalSet): string => {
    const enabledCount = [
      approvalSet.can_approve_insights,
      approvalSet.can_approve_marketplace,
      approvalSet.can_approve_pattern,
      approvalSet.can_approve_predict
    ].filter(Boolean).length;

    if (enabledCount === 0) return 'Disabled';
    if (enabledCount === 4) return 'All Enabled';
    return `${enabledCount}/4 Enabled`;
  };

  // Database connections loading
  const fetchDatabaseConnections = async () => {
    setIsDbLoading(true);
    try {
      const connections = await fetchUserConnections("a6e3020d-984a-4394-ac73-da7ec5393314");
      setDbConnections(connections);
    } catch (error) {
      console.error('Error fetching database connections:', error);
      setDbConnections([]);
    } finally {
      setIsDbLoading(false);
    }
  };

  // Approval sets loading
  const fetchApprovalSets = async () => {
    setIsLoadingApprovalSets(true);
    try {
      const response = await fetch('http://10.100.0.22:8001/account-management/get-all-approval-set', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('Approval sets API response:', data);

      // Handle different response formats
      let approvalSetsData: ApprovalSet[] = [];
      if (Array.isArray(data)) {
        approvalSetsData = data;
      } else if (data && Array.isArray(data.approval_sets)) {
        approvalSetsData = data.approval_sets;
      } else if (data && Array.isArray(data.data)) {
        approvalSetsData = data.data;
      }

      setApprovalSets(approvalSetsData);
    } catch (error) {
      console.error('Error fetching approval sets:', error);
      setApprovalSets([]);
    } finally {
      setIsLoadingApprovalSets(false);
    }
  };

  // Load database connections for all admin pages (except specific pages that don't need them)
  useEffect(() => {
    if (!isTableRelationshipPage && !isApprovalSetPage && !isWorkspacePage) {
      fetchDatabaseConnections();
    }
  }, [isTableRelationshipPage, isApprovalSetPage, isWorkspacePage]);

  // Mock data loading
  useEffect(() => {
    if (isTableRelationshipPage) {
      setIsRelationshipsLoading(true);
      setTimeout(() => {
        const mockRelationships = [
          { id: '1', name: 'RSP_TABLE1_TABLE2_ID1', leftTable: 'Table Name', rightTable: 'Table Name', dbConnection: 'DSC_SQL_DEV1' },
          { id: '2', name: 'RSP_TABLE1_TABLE2_ID2', leftTable: 'Table Name', rightTable: 'Table Name', dbConnection: 'DSC_SQL_DEV1' },
          { id: '3', name: 'RSP_TABLE1_TABLE3_ID1', leftTable: 'Table Name', rightTable: 'Table Name', dbConnection: 'DSC_SQL_DEV1' }
        ];
        setTableRelationships(mockRelationships);
        setIsRelationshipsLoading(false);
      }, 1000);
    }
  }, [isTableRelationshipPage]);

  useEffect(() => {
    if (isApprovalSetPage) {
      fetchApprovalSets();
    }
  }, [isApprovalSetPage]);

  useEffect(() => {
    if (isPermissionSetPage) {
      setIsLoadingPermissionSets(true);
      setTimeout(() => {
        const mockPermissionSets = [
          { id: '1', name: 'Admin_PermissionSet', permissionCount: 15, enabled: true },
          { id: '2', name: 'User_PermissionSet', permissionCount: 8, enabled: true },
          { id: '3', name: 'ReadOnly_PermissionSet', permissionCount: 5, enabled: false }
        ];
        setPermissionSets(mockPermissionSets);
        setIsLoadingPermissionSets(false);
      }, 1000);
    }
  }, [isPermissionSetPage]);

  useEffect(() => {
    if (isWorkspacePage) {
      setIsLoadingWorkspaces(true);
      setTimeout(() => {
        const mockWorkspaces = [
          { id: '1', name: 'GlobalWorkspace (In-built)', dbConnections: ['Sql_Connection_1', 'Sql_Connection_2'] },
          { id: '2', name: 'Analytics_DSP', dbConnections: ['Postgres_Connection_1'] },
          { id: '3', name: 'DataEngineering_DSP', dbConnections: ['Datawarehouse_Connection_1'] },
          { id: '4', name: 'Finance_DSP', dbConnections: [] }
        ];
        setWorkspaces(mockWorkspaces);
        setIsLoadingWorkspaces(false);
      }, 1000);
    }
  }, [isWorkspacePage]);

  useEffect(() => {
    if (isMarketplacePage) {
      setIsLoadingMarketplaces(true);
      setTimeout(() => {
        const mockMarketplaces = [
          { id: '1', name: 'Analytics_DMP', approvalSet: 'ApprovalSet1' },
          { id: '2', name: 'DataEngineering_DMP', approvalSet: 'ApprovalSet2' },
          { id: '3', name: 'Finance_DMP', approvalSet: 'ApprovalSet1' }
        ];
        setMarketplaces(mockMarketplaces);
        setIsLoadingMarketplaces(false);
      }, 1000);
    }
  }, [isMarketplacePage]);

  if (effectiveCollapsed) {
    return (
      <div className="w-16 bg-gray-200 border-r border-gray-200 flex flex-col items-center py-4 relative">
        <button
          onClick={toggleSidebar}
          className="w-10 h-10 rounded-md flex items-center justify-center hover:bg-gray-100"
          aria-label="Expand sidebar"
        >
          <PanelLeftOpen size={20} className="h-5 w-5 text-gray-600" />
        </button>
        <div className="flex flex-col space-y-2 mt-4">
          <button
            onClick={() => navigate('/Admin/database')}
            className="w-10 h-10 rounded-md flex items-center justify-center hover:bg-gray-100 text-gray-600"
          >
            <Database size={20} />
          </button>
        </div>
      </div>
    );
  }

  const renderContent = () => {
    if (isAdminDashboard) {
      return (
        <div className="px-3">
          <div className="flex items-center p-2 mb-2">
            <Database className="h-5 w-5 mr-2 text-green-600" />
            <span className="text-sm font-normal text-gray-600">Quick Navigation</span>
          </div>
          <div className="space-y-1">
            <div
              onClick={() => navigate('/Admin/menu')}
              className="px-2 py-2 text-sm cursor-pointer text-gray-700 hover:text-[#008282] hover:bg-[#008282]/10 rounded transition-colors"
            >
              <div className="font-normal">DB Connections</div>
              <div className="text-xs text-gray-500">Manage database connections</div>
            </div>
            <div
              onClick={() => navigate('/Admin/mlz-scanner')}
              className="px-2 py-2 text-sm cursor-pointer text-gray-700 hover:text-[#008282] hover:bg-[#008282]/10 rounded transition-colors"
            >
              <div className="font-normal">MLZ Data Scanner</div>
              <div className="text-xs text-gray-500">Scan and manage metadata</div>
            </div>
            <div
              onClick={() => navigate('/Admin/TableRelationshipMapper')}
              className="px-2 py-2 text-sm cursor-pointer text-gray-700 hover:text-[#008282] hover:bg-[#008282]/10 rounded transition-colors"
            >
              <div className="font-normal">Table Relationships</div>
              <div className="text-xs text-gray-500">Manage table relationships</div>
            </div>
            <div
              onClick={() => navigate('/Admin/approval-set')}
              className="px-2 py-2 text-sm cursor-pointer text-gray-700 hover:text-[#008282] hover:bg-[#008282]/10 rounded transition-colors"
            >
              <div className="font-normal">Approval Sets</div>
              <div className="text-xs text-gray-500">Manage approval workflows</div>
            </div>
            <div
              onClick={() => navigate('/Admin/permission-set')}
              className="px-2 py-2 text-sm cursor-pointer text-gray-700 hover:text-[#008282] hover:bg-[#008282]/10 rounded transition-colors"
            >
              <div className="font-normal">Permission Sets</div>
              <div className="text-xs text-gray-500">Manage data permissions</div>
            </div>
            <div
              onClick={() => navigate('/Admin/workspace')}
              className="px-2 py-2 text-sm cursor-pointer text-gray-700 hover:text-[#008282] hover:bg-[#008282]/10 rounded transition-colors"
            >
              <div className="font-normal">Workspaces</div>
              <div className="text-xs text-gray-500">Manage workspaces</div>
            </div>
            <div
              onClick={() => navigate('/Admin/marketplace')}
              className="px-2 py-2 text-sm cursor-pointer text-gray-700 hover:text-[#008282] hover:bg-[#008282]/10 rounded transition-colors"
            >
              <div className="font-normal">Marketplace</div>
              <div className="text-xs text-gray-500">Manage marketplaces</div>
            </div>
          </div>
        </div>
      );
    }

    if (isDatabaseConnectionPage || isMLZScannerPage) {
      return (
        <div className="px-3">
          <div
            className="flex items-center justify-between p-2 hover:bg-gray-100 rounded cursor-pointer"
            onClick={() => setIsDbExpanded(!isDbExpanded)}
          >
            <div className="flex items-center">
              <Database className="h-5 w-5 mr-2 text-green-600" />
              <span className="text-sm font-normal">Database Connections</span>
            </div>
            <div className="flex items-center">
              {isDbLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin text-green-600" />}
              {isDbExpanded ? (
                <ChevronUp className="h-4 w-4 text-gray-500" />
              ) : (
                <ChevronDown className="h-4 w-4 text-gray-500" />
              )}
            </div>
          </div>



          {isDbExpanded && (
            <div className="ml-7 mt-1">
              {isDbLoading ? (
                <div className="text-center py-4 text-gray-500">
                  <Loader2 className="h-4 w-4 mx-auto mb-2 animate-spin text-gray-400" />
                  Loading connections...
                </div>
              ) : dbConnections.length > 0 ? (
                <div className="space-y-1">
                  {dbConnections
                    .filter((connection) =>
                      connection.connection_name.toLowerCase().includes(searchQuery.toLowerCase())
                    )
                    .map((connection) => (
                      <div
                        key={connection.connection_id}
                        onClick={() => {
                          // Context-aware navigation based on current page
                          if (isDatabaseConnectionPage) {
                            // On DB connection pages, navigate to edit connection
                            navigate(`/Admin/edit-connection/${connection.connection_id}`);
                          } else if (isTableViewAccessPage || isApprovalSetPage || isPermissionSetPage || location.pathname.includes('/Admin/menu')) {
                            // On table/permission related pages or admin menu, navigate to dedicated table access page
                            navigate(`/Admin/table-access?connectionId=${connection.connection_id}&connectionName=${encodeURIComponent(connection.connection_name)}`);
                          } else {
                            // On other pages (MLZ scanner, etc.), navigate to metadata scanner
                            navigate(`/Admin/mlz-scanner?connectionId=${connection.connection_id}&connectionName=${encodeURIComponent(connection.connection_name)}`);
                          }
                        }}
                        className="px-2 py-1 text-sm cursor-pointer text-gray-700 hover:text-[#008282] hover:bg-[#008282]/10 rounded transition-colors"
                      >
                        <div className="truncate font-normal">{connection.connection_name}</div>
                        <div className="text-xs text-gray-500">{connection.database_dialect}</div>
                      </div>
                    ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500 p-2">
                  {searchQuery ? 'No matching connections' : 'No database connections yet'}
                </p>
              )}
            </div>
          )}
        </div>
      );
    }

    if (isTableRelationshipPage) {
      return (
        <div className="px-3">
          <div
            className="flex items-center justify-between p-2 hover:bg-gray-100 rounded cursor-pointer"
            onClick={() => setIsRelationshipsExpanded(!isRelationshipsExpanded)}
          >
            <div className="flex items-center">
              <span className="text-sm font-normal">Table Relationships</span>
            </div>
            <div className="flex items-center">
              {isRelationshipsLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin text-blue-600" />}
              {isRelationshipsExpanded ? (
                <ChevronUp className="h-4 w-4 text-gray-500" />
              ) : (
                <ChevronDown className="h-4 w-4 text-gray-500" />
              )}
            </div>
          </div>

          {isRelationshipsExpanded && (
            <div className="ml-7 mt-1">
              {isRelationshipsLoading ? (
                <div className="text-center py-4 text-gray-500">
                  <Loader2 className="h-4 w-4 mx-auto mb-2 animate-spin text-gray-400" />
                  Loading relationships...
                </div>
              ) : tableRelationships.length > 0 ? (
                <div className="space-y-1">
                  {tableRelationships
                    .filter((rel) =>
                      rel.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                      rel.leftTable.toLowerCase().includes(searchQuery.toLowerCase()) ||
                      rel.rightTable.toLowerCase().includes(searchQuery.toLowerCase())
                    )
                    .map((relationship) => (
                      <div
                        key={relationship.id}
                        onClick={() => navigate(`/Admin/TableRelationshipMapper?id=${relationship.id}`)}
                        className="px-2 py-1 text-sm cursor-pointer text-gray-700 hover:text-[#008282] hover:bg-[#008282]/10 rounded transition-colors"
                      >
                        <div className="truncate font-normal">{relationship.name}</div>
                        <div className="text-xs text-gray-500 truncate">
                          {relationship.leftTable} → {relationship.rightTable}
                        </div>
                        <div className="text-xs text-gray-400">{relationship.dbConnection}</div>
                      </div>
                    ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500 p-2">
                  {searchQuery ? 'No matching relationships' : 'No relationships created yet'}
                </p>
              )}
            </div>
          )}


        </div>
      );
    }

    if (isApprovalSetPage) {
      return (
        <div className="px-3">
          <div
            className="flex items-center justify-between p-2 hover:bg-gray-100 rounded cursor-pointer"
            onClick={() => setIsApprovalSetsExpanded(!isApprovalSetsExpanded)}
          >
            <div className="flex items-center">
              <span className="text-sm font-normal">Approval Sets</span>
            </div>
            <div className="flex items-center">
              {isLoadingApprovalSets && <Loader2 className="h-4 w-4 mr-2 animate-spin text-green-600" />}
              {isApprovalSetsExpanded ? (
                <ChevronUp className="h-4 w-4 text-gray-500" />
              ) : (
                <ChevronDown className="h-4 w-4 text-gray-500" />
              )}
            </div>
          </div>

          {isApprovalSetsExpanded && (
            <div className="ml-7 mt-1">
              {isLoadingApprovalSets ? (
                <div className="text-center py-4 text-gray-500">
                  <Loader2 className="h-4 w-4 mx-auto mb-2 animate-spin text-gray-400" />
                  Loading approval sets...
                </div>
              ) : approvalSets.length > 0 ? (
                <div className="space-y-1">
                  {approvalSets
                    .filter((set) =>
                      set.approval_set_name.toLowerCase().includes(searchQuery.toLowerCase())
                    )
                    .map((approvalSet) => (
                      <div
                        key={approvalSet.id}
                        onClick={() => navigate(`/Admin/approval-set?id=${approvalSet.id}`)}
                        className="px-2 py-1 text-sm cursor-pointer text-gray-700 hover:text-[#008282] hover:bg-[#008282]/10 rounded transition-colors"
                      >
                        <div className="truncate font-normal">{approvalSet.approval_set_name}</div>
                        <div className="text-xs text-gray-500 truncate">
                          {approvalSet.approvers?.length || 0} approvers
                        </div>
                        <div className="text-xs text-gray-400">
                          {getApprovalSetStatus(approvalSet)}
                        </div>
                      </div>
                    ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500 p-2">
                  {searchQuery ? 'No matching approval sets' : 'No approval sets created yet'}
                </p>
              )}
            </div>
          )}

        </div>
      );
    }

    if (isPermissionSetPage) {
      return (
        <div className="px-3">

          <div
            className="flex items-center justify-between p-2 hover:bg-gray-100 rounded cursor-pointer"
            onClick={() => setIsPermissionSetsExpanded(!isPermissionSetsExpanded)}
          >
            <div className="flex items-center">
              <span className="text-sm font-normal">Permission Sets</span>
            </div>
            <div className="flex items-center">
              {isLoadingPermissionSets && <Loader2 className="h-4 w-4 mr-2 animate-spin text-purple-600" />}
              {isPermissionSetsExpanded ? (
                <ChevronUp className="h-4 w-4 text-gray-500" />
              ) : (
                <ChevronDown className="h-4 w-4 text-gray-500" />
              )}
            </div>
          </div>

          {isPermissionSetsExpanded && (
            <div className="ml-7 mt-1">
              {isLoadingPermissionSets ? (
                <div className="text-center py-4 text-gray-500">
                  <Loader2 className="h-4 w-4 mx-auto mb-2 animate-spin text-gray-400" />
                  Loading permission sets...
                </div>
              ) : permissionSets.length > 0 ? (
                <div className="space-y-1">
                  {permissionSets
                    .filter((set) =>
                      set.name.toLowerCase().includes(searchQuery.toLowerCase())
                    )
                    .map((permissionSet) => (
                      <div
                        key={permissionSet.id}
                        onClick={() => navigate(`/Admin/permission-set?id=${permissionSet.id}`)}
                        className="px-2 py-1 text-sm cursor-pointer text-gray-700 hover:text-[#008282] hover:bg-[#008282]/10 rounded transition-colors"
                      >
                        <div className="truncate font-normal">{permissionSet.name}</div>
                        <div className="text-xs text-gray-500 truncate">
                          {permissionSet.permissionCount} permissions
                        </div>
                        <div className="text-xs text-gray-400">
                          {permissionSet.enabled ? 'Enabled' : 'Disabled'}
                        </div>
                      </div>
                    ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500 p-2">
                  {searchQuery ? 'No matching permission sets' : 'No permission sets created yet'}
                </p>
              )}
            </div>
          )}

        </div>
      );
    }

    if (isWorkspacePage) {
      return (
        <div className="px-3">

          <div
            className="flex items-center justify-between p-2 hover:bg-gray-100 rounded cursor-pointer"
            onClick={() => setIsWorkspacesExpanded(!isWorkspacesExpanded)}
          >
            <div className="flex items-center">
              <span className="text-sm font-normal">Workspaces</span>
            </div>
            <div className="flex items-center">
              {isLoadingWorkspaces && <Loader2 className="h-4 w-4 mr-2 animate-spin text-purple-600" />}
              {isWorkspacesExpanded ? (
                <ChevronUp className="h-4 w-4 text-gray-500" />
              ) : (
                <ChevronDown className="h-4 w-4 text-gray-500" />
              )}
            </div>
          </div>

          {isWorkspacesExpanded && (
            <div className="ml-7 mt-1">
              {isLoadingWorkspaces ? (
                <div className="text-center py-4 text-gray-500">
                  <Loader2 className="h-4 w-4 mx-auto mb-2 animate-spin text-gray-400" />
                  Loading workspaces...
                </div>
              ) : workspaces.length > 0 ? (
                <div className="space-y-1">
                  {workspaces
                    .filter((workspace) =>
                      workspace.name.toLowerCase().includes(searchQuery.toLowerCase())
                    )
                    .map((workspace) => (
                      <div
                        key={workspace.id}
                        onClick={() => navigate(`/Admin/workspace?id=${workspace.id}`)}
                        className="px-2 py-1 text-sm cursor-pointer text-gray-700 hover:text-[#008282] hover:bg-[#008282]/10 rounded transition-colors"
                      >
                        <div className="truncate font-normal">{workspace.name}</div>
                        <div className="text-xs text-gray-500 truncate">
                          {workspace.dbConnections?.length || 0} connections
                        </div>
                      </div>
                    ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500 p-2">
                  {searchQuery ? 'No matching workspaces' : 'No workspaces created yet'}
                </p>
              )}
            </div>
          )}


        </div>
      );
    }

    if (isMarketplacePage) {
      return (
        <div className="px-3">
          <div
            className="flex items-center justify-between p-2 hover:bg-gray-100 rounded cursor-pointer"
            onClick={() => setIsMarketplacesExpanded(!isMarketplacesExpanded)}
          >
            <div className="flex items-center">
              <span className="text-sm font-normal">Marketplaces</span>
            </div>
            <div className="flex items-center">
              {isLoadingMarketplaces && <Loader2 className="h-4 w-4 mr-2 animate-spin text-orange-600" />}
              {isMarketplacesExpanded ? (
                <ChevronUp className="h-4 w-4 text-gray-500" />
              ) : (
                <ChevronDown className="h-4 w-4 text-gray-500" />
              )}
            </div>
          </div>

          {isMarketplacesExpanded && (
            <div className="ml-7 mt-1">
              {isLoadingMarketplaces ? (
                <div className="text-center py-4 text-gray-500">
                  <Loader2 className="h-4 w-4 mx-auto mb-2 animate-spin text-gray-400" />
                  Loading marketplaces...
                </div>
              ) : marketplaces.length > 0 ? (
                <div className="space-y-1">
                  {marketplaces
                    .filter((marketplace) =>
                      marketplace.name.toLowerCase().includes(searchQuery.toLowerCase())
                    )
                    .map((marketplace) => (
                      <div
                        key={marketplace.id}
                        onClick={() => navigate(`/Admin/marketplace?id=${marketplace.id}`)}
                        className="px-2 py-1 text-sm cursor-pointer text-gray-700 rounded transition-colors"
                        onMouseEnter={(e) => {
                          e.currentTarget.style.color = 'rgb(0, 130, 130)';
                          e.currentTarget.style.backgroundColor = 'rgba(0, 130, 130, 0.1)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.color = 'rgb(55, 65, 81)';
                          e.currentTarget.style.backgroundColor = 'transparent';
                        }}
                      >
                        <div className="truncate font-normal">{marketplace.name}</div>
                        <div className="text-xs text-gray-500 truncate">
                          {marketplace.approvalSet}
                        </div>
                      </div>
                    ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500 p-2">
                  {searchQuery ? 'No matching marketplaces' : 'No marketplaces created yet'}
                </p>
              )}
            </div>
          )}
        </div>
      );
    }

    // Default case for other admin pages - show database connections
    return (
      <div className="px-3">
        <div
          className="flex items-center justify-between p-2 hover:bg-gray-100 rounded cursor-pointer"
          onClick={() => setIsDbExpanded(!isDbExpanded)}
        >
          <div className="flex items-center">
            <Database className="h-5 w-5 mr-2 text-blue-600" />
            <span className="text-sm font-normal">Database Connections</span>
          </div>
          <div className="flex items-center">
            {isDbLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin text-blue-600" />}
            {isDbExpanded ? (
              <ChevronUp className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronDown className="h-4 w-4 text-gray-500" />
            )}
          </div>
        </div>



        {isDbExpanded && (
          <div className="ml-7 mt-1">
            {isDbLoading ? (
              <div className="text-center py-4 text-gray-500">
                <Loader2 className="h-4 w-4 mx-auto mb-2 animate-spin text-gray-400" />
                Loading connections...
              </div>
            ) : dbConnections.length > 0 ? (
              <div className="space-y-1">
                {dbConnections
                  .filter((connection) =>
                    connection.connection_name.toLowerCase().includes(searchQuery.toLowerCase())
                  )
                  .map((connection) => (
                    <div
                      key={connection.connection_id}
                      onClick={() => {
                        // Context-aware navigation based on current page
                        if (isDatabaseConnectionPage) {
                          // On DB connection pages, navigate to edit connection
                          navigate(`/Admin/edit-connection/${connection.connection_id}`);
                        } else if (isTableViewAccessPage || isApprovalSetPage || isPermissionSetPage || location.pathname.includes('/Admin/menu')) {
                          // On table/permission related pages or admin menu, navigate to dedicated table access page
                          navigate(`/Admin/table-access?connectionId=${connection.connection_id}&connectionName=${encodeURIComponent(connection.connection_name)}`);
                        } else {
                          // On other pages (MLZ scanner, etc.), navigate to metadata scanner
                          navigate(`/Admin/mlz-scanner?connectionId=${connection.connection_id}&connectionName=${encodeURIComponent(connection.connection_name)}`);
                        }
                      }}
                      className="px-2 py-1 text-sm cursor-pointer text-gray-700 hover:text-[#008282] hover:bg-[#008282]/10 rounded transition-colors"
                    >
                      <div className="truncate font-normal">{connection.connection_name}</div>
                      <div className="text-xs text-gray-500">{connection.database_dialect}</div>
                    </div>
                  ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500 p-2">
                {searchQuery ? 'No matching connections' : 'No database connections yet'}
              </p>
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div
      className="w-64 flex flex-col h-full bg-gray-200 border relative"
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      <div className="p-3">
        <div className="flex items-center space-x-2 group">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="text"
              placeholder={
                isDatabaseConnectionPage || isMLZScannerPage ? "Search connections..." :
                isTableRelationshipPage ? "Search relationships..." :
                isApprovalSetPage ? "Search approval sets..." :
                isPermissionSetPage ? "Search permission sets..." :
                isWorkspacePage ? "Search workspaces..." :
                isMarketplacePage ? "Search marketplaces..." :
                "Search files..."
              }
              className="pl-8 h-9"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <button
            onClick={toggleSidebar}
            className={`flex-shrink-0 w-8 h-8 rounded-md flex items-center justify-center hover:bg-gray-100 transition-opacity duration-200 ${
              isHovering ? 'opacity-100' : 'opacity-0'
            }`}
            aria-label="Collapse sidebar"
          >
            <PanelLeftClose
              size={20}
              className="h-5 w-5 text-gray-600 transition-colors"
              onMouseEnter={(e) => e.currentTarget.style.color = 'rgb(0, 130, 130)'}
              onMouseLeave={(e) => e.currentTarget.style.color = 'rgb(75, 85, 99)'}
            />
          </button>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto py-2 [scrollbar-width:none] [-ms-overflow-style:none] [&::-webkit-scrollbar]:hidden">
        <div className="space-y-1">
          {!isAdminDashboard && (
            <div className="px-3 mb-4">
              <button
                onClick={() => navigate('/Admin')}
                className="w-full flex items-center px-3 py-2 text-sm rounded-lg transition-colors"
                style={{ color: 'rgb(0, 130, 130)' }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.color = 'rgb(0, 110, 110)';
                  e.currentTarget.style.backgroundColor = 'rgba(0, 130, 130, 0.1)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.color = 'rgb(0, 130, 130)';
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Admin
              </button>
            </div>
          )}

          {renderContent()}
        </div>
      </div>
    </div>
  );
};

export default AdminSidebar;
