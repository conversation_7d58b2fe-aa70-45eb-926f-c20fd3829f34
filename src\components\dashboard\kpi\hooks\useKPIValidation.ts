import { useState, useCallback } from 'react';
import { KPIValidationResult } from '../types/kpiTypes';
import { validateKPISQL } from '@/services/api/chart/kpiService';

interface UseKPIValidationReturn {
  isValidating: Record<string, boolean>;
  validationResults: Record<string, KPIValidationResult | null>;
  validateSQL: (sql: string, fieldKey: string) => Promise<KPIValidationResult>;
  clearValidation: (fieldKey?: string) => void;
  isFieldValid: (fieldKey: string) => boolean;
  areAllFieldsValid: (requiredFields: string[]) => boolean;
}

const useKPIValidation = (): UseKPIValidationReturn => {
  const [isValidating, setIsValidating] = useState<Record<string, boolean>>({});
  const [validationResults, setValidationResults] = useState<Record<string, KPIValidationResult | null>>({});

  const validateSQL = useCallback(async (sql: string, fieldKey: string): Promise<KPIValidationResult> => {
    setIsValidating(prev => ({ ...prev, [fieldKey]: true }));
    try {
      // Use our own KPI SQL validation service
      const result = await validateKPISQL(sql);
      setValidationResults(prev => ({ ...prev, [fieldKey]: result }));
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Validation error occurred';
      const result: KPIValidationResult = {
        isValid: false,
        message: errorMessage
      };
      setValidationResults(prev => ({ ...prev, [fieldKey]: result }));
      return result;
    } finally {
      setIsValidating(prev => ({ ...prev, [fieldKey]: false }));
    }
  }, []);

  const clearValidation = useCallback((fieldKey?: string) => {
    if (fieldKey) {
      // Clear specific field
      setValidationResults(prev => ({ ...prev, [fieldKey]: null }));
      setIsValidating(prev => ({ ...prev, [fieldKey]: false }));
    } else {
      // Clear all fields
      setValidationResults({});
      setIsValidating({});
    }
  }, []);

  const isFieldValid = useCallback((fieldKey: string): boolean => {
    return validationResults[fieldKey]?.isValid === true;
  }, [validationResults]);

  const areAllFieldsValid = useCallback((requiredFields: string[]): boolean => {
    return requiredFields.every(fieldKey => isFieldValid(fieldKey));
  }, [isFieldValid]);

  return {
    isValidating,
    validationResults,
    validateSQL,
    clearValidation,
    isFieldValid,
    areAllFieldsValid
  };
};

export default useKPIValidation;
