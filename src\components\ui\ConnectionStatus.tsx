
import { useConnection } from '@/contexts/ConnectionContext';

interface ConnectionStatusProps {
  className?: string;
}

const ConnectionStatus: React.FC<ConnectionStatusProps> = ({ className = '' }) => {
  const { status, isChecking, checkConnection } = useConnection();
  
  // No need for local state or useEffect since we're using the context
  
  return (
    <div className={`flex items-center text-xs ${className}`}>
      {status === 'connected' ? (
        <>
          <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
          <span className="text-green-600">Connected</span>
        </>
      ) : (
        <>
          <div className="w-2 h-2 bg-red-500 rounded-full mr-1"></div>
          <span className="text-red-600">Disconnected</span>
          <button 
            onClick={() => checkConnection()} 
            className="ml-2 text-blue-500 hover:text-blue-700"
            disabled={isChecking}
          >
            {isChecking ? 'Checking...' : 'Retry'}
          </button>
        </>
      )}
    </div>
  );
};

export default ConnectionStatus;
