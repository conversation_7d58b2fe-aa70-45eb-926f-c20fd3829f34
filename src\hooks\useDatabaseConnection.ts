import { useState } from 'react';
import { toast } from 'sonner';
import { DatabaseService, DatabaseHelpers, type DatabaseConnectionForm } from '@/services/api/databaseService';

export const useDatabaseConnection = () => {
  // Database connection form state
  const [dbConnection, setDbConnection] = useState<DatabaseConnectionForm>({
    connectionName: '',
    host: '',
    port: '5432',
    databaseName: '',
    username: '',
    password: '',
    driverDialect: 'postgresql'
  });

  // Connection validation state
  const [connectionTested, setConnectionTested] = useState(false);
  const [connectionValid, setConnectionValid] = useState(false);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [isSubmittingDb, setIsSubmittingDb] = useState(false);

  // Form validation
  const validateForm = (): boolean => {
    const requiredFields = ['connectionName', 'host', 'port', 'databaseName', 'username', 'password'];
    return requiredFields.every(field => {
      const value = dbConnection[field as keyof DatabaseConnectionForm];
      return value && value.toString().trim() !== '';
    });
  };

  // Handle form input changes
  const handleInputChange = (field: keyof DatabaseConnectionForm, value: string) => {
    setDbConnection(prev => ({ ...prev, [field]: value }));
    // Reset validation when form changes
    if (connectionTested) {
      setConnectionTested(false);
      setConnectionValid(false);
    }
  };

  // Test database connection
  const testConnection = async (): Promise<boolean> => {
    if (!validateForm()) {
      toast.error('Please fill in all required fields');
      return false;
    }

    setIsTestingConnection(true);
    try {
      const userId = 'a6e3020d-984a-4394-ac73-da7ec5393314';
      const payload = DatabaseHelpers.formToPayload(dbConnection, userId);

      console.log('Testing connection with payload:', payload);

      const result = await DatabaseService.testConnection(payload);
      if (result.status === 'success') {
        toast.success('Database connection test successful!');
        setConnectionValid(true);
        return true;
      } else {
        toast.error(result.message || 'Failed to connect to database');
        setConnectionValid(false);
        return false;
      }
    } catch (error) {
      console.error('Error testing connection:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to connect to database');
      setConnectionValid(false);
      return false;
    } finally {
      setIsTestingConnection(false);
      setConnectionTested(true);
    }
  };

  // Submit database connection
  const submitConnection = async (): Promise<boolean> => {
    if (!validateForm()) {
      toast.error('Please fill in all required fields');
      return false;
    }

    if (!connectionTested || !connectionValid) {
      toast.error('Please test the connection successfully before saving');
      return false;
    }

    setIsSubmittingDb(true);
    try {
      const userId = '1d9f3bef-f399-42c9-8cfb-867c1a6ba176';
      const payload = DatabaseHelpers.formToPayload(dbConnection, userId);

      console.log('Saving database connection with payload:', payload);
      
      const result = await DatabaseService.createConnection(payload);
      console.log('Database connection created:', result);
      
      // Reset form on success
      resetForm();
      toast.success('Database connection created successfully!');
      return true;
    } catch (error) {
      console.error('Error saving database connection:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to save database connection');
      return false;
    } finally {
      setIsSubmittingDb(false);
    }
  };

  // Reset form to initial state
  const resetForm = () => {
    setDbConnection({
      connectionName: '',
      host: '',
      port: '5432',
      databaseName: '',
      username: '',
      password: '',
      driverDialect: 'postgresql'
    });
    setConnectionTested(false);
    setConnectionValid(false);
  };

  return {
    // State
    dbConnection,
    connectionTested,
    connectionValid,
    isTestingConnection,
    isSubmittingDb,
    
    // Actions
    handleInputChange,
    testConnection,
    submitConnection,
    resetForm,
    validateForm
  };
};
