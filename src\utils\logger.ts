// A proper logger utility
type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LoggerOptions {
  enabled: boolean;
  level: LogLevel;
}

// Default configuration that can be overridden
const DEFAULT_CONFIG: Record<string, LoggerOptions> = {
  default: {
    enabled: process.env.NODE_ENV !== 'production',
    level: 'info'
  },
  FileCacheManager: {
    enabled: process.env.NODE_ENV !== 'production',
    level: 'warn' // Only show warnings and errors by default
  }
};

// Allow configuration via localStorage for development
const getLoggerConfig = (namespace: string): LoggerOptions => {
  if (typeof window !== 'undefined') {
    try {
      const configStr = localStorage.getItem('logger_config');
      if (configStr) {
        const config = JSON.parse(configStr);
        if (config[namespace]) {
          return config[namespace];
        }
      }
    } catch (e) {
      // Ignore localStorage errors
    }
  }
  
  return DEFAULT_CONFIG[namespace] || DEFAULT_CONFIG.default;
};

// Create a logger with namespace
export const createLogger = (namespace: string) => {
  const getConfig = () => getLoggerConfig(namespace);
  
  const shouldLog = (level: LogLevel): boolean => {
    const config = getConfig();
    if (!config.enabled) return false;
    
    const levels: LogLevel[] = ['debug', 'info', 'warn', 'error'];
    const configLevelIndex = levels.indexOf(config.level);
    const logLevelIndex = levels.indexOf(level);
    
    return logLevelIndex >= configLevelIndex;
  };
  
  return {
    debug: (...args: any[]) => {
      if (shouldLog('debug')) {
        console.debug(`[${namespace}]`, ...args);
      }
    },
    info: (...args: any[]) => {
      if (shouldLog('info')) {
        console.info(`[${namespace}]`, ...args);
      }
    },
    warn: (...args: any[]) => {
      if (shouldLog('warn')) {
        console.warn(`[${namespace}]`, ...args);
      }
    },
    error: (...args: any[]) => {
      if (shouldLog('error')) {
        console.error(`[${namespace}]`, ...args);
      }
    }
  };
};