
export type DatasetStep = 
  | 'datasource' 
  | 'tables' 
  | 'columns' 
  | 'derived-columns' 
  | 'filter' 
  | 'define' 
  | 'generate' 
  | 'save';

export interface DataConnection {
  name: string;
  type: string;
  children: {name: string; type: string}[];
}

export interface ColumnDefinition {
  name: string;
  dataType: string;
  description: string;
  tableOrigin?: string;
}

export interface DerivedColumn {
  name: string;
  formula: string;
  naturalLanguage: string;
  description: string;
  dataType: string;
}

export interface FilterCondition {
  columnName: string;
  condition: string;
  value: string;
  naturalLanguage: string;
}

export interface DatasetState {
  showDatasetScreen: boolean;
  currentStep: DatasetStep;
  stepNumber: number;
  searchQuery: string;
  
  selectedConnections: string[];
  selectedTables: string[];
  selectedColumns: ColumnDefinition[];
  derivedColumns: DerivedColumn[];
  filterConditions: FilterCondition[];
  
  datasetName: string;
  datasetDescription: string;
  destinationConnection: string;
  schemaName: string;
  
  availableConnections: DataConnection[];
}

export const stepToNumber: Record<DatasetStep, number> = {
  'datasource': 1,
  'tables': 2,
  'columns': 3,
  'derived-columns': 4,
  'filter': 5,
  'define': 6,
  'generate': 7,
  'save': 8
};
