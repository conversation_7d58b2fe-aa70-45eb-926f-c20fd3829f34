import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ArrowLeft, Search, PanelLeftClose, PanelLeftOpen, Plus } from 'lucide-react';

interface AdminPageLayoutProps {
  // Page configuration
  pageTitle: string;
  showSidebar?: boolean;
  sidebarTitle?: string;
  searchPlaceholder?: string;
  
  // Sidebar content
  sidebarItems?: React.ReactNode;
  sidebarFooter?: React.ReactNode;
  showNewButton?: boolean;
  newButtonText?: string;
  onNewClick?: () => void;
  
  // Search functionality
  searchQuery?: string;
  onSearchChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  
  // Main content
  children: React.ReactNode;
  
  // Loading state
  isLoading?: boolean;
}

const AdminPageLayout: React.FC<AdminPageLayoutProps> = ({
  pageTitle,
  showSidebar = true,
  sidebarTitle = "Items",
  searchPlaceholder = "Search...",
  sidebarItems,
  sidebarFooter,
  showNewButton = true,
  newButtonText = "New Item",
  onNewClick,
  searchQuery = "",
  onSearchChange,
  children,
  isLoading = false
}) => {
  const navigate = useNavigate();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const handleSidebarToggle = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  // If sidebar is disabled, show simple layout
  if (!showSidebar) {
    return (
      <div className="h-screen bg-gray-50">
        <div className="p-6 space-y-6">
          {/* Back to Admin Button */}
          <div className="flex items-center mb-4">
            <Button 
              variant="ghost" 
              onClick={() => navigate('/Admin')}
              className="p-0 hover:bg-transparent transition-colors"
              style={{ color: 'rgb(0, 130, 130)' }}
              onMouseEnter={(e) => e.currentTarget.style.color = 'rgb(0, 110, 110)'}
              onMouseLeave={(e) => e.currentTarget.style.color = 'rgb(0, 130, 130)'}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Admin
            </Button>
          </div>

          {/* Header */}
          <div className="flex items-center justify-between">
            <h1 className="text-sm font-normal text-gray-700">{pageTitle}</h1>
            {showNewButton && onNewClick && (
              <Button
                onClick={onNewClick}
                className="text-white hover:opacity-90 transition-opacity"
                style={{ backgroundColor: 'rgb(0, 130, 130)' }}
              >
                <Plus className="w-4 h-4 mr-2" />
                {newButtonText}
              </Button>
            )}
          </div>

          {/* Main Content */}
          {children}
        </div>
      </div>
    );
  }

  // Collapsed sidebar view
  if (sidebarCollapsed) {
    return (
      <div className="flex h-screen bg-gray-50">
        {/* Collapsed Sidebar */}
        <div className="w-16 bg-gray-100 border-r border-gray-200 flex flex-col items-center py-4">
          <button
            onClick={handleSidebarToggle}
            className="w-10 h-10 rounded-md flex items-center justify-center hover:bg-gray-200"
            aria-label="Expand sidebar"
          >
            <PanelLeftOpen size={20} className="h-5 w-5 text-gray-600" />
          </button>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6 space-y-6">
          {/* Back to Admin Button */}
          <div className="flex items-center mb-4">
            <Button 
              variant="ghost" 
              onClick={() => navigate('/Admin')}
              className="p-0 hover:bg-transparent transition-colors"
              style={{ color: 'rgb(0, 130, 130)' }}
              onMouseEnter={(e) => e.currentTarget.style.color = 'rgb(0, 110, 110)'}
              onMouseLeave={(e) => e.currentTarget.style.color = 'rgb(0, 130, 130)'}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Admin
            </Button>
          </div>

          {/* Header */}
          <div className="flex items-center justify-between">
            <h1 className="text-sm font-normal text-gray-700">{pageTitle}</h1>
            {showNewButton && onNewClick && (
              <Button
                onClick={onNewClick}
                className="text-white hover:opacity-90 transition-opacity"
                style={{ backgroundColor: 'rgb(0, 130, 130)' }}
              >
                <Plus className="w-4 h-4 mr-2" />
                {newButtonText}
              </Button>
            )}
          </div>

          {/* Main Content */}
          {children}
        </div>
      </div>
    );
  }

  // Full layout with sidebar
  return (
    <div className="flex h-screen bg-gray-50">
      {/* Left Sidebar */}
      <div className="w-64 bg-gray-100 border-r border-gray-200 flex flex-col">
        {/* Header */}
        <div className="p-3 border-b border-gray-200">
          <div className="flex items-center space-x-2 group">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="text"
                placeholder={searchPlaceholder}
                className="w-full pl-8 pr-2 py-2 border rounded text-sm"
                value={searchQuery}
                onChange={onSearchChange}
              />
            </div>
            <button 
              onClick={handleSidebarToggle}
              className="p-2 text-gray-500 hover:text-gray-700 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
            >
              <PanelLeftClose size={18} />
            </button>
          </div>
        </div>

        {/* Back to Admin */}
        <div className="px-3 py-2 border-b border-gray-200">
          <Button 
            variant="ghost" 
            onClick={() => navigate('/Admin')}
            className="w-full justify-start p-2 transition-colors"
            style={{ color: 'rgb(0, 130, 130)' }}
            onMouseEnter={(e) => {
              e.currentTarget.style.color = 'rgb(0, 110, 110)';
              e.currentTarget.style.backgroundColor = 'rgba(0, 130, 130, 0.1)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.color = 'rgb(0, 130, 130)';
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Admin
          </Button>
        </div>

        {/* Sidebar Content */}
        <div className="flex-1 overflow-y-auto py-2">
          {isLoading ? (
            <div className="flex justify-center items-center h-full">
              <div className="text-sm text-gray-600">Loading {sidebarTitle.toLowerCase()}...</div>
            </div>
          ) : (
            <>
              {/* New Button */}
              {showNewButton && onNewClick && (
                <div className="px-3 mb-2">
                  <Button
                    onClick={onNewClick}
                    className="w-full justify-start text-white hover:opacity-90 transition-opacity"
                    style={{ backgroundColor: 'rgb(0, 130, 130)' }}
                    size="sm"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    {newButtonText}
                  </Button>
                </div>
              )}

              {/* Sidebar Items */}
              <div className="space-y-1 px-3">
                {sidebarItems}
              </div>
            </>
          )}
        </div>
        
        {/* Footer */}
        {sidebarFooter && (
          <div className="p-3 border-t border-gray-200">
            {sidebarFooter}
          </div>
        )}
      </div>

      {/* Main Content */}
      <div className="flex-1 p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h1 className="text-sm font-normal text-gray-700">{pageTitle}</h1>
        </div>

        {/* Main Content */}
        {children}
      </div>
    </div>
  );
};

export default AdminPageLayout;
