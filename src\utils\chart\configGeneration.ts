
/**
 * Helper function to create tables array for API requests - supports multiple tables
 */
export const createTablesArray = (
  xAxisTable: string, 
  xAxisColumn: string,
  yAxisColumns: string[], 
  yAxisTables: string[]
) => {
  console.log("createTablesArray called with:", {
    xAxisTable,
    xAxisColumn,
    yAxisColumns,
    yAxisTables
  });
  
  // Collect all unique tables
  const tablesMap = new Map<string, Set<string>>();
  
  // Add X-axis table and column
  if (xAxisTable && xAxisColumn) {
    if (!tablesMap.has(xAxisTable)) {
      tablesMap.set(xAxisTable, new Set());
    }
    tablesMap.get(xAxisTable)!.add(xAxisColumn);
    console.log("Added x-axis:", xAxisTable, xAxisColumn);
  }
  
  // Add Y-axis tables and columns with proper validation
  yAxisColumns.forEach((column, index) => {
    if (!column) return; // Skip empty columns
    
    const tableName = yAxisTables[index] || xAxisTable;
    if (!tableName) return; // Skip if no table name
    
    if (!tablesMap.has(tableName)) {
      tablesMap.set(tableName, new Set());
    }
    tablesMap.get(tableName)!.add(column);
    console.log("Added y-axis:", tableName, column);
  });
  
  // Convert to API format and validate
  const result = Array.from(tablesMap.entries())
    .filter(([tableName, columnsSet]) => tableName && columnsSet.size > 0)
    .map(([tableName, columnsSet]) => ({
      table_name: tableName,
      columns: Array.from(columnsSet)
    }));
  
  console.log("createTablesArray result:", result);
  
  if (result.length === 0) {
    console.warn("No valid tables created - this may cause API errors");
  }
  
  return result;
};

/**
 * Function to create chart configuration with enhanced multi-table support and editing validation
 */
export const createChartConfig = (
  chartStyle: string,
  xAxisColumn: string,
  yAxisColumns: string[],
  xAxisTable: string,
  yAxisTables: string[],
  chartName: string,
  selectedDatabase: string,
  aggregationType: string | null,
  filters?: { [key: string]: any } // Updated type to handle complex filter objects
) => {
  console.log("createChartConfig called with:", {
    chartStyle,
    xAxisColumn,
    yAxisColumns,
    xAxisTable,
    yAxisTables,
    chartName,
    selectedDatabase,
    aggregationType,
    filters // Log filters
  });
  
  // Validate inputs before proceeding
  if (!xAxisTable && yAxisTables.length === 0) {
    console.error("createChartConfig: No tables provided");
    throw new Error("At least one table must be specified");
  }
  
  if (!xAxisColumn && yAxisColumns.length === 0) {
    console.error("createChartConfig: No columns provided");
    throw new Error("At least one column must be specified");
  }
  
  // Ensure yAxisTables array has the same length as yAxisColumns
  const normalizedYAxisTables = yAxisColumns.map((_, index) => 
    yAxisTables[index] || xAxisTable
  ).filter(Boolean);
  
  console.log("Normalized y-axis tables:", normalizedYAxisTables);
  
  // Create tables array with proper table associations using the helper function
  const tables = createTablesArray(xAxisTable, xAxisColumn, yAxisColumns, normalizedYAxisTables);
  
  // Format y_axis according to backend expectations
  let formattedYAxis;
  
  if (aggregationType) {
    // For aggregated data, use object format with aggregation
    formattedYAxis = yAxisColumns.map((column, index) => ({
      column,
      table_name: normalizedYAxisTables[index] || xAxisTable,
      aggregation: aggregationType
    }));
    console.log("Created aggregated y-axis format:", formattedYAxis);
  } else {
    // For non-aggregated data, use simple string format if only one column
    // or array of strings if multiple columns
    formattedYAxis = yAxisColumns.length === 1 
      ? yAxisColumns[0] 
      : yAxisColumns;
    console.log("Created non-aggregated y-axis format:", formattedYAxis);
  }
  
  // Set group_by to the X-axis column when using aggregation
  const groupBy = aggregationType ? [xAxisColumn] : null;
  
  const config = {
    chart_type: chartStyle,
    x_axis: xAxisColumn,
    y_axis: formattedYAxis,
    tables: tables,
    chart_name: chartName,
    db_type: selectedDatabase,
    clipboard: true,
    editable: true,
    refresh: true,
    shareable: true,
    export: true,
    verfied: true,
    group_by: groupBy,
    filters: filters // Add filters to the config
  };
  
  console.log('createChartConfig: Final config created:', config);
  
  // Validate the final config
  if (tables.length === 0) {
    console.error("createChartConfig: No valid tables in final config");
    throw new Error("Configuration must include at least one valid table");
  }
  
  return config;
};
