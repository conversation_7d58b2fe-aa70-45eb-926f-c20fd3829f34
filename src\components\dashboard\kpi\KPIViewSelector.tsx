import React from 'react';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { KPIViewSelectorProps, KPIViewType } from './types/kpiTypes';

const KPIViewSelector: React.FC<KPIViewSelectorProps> = ({
  selectedView,
  onViewChange
}) => {
  const kpiViewOptions: { value: KPIViewType; label: string }[] = [
    { value: 'kpi-only', label: 'KPI Only' },
    { value: 'current-vs-prior', label: 'KPI Current Vs Prior' },
    { value: 'target-based', label: 'KPI TargetBased' }
  ];

  return (
    <div className="flex items-center space-x-8 mb-6 p-3 bg-gray-50 rounded-lg border">
      <span className="text-sm font-medium text-gray-700">KPI Type:</span>

      <RadioGroup
        value={selectedView}
        onValueChange={onViewChange}
        className="flex items-center space-x-10"
      >
        {kpiViewOptions.map((option) => (
          <div key={option.value} className="flex items-center space-x-2">
            <RadioGroupItem
              value={option.value}
              id={`kpi-${option.value}`}
              className="text-blue-600 border-blue-300 focus:ring-blue-500"
            />
            <Label
              htmlFor={`kpi-${option.value}`}
              className={`text-sm font-medium cursor-pointer ${
                selectedView === option.value ? 'text-blue-600' : 'text-gray-700'
              }`}
            >
              {option.label}
            </Label>
          </div>
        ))}
      </RadioGroup>
    </div>
  );
};

export default KPIViewSelector;
