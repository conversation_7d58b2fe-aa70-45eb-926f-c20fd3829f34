import { centralApiClient } from '@/services/api/centralApiClient';
import {
  DataGridRequest,
  DataGridResponse,
  DataGridError,
  DataGridSQLRequest
} from './datagridTypes';

export const fetchDataGrid = async (request: DataGridRequest): Promise<DataGridResponse> => {
  try {
    console.log('DataGrid Service: Executing DataGrid with request:', JSON.stringify(request, null, 2));

    // Make API call using centralApiClient
    const response = await centralApiClient.makeRequest('chart', '/data-grid/fetch', {
      method: 'POST',
      body: request
    });

    console.log('DataGrid Service: Received response:', response);

    // Type assertion and validation
    const data = response as DataGridResponse;

    if (!data || !data.status) {
      throw new Error('Invalid response format from DataGrid API');
    }

    return data;
  } catch (error: any) {
    console.error('DataGrid Service: Error executing DataGrid:', error);

    const errorMessage = error.message || 'Failed to fetch data grid';
    const errorStatus = error.status;

    const dataGridError: DataGridError = {
      message: errorMessage,
      status: errorStatus,
    };

    throw dataGridError;
  }
};

// Helper function to transform columns to API request format
export const transformColumnsToRequest = (
  connectionId: string,
  selectedColumns: { name: string; tableName?: string }[],
  filterConditions?: { column: string; operator: string; value: string }[]
): DataGridRequest => {
  // Group columns by table name
  const groupedColumns: { [tableName: string]: string[] } = {};

  selectedColumns.forEach(column => {
    const tableName = column.tableName || 'unknown';
    if (!groupedColumns[tableName]) {
      groupedColumns[tableName] = [];
    }
    groupedColumns[tableName].push(column.name);
  });

  // Convert to API request format
  const tables = Object.entries(groupedColumns).map(([tableName, columns]) => ({
    table_name: tableName,
    columns: columns,
  }));

  const request: DataGridRequest = {
    connection_id: connectionId,
    tables: tables,
  };

  // Process filter conditions and convert to chart-like filter format
  if (filterConditions && filterConditions.length > 0) {
    const filters: { [key: string]: any } = {};

    filterConditions.forEach(condition => {
      if (condition.column && condition.value) {
        const column = condition.column;
        const operator = condition.operator;
        const value = condition.value;

        if (operator === '=' || !operator) {
          // Simple equality filter: "column": "value"
          filters[column] = value;
        } else if (operator === 'in' || operator === 'not_in') {
          // Multiple values filter: "column": { "in": ["value1", "value2"] } or { "not_in": ["value1", "value2"] }
          const values = value.split(',').map(v => v.trim()).filter(v => v);
          if (values.length > 1) {
            filters[column] = { [operator]: values };
          } else if (values.length === 1) {
            if (operator === 'in') {
              filters[column] = values[0]; // Single value with 'in', use simple format
            } else {
              filters[column] = { [operator]: values }; // Single value with 'not_in', keep operator format
            }
          }
        } else {
          // Other operators: "column": { "operator": "value" }
          const operatorMap: { [key: string]: string } = {
            '>': 'gt',
            '<': 'lt',
            '>=': 'gte',
            '<=': 'lte',
            '!=': 'ne',
            'LIKE': 'like'
          };

          const mappedOperator = operatorMap[operator] || operator;
          filters[column] = { [mappedOperator]: value };
        }
      }
    });

    if (Object.keys(filters).length > 0) {
      request.filters = filters;
      console.log('DataGrid API: Processed filters:', JSON.stringify(filters, null, 2));
    }
  }

  return request;
};

// New API function for SQL execution in DataGrid
export const executeDataGridSQL = async (request: DataGridSQLRequest): Promise<DataGridResponse> => {
  try {
    console.log('DataGrid SQL Service: Executing SQL with request:', JSON.stringify(request, null, 2));

    // Make API call using centralApiClient
    const response = await centralApiClient.makeRequest('chart', '/data-grid/execute', {
      method: 'POST',
      body: request
    });

    console.log('DataGrid SQL Service: Received response:', response);

    // Type assertion and validation
    const data = response as DataGridResponse;

    if (!data || !data.status) {
      throw new Error('Invalid response format from DataGrid SQL API');
    }

    return data;
  } catch (error: any) {
    console.error('DataGrid SQL Service: Error executing SQL:', error);

    const errorMessage = error.message || 'Failed to execute SQL query';
    const errorStatus = error.status;

    const dataGridError: DataGridError = {
      message: errorMessage,
      status: errorStatus,
    };

    throw dataGridError;
  }
};
