
import React, { useState } from 'react';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useAppDispatch, useAppSelector } from '@/hooks/useRedux';
import { 
  addFilterCondition,
  updateFilterCondition,
  removeFilterCondition
} from '@/stores/datasetSlice';
import StepHeader, { NavigationButtons } from './StepHeader';

interface FilterStepProps {
  onClose: () => void;
}

const FilterStep: React.FC<FilterStepProps> = ({ onClose }) => {
  const dispatch = useAppDispatch();
  const { selectedColumns, derivedColumns, filterConditions } = useAppSelector(state => state.dataset);
  
  const [newFilterCondition, setNewFilterCondition] = useState({
    columnName: '',
    condition: '',
    value: '',
    naturalLanguage: ''
  });

  const handleSaveFilterCondition = () => {
    if (newFilterCondition.columnName && newFilterCondition.condition) {
      dispatch(addFilterCondition(newFilterCondition));
      setNewFilterCondition({
        columnName: '',
        condition: '',
        value: '',
        naturalLanguage: ''
      });
    }
  };

  return (
    <div className="p-4">
      <StepHeader 
        title="Filter Conditions" 
        onClose={onClose}
      />
      
      <div className="border border-gray-200 p-4 rounded-lg mb-4">
        <h3 className="font-medium mb-4">Add New Filter</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
          <div>
            <label className="block text-sm mb-1">Column Name:</label>
            <select
              className="w-full p-2 border border-gray-300 rounded-md"
              value={newFilterCondition.columnName}
              onChange={(e) => setNewFilterCondition({...newFilterCondition, columnName: e.target.value})}
            >
              <option value="">Select Column</option>
              {selectedColumns.map(col => (
                <option key={`${col.tableOrigin}_${col.name}`} value={col.name}>
                  {col.name} ({col.tableOrigin})
                </option>
              ))}
              {derivedColumns.map(col => (
                <option key={`derived_${col.name}`} value={col.name}>
                  {col.name} (Derived)
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm mb-1">Filter Condition:</label>
            <input
              type="text"
              className="w-full p-2 border border-gray-300 rounded-md"
              value={newFilterCondition.condition}
              onChange={(e) => setNewFilterCondition({...newFilterCondition, condition: e.target.value})}
            />
          </div>
          <div>
            <label className="block text-sm mb-1">Filter Value:</label>
            <input
              type="text"
              className="w-full p-2 border border-gray-300 rounded-md"
              value={newFilterCondition.value}
              onChange={(e) => setNewFilterCondition({...newFilterCondition, value: e.target.value})}
            />
          </div>
          <div>
            <label className="block text-sm mb-1">Define Filter in NL:</label>
            <input
              type="text"
              className="w-full p-2 border border-gray-300 rounded-md"
              value={newFilterCondition.naturalLanguage}
              onChange={(e) => setNewFilterCondition({...newFilterCondition, naturalLanguage: e.target.value})}
            />
          </div>
        </div>
        <div className="flex justify-end gap-2">
          <Button
            variant="outline"
            onClick={() => setNewFilterCondition({
              columnName: '',
              condition: '',
              value: '',
              naturalLanguage: ''
            })}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSaveFilterCondition}
            disabled={!newFilterCondition.columnName || !newFilterCondition.condition}
          >
            Save
          </Button>
        </div>
      </div>

      <Table className="border">
        <TableHeader className="bg-gray-50">
          <TableRow>
            <TableHead className="w-1/4">Column Name</TableHead>
            <TableHead className="w-1/4">Filter Condition</TableHead>
            <TableHead className="w-1/4">Filter Value</TableHead>
            <TableHead className="w-1/4">Define Filter in NL</TableHead>
            <TableHead className="w-16">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filterConditions.length === 0 ? (
            <TableRow>
              <TableCell colSpan={5} className="text-center py-4 text-gray-500 italic">
                No filter conditions defined
              </TableCell>
            </TableRow>
          ) : (
            filterConditions.map((filter, index) => (
              <TableRow key={index}>
                <TableCell>{filter.columnName}</TableCell>
                <TableCell>{filter.condition}</TableCell>
                <TableCell>{filter.value}</TableCell>
                <TableCell>{filter.naturalLanguage}</TableCell>
                <TableCell>
                  <button
                    className="text-red-500 hover:text-red-700"
                    onClick={() => dispatch(removeFilterCondition(index))}
                  >
                    <X size={18} />
                  </button>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
      
      {/* Add navigation buttons at the bottom */}
      <div className="mt-6">
        <NavigationButtons 
          showPrevious={true} 
          showNext={true}
        />
      </div>
    </div>
  );
};

export default FilterStep;
