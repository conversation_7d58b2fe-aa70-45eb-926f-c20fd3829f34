
export interface Meeting {
  id: number;
  title: string;
  date: string;
  time: string;
  attendees: string[];
  agenda: string[];
  summary: string;
}

export interface MeetingData {
  meetingIds: string[];
  questions?: {
    question: string;
    answer: string;
  }[];
}

export interface Message {
  type: 'query' | 'response';
  content: string;
  minimized: boolean;
  queryResult?: QueryResultData;
  meetingData?: MeetingData;
}

export interface Project {
  id: number;
  name: string;
  description: string;
  startDate: string;
  endDate: string;
  status: 'active' | 'completed' | 'on hold';
  tasks: Task[];
}

export interface Task {
  id: number;
  name: string;
  description: string;
  status: 'to do' | 'in progress' | 'completed';
  assignee: string;
  dueDate: string;
}

export interface ChatTopic {
  id: number;
  title: string;
  messages: Message[];
}

export interface VisualizationData {
  can_visualize: boolean;
  best_chart_type: 'bar' | 'line' | 'pie' | string;
  chart_title: string;
  x_axis_key_name: string;
  y_axis_key_name: string;
  reasoning?: string;
}

export interface QueryResultData {
  query: string;
  tableData?: {
    columns: string[];
    rows: any[];
  };
  sql_result?: any[];
  visualization?: VisualizationData;
}

export type TranscriptTab = 'transcript' | 'summary' | 'actionItems' | 'notes' | 'openQuestions';

export interface UploadedFile {
  name: string;
  type: string;
  size: number;
  section: TranscriptTab[];
  url?: string;
}

export interface TextSizeStyle {
  size: string;
  spacing: string;
}

export interface TextSizeStyles {
  small: {
    size: string;
    spacing: string;
  };
  medium: {
    size: string;
    spacing: string;
  };
  large: {
    size: string;
    spacing: string;
  };
  [key: string]: {
    size: string;
    spacing: string;
  };
}

export interface ContentProps {
  dashboardType: number;
  textSize?: 'small' | 'medium' | 'large';
  searchQuery?: string;
  headerSearchQuery?: string;
  showMetricScreen?: boolean;
  showDatasetScreen?: boolean;
  setShowMetricScreen: (show: boolean) => void;
  setShowDatasetScreen: (show: boolean) => void;
}

export const textSizeStyles: TextSizeStyles = {
  small: {
    size: "text-sm",
    spacing: "space-y-3"
  },
  medium: {
    size: "text-base",
    spacing: "space-y-4"
  },
  large: {
    size: "text-lg",
    spacing: "space-y-5"
  }
};


