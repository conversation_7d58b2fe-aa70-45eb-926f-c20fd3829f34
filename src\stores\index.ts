import { configureStore } from '@reduxjs/toolkit';
import messageReducer from './messageSlice';
import datasetReducer from './datasetSlice';
// import fileReducer from './fileSlice';
import errorReducer from './errorSlice';
// import projectReducer from './projectSlice';
import chartReducer from './chartSlice';

export const store = configureStore({
  reducer: {
    message: messageReducer,
    dataset: datasetReducer,
    // file: fileReducer,
    error: errorReducer,
    // project: projectReducer,
    chart: chartReducer
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;



