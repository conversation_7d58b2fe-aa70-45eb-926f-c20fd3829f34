
import { createSlice } from '@reduxjs/toolkit';
import { MessageState, initialState } from './message/types';
import { messageReducers } from './message/reducers';
import { activateVoiceInput } from './message/extraActions';
import { loadMessages, submitQuery, regenerateQueryAction } from './message/thunks';
import { toast } from 'sonner';

export const messageSlice = createSlice({
  name: 'message',
  initialState,
  reducers: messageReducers,
  extraReducers: (builder) => {
    builder
      .addCase(loadMessages.fulfilled, (state, action) => {
        state.messages = action.payload;
        state.filteredMessages = action.payload;
      })
      .addCase(submitQuery.pending, (state, action) => {
        const { dashboardType } = action.meta.arg;
        let inputContent = '';
        
        switch (dashboardType) {
          case 1:
            inputContent = state.chatbotInputValue;
            state.chatbotInputValue = '';
            break;
          case 2:
            inputContent = state.transcriptInputValue;
            state.transcriptInputValue = '';
            break;
          case 3:
            inputContent = state.dadaInputValue;
            state.dadaInputValue = '';
            break;
          default:
            inputContent = state.inputValue;
            state.inputValue = '';
        }
        
        state.messages.push({
          type: 'query',
          content: inputContent,
          minimized: false,
        });
        state.filteredMessages = state.messages;
        state.isLoading = true;
      })
      .addCase(submitQuery.fulfilled, (state, action) => {
        // Check if action.payload exists
        if (!action.payload) {
          state.isLoading = false;
          return;
        }
        
        // Use type guards to check what properties exist in the payload
        if ('meetingData' in action.payload) {
          // Handle dashboard-1 response (meeting data)
          state.messages.push({
            type: 'response',
            content: action.payload.formattedMessage || 'No response received',
            minimized: false,
            meetingData: action.payload.meetingData
          });
        } 
        else if ('queryResult' in action.payload) {
          // Handle dashboard-3 response (query result)
          state.messages.push({
            type: 'response',
            content: 'Here are the results of your query:',
            minimized: false,
            queryResult: action.payload.queryResult,
          });
        }
        
        state.filteredMessages = state.messages;
        state.isLoading = false;
      })
      .addCase(submitQuery.rejected, (state) => {
        state.isLoading = false;
        toast.error('Query Error', {
          description: 'Failed to process your query. Please try again.',
          style: { backgroundColor: '#FFDEE2', color: '#505050', border: '1px solid #ea384c' }
        });
      })
      .addCase(regenerateQueryAction.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(regenerateQueryAction.fulfilled, (state, action) => {
        const queryIndex = state.messages.findIndex(
          message => message.type === 'query' && message.content === action.payload.originalQuery
        );

        if (queryIndex !== -1 && queryIndex + 1 < state.messages.length) {
          state.messages[queryIndex + 1].queryResult = action.payload.queryResult;
          state.filteredMessages = state.messages;
        }

        state.isLoading = false;
      })
      .addCase(regenerateQueryAction.rejected, (state) => {
        state.isLoading = false;
        toast.error('Regenerate Query Error', {
          description: 'Failed to regenerate the query. Please try again.',
          style: { backgroundColor: '#FFDEE2', color: '#505050', border: '1px solid #ea384c' }
        });
      });
  }
});

export const { 
  setInputValue, 
  setSearchQuery, 
  setHeaderSearchQuery, 
  setUploadedFile,
  deleteMessage,
  toggleMinimizeMessage,
  updateQueryResultForMessage
} = messageSlice.actions;

export { activateVoiceInput, loadMessages, submitQuery, regenerateQueryAction };

export default messageSlice.reducer;
