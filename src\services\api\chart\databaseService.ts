import { 
  DatabaseConnectionParams, 
  DatabaseConnectionResponse, 
  DatabaseDisconnectResponse,
  ColumnInfo 
} from './chartTypes';
import { centralApiClient } from '@/services/api/centralApiClient';

/**
 * Fetches available databases from the chart API
 * @returns Promise with list of database names
 */
export const fetchDatabases = async (): Promise<string[]> => {
  try {
    console.log('Fetching available databases');
    
    const response = await centralApiClient.makeRequest('chart', '/databases', {
      method: 'GET'
    });
    
    // Type assertion for response
    const data = response as { databases?: string[] };
    return data.databases || [];
  } catch (error) {
    console.error('Error fetching databases:', error);
    throw error;
  }
};

/**
 * Connects to a database using the provided connection parameters
 * @param params Database connection parameters
 * @returns Promise with connection response
 */
export const connectToDatabase = async (params: DatabaseConnectionParams): Promise<DatabaseConnectionResponse> => {
  try {
    console.log('Connecting to database with params:', {
      db_type: params.db_type,
      // Don't log sensitive information like passwords
      has_password: !!params.password
    });
    
    const response = await centralApiClient.makeRequest('chart', '/connect', {
      method: 'POST',
      body: params
    });
    
    // Type assertion for response
    const data = response as { status?: string; connection_id?: string };
    
    return {
      status: data.status || 'connected',
      connection_id: data.connection_id || ''
    };
  } catch (error) {
    console.error('Error connecting to database:', error);
    throw error;
  }
};

/**
 * Disconnects from a database using the connection ID
 * @param connectionId The database connection ID to disconnect
 * @returns Promise with disconnect response
 */
export const disconnectDatabase = async (connectionId: string): Promise<DatabaseDisconnectResponse> => {
  try {
    console.log('Disconnecting from database with connection ID:', connectionId);
    
    const response = await centralApiClient.makeRequest('chart', `/disconnect/${connectionId}`, {
      method: 'DELETE'
    });
    
    // Type assertion for response
    const data = response as { status?: string };
    
    return {
      status: data.status || 'disconnected'
    };
  } catch (error) {
    console.error('Error disconnecting from database:', error);
    throw error;
  }
};

/**
 * Fetches available tables for a given connection ID
 * @param connectionId The database connection ID
 * @returns Promise with list of table names
 */
export const fetchTables = async (connectionId: string): Promise<string[]> => {
  try {
    console.log('Fetching tables for connection:', connectionId);
    
    const response = await centralApiClient.makeRequest('chart', `/tables/${connectionId}`, {
      method: 'GET'
    });
    
    // Type assertion for response
    const data = response as { tables?: string[] };
    
    return data.tables || [];
  } catch (error) {
    console.error('Error fetching tables:', error);
    throw error;
  }
};

/**
 * Fetches columns for a specific table
 * @param connectionId The database connection ID
 * @param tableName The name of the table
 * @returns Promise with list of column information
 */
export const fetchTableColumns = async (connectionId: string, tableName: string): Promise<ColumnInfo[]> => {
  try {
    console.log(`Fetching columns for table ${tableName} with connection ${connectionId}`);
    
    const response = await centralApiClient.makeRequest('chart', `/columns/${connectionId}/${tableName}`, {
      method: 'GET'
    });
    
    // Type assertion for response
    const data = response as { columns?: ColumnInfo[] };
    
    return data.columns || [];
  } catch (error) {
    console.error('Error fetching table columns:', error);
    throw error;
  }
};
