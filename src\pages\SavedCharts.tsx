
import React, { useState, useMemo, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import DashboardContainer from '@/components/dashboard/layout/DashboardContainer';
import DashboardMain from '@/components/dashboard/layout/DashboardMain';
import Header from '@/components/dashboard/Header';
import SharedSidebar from '@/components/shared/SharedSidebar';
import { useAppDispatch, useAppSelector } from '@/hooks/useRedux';
import { useConnectionManager } from '@/hooks/useConnectionManager';
import { useChartNavigation } from '@/hooks/useChartNavigation';
import { useChartsData } from '@/hooks/useChartsData';
import { SavedChart, ChartViewMode } from '@/types/chartTypes';
import ChartGrid from '@/components/savedCharts/ChartGrid';
import ChartDetail from '@/components/savedCharts/ChartDetail';
import { toast } from 'sonner';
import { ChevronDown, ChevronRight, FolderIcon,Loader2 } from 'lucide-react';

const SavedChartsPage: React.FC = () => {
  const { chartId } = useParams<{ chartId?: string }>();
  const navigate = useNavigate();
  const { navigateToChartBuilder } = useChartNavigation();
  const { connectionId, selectedDatabase } = useConnectionManager();
  
  const {
    charts,
    loading,
    chartData,
    loadingChartData,
    setChartData,
    loadCharts,
    loadChartById,
    loadChartData,
    establishConnection,
    safeIncrementChartViews
  } = useChartsData();
  
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedChart, setSelectedChart] = useState<SavedChart | null>(null);
  const [activeConnectionId, setActiveConnectionId] = useState<string>('');
  const [viewMode, setViewMode] = useState<ChartViewMode>('grid');
  const [foldersExpanded, setFoldersExpanded] = useState<Record<string, boolean>>({
    'myCharts': true
  });

  const toggleFolder = (folderName: string) => {
    setFoldersExpanded(prev => ({
      ...prev,
      [folderName]: !prev[folderName]
    }));
  };

  const filteredCharts = useMemo(() => 
    charts.filter(chart => 
      chart.chart_name.toLowerCase().includes(searchQuery.toLowerCase())
    ), 
    [charts, searchQuery]
  );
  
  const toggleSidebar = () => setSidebarCollapsed(prev => !prev);
  
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };
  
  const handleSelectChart = async (chart: SavedChart) => {
    if (!chart.chart_id) {
      setSelectedChart(chart);
      setViewMode('detail');
      return;
    }
    
    try {
      // First navigate to the chart detail page
      navigate(`/saved-charts/${chart.chart_id}`, { replace: true });
      
      // Then increment the view count (with centralApiClient's built-in deduplication)
      safeIncrementChartViews(chart.chart_id).catch(err => {
        // Log but don't block on view increment errors
        console.error('View increment failed:', err);
      });
      
      // Then load the chart data
      const chartData = await loadChartById(chart.chart_id);
      if (chartData) {
        setSelectedChart(chartData);
        setViewMode('detail');
      }
    } catch (error) {
      console.error('Error selecting chart:', error);
      toast.error('Failed to load chart');
    }
  };
  
  const handleSelectChartById = async (chartId: string) => {
    try {
      // First increment the view count (with centralApiClient's built-in deduplication)
      safeIncrementChartViews(chartId).catch(err => {
        // Log but don't block on view increment errors
        console.error('View increment failed:', err);
      });
      
      // Then load the chart data
      const chartData = await loadChartById(chartId);
      if (chartData) {
        setSelectedChart(chartData);
        setViewMode('detail');
      } else {
        toast.error('Chart not found');
      }
    } catch (error) {
      console.error('Error selecting chart by ID:', error);
      toast.error('Failed to load chart');
    }
  };
  
  const handleBackToGrid = () => {
    // First navigate back to the main saved charts page
    navigate('/saved-charts', { replace: true });
    
    // Reset view mode and selected chart
    setViewMode('grid');
    setSelectedChart(null);
    setChartData([]);
    
    // Refresh the charts list to get the latest data
    // This will trigger a re-fetch of all charts from the server
    const refreshCharts = async () => {
      try {
        // Re-fetch charts data
        await loadCharts();
      } catch (error) {
        console.error('Error refreshing charts:', error);
        toast.error('Failed to refresh charts');
      }
    };
    
    refreshCharts();
  };
  
  const handleOpenInChartBuilder = async () => {
    if (!selectedChart) return;
    
    try {
      console.log("Opening chart in builder:", selectedChart.chart_name);
      
      let chartToEdit = { ...selectedChart };
      
      // Use existing chart data if available
      if (selectedChart.chart_response && selectedChart.chart_response.data) {
        chartToEdit.chart_data = selectedChart.chart_response.data;
      } else if (chartData && chartData.length > 0) {
        chartToEdit.chart_data = chartData;
      }
      
      // Use existing connection if available, otherwise let the navigation handle it
      const connId = activeConnectionId || selectedChart.connection_id;
      
      const success = await navigateToChartBuilder(chartToEdit, connId);
      
      if (!success) {
        toast.error('Failed to open chart in builder');
      }
    } catch (error) {
      console.error('Error opening chart in builder:', error);
      toast.error('Failed to open chart in builder');
    }
  };

  const handleRefreshChartData = async () => {
    if (selectedChart && selectedChart.chart_id) {
      try {
        // First reload the chart configuration
        const refreshedChart = await loadChartById(selectedChart.chart_id);
        
        if (refreshedChart) {
          // Then reload the chart data using the connection ID
          const connId = activeConnectionId || refreshedChart.connection_id;
          if (connId) {
            await loadChartData(refreshedChart, connId);
          }
        }
      } catch (error) {
        console.error('Error refreshing chart data:', error);
        toast.error('Failed to refresh chart data');
      }
    }
  };

  // Handle connection and data loading
  useEffect(() => {
    if (selectedChart && viewMode === 'detail') {
      const handleConnection = async () => {
        // First check if we already have data in the chart_response
        if (selectedChart.chart_response?.data?.length > 0) {
          console.log('Using existing chart_response data:', selectedChart.chart_response.data.length, 'items');
          setChartData(selectedChart.chart_response.data);
          return;
        }
        
        // Then check if we have data in chart_data
        if (selectedChart.chart_data?.length > 0) {
          console.log('Using existing chart_data:', selectedChart.chart_data.length, 'items');
          setChartData(selectedChart.chart_data);
          return;
        }
        
        // If no data is available, try to fetch it
        console.log('No existing chart data, attempting to fetch it');
        
        // Priority 1: Use existing connection from chart
        if (selectedChart.connection_id) {
          console.log('Using existing connection from chart:', selectedChart.connection_id);
          setActiveConnectionId(selectedChart.connection_id);
          await loadChartData(selectedChart, selectedChart.connection_id);
          return;
        }
        
        // Priority 2: Use current active connection
        if (activeConnectionId) {
          console.log('Using current active connection:', activeConnectionId);
          await loadChartData(selectedChart, activeConnectionId);
          return;
        }
        
        // Priority 3: Establish new connection
        console.log('Establishing new connection for chart');
        const newConnectionId = await establishConnection(selectedChart, selectedDatabase);
        if (newConnectionId) {
          setActiveConnectionId(newConnectionId);
          await loadChartData(selectedChart, newConnectionId);
        }
      };
      
      handleConnection();
    }
  }, [selectedChart, viewMode, selectedDatabase, activeConnectionId]);
  
  useEffect(() => {
    if (chartId && charts.length > 0) {
      const chartFromList = charts.find(chart => chart.chart_id === chartId);
      
      if (chartFromList) {
        handleSelectChart(chartFromList);
      } else {
        handleSelectChartById(chartId);
      }
    }
  }, [chartId, charts]);

  const renderSidebarContent = () => {
    if (loading) {
      return (
        <div className="flex justify-center items-center p-3">
          <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />
        </div>
      );
    }

    if (filteredCharts.length === 0) {
      return (
        <div className="text-center p-3 text-xs text-gray-500">
          No charts found
        </div>
      );
    }

    return (
      <div className="space-y-1 px-2">
        {/* My Charts folder */}
        <div className="space-y-1">
          <div 
            className="flex items-center justify-between p-1.5 rounded cursor-pointer hover:bg-gray-100"
            onClick={() => toggleFolder('myCharts')}
          >
            <div className="flex items-center">
              <FolderIcon size={14} className="mr-1.5 text-blue-500" />
              <span className="text-xs font-medium">My Charts</span>
              <span className="ml-1.5 text-xs text-gray-500">({filteredCharts.length})</span>
            </div>
            {foldersExpanded['myCharts'] ? 
              <ChevronDown size={14} className="text-gray-500" /> : 
              <ChevronRight size={14} className="text-gray-500" />
            }
          </div>
          
          {foldersExpanded['myCharts'] && (
            <div className="pl-6 space-y-0.5">
              {filteredCharts.map((chart) => (
                <div 
                  key={chart.chart_id}
                  className={`p-1 rounded cursor-pointer hover:bg-gray-100 ${
                    selectedChart?.chart_id === chart.chart_id ? 'bg-blue-100' : ''
                  }`}
                  onClick={() => handleSelectChart(chart)}
                >
                  <div className="text-xs truncate">{chart.chart_name}</div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  };
  
  return (
    <DashboardContainer textSize="medium">
      <Header 
        toggleSidebar={toggleSidebar} 
        sidebarCollapsed={sidebarCollapsed}
        rightSidebarVisible={false}
        dashboardName="Market Place"
        toggleRightSidebar={() => {}}
        onHeaderSearch={() => {}}
        dashboardType={2}
      />
      
      <DashboardMain>
        <div className="flex h-full w-full">
          <SharedSidebar
            collapsed={sidebarCollapsed}
            onToggle={toggleSidebar}
            searchQuery={searchQuery}
            onSearchChange={handleSearch}
            searchPlaceholder="Search charts..."
            loading={loading}
            onCreateNew={() => navigate('/chartbuilder')}
            createNewLabel="Create New"
          >
            {renderSidebarContent()}
          </SharedSidebar>
          
          <div className="flex-1 overflow-y-auto">
            {viewMode === 'grid' ? (
              <ChartGrid
                charts={filteredCharts}
                loading={loading}
                onSelectChart={handleSelectChart}
              />
            ) : (
              <ChartDetail
                chart={selectedChart}
                chartData={chartData}
                loadingChartData={loadingChartData}
                onBack={handleBackToGrid}
                onEdit={handleOpenInChartBuilder}
                onRefresh={handleRefreshChartData}
              />
            )}
          </div>
        </div>
      </DashboardMain>
    </DashboardContainer>
  );
};

export default SavedChartsPage;
