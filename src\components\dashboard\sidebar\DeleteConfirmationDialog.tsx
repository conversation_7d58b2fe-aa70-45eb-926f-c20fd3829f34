
import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2, Check, X } from "lucide-react";

interface DeleteConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  fileName: string | null;
  onConfirm: () => Promise<void>;
  status: {
    status: 'idle' | 'loading' | 'success' | 'error';
    errorCode?: number;
  };
}

const DeleteConfirmationDialog: React.FC<DeleteConfirmationDialogProps> = ({ 
  isOpen, 
  onClose,
  fileName, 
  onConfirm,
  status 
}) => {
  const renderContent = () => {
    switch (status.status) {
      case 'loading':
        return (
          <div className="flex flex-col items-center justify-center py-6">
            <Loader2 className="h-12 w-12 text-blue-500 animate-spin mb-4" />
            <p className="text-gray-600">Deleting your file...</p>
          </div>
        );
      
      case 'success':
        return (
          <div className="flex flex-col items-center justify-center py-6">
            <Check className="h-12 w-12 text-green-500 mb-4 bg-green-100 p-2 rounded-full" />
            <p className="text-green-600">Your file has been deleted successfully!</p>
          </div>
        );
      
      case 'error':
        return (
          <div className="flex flex-col items-center justify-center py-6">
            <X className="h-12 w-12 text-red-500 mb-4" />
            <p className="text-red-600">Failed to delete file</p>
            <p className="text-gray-500 text-sm">Status Code: {status.errorCode}</p>
          </div>
        );
      
      default:
        return (
          <>
            <DialogHeader>
              <DialogTitle>
                Are you sure you want to delete this file:
                <span className='text-blue-600 pt-2 block'>{fileName}?</span>
              </DialogTitle>
              <DialogDescription className='text-red-500 text-md'>
                This action can not be undone.
              </DialogDescription>
            </DialogHeader>
            <div className="flex justify-end space-x-2">
              <Button variant="default" onClick={onClose}>
                Cancel
              </Button>
              <Button 
                variant="destructive" 
                onClick={onConfirm}
              >
                Delete
              </Button>
            </div>
          </>
        );
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        {renderContent()}
      </DialogContent>
    </Dialog>
  );
};

export default DeleteConfirmationDialog;
