
import React from 'react';
import { MessageCircle, Maximize, Minimize, Share, FolderInput, Edit, ThumbsUp, ThumbsDown, Copy, Trash } from 'lucide-react';
import { Message, TextSizeStyles } from './models';
import SqlResultView from './SqlResultView';
import MarkdownRenderer from './MarkdownRenderer';

interface MessageItemProps {
  message: Message;
  index: number;
  textSizeStyles: TextSizeStyles;
  toggleMinimizeMessage: (index: number) => void;
  handleDeleteMessage: (index: number) => void;
}

const MessageItem = ({ message, index, textSizeStyles, toggleMinimizeMessage, handleDeleteMessage }: MessageItemProps) => {
  const currentSize = textSizeStyles.medium.size; // Default to medium if specific size not found
  const currentSpacing = textSizeStyles.medium.spacing;
  
  // Function to remove @@ symbols from query content
  const formatContent = (content: string) => {
    return content.replace(/^@@\s*/, '');
  };

  // Improved function to check if content is markdown
  const isMarkdown = (content: string): boolean => {
    // Check for common markdown patterns
    const markdownPatterns = [
      /\*\*.*?\*\*/,  // Bold text
      /\*.*?\*/,      // Italic text
      /^#+\s+/m,      // Headers
      /^-\s+/m,       // Unordered lists
      /^[0-9]+\.\s+/m, // Ordered lists
      /\[.*?\]\(.*?\)/, // Links
      /^>\s+/m,       // Blockquotes
      /^```[\s\S]*?```/m, // Code blocks
      /\n\n/          // Paragraphs
    ];
    
    // Log for debugging
    console.log("Checking if content is markdown:", content);
    const isMarkdown = markdownPatterns.some(pattern => pattern.test(content));
    console.log("Is markdown:", isMarkdown);
    
    // For this specific case, if the content contains "**Highlights", force it to be treated as markdown
    if (content.includes("**Highlights")) {
      console.log("Content contains **Highlights, treating as markdown");
      return true;
    }
    
    return isMarkdown;
  };

  // Function to render the appropriate content based on message type and data
  const renderResponseContent = () => {
    // If we have meeting data (dashboard-1), display it
    if (message.type === 'response' && message.meetingData) {
      // Check if the content appears to be markdown
      if (isMarkdown(message.content)) {
        return (
          <div className="meeting-response">
            <MarkdownRenderer content={message.content} className="text-gray-800" />
            
            {/* If there are meeting IDs, display them */}
            {message.meetingData.meetingIds && message.meetingData.meetingIds.length > 0 && (
              <div className="meeting-ids mt-4 p-3 bg-gray-50 rounded-md">
                <h4 className="text-sm font-medium text-gray-700">Related Meetings:</h4>
                <ul className="list-disc pl-5 mt-2">
                  {message.meetingData.meetingIds.map((id: string, index: number) => (
                    <li key={index} className="text-sm text-blue-600 hover:underline">
                      <a href={`#meeting-${id}`}>{id}</a>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        );
      } else {
        // Regular text content
        return (
          <div className="meeting-response">
            <div className="meeting-answer whitespace-pre-wrap">
              {message.content}
            </div>
            
            {/* If there are meeting IDs, display them */}
            {message.meetingData.meetingIds && message.meetingData.meetingIds.length > 0 && (
              <div className="meeting-ids mt-4 p-3 bg-gray-50 rounded-md">
                <h4 className="text-sm font-medium text-gray-700">Related Meetings:</h4>
                <ul className="list-disc pl-5 mt-2">
                  {message.meetingData.meetingIds.map((id: string, index: number) => (
                    <li key={index} className="text-sm text-blue-600 hover:underline">
                      <a href={`#meeting-${id}`}>{id}</a>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        );
      }
    }
    
    // If we have query results (dashboard-3), display them
    if (message.queryResult) {
      return (
        <SqlResultView 
          message={message}
          index={index}
          toggleMinimizeMessage={toggleMinimizeMessage}
          handleDeleteMessage={handleDeleteMessage}
          messages={[message]} // Pass as array for compatibility
        />
      );
    }
    
    // For regular text content, check if it's markdown
    if (isMarkdown(message.content)) {
      return (
        <div className="markdown-content">
          <MarkdownRenderer content={message.content} />
        </div>
      );
    }
    
    // Otherwise, just display the content as plain text
    return <div className="whitespace-pre-wrap">{formatContent(message.content)}</div>;
  };

  if (message.type === 'query') {
    return (
      <div className="mb-4"> {/* Replace dynamic spacing with fixed margin */}
        <div className="flex items-center">
          <div className="flex items-center justify-center w-6 mr-2 text-black-400">
            <MessageCircle size={20} />
          </div>
          <div className="flex-1">
            <div className="flex items-center justify-between">
              <p className={`text-black-600 ${currentSize}`}>{formatContent(message.content)}</p>
              <div className="flex items-center space-x-2">
                <button 
                  onClick={() => toggleMinimizeMessage(index + 1)} 
                  className="p-1 text-gray-400 hover:text-gray-600"
                  aria-label={message.minimized ? "Maximize" : "Minimize"}
                >
                  {message.minimized ? <Maximize size={18} /> : <Minimize size={18} />}
                </button>
                <button 
                  onClick={() => handleDeleteMessage(index)} 
                  className="p-1 text-gray-400 hover:text-red-500"
                  aria-label="Delete"
                >
                  <Trash size={18} />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  } else {
    return (
      <div className={currentSpacing}>
        {!message.minimized ? (
          <div className="flex items-start mb-4">
            <div className="w-full">
              <p className={`font-medium ${currentSize} mb-2`}>Answer</p>
              <div className={currentSize}>
                {renderResponseContent()}
              </div>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-between px-2 py-1 bg-gray-50 rounded">
            <span className="text-sm text-gray-500">[Answer minimized]</span>
            <button 
              onClick={() => toggleMinimizeMessage(index)}
              className="p-1 text-gray-400 hover:text-gray-600"
              aria-label="Maximize"
            >
              <Maximize size={16} />
            </button>
          </div>
        )}
        
        {!message.minimized && (
          <div className="flex justify-between items-center mt-3">
            <div className="flex space-x-3">
              <button className="flex items-center text-gray-500 hover:text-gray-700">
                <Share size={16} className="mr-1" />
                <span className="text-sm">Share</span>
              </button>
              <button className="flex items-center text-gray-500 hover:text-gray-700">
                <FolderInput size={16} className="mr-1" />
                <span className="text-sm">Export</span>
              </button>
              <button className="flex items-center text-gray-500 hover:text-gray-700">
                <Edit size={16} className="mr-1" />
                <span className="text-sm">Rewrite</span>
              </button>
            </div>
            <div className="flex items-center space-x-2">
              <button className="p-1 text-gray-400 hover:text-gray-600">
                <ThumbsUp size={16} />
              </button>
              <button className="p-1 text-gray-400 hover:text-gray-600">
                <ThumbsDown size={16} />
              </button>
              <button className="p-1 text-gray-400 hover:text-gray-600">
                <Copy size={16} />
              </button>
            </div>
          </div>
        )}
      </div>
    );
  }
};

export default MessageItem;
