import { useCallback } from 'react';
import { DragEndEvent, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { SavedChart } from '@/types/chartTypes';
import { toast } from 'sonner';

interface DragAndDropHookProps {
  onCardDrop: (draggedChart: SavedChart, cardId: string, cardType: 'chart' | 'table') => Promise<void>;
  onZoneDrop: (draggedChart: SavedChart, zoneType: 'chart' | 'table') => Promise<void>;
}

export const useDragAndDrop = ({ onCardDrop, onZoneDrop }: DragAndDropHookProps) => {
  // Drag & Drop sensors
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  // Handle drag end for chart/table zones and cards
  const handleDragEnd = useCallback(async (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over) return;

    const draggedChart = active.data.current?.chart as SavedChart;
    const dropZone = over.id as string;
    const dropData = over.data.current;

    if (!draggedChart) return;

    try {
      // Check if it's a specific card drop zone
      if (dropData?.cardId) {
        const cardId = dropData.cardId;
        const cardType = dropData.type === 'chart-zone' ? 'chart' : 'table';

        await onCardDrop(draggedChart, cardId, cardType);
        toast.success(`Added "${draggedChart.chart_name}" to ${cardType} card`);
        return;
      }

      // Legacy zone handling (fallback)
      if (dropZone === 'chart-zone') {
        await onZoneDrop(draggedChart, 'chart');
        toast.success(`Added "${draggedChart.chart_name}" to Chart zone`);
      } else if (dropZone === 'table-zone') {
        await onZoneDrop(draggedChart, 'table');
        toast.success(`Added "${draggedChart.chart_name}" to Table zone`);
      }
    } catch (error) {
      console.error('Error handling drag and drop:', error);
      toast.error('Failed to add chart');
    }
  }, [onCardDrop, onZoneDrop]);

  return {
    sensors,
    handleDragEnd,
  };
};