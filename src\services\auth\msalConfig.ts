// MSAL configuration for Microsoft authentication
export const msalConfig = {
  auth: {
    clientId: import.meta.env.VITE_AZURE_CLIENT_ID || '',
    authority: `https://login.microsoftonline.com/${import.meta.env.VITE_AZURE_TENANT_ID || 'common'}`,
    redirectUri: window.location.origin,
    validRedirectUris: [
      window.location.origin,
      'https://10.100.0.17:8080',
    ]
  },
  cache: {
    cacheLocation: "sessionStorage",
    storeAuthStateInCookie: false,
  }
};

// Login request scopes - match exactly what's in Azure AD
export const loginRequest = {
  scopes: ["User.Read", "email", "profile", "openid"]
};






