import React from 'react';
import { FilterSection, FilterCondition } from '@/components/dashboard/chartboard/FilterSection';

interface DataGridFiltersProps {
  filterConditions: FilterCondition[];
  onFilterChange: (filters: FilterCondition[]) => void;
  onDragOver: (e: React.DragEvent) => void;
  visible: boolean;
}

const DataGridFilters: React.FC<DataGridFiltersProps> = ({
  filterConditions,
  onFilterChange,
  onDragOver,
  visible
}) => {
  if (!visible) return null;

  return (
    <FilterSection
      filterConditions={filterConditions}
      setFilterConditions={onFilterChange}
      handleDragOver={onDragOver}
    />
  );
};

export default DataGridFilters;