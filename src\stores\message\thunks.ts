import { createAsyncThunk } from '@reduxjs/toolkit';
import { queryService } from '@/services/api';
import { fetchMeetingData } from '@/services/api/dashboard1/meetingService';
import { processQuery } from '@/services/api/dadaAI/queryService';
import { toast } from 'sonner';
import { QueryResultData } from '@/components/dashboard/models';
import { RootState } from '@/stores/store';

// Define return types for better type checking
interface MeetingData {
  meetingIds: string[];
  questions?: {
    question: string;
    answer: string;
  }[];
}

type Dashboard1Response = {
  inputValue: string;
  meetingData: MeetingData;
  formattedMessage: string;
};

type Dashboard3Response = {
  inputValue: string;
  queryResult: QueryResultData;
};

// Union type for all possible responses
type SubmitQueryResponse = Dashboard1Response | Dashboard3Response;

// Flag to prevent duplicate success toasts
let successToastShown = false;

export const loadMessages = createAsyncThunk(
  'message/loadMessages',
  async (dashboardType: number) => {
    // For dashboard type 1, we don't need to fetch messages anymore
    // Just return an empty array to initialize the state
    return [];
  }
);

export const submitQuery = createAsyncThunk<
  SubmitQueryResponse | null,
  { inputValue: string; dashboardType: number },
  { rejectValue: string }
>(
  'message/submitQuery',
  async ({ inputValue, dashboardType }, { getState, rejectWithValue }) => {
    if (dashboardType === 3) {
      console.log(`Making API call for DADA AI query: ${inputValue}`);
      try {
        // Get the current state to access the selected connection ID and file session
        const state = getState() as RootState;

        // Check if we're in file mode
        const isFileMode = state.dada?.isFileMode;
        const fileSessionId = state.dada?.fileSessionId;

        if (isFileMode) {
          console.log(`Using file mode with session ID: ${fileSessionId}`);

          if (!fileSessionId) {
            toast.error('No files uploaded', {
              description: 'Please upload CSV or Excel files first',
              duration: 5000,
              style: { backgroundColor: '#FFDEE2', color: '#505050', border: '1px solid #ea384c' }
            });

            return rejectWithValue('No files uploaded. Please upload CSV or Excel files first.');
          }

          // Import the file query service
          const { queryFiles } = await import('@/services/api/dadaAI/fileUploadService');

          // Use file query API
          const fileQueryResult = await queryFiles(inputValue, fileSessionId);

          console.log('File query result:', fileQueryResult);

          toast.success('File Query Successful', {
            description: 'Data retrieved from uploaded files successfully',
            style: { backgroundColor: '#F2FCE2', color: '#505050', border: '1px solid #86c555' }
          });

          // Transform file query response to match expected format
          // This will need to be adjusted based on the actual API response structure
          return {
            inputValue: inputValue, // Add the missing inputValue property
            queryResult: {
              query: inputValue,
              tableData: {
                columns: fileQueryResult.data?.columns || [],
                rows: fileQueryResult.data?.rows || []
              }
            }
          };
        } else {
          // Database mode - existing logic
          const connectionId = state.dada?.selectedConnectionId;

          console.log(`Using database mode with connection ID: ${connectionId}`);

          if (!connectionId) {
            toast.error('No database connection selected', {
              description: 'Please select a database connection from the dropdown',
              duration: 5000,
              style: { backgroundColor: '#FFDEE2', color: '#505050', border: '1px solid #ea384c' }
            });

            return rejectWithValue('No database connection selected. Please select a database connection first.');
          }

          // Use our DADA AI query service
          const queryResult = await processQuery(connectionId, inputValue);

          console.log('Database query result:', queryResult);

          toast.success('Query Successful', {
            description: 'Data retrieved successfully',
            style: { backgroundColor: '#F2FCE2', color: '#505050', border: '1px solid #86c555' }
          });

          return { queryResult, inputValue } as Dashboard3Response;
        }
      } catch (error) {
        console.error('Error processing DADA AI query:', error);
        
        toast.error('Query Error', {
          description: error instanceof Error ? error.message : 'Failed to process query',
          style: { backgroundColor: '#FFDEE2', color: '#505050', border: '1px solid #ea384c' }
        });
        
        return rejectWithValue(error instanceof Error ? error.message : 'Failed to process query');
      }
    } else if (dashboardType === 1) {
      // Use real API for dashboard-1
      try {
        console.log(`Making API call for dashboard-1 query: ${inputValue}`);
        const meetingData = await fetchMeetingData(inputValue);
        
        toast.success('Query Successful', {
          description: 'Meeting data retrieved successfully',
          style: { backgroundColor: '#F2FCE2', color: '#505050', border: '1px solid #86c555' }
        });
        
        // Log the meeting data to verify it's in the correct format
        console.log('Meeting data received:', meetingData);
        
        // Format the meeting data for display
        const formattedMessage = meetingData.questions && meetingData.questions.length > 0 
          ? meetingData.questions[0].answer 
          : "No answer found for your query.";
        
        return {
          inputValue,
          meetingData,
          formattedMessage
        } as Dashboard1Response;
      } catch (error) {
        console.error('Error processing dashboard-1 query:', error);
        toast.error('Query Error', {
          description: error instanceof Error ? error.message : 'Failed to retrieve meeting data',
          style: { backgroundColor: '#FFDEE2', color: '#505050', border: '1px solid #ea384c' }
        });
        throw error;
      }
    } else if (dashboardType === 2) {
      // For dashboardType 2, we'll return null for now
      // This will be handled in the UI to show an appropriate message
      return null;
    } else {
      throw new Error(`Unsupported dashboard type: ${dashboardType}`);
    }
  }
);

export const regenerateQueryAction = createAsyncThunk(
  'message/regenerateQuery',
  async (originalQuery: string) => {
    console.log(`Regenerating query: ${originalQuery}`);
    try {
      const queryResult = await queryService.regenerateQuery(originalQuery);
      return { queryResult, originalQuery };
    } catch (error) {
      console.error('Error regenerating query:', error);
      throw error;
    }
  }
);
