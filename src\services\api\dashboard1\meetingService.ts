import { centralApiClient } from '@/services/api/centralApiClient';

// Define interfaces for the meeting data
export interface MeetingQuestion {
  question: string;
  answer: string;
}

export interface MeetingData {
  meetingIds: string[];
  questions?: MeetingQuestion[];
}

/**
 * Fetches meeting data from the dashboard-1 API
 * @param query The search query for meetings
 * @returns Promise with meeting data
 */
export const fetchMeetingData = async (query: string): Promise<MeetingData> => {
  try {
    console.log('Fetching meeting data with query:', query);
    
    // Use the direct URL instead of proxy path
    const dashboard1Url = import.meta.env.VITE_DASHBOARD1_API_URL || 'http://10.100.0.22:8000';
    const endpoint = `${dashboard1Url}/meetings/rag_meeting`;
    
    console.log('Making request to:', endpoint);
    
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        "meeting_search_query": query
      })
    });
    
    console.log('Response status:', response.status);
    
    if (!response.ok) {
      throw new Error(`Server responded with status ${response.status}`);
    }
    
    const data = await response.json();
    console.log('API response data:', data);
    
    // Handle string response - this is what we're getting based on your screenshot
    if (typeof data === 'string') {
      return {
        meetingIds: [],
        questions: [{
          question: query,
          answer: data
        }]
      };
    }
    
    // Handle array or object response
    const meetingIds = Array.isArray(data) ? data : 
                      (data && typeof data === 'object' && data.meetingIds) ? data.meetingIds : [];
    
    return {
      meetingIds,
      questions: data.questions || [{
        question: query,
        answer: JSON.stringify(data) // Fallback to show raw data
      }]
    };
  } catch (error) {
    console.error('Error fetching meeting data:', error);
    return { 
      meetingIds: [],
      questions: [{
        question: query,
        answer: "Failed to retrieve meeting data. Please try again later."
      }]
    };
  }
};

// Helper function to parse the response string
function parseResponseString(response: any): string[] {
  if (!response) return [];
  
  // If response is already an array, return it
  if (Array.isArray(response)) return response;
  
  // If response is a string, try to parse it
  if (typeof response === 'string') {
    try {
      const parsed = JSON.parse(response);
      return Array.isArray(parsed) ? parsed : [parsed.toString()];
    } catch {
      // If not valid JSON, split by commas or newlines
      return response.split(/[,\n]/).map(id => id.trim()).filter(Boolean);
    }
  }
  
  // If response is an object with meetingIds property
  if (response && typeof response === 'object' && response.meetingIds) {
    return response.meetingIds;
  }
  
  return [];
}

// Import the getAccessToken function if you need authentication
import { getAccessToken } from '@/services/auth/msalService';




