import React, { useState } from 'react';
import { DatabaseConnectionForm } from '@/components/admin/DatabaseConnectionForm';
import { TableAccessManager } from '@/components/admin/TableAccessManager';

const AdminMenuSimple: React.FC = () => {
  // Navigation state for switching between views
  const [activeView, setActiveView] = useState<'db-connection' | 'table-view-access'>('db-connection');

  // Handle successful connection creation
  const handleConnectionCreated = () => {
    console.log('✅ Database connection created successfully');
    // Optionally switch to table view or refresh connections
  };

  // Handle successful table application
  const handleTablesApplied = () => {
    console.log('✅ Tables applied successfully');
    // Optionally show success message or update UI
  };

  return (
    <div className="bg-white max-w-5xl mx-auto">
      {/* Navigation Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="flex space-x-8">
          <button
            onClick={() => setActiveView('db-connection')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeView === 'db-connection'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Database Connection
          </button>
          <button
            onClick={() => setActiveView('table-view-access')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeView === 'table-view-access'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Table/View Access
          </button>
        </nav>
      </div>

      {/* Content Area */}
      <div className="space-y-6">
        {activeView === 'db-connection' && (
          <DatabaseConnectionForm onConnectionCreated={handleConnectionCreated} />
        )}

        {activeView === 'table-view-access' && (
          <TableAccessManager onTablesApplied={handleTablesApplied} />
        )}
      </div>
    </div>
  );
};

export default AdminMenuSimple;
