import React from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';

interface AdminModule {
  id: string;
  title: string;
  path?: string;
  children?: AdminModule[];
  isCategory?: boolean;
}

const AdminDashboard: React.FC = () => {
  const navigate = useNavigate();

  const adminModules: AdminModule[] = [
    {
      id: 'data',
      title: 'DATA',
      isCategory: true,
      children: [
        { id: 'myql', title: 'MyQL' },
        { id: 'insights', title: 'Insights' },
        { id: 'patterns', title: 'Patterns' },
        { id: 'predict', title: 'Predict' },
        { id: 'codegen', title: 'CodeGen' },
        { id: 'datagen', title: 'DataGen' },
        { id: 'dataview-builder', title: 'Dataview Builder', path: '/Admin/dataview-builder' },
        { id: 'approvals', title: 'Approvals' },
        { id: 'scheduler', title: 'Scheduler' }
      ]
    },
    {
      id: 'setting',
      title: 'SETTING',
      isCategory: true,
      children: [
        { id: 'setup', title: 'Setup' },
        { id: 'db-connections', title: 'DB Connections', path: '/Admin/menu' },
        { id: 'metadata-scan', title: 'Metadata Scan', path: '/Admin/mlz-scanner' },
        { id: 'TableRelationshipMapper', title: 'TableRelationshipMapper', path: '/Admin/TableRelationshipMapper' },
        { id: 'new-relationship', title: 'New Relationship', path: '/Admin/TableRelationshipMapper?new=true' },
        { id: 'cascade-metadata-changes', title: 'Cascade Metadata Changes' },
        { id: 'workspace', title: 'Workspace', path: '/Admin/workspace?new=true' },
        { id: 'marketplace', title: 'Marketplace', path: '/Admin/marketplace?new=true' }
      ]
    },
    {
      id: 'access-management',
      title: 'Access Management',
      isCategory: true,
      children: [
        { id: 'users', title: 'Users', path: '/Admin/users' },
        { id: 'groups', title: 'Groups', path: '/Admin/groups' },
        { id: 'permission-set', title: 'Permission set', path: '/Admin/permission-set' },
        { id: 'approval-set', title: 'Approval set', path: '/Admin/approval-set?new=true' },
         { id: 'service-principal', title: 'Service Principal', path: '/Admin/service-principal' },
        { id: 'dataview-builder', title: 'Dataview Builder', path: '/Admin/dataview-builder' }


      ]
    }
  ];

  const handleModuleClick = (module: AdminModule) => {
    if (module.isCategory) return; // Don't navigate for categories

    if (module.path) {
      navigate(module.path);
    } else {
      // For modules that don't have pages yet, show a toast
      toast.info(`${module.title} module coming soon!`);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
<h1 className="text-3xl font-normal text-teal-700 mb-2">Admin Dashboard</h1>
          <p className="text-gray-600">Manage your system settings, data, and access controls</p>
        </div>

        {/* Admin Modules List - Side by Side Layout */}
        <div className="bg-white rounded-lg shadow-sm p-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {adminModules.map((category) => (
              <div key={category.id} className="space-y-4">
                {/* Category Title */}
                <div className="border-b border-gray-200 pb-2">
                  <h2 className="text-lg font-normal text-gray-900">{category.title}</h2>
                </div>

                {/* Category Items */}
                <div className="space-y-2">
                  {category.children?.map((module) => (
                    <div
                      key={module.id}
                      onClick={() => handleModuleClick(module)}
                      className={`text-gray-700 hover:text-blue-600 transition-colors duration-200 ${
                        module.path ? 'cursor-pointer hover:underline' : 'cursor-pointer'
                      }`}
                    >
                      {/* <span className="text-gray-400 mr-2">-</span> */}
                      {module.title}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
