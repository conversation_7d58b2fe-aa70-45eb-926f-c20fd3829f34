
import { DatasetState } from './types';

export const initialState: DatasetState = {
  showDatasetScreen: false,
  currentStep: 'datasource',
  stepNumber: 1,
  searchQuery: '',
  
  selectedConnections: [],
  selectedTables: [],
  selectedColumns: [],
  derivedColumns: [],
  filterConditions: [],
  
  datasetName: '',
  datasetDescription: '',
  destinationConnection: '',
  schemaName: '',
  
  availableConnections: [
    {
      name: 'SqlServer',
      type: 'folder',
      children: [
        { name: 'Athena_Prod', type: 'connection' },
        { name: 'Quickcap_Prod', type: 'connection' }
      ]
    },
    {
      name: 'EDW',
      type: 'folder',
      children: [
        { name: 'Databricks_Prod', type: 'connection' }
      ]
    },
    {
      name: 'Oracle',
      type: 'folder',
      children: [
        { name: 'Netsuite', type: 'connection' }
      ]
    },
    {
      name: 'API',
      type: 'folder',
      children: [
        { name: 'Skedulo', type: 'connection' },
        { name: 'App_1', type: 'connection' }
      ]
    }
  ]
};
