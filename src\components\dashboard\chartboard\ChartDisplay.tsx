import React from 'react';
import UniversalChart from '@/components/charts/UniversalChart';
import { NoDataDisplay } from '../dadaResultViews/chart/NoDataDisplay';
import { ChartDataItem } from '@/services/api/chart/chartTypes';

// ChartJS registration moved to UniversalChart

interface ColumnOption {
  id: string;
  name: string;
  tableName: string;
}

interface ChartDisplayProps {
  chartStyle: 'bar' | 'line' | 'pie' | 'doughnut';
  combinedChartData: ChartDataItem[];
  xAxisColumn: string;
  yAxisColumns: string[];
  availableColumns: ColumnOption[];
  chartXAxisKey: string;
  chartYAxisKey: string;
  handleSaveButtonClick: () => void;
  selectedDatabase?: string;
}

const ChartDisplay: React.FC<ChartDisplayProps> = ({
  chartStyle,
  combinedChartData,
  xAxisColumn,
  yAxisColumns,
  availableColumns,
  chartXAxisKey,
  chartYAxisKey,
  handleSaveButtonClick,
  selectedDatabase
}) => {
  // Add logging to check if selectedDatabase is passed correctly
  React.useEffect(() => {
    console.log("ChartDisplay received selectedDatabase:", selectedDatabase);
  }, [selectedDatabase]);

  // Function to get chart title
  const getChartTitle = () => {
    const xAxisName = availableColumns.find(c => c.id === xAxisColumn)?.name || xAxisColumn;
    const yAxisName = yAxisColumns.map(col => 
      availableColumns.find(c => c.id === col)?.name || col
    ).join(' & ');
    
    return `${yAxisName} by ${xAxisName}`;
  };

  if (!combinedChartData || combinedChartData.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow p-4 chart-display-container">
        <div className="h-[350px]">
          <NoDataDisplay 
            height="h-[300px]"
            textSize="text-base"
            message="No Chart Data"
            subMessage="No data available for the selected columns."
          />
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow p-4 chart-display-container">
      <div className="h-[350px]">
        <UniversalChart
          rawData={combinedChartData}
          xAxis={xAxisColumn}
          yAxis={yAxisColumns}
          chartType={chartStyle}
          size="default"
          context="chartboard"
          title={getChartTitle()}
          showTitle={true}
          showLegend={true}
          showAxes={true}
          onSave={handleSaveButtonClick}
          className="w-full h-full"
        />
      </div>
    </div>
  );
};

export default ChartDisplay;
