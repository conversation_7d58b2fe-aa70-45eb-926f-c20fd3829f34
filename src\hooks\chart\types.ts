
import { ChartDataItem } from '@/services/api/chart/chartTypes';
import { FilterCondition } from '@/components/dashboard/chartboard/FilterSection';

export interface ChartInitializationData {
  chartStyle: 'bar' | 'line' | 'pie' | 'doughnut';
  xAxisColumn: string;
  yAxisColumns: string[];
  xAxisTable: string;
  yAxisTables: string[];
  aggregationType: string | null;
  chartData?: ChartDataItem[];
  connectionId?: string;
  hasExistingData?: boolean;
  filters?: { [key: string]: any };
}

export interface ChartExecutionParams {
  xAxisColumn: string;
  yAxisColumns: string[];
  xAxisTable: string;
  yAxisTables: string[];
  aggregationType: string | null;
  connectionId: string;
  filterConditions?: FilterCondition[];
}

export interface ChartCacheKey {
  xAxisColumn: string;
  yAxisColumns: string[];
  xAxisTable: string;
  yAxisTables: string[];
  aggregationType: string | null;
  filters?: FilterCondition[]; // Add filters to the cache key
}
