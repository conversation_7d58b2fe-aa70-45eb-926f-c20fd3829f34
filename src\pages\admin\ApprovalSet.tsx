import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

import { Plus, Trash2 } from 'lucide-react';
import { toast } from 'sonner';
import <PERSON><PERSON>ield from '@/components/admin/FormField';
import <PERSON><PERSON>ield from '@/components/admin/SelectField';

interface ApprovalSet {
  id: string;
  name: string;
}

interface Approver {
  id: string;
  email: string;
  sequence: number;
  is_allowed_to_approve: boolean;
  is_primary_approver: boolean;
  privacy: boolean;
  start_date: string;
  end_date: string;
}

interface ApprovalType {
  id: string;
  name: string;
  key: string;
  enabled: boolean;
}

interface CreateApprovalSetRequest {
  user_id: string;
  approval_set_name: string;
  can_approve_insights: boolean;
  can_approve_marketplace: boolean;
  can_approve_pattern: boolean;
  can_approve_predict: boolean;
  approvers: {
    email: string;
    sequence: number;
    is_allowed_to_approve: boolean;
    is_primary_approver: boolean;
    privacy: boolean;
    start_date: string;
    end_date: string;
  }[];
}

const ApprovalSet: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [approvalSets, setApprovalSets] = useState<ApprovalSet[]>([]);
  const [selectedApprovalSet, setSelectedApprovalSet] = useState<ApprovalSet | null>(null);
  const [approvalSetName, setApprovalSetName] = useState('');
  const [approvers, setApprovers] = useState<Approver[]>([]);
  const [approvalTypes, setApprovalTypes] = useState<ApprovalType[]>([]);
  const [isNewApprovalSet, setIsNewApprovalSet] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Mock data and initialization
  useEffect(() => {
    const initializeData = async () => {
      setIsLoading(true);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      const mockApprovalSets: ApprovalSet[] = [
        { id: '1', name: 'ApprovalSet1' },
        { id: '2', name: 'ApprovalSet2' },
        { id: '3', name: 'ApprovalSet3' }
      ];

      const mockApprovers: Approver[] = [
        {
          id: '1',
          email: '<EMAIL>',
          sequence: 0,
          is_allowed_to_approve: true,
          is_primary_approver: true,
          privacy: true,
          start_date: '',
          end_date: ''
        },
        {
          id: '2',
          email: '<EMAIL>',
          sequence: 1,
          is_allowed_to_approve: true,
          is_primary_approver: true,
          privacy: true,
          start_date: '',
          end_date: ''
        }
      ];

      const mockApprovalTypes: ApprovalType[] = [
        { id: '1', name: 'Insight Approver', key: 'can_approve_insights', enabled: false },
        { id: '2', name: 'Marketplace Approver', key: 'can_approve_marketplace', enabled: false },
        { id: '3', name: 'Pattern Scan Approver', key: 'can_approve_pattern', enabled: false },
        { id: '4', name: 'Predict Approver', key: 'can_approve_predict', enabled: false }
      ];

      setApprovalSets(mockApprovalSets);
      setApprovers(mockApprovers);
      setApprovalTypes(mockApprovalTypes);
      setIsLoading(false);
    };

    initializeData();
  }, []);

  // Handle URL parameters for selecting approval sets
  useEffect(() => {
    const approvalSetId = searchParams.get('id');
    const isNew = searchParams.get('new');

    if (isNew === 'true') {
      handleNewApprovalSet();
    } else if (approvalSetId && approvalSets.length > 0) {
      const approvalSet = approvalSets.find(set => set.id === approvalSetId);
      if (approvalSet) {
        setSelectedApprovalSet(approvalSet);
        setApprovalSetName(approvalSet.name);
        setIsNewApprovalSet(false);
      }
    } else if (approvalSets.length > 0 && !selectedApprovalSet) {
      // Auto-select first approval set if none selected
      setSelectedApprovalSet(approvalSets[0]);
      setApprovalSetName(approvalSets[0].name);
      setIsNewApprovalSet(false);
    }
  }, [searchParams, approvalSets, selectedApprovalSet]);

  const handleNewApprovalSet = () => {
    const newSet: ApprovalSet = {
      id: Date.now().toString(),
      name: 'New Approval Set'
    };
    setSelectedApprovalSet(newSet);
    setApprovalSetName('');
    setApprovers([]);
    // Reset all approval types to unchecked for new approval set
    setApprovalTypes(prev => prev.map(type => ({ ...type, enabled: false })));
    setIsNewApprovalSet(true);
  };

  const handleAddApprover = () => {
    const newApprover: Approver = {
      id: Date.now().toString(),
      email: '',
      sequence: approvers.length,
      is_allowed_to_approve: true,
      is_primary_approver: true,
      privacy: true,
      start_date: '',
      end_date: ''
    };
    setApprovers([...approvers, newApprover]);
  };

  const handleRemoveApprover = (index: number) => {
    const updatedApprovers = approvers.filter((_, i) => i !== index);
    // Update sequences to maintain order
    const resequencedApprovers = updatedApprovers.map((approver, i) => ({
      ...approver,
      sequence: i
    }));
    setApprovers(resequencedApprovers);
  };

  const handleApproverChange = (index: number, field: keyof Approver, value: any) => {
    const updatedApprovers = [...approvers];
    updatedApprovers[index] = { ...updatedApprovers[index], [field]: value };
    setApprovers(updatedApprovers);
  };

  const handleApprovalTypeChange = (typeId: string, enabled: boolean) => {
    setApprovalTypes(prev => 
      prev.map(type => 
        type.id === typeId ? { ...type, enabled } : type
      )
    );
  };

  const createApprovalSet = async (data: CreateApprovalSetRequest) => {
    try {
      const response = await fetch('http://10.100.0.22:8001/account-management/create-approval-set', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error creating approval set:', error);
      throw error;
    }
  };

  const handleSave = async () => {
    if (!approvalSetName.trim()) {
      toast.error('Please enter an approval set name');
      return;
    }

    if (isNewApprovalSet) {
      try {
        // Prepare API request data
        const requestData: CreateApprovalSetRequest = {
          user_id: 'a6e3020d-984a-4394-ac73-da7ec5393314',
          approval_set_name: approvalSetName,
          can_approve_insights: approvalTypes.find(t => t.key === 'can_approve_insights')?.enabled || false,
          can_approve_marketplace: approvalTypes.find(t => t.key === 'can_approve_marketplace')?.enabled || false,
          can_approve_pattern: approvalTypes.find(t => t.key === 'can_approve_pattern')?.enabled || false,
          can_approve_predict: approvalTypes.find(t => t.key === 'can_approve_predict')?.enabled || false,
          approvers: approvers.map(approver => ({
            email: approver.email,
            sequence: approver.sequence,
            is_allowed_to_approve: approver.is_allowed_to_approve,
            is_primary_approver: true, // Always pass true as requested
            privacy: approver.privacy,
            start_date: approver.start_date ? new Date(approver.start_date).toISOString() : new Date().toISOString(),
            end_date: approver.end_date ? new Date(approver.end_date).toISOString() : new Date().toISOString(),
          }))
        };

        await createApprovalSet(requestData);

        // Add new approval set to local state
        const newSet: ApprovalSet = {
          id: Date.now().toString(),
          name: approvalSetName
        };
        setApprovalSets(prev => [...prev, newSet]);
        setSelectedApprovalSet(newSet);
        toast.success('Approval set created successfully');
        setIsNewApprovalSet(false);
      } catch (error) {
        toast.error('Failed to create approval set. Please try again.');
      }
    } else {
      // Update existing approval set (local only for now)
      setApprovalSets(prev =>
        prev.map(set => set.id === selectedApprovalSet?.id ? { ...set, name: approvalSetName } : set)
      );
      toast.success('Approval set updated successfully');
    }
  };

  const handleCancel = () => {
    if (isNewApprovalSet) {
      setSelectedApprovalSet(null);
    }
    setIsNewApprovalSet(false);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading approval sets...</div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* Header with Back Button */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center gap-4">
          <button
            onClick={() => navigate('/admin')}
            className="flex items-center gap-2 text-teal-600 hover:text-teal-700 transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Back to Admin
          </button>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200 bg-white">
        <nav className="flex space-x-8 px-6">
          <div className="py-4 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600">
            Approval Sets
          </div>
        </nav>
      </div>

      {/* Content Area */}
      <div className="flex-1 p-6 overflow-auto bg-gray-50">
        {/* Header with New Approval Set Button */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h2 className="text-lg font-normal text-gray-700">
              {selectedApprovalSet && !isNewApprovalSet
                ? `Edit Approval Set: ${selectedApprovalSet.name}`
                : isNewApprovalSet
                ? 'Create New Approval Set'
                : 'Approval Sets'
              }
            </h2>
            <p className="text-sm text-gray-500 mt-1">
              {selectedApprovalSet && !isNewApprovalSet
                ? 'Modify the approval set configuration below'
                : isNewApprovalSet
                ? 'Configure a new approval set'
                : 'Select an approval set from the sidebar to edit, or create a new one'
              }
            </p>
          </div>
          <Button
            onClick={handleNewApprovalSet}
            className="text-white hover:opacity-90 transition-opacity"
            style={{ backgroundColor: 'rgb(0, 130, 130)' }}
          >
            <Plus className="w-4 h-4 mr-2" />
            New Approval Set
          </Button>
        </div>

        {/* Form or Empty State */}
        {selectedApprovalSet ? (
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
            {/* Form Header */}
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
              <h3 className="text-base font-normal text-gray-700">Approval Set Configuration</h3>
            </div>

            <div className="p-6 space-y-6">
              {/* Approval Set Name */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-normal text-gray-600 mb-2">
                    Approval Set Name *
                  </label>
                  <Input
                    value={approvalSetName}
                    onChange={(e) => setApprovalSetName(e.target.value)}
                    placeholder="Enter approval set name"
                    className="w-full"
                  />
                </div>
              </div>

              {/* Approval Types */}
              <div className="space-y-4">
                <h4 className="text-sm font-normal text-gray-600">Approval Types</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {approvalTypes.map((type) => (
                    <div key={type.id} className="flex items-center space-x-3">
                      <div className="relative">
                        <input
                          type="checkbox"
                          id={type.id}
                          checked={type.enabled}
                          onChange={(e) => handleApprovalTypeChange(type.id, e.target.checked)}
                          className="sr-only"
                        />
                        <div
                          onClick={() => handleApprovalTypeChange(type.id, !type.enabled)}
                          className={`w-5 h-5 rounded border-2 cursor-pointer transition-all duration-200 flex items-center justify-center ${
                            type.enabled
                              ? 'bg-teal-600 border-teal-600'
                              : 'bg-white border-gray-300 hover:border-gray-400'
                          }`}
                        >
                          {type.enabled && (
                            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          )}
                        </div>
                      </div>
                      <label htmlFor={type.id} className="text-sm text-gray-700 cursor-pointer">
                        {type.name}
                      </label>
                    </div>
                  ))}
                </div>

              </div>

              {/* Approvers Section */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <h4 className="text-sm font-normal text-gray-600">Approvers</h4>
                    <Button
                      onClick={handleAddApprover}
                      size="sm"
                      variant="outline"
                      className="text-teal-600 border-teal-300 hover:bg-teal-50 hover:border-teal-400 transition-colors duration-200"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Add Approver
                    </Button>
                  </div>
                </div>

                {approvers.length > 0 && (
                  <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                    {/* Table Header */}
                    <div className="bg-gray-50 border-b border-gray-200">
                      <div className="grid grid-cols-12 gap-3 px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <div className="col-span-4">Approver Email</div>
                        <div className="col-span-1">Sequence</div>
                        <div className="col-span-1 text-center">Enabled</div>
                        <div className="col-span-1 text-center">Privacy</div>
                        <div className="col-span-2">Start Date</div>
                        <div className="col-span-2">End Date</div>
                        <div className="col-span-1 text-center">Actions</div>
                      </div>
                    </div>

                    {/* Table Body */}
                    <div className="divide-y divide-gray-200">
                      {approvers.map((approver, index) => (
                        <div key={approver.id} className="grid grid-cols-12 gap-3 px-6 py-4 hover:bg-gray-50 transition-colors duration-150">
                          {/* Email */}
                          <div className="col-span-4">
                            <Input
                              value={approver.email}
                              onChange={(e) => handleApproverChange(index, 'email', e.target.value)}
                              placeholder="<EMAIL>"
                              className="w-full border-gray-200 rounded-md focus:border-teal-500 focus:ring-1 focus:ring-teal-500 transition-all duration-200"
                            />
                          </div>

                          {/* Sequence */}
                          <div className="col-span-1">
                            <Input
                              type="number"
                              value={approver.sequence}
                              onChange={(e) => handleApproverChange(index, 'sequence', parseInt(e.target.value))}
                              className="w-full border-gray-200 rounded-md focus:border-teal-500 focus:ring-1 focus:ring-teal-500 transition-all duration-200"
                              min="0"
                            />
                          </div>

                          {/* Enabled Checkbox */}
                          <div className="col-span-1 flex justify-center items-center">
                            <div className="relative">
                              <input
                                type="checkbox"
                                checked={approver.is_allowed_to_approve}
                                onChange={(e) => handleApproverChange(index, 'is_allowed_to_approve', e.target.checked)}
                                className="sr-only"
                              />
                              <div
                                onClick={() => handleApproverChange(index, 'is_allowed_to_approve', !approver.is_allowed_to_approve)}
                                className={`w-5 h-5 rounded border-2 cursor-pointer transition-all duration-200 flex items-center justify-center ${
                                  approver.is_allowed_to_approve
                                    ? 'bg-teal-600 border-teal-600'
                                    : 'bg-white border-gray-300 hover:border-gray-400'
                                }`}
                              >
                                {approver.is_allowed_to_approve && (
                                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                  </svg>
                                )}
                              </div>
                            </div>
                          </div>

                          {/* Privacy Checkbox */}
                          <div className="col-span-1 flex justify-center items-center">
                            <div className="relative">
                              <input
                                type="checkbox"
                                checked={approver.privacy}
                                onChange={(e) => handleApproverChange(index, 'privacy', e.target.checked)}
                                className="sr-only"
                              />
                              <div
                                onClick={() => handleApproverChange(index, 'privacy', !approver.privacy)}
                                className={`w-5 h-5 rounded border-2 cursor-pointer transition-all duration-200 flex items-center justify-center ${
                                  approver.privacy
                                    ? 'bg-teal-600 border-teal-600'
                                    : 'bg-white border-gray-300 hover:border-gray-400'
                                }`}
                              >
                                {approver.privacy && (
                                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                  </svg>
                                )}
                              </div>
                            </div>
                          </div>

                          {/* Start Date */}
                          <div className="col-span-2">
                            <Input
                              type="date"
                              value={approver.start_date}
                              onChange={(e) => handleApproverChange(index, 'start_date', e.target.value)}
                              className="w-full border-gray-200 rounded-md focus:border-teal-500 focus:ring-1 focus:ring-teal-500 transition-all duration-200"
                            />
                          </div>

                          {/* End Date */}
                          <div className="col-span-2">
                            <Input
                              type="date"
                              value={approver.end_date}
                              onChange={(e) => handleApproverChange(index, 'end_date', e.target.value)}
                              className="w-full border-gray-200 rounded-md focus:border-teal-500 focus:ring-1 focus:ring-teal-500 transition-all duration-200"
                            />
                          </div>

                          {/* Actions */}
                          <div className="col-span-1 flex justify-center items-center">
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleRemoveApprover(index)}
                              className="text-red-500 hover:text-red-700 hover:bg-red-50 p-1 rounded transition-colors duration-200"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end gap-3 pt-6 border-t border-gray-200">
                <Button
                  variant="outline"
                  onClick={handleCancel}
                  className="px-6 py-2 border-gray-300 text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSave}
                    variant='greenmind'
>
                  {isNewApprovalSet ? 'Create' : 'Save'} Approval Set
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-64 text-gray-500">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                <Plus className="w-8 h-8 text-gray-400" />
              </div>
              <p className="text-lg font-normal text-gray-600 mb-2">No approval set selected</p>
              <p className="text-sm text-gray-500 mb-4">
                Choose an approval set from the sidebar to edit, or create a new one using the button above.
              </p>
            </div>
          </div>
        )}

        {/* Information Cards */}
        {selectedApprovalSet && (
          <div className="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 rounded-full flex items-center justify-center" style={{ backgroundColor: 'rgba(0, 130, 130, 0.1)' }}>
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" style={{ color: 'rgb(0, 130, 130)' }}>
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
                <div className="ml-3">
                  <h4 className="text-sm font-medium mb-2" style={{ color: 'rgb(0, 130, 130)' }}>Approval Workflows</h4>
                  <div className="text-sm text-gray-700 space-y-1">
                    <p>• Workspace Approval Flow (for Analysis approval)</p>
                    <p>• Market Place Approval (for chart and dashboard approval)</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
                <div className="ml-3">
                  <h4 className="text-sm font-medium text-green-900 mb-2">Features</h4>
                  <div className="text-sm text-green-800 space-y-1">
                    <p>• Multiple approvers with sequence control</p>
                    <p>• Granular access control and privacy settings</p>
                    <p>• Automated email notifications to marketplace members</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Approval Sequence Display */}
        {selectedApprovalSet && approvers.length > 0 && (
          <div className="mt-6 bg-gray-50 border border-gray-200 rounded-lg p-4">
            <div className="text-center">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Current Approval Sequence</h4>
              <div className="text-lg font-mono text-gray-900">
                {approvers.map(a => a.sequence).sort((a, b) => a - b).join(' → ')}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ApprovalSet;
