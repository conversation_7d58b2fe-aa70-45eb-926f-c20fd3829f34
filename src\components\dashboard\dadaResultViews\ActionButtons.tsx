import { Share, FolderInput, Rota<PERSON><PERSON>c<PERSON>, ThumbsUp, ThumbsDown, Co<PERSON> } from "lucide-react"
import { toast } from "sonner"
import { useAppDispatch } from "@/hooks/useRedux"
import { regenerateQueryAction } from "@/stores/messageSlice"
import { useState } from "react"
import { SuccessDialog } from "@/components/ui/success-dialog"
import { queryService } from "@/services/api"

interface ActionButtonsProps {
  size?: "large" | "default" | "small"
  queryResult?: any
  originalQuery?: string
  onQueryRegenerated?: (newResult: any) => void
}

export const ActionButtons = ({ 
  size = "default", 
  queryResult, 
  originalQuery,
  onQueryRegenerated 
}: ActionButtonsProps) => {
  const dispatch = useAppDispatch()
  const [showSuccessDialog, setShowSuccessDialog] = useState(false)
  const [downloadedFileName, setDownloadedFileName] = useState("")
  
  const iconSize = size === "default" ? 14 : 12
  const textSize = size === "default" ? "text-xs" : "text-xs"
  const padding = size === "default" ? "p-2" : "p-1.5"

  const handleExportCSV = async () => {
    if (!originalQuery) {
      toast.error("No data to export", {
        description: "Please run a query first",
        style: { backgroundColor: '#FFDEE2', color: '#505050', border: '1px solid #ea384c' }
      });
      return;
    }

    try {
      toast.info("Exporting data...", {
        style: { backgroundColor: '#FEF7CD', color: '#505050', border: '1px solid #f0c000' }
      });
      
      const fileName = await queryService.exportToCSV(originalQuery);
      setDownloadedFileName(fileName);
      setShowSuccessDialog(true);
      
    } catch (error) {
      console.error("Export error:", error);
      toast.error("Export failed", {
        description: "Could not export the data to CSV",
        style: { backgroundColor: '#FFDEE2', color: '#505050', border: '1px solid #ea384c' }
      });
    }
  };

  const handleCloseSuccessDialog = () => {
    setShowSuccessDialog(false);
  };

  const handleRegenerateQuery = async () => {
    if (!originalQuery) {
      toast.error("No query to regenerate", {
        description: "Please run a query first",
        style: { backgroundColor: '#FFDEE2', color: '#505050', border: '1px solid #ea384c' }
      });
      return;
    }

    try {
      toast.info("Regenerating query...", {
        style: { backgroundColor: '#FEF7CD', color: '#505050', border: '1px solid #f0c000' }
      });
      
      // Dispatch the regenerate action
      const resultAction = await dispatch(regenerateQueryAction(originalQuery)).unwrap();
      
      // If we have a callback, call it with the new result
      if (onQueryRegenerated) {
        onQueryRegenerated(resultAction.queryResult);
      }
      
      toast.success("Query regenerated", {
        description: "Query has been successfully regenerated",
        style: { backgroundColor: '#F2FCE2', color: '#505050', border: '1px solid #86c555' }
      });
    } catch (error) {
      console.error("Regenerate query error:", error);
      toast.error("Regenerate failed", {
        description: "Could not regenerate the query",
        style: { backgroundColor: '#FFDEE2', color: '#505050', border: '1px solid #ea384c' }
      });
    }
  };

  return (
    <>
      <div className={`flex justify-between items-center ${padding} bg-gray-50`}>
        <div className="flex space-x-2">
          <button className="flex items-center text-gray-500 hover:text-gray-700">
            <Share size={iconSize} className="mr-1" />
            <span className={textSize}>Share</span>
          </button>
          <button 
            className="flex items-center text-gray-500 hover:text-gray-700"
            onClick={handleExportCSV}
          >
            <FolderInput size={iconSize} className="mr-1" />
            <span className={textSize}>Export</span>
          </button>
          <button 
            className="flex items-center text-gray-500 hover:text-gray-700"
            onClick={handleRegenerateQuery}
          >
            <RotateCcw size={iconSize} className="mr-1" />
            <span className={textSize}>Regenerate Query</span>
          </button>
        </div>

        <div className="flex items-center space-x-2">
          <button className="text-gray-400 hover:text-gray-600">
            <ThumbsUp size={iconSize} />
          </button>
          <button className="text-gray-400 hover:text-gray-600">
            <ThumbsDown size={iconSize} />
          </button>
          <button className="text-gray-400 hover:text-gray-600">
            <Copy size={iconSize} />
          </button>
        </div>
      </div>

      <SuccessDialog
        open={showSuccessDialog}
        onOpenChange={setShowSuccessDialog}
        onClose={handleCloseSuccessDialog}
        title="Download Complete"
        message={`Your file ${downloadedFileName} has been successfully downloaded!`}
      />
    </>
  );
};
