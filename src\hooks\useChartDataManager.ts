import { useState, useEffect, useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import { useAppDispatch } from './useRedux';
import { useConnectionManager } from './useConnectionManager';
import { useChartStateManager } from './useChartStateManager';
import { useChartStatePersistence } from './useChartStatePersistence';
import { useChartInitialization } from './chart/useChartInitialization';
import { useChartExecution } from './chart/useChartExecution';
import { useChartCache } from './chart/useChartCache';
import { ChartDataItem } from '@/services/api/chart/chartTypes';
import { identifyChartKeys } from '@/utils/chart/dataProcessing';
import { clearEditingChartData } from '@/stores/chartSlice';
import { FilterCondition } from '@/components/dashboard/chartboard/FilterSection';
import { toast } from 'sonner';

export const useChartDataManager = (isEditMode: boolean, chartId?: string, viewType: 'chart' | 'kpi' | 'datagrid' = 'chart') => {
  const dispatch = useAppDispatch();
  const { getCurrentConnection, selectedDatabase } = useConnectionManager();
  const { chartState, updateChartState, resetChartState } = useChartStateManager();
  const { initializeChartData } = useChartInitialization();
  const { executeChart: executeChartApi, isLoading } = useChartExecution();
  const { shouldSkipExecution, updateExecutionParams, clearCache } = useChartCache();
  
  const [chartXAxisKey, setChartXAxisKey] = useState<string>('');
  const [chartYAxisKey, setChartYAxisKey] = useState<string>('');
  const [dataInitialized, setDataInitialized] = useState(false);
  const [filterConditions, setFilterConditions] = useState<FilterCondition[]>([]);
  const [showFilters, setShowFilters] = useState(false);

  // Get current connection ID using optimized connection manager with session persistence
  const connectionId = getCurrentConnection();

  // Initialize chart data on mount (only for chart view)
  useEffect(() => {
    const initialize = async () => {
      if (dataInitialized || viewType !== 'chart') return;
      
      const initData = await initializeChartData();
      
      if (initData) {
        updateChartState({
          chartStyle: initData.chartStyle,
          xAxisColumn: initData.xAxisColumn,
          yAxisColumns: initData.yAxisColumns,
          xAxisTable: initData.xAxisTable,
          yAxisTables: initData.yAxisTables,
          aggregationType: initData.aggregationType
        });
        
        // Process filters if they exist
        if (initData.filters && Object.keys(initData.filters).length > 0) {
          // Convert filters object to FilterCondition array
          const filterConditionsArray = Object.entries(initData.filters).map(([column, value]) => {
            if (typeof value === 'object') {
              // Handle complex filters with operators (e.g., {gt: 100})
              const operator = Object.keys(value)[0];
              const filterValue = value[operator];
              return {
                column,
                operator,
                value: Array.isArray(filterValue) ? filterValue.join(',') : String(filterValue)
              };
            } else {
              // Handle simple equality filters (e.g., {column: 'value'})
              return {
                column,
                operator: '=',
                value: String(value)
              };
            }
          });
          
          // Set filter conditions
          setFilterConditions(filterConditionsArray);
          // Enable filters UI
          setShowFilters(true);
        }
        
        if (initData.chartData && initData.hasExistingData) {
          updateChartState({
            combinedChartData: initData.chartData,
            showChart: true
          });
          
          const { xAxisKey, yAxisKey } = identifyChartKeys(
            initData.chartData, 
            initData.xAxisColumn, 
            initData.yAxisColumns
          );
          setChartXAxisKey(xAxisKey);
          setChartYAxisKey(yAxisKey);
        }
      }
      
      setDataInitialized(true);
    };

    initialize();
  }, [initializeChartData, updateChartState, dataInitialized, viewType]);

  // Enhanced chart execution with filter conditions support
  const executeChart = useCallback(async (conditions?: FilterCondition[]) => {
    // Skip execution for non-chart views
    if (viewType !== 'chart') {
      console.log('Skipping chart execution - not in chart view');
      return;
    }

    if (conditions) {
      setFilterConditions(conditions);
    }

    const { xAxisColumn, yAxisColumns, xAxisTable, yAxisTables, aggregationType, combinedChartData } = chartState;

    const activeConnectionId = getCurrentConnection();
    if (!activeConnectionId) {
      toast.error('No database connection available');
      return;
    }

    // Check if we should skip execution due to caching
    const cacheParams = {
      xAxisColumn,
      yAxisColumns,
      xAxisTable,
      yAxisTables,
      aggregationType
    };

    if (shouldSkipExecution(cacheParams, isEditMode, combinedChartData.length > 0)) {
      console.log('Chart data already exists with same parameters, skipping API call - edit mode optimization');
      updateChartState({ showChart: true });
      return;
    }

    const executionParams = {
      xAxisColumn,
      yAxisColumns,
      xAxisTable,
      yAxisTables,
      aggregationType,
      connectionId: activeConnectionId,
      filterConditions: conditions // Add filter conditions to execution params
    };

    const result = await executeChartApi(executionParams);
    
    if (result) {
      const { data, xAxisKey, yAxisKey } = result;
      
      setChartXAxisKey(xAxisKey);
      setChartYAxisKey(yAxisKey);
      updateExecutionParams(cacheParams);
      
      updateChartState({
        combinedChartData: data,
        showChart: true
      });
    }
  }, [chartState, getCurrentConnection, executeChartApi, shouldSkipExecution, updateExecutionParams, updateChartState, isEditMode, viewType]);

  // Enhanced reset function
  const enhancedResetChartState = useCallback(() => {
    resetChartState();
    setChartXAxisKey('');
    setChartYAxisKey('');
    clearCache();
    console.log('Chart state reset - ready for new execution');
  }, [resetChartState, clearCache]);

  return {
    ...chartState,
    chartXAxisKey,
    chartYAxisKey,
    isLoading,
    updateChartState,
    resetChartState: enhancedResetChartState,
    executeChart,
    selectedDatabase,
    connectionId,
    filterConditions,
    showFilters
  };
};
