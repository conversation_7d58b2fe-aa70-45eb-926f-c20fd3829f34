
import React, { useState } from 'react';
import { MessageSquare, Folder, Type, Clipboard, Database } from 'lucide-react';
import { <PERSON><PERSON>, DialogTrigger, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { SidebarNavProps } from './types';
import { useAppSelector } from "@/hooks/useRedux";

const SidebarNav: React.FC<SidebarNavProps> = ({ 
  textSize, 
  handleTextSizeChange, 
  onDatasetIconClick,
  isDatasetScreenOpen 
}) => {
  const [activeIcons, setActiveIcons] = useState<{ [key: string]: boolean }>({});
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const datasetState = useAppSelector(state => state.dataset);

  const handleIconClick = (iconName: string) => {
    if (iconName === 'type') {
      setActiveIcons(prev => ({
        ...prev,
        [iconName]: true
      }));
    } else {
      setActiveIcons(prev => ({
        ...prev,
        [iconName]: !prev[iconName]
      }));
    }

    if (iconName === 'dataset' && typeof onDatasetIconClick === "function") {
      onDatasetIconClick();
    }
  };

  const handleDialogClose = () => {
    setIsDialogOpen(false);
    setActiveIcons(prev => ({
      ...prev,
      type: false
    }));
  };

  const getIconClass = (iconName: string) => `
    w-10 h-10 rounded-md flex items-center justify-center hover:bg-gray-100
    ${(activeIcons[iconName] || (iconName === 'dataset' && isDatasetScreenOpen))
      ? 'text-blue-500' 
      : 'text-gray-500'}
  `;

  return (
    <div className="w-16 bg-gray-200 border-r border-gray-200 flex flex-col items-center py-4 justify-between h-full">
      <div className="flex flex-col items-center">
        
        
        <nav className="flex flex-col space-y-6">
          {/* <button 
            className={getIconClass('message')}
            onClick={() => handleIconClick('message')}
          >
            <MessageSquare size={20} />
          </button> */}
          
          {/* <button 
            className={getIconClass('folder')}
            onClick={() => handleIconClick('folder')}
          >
            <Folder size={20} />
          </button> */}
          
          <Dialog open={isDialogOpen} onOpenChange={(open) => {
            setIsDialogOpen(open);
            if (!open) handleDialogClose();
          }}>
            <DialogTrigger asChild>
              <button 
                className={getIconClass('type')}
                onClick={() => handleIconClick('type')}
              >
                <Type size={20} />
              </button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Text size and spacing</DialogTitle>
              </DialogHeader>
              <div className="py-4">
                <p className="text-sm text-gray-600 mb-4">This will change the font size and number of messages.</p>
                <RadioGroup 
                  value={textSize} 
                  onValueChange={(value) => handleTextSizeChange(value as 'small' | 'medium' | 'large')}
                  className="space-y-4"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="small" id="small" />
                    <Label htmlFor="small">Small</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="medium" id="medium" />
                    <Label htmlFor="medium">Medium</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="large" id="large" />
                    <Label htmlFor="large">Large</Label>
                  </div>
                </RadioGroup>
              </div>
            </DialogContent>
          </Dialog>
          
          {/* <button 
            className={getIconClass('clipboard')}
            onClick={() => handleIconClick('clipboard')}
          >
            <Clipboard size={20} />
          </button> */}
          
          <button 
            className={getIconClass('dataset')}
            onClick={() => handleIconClick('dataset')}
          >
            <Database size={20} />
          </button>
        </nav>
      </div>
    </div>
  );
};

export default SidebarNav;
