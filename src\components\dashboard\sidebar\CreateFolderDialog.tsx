
// import React, { useState } from 'react';
// import {
//   Dialog,
//   DialogContent,
//   DialogHeader,
//   DialogTitle,
//   DialogFooter,
// } from "@/components/ui/dialog";
// import { Input } from "@/components/ui/input";
// import { Button } from "@/components/ui/button";
// import { FolderType } from '@/services/api/audioTranscript/types';

// interface CreateFolderDialogProps {
//   isOpen: boolean;
//   onClose: () => void;
//   onSave: (folderName: string, parentFolder?: string) => void;
//   folderType: FolderType;
//   parentFolder?: string;
// }

// const CreateFolderDialog: React.FC<CreateFolderDialogProps> = ({
//   isOpen,
//   onClose,
//   onSave,
//   folderType,
//   parentFolder
// }) => {
//   const [folderName, setFolderName] = useState('');

//   const handleSave = () => {
//     if (folderName.trim()) {
//       onSave(folderName.trim(), parentFolder);
//       setFolderName('');
//       onClose();
//     }
//   };

//   return (
//     <Dialog open={isOpen} onOpenChange={onClose}>
//       <DialogContent className="sm:max-w-[425px]">
//         <DialogHeader>
//           <DialogTitle>
//             {parentFolder 
//               ? `Create New Subfolder in ${parentFolder}`
//               : `Create New Folder in ${folderType === 'personal' ? 'Personal' : folderType === 'project' ? 'Project' : 'Tracker'} Files`
//             }
//           </DialogTitle>
//         </DialogHeader>
//         <div className="py-4">
//           <Input
//             placeholder="Enter folder name"
//             value={folderName}
//             onChange={(e) => setFolderName(e.target.value)}
//             className="w-full"
//           />
//         </div>
//         <DialogFooter>
//           <Button variant="outline" onClick={onClose}>Cancel</Button>
//           <Button onClick={handleSave}>Save</Button>
//         </DialogFooter>
//       </DialogContent>
//     </Dialog>
//   );
// };

// export default CreateFolderDialog;
