
import React from 'react';

interface SelectedTablesListProps {
  selectedTables: string[];
  localSelectedTables: string[];
  onToggleTable: (tableId: string) => void;
}

const SelectedTablesList: React.FC<SelectedTablesListProps> = ({
  selectedTables,
  localSelectedTables,
  onToggleTable
}) => {
  return (
    <div className="border border-gray-300 rounded-md h-72 overflow-y-auto p-2">
      {selectedTables.length === 0 ? (
        <div className="text-gray-500 text-sm italic p-2">No tables selected</div>
      ) : (
        <ul className='text-sm'>
          {selectedTables.map((table) => {
            const [conn, tableName] = table.split('_');
            return (
              <li 
                key={table}
                className={`py-1 cursor-pointer hover:bg-blue-50 pl-1 rounded ${
                  localSelectedTables.includes(table) ? 'bg-blue-100' : ''
                }`}
                onClick={() => onToggleTable(table)}
              >
                {tableName} ({conn})
              </li>
            );
          })}
        </ul>
      )}
    </div>
  );
};

export default SelectedTablesList;
