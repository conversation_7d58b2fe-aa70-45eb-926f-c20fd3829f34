
import React, { useState } from 'react';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useAppDispatch, useAppSelector } from '@/hooks/useRedux';
import { 
  addDerivedColumn,
  updateDerivedColumn,
  removeDerivedColumn
} from '@/stores/datasetSlice';
import StepHeader, { NavigationButtons } from './StepHeader';

interface DerivedColumnsStepProps {
  onClose: () => void;
}

const DerivedColumnsStep: React.FC<DerivedColumnsStepProps> = ({ onClose }) => {
  const dispatch = useAppDispatch();
  const { derivedColumns } = useAppSelector(state => state.dataset);
  
  const [newDerivedColumn, setNewDerivedColumn] = useState({
    name: '',
    formula: '',
    naturalLanguage: '',
    description: '',
    dataType: 'String'
  });

  const handleSaveDerivedColumn = () => {
    if (newDerivedColumn.name && newDerivedColumn.formula) {
      dispatch(addDerivedColumn(newDerivedColumn));
      setNewDerivedColumn({
        name: '',
        formula: '',
        naturalLanguage: '',
        description: '',
        dataType: 'String'
      });
    }
  };

  return (
    <div className="p-4">
      <StepHeader 
        title="Derived Columns" 
        onClose={onClose}
      />
      
      <div className="border border-gray-200 p-4 rounded-lg mb-4">
        <h3 className="font-medium text-md mb-4">Add New Derived Column</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm mb-1">Derived Column Name:</label>
            <input
              type="text"
              className="w-full p-2 border border-gray-300 rounded-md"
              value={newDerivedColumn.name}
              onChange={(e) => setNewDerivedColumn({...newDerivedColumn, name: e.target.value})}
            />
          </div>
          <div>
            <label className="block text-sm mb-1">Formula:</label>
            <input
              type="text"
              className="w-full p-2 border border-gray-300 rounded-md"
              value={newDerivedColumn.formula}
              onChange={(e) => setNewDerivedColumn({...newDerivedColumn, formula: e.target.value})}
            />
          </div>
          <div>
            <label className="block text-sm mb-1">Formula in Natural Language:</label>
            <input
              type="text"
              className="w-full p-2 border border-gray-300 rounded-md"
              value={newDerivedColumn.naturalLanguage}
              onChange={(e) => setNewDerivedColumn({...newDerivedColumn, naturalLanguage: e.target.value})}
            />
          </div>
          <div>
            <label className="block text-sm mb-1">Column Description:</label>
            <textarea
              className="w-full p-2 border border-gray-300 rounded-md"
              rows={2}
              value={newDerivedColumn.description}
              onChange={(e) => setNewDerivedColumn({...newDerivedColumn, description: e.target.value})}
            />
          </div>
          <div>
            <label className="block text-sm mb-1">Data Type:</label>
            <select
              className="w-full p-2 border border-gray-300 rounded-md"
              value={newDerivedColumn.dataType}
              onChange={(e) => setNewDerivedColumn({...newDerivedColumn, dataType: e.target.value})}
            >
              <option value="String">String</option>
              <option value="Int">Integer</option>
              <option value="Decimal">Decimal</option>
              <option value="Date">Date</option>
              <option value="Boolean">Boolean</option>
            </select>
          </div>
        </div>
        <div className="flex justify-end gap-2">
          <Button
            variant="outline"
            onClick={() => setNewDerivedColumn({
              name: '',
              formula: '',
              naturalLanguage: '',
              description: '',
              dataType: 'String'
            })}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSaveDerivedColumn}
            disabled={!newDerivedColumn.name || !newDerivedColumn.formula}
          >
            Save
          </Button>
        </div>
      </div>

      <Table className="border">
        <TableHeader className="bg-gray-50">
          <TableRow>
            <TableHead className="w-1/5">Derived Column Name</TableHead>
            <TableHead className="w-1/5">Formula</TableHead>
            <TableHead className="w-1/5">Formula in Natural Language</TableHead>
            <TableHead className="w-1/5">Column Description</TableHead>
            <TableHead className="w-1/5">Data Type</TableHead>
            <TableHead className="w-16">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {derivedColumns.length === 0 ? (
            <TableRow>
              <TableCell colSpan={6} className="text-center py-4 text-gray-500 italic">
                No derived columns defined
              </TableCell>
            </TableRow>
          ) : (
            derivedColumns.map((col, index) => (
              <TableRow key={index}>
                <TableCell>{col.name}</TableCell>
                <TableCell>{col.formula}</TableCell>
                <TableCell>{col.naturalLanguage}</TableCell>
                <TableCell>{col.description}</TableCell>
                <TableCell>{col.dataType || 'String'}</TableCell>
                <TableCell>
                  <button
                    className="text-red-500 hover:text-red-700"
                    onClick={() => dispatch(removeDerivedColumn(index))}
                  >
                    <X size={18} />
                  </button>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
      
      {/* Navigation buttons at the bottom */}
      <div className="mt-6">
        <NavigationButtons />
      </div>
    </div>
  );
};

export default DerivedColumnsStep;
