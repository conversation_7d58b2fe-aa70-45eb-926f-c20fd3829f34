import { centralApiClient } from '@/services/api/centralApiClient';

/**
 * Force refresh all file data by clearing caches and fetching fresh data
 */
export const forceRefreshFiles = async (): Promise<boolean> => {
  console.log('Force refreshing files');
  
  try {
    // Clear all caches
    localStorage.removeItem('fileCache');
    sessionStorage.removeItem('sidebarLastFetchTimestamp');
    
    // Remove the timestamp to bypass throttling
    sessionStorage.removeItem('lastFetchAllTimestamp');
    
    // Clear any ongoing fetches to prevent duplicate requests
    if (typeof window.clearOngoingFetches === 'function') {
      window.clearOngoingFetches();
    }
    
    // Dispatch event to notify components to refresh
    window.dispatchEvent(new Event('file-updated'));
    
    return true;
  } catch (error) {
    console.error('Error refreshing files:', error);
    return false;
  }
};

/**
 * Setup global connection status listeners
 */
export const setupConnectionListeners = (): void => {
  // Listen for backend disconnection events
  window.addEventListener('backend-disconnected', () => {
    console.log('Backend disconnected event received');
    
    // Dispatch global event for components to react
    document.body.classList.add('backend-offline');
    
    // Show toast notification
    if (window.toast) {
      window.toast.error('Lost connection to server', {
        description: 'Trying to reconnect...',
        duration: 5000,
      });
    }
  });
  
  // Listen for backend reconnection events
  window.addEventListener('backend-connected', () => {
    console.log('Backend connected event received');
    
    // Remove offline class
    document.body.classList.remove('backend-offline');
    
    // Show success toast
    if (window.toast) {
      window.toast.success('Connection restored', {
        description: 'Refreshing data...',
        duration: 3000,
      });
    }
    
    // Force refresh files
    forceRefreshFiles();
  });
  
  // Listen for online/offline events
  window.addEventListener('online', () => {
    console.log('Browser online event received');
    
    // Check if backend is also available
    setTimeout(() => {
      centralApiClient.healthCheck('dada')  // Changed from 'audio' to 'dada'
        .then(isHealthy => {
          if (isHealthy) {
            window.dispatchEvent(new Event('backend-connected'));
          }
        })
        .catch(() => {
          console.log('Backend still unavailable despite browser being online');
        });
    }, 2000);
  });
  
  window.addEventListener('offline', () => {
    console.log('Browser offline event received');
    window.dispatchEvent(new Event('backend-disconnected'));
  });
};

// Call this function early in your app initialization



