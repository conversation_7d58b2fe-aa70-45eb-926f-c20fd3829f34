
import React from 'react';
import { Check } from 'lucide-react';
import { useAppSelector } from '@/hooks/useRedux';
import { Progress } from '@/components/ui/progress';

const capitalize = (text: string): string => {
  return text.charAt(0).toUpperCase() + text.slice(1);
};

const DatasetStepIndicator: React.FC = () => {
  const { stepNumber } = useAppSelector(state => state.dataset);
  
  // Map steps to more user-friendly labels
  const steps = [
    { step: 'datasource', label: 'Data Source' },
    { step: 'tables', label: 'Tables' },
    { step: 'columns', label: 'Columns' },
    { step: 'derived-columns', label: 'Derived Columns' },
    { step: 'filter', label: 'Filter' },
    { step: 'define', label: 'Define' },
    { step: 'generate', label: 'Generate' },
    { step: 'save', label: 'Save' }
  ];
  
  // Calculate progress percentage
  const progressPercent = ((stepNumber - 1) / (steps.length - 1)) * 100;

  return (
    <div className="mb-6">
      {/* Progress bar that shows completion percentage */}
      <Progress value={progressPercent} className="h-1 mb-6" />
      
      <div className="flex justify-between items-center relative">
        {steps.map((step, index) => {
          const stepNum = index + 1;
          const isCompleted = stepNumber > stepNum;
          const isActive = stepNumber === stepNum;
          
          // Determine if we should render a connector line after this step
          const showConnector = index < steps.length - 1;
          
          // Instead of using React.Fragment directly, let's use a div as a wrapper
          return (
            <div key={step.step} className="flex items-center flex-shrink-0 flex-grow">
              {/* Step circle with number or check icon */}
              <div className="flex flex-col items-center z-10">
                <div 
                  className={`w-8 h-8 rounded-full flex items-center justify-center mb-2
                    ${isCompleted 
                      ? 'bg-green-500 text-white' 
                      : isActive
                        ? 'bg-blue-500 text-white border-2 border-blue-600'
                        : 'bg-white text-gray-400 border-2 border-gray-200'}
                  `}
                >
                  {isCompleted ? (
                    <Check size={16} className="text-white" />
                  ) : (
                    <span className={`text-sm font-medium ${isActive ? 'text-white' : 'text-gray-500'}`}>
                      {stepNum}
                    </span>
                  )}
                </div>
                
                {/* Step label */}
                <span className={`text-xs ${isActive || isCompleted ? 'text-gray-700 font-medium' : 'text-gray-400'}`}>
                  {step.label}
                </span>
              </div>
              
              {/* Connector line between steps */}
              {showConnector && (
                <div className={`flex-1 h-1 z-0 mx-1 mb-[1.25rem] ${
                  isCompleted && stepNumber > stepNum + 1 
                    ? 'bg-green-500' 
                    : 'bg-gray-200'
                }`}></div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default DatasetStepIndicator;
