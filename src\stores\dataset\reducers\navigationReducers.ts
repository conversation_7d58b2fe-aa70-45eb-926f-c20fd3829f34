
import { PayloadAction } from '@reduxjs/toolkit';
import { DatasetState, DatasetStep, stepToNumber } from '../types';
import { initialState } from '../initialState';

export const navigationReducers = {
  setShowDatasetScreen: (state: DatasetState, action: PayloadAction<boolean>) => {
    state.showDatasetScreen = action.payload;
  },
  
  setCurrentStep: (state: DatasetState, action: PayloadAction<DatasetStep>) => {
    state.currentStep = action.payload;
    state.stepNumber = stepToNumber[action.payload];
  },
  
  setStepNumber: (state: DatasetState, action: PayloadAction<number>) => {
    const stepNumber = action.payload;
    if (stepNumber >= 1 && stepNumber <= 8) {
      const steps = Object.entries(stepToNumber);
      const step = steps.find(([_, num]) => num === stepNumber);
      if (step) {
        state.currentStep = step[0] as DatasetStep;
        state.stepNumber = stepNumber;
      }
    }
  },
  
  setSearchQuery: (state: DatasetState, action: PayloadAction<string>) => {
    state.searchQuery = action.payload;
  },
  
  resetDatasetState: () => {
    return initialState;
  },
  
  nextStep: (state: DatasetState) => {
    const currentIndex = stepToNumber[state.currentStep];
    if (currentIndex < 8) {
      const steps = Object.entries(stepToNumber);
      const nextStep = steps.find(([_, num]) => num === currentIndex + 1);
      if (nextStep) {
        state.currentStep = nextStep[0] as DatasetStep;
        state.stepNumber = currentIndex + 1;
      }
    }
  },
  
  previousStep: (state: DatasetState) => {
    const currentIndex = stepToNumber[state.currentStep];
    if (currentIndex > 1) {
      const steps = Object.entries(stepToNumber);
      const prevStep = steps.find(([_, num]) => num === currentIndex - 1);
      if (prevStep) {
        state.currentStep = prevStep[0] as DatasetStep;
        state.stepNumber = currentIndex - 1;
      }
    }
  },
};
