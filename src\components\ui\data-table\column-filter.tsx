
import * as React from "react"
import { Column } from "@tanstack/react-table"
import { Filter, FilterX } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"

interface ColumnFilterProps<TData, TValue> extends React.HTMLAttributes<HTMLDivElement> {
  column: Column<TData, TValue>
  title?: string
  className?: string
  onFilterShow?: () => void
  onFilterClear?: () => void
}

export function ColumnFilter<TData, TValue>({
  column,
  title,
  className,
  onFilterShow,
  onFilterClear,
}: ColumnFilterProps<TData, TValue>) {
  const [value, setValue] = React.useState<string>(
    (column.getFilterValue() as string) || ""
  )

  React.useEffect(() => {
    setValue((column.getFilterValue() as string) || "")
  }, [column.getFilterValue()])

  const handleFilterChange = (newValue: string) => {
    setValue(newValue)
    column.setFilterValue(newValue || undefined)
  }

  const clearFilter = () => {
    setValue("")
    column.setFilterValue(undefined)
    if (onFilterClear) {
      onFilterClear()
    }
  }

  const hasFilter = Boolean(column.getFilterValue())

  // If we have onFilterClear prop, we're in "input mode"
  if (onFilterClear) {
    return (
      <div className={cn("flex items-center justify-between p-1 w-full", className)}>
        <input
          type="text"
          value={value}
          onChange={(e) => handleFilterChange(e.target.value)}
          placeholder={`Filter ${title || column.id}...`}
          className="w-full h-7 px-2 text-sm border rounded text-black focus:outline-none"
          autoFocus
        />
        <Button
          variant="ghost"
          size="sm"
          onClick={clearFilter}
          className="h-6 w-6 p-0 ml-1 text-white hover:bg-teal-700 hover:text-white"
          title="Clear filter"
        >
          <FilterX className="h-3 w-3" />
        </Button>
      </div>
    );
  }

  // Otherwise we're in "button mode"
  return (
    <div className={cn("flex items-center", className)}>
      {hasFilter ? (
        <Button
          variant="ghost"
          size="sm"
          onClick={clearFilter}
          className="h-6 w-6 p-0 text-white hover:bg-teal-700 hover:text-white"
          title="Clear filter"
        >
          <FilterX className="h-3 w-3" />
        </Button>
      ) : (
        <Button
          variant="ghost"
          size="sm"
          onClick={onFilterShow}
          className="h-6 w-6 p-0 text-white hover:bg-teal-700 hover:text-white"
          title={`Filter ${title || column.id}`}
        >
          <Filter className="h-3 w-3" />
        </Button>
      )}
    </div>
  )
}
