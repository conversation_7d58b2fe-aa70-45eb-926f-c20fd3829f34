
import React, { useRef } from 'react';
import { cn } from '@/lib/utils';
import Container from '@/components/ui/container';
import { Button } from '@/components/ui/button';
import { useInView } from '@/lib/animations';
import { Link } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';

const Hero = () => {
  const heroRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(heroRef);
  const { isAuthenticated, hasAccess } = useAuth();

  return (
    <section 
      ref={heroRef}
      className="relative min-h-screen flex items-center pt-20 overflow-hidden"
      id="hero"
    >
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-b from-background to-background/40 pointer-events-none" />
      <Container className="relative z-10"> 
        <div className="max-w-3xl mx-auto text-center mb-16">
          <h1 
            className={cn(
              "text-4xl md:text-5xl font-bold mb-6 transition-all duration-700 delay-100 text-gradient", 
              isInView ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
            )}
          >
            Welcome to Mind-Labz
          </h1> 
          
          {/* Dashboard Navigation Buttons */}
          {isAuthenticated && (
            <div 
              className={cn(
                "flex flex-wrap justify-center gap-4 mt-8 transition-all duration-700 delay-300",
                isInView ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
              )}
            >
              {hasAccess('chatbot') && (
                <Link to="/chatbot">
                  <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                    Chatbot AI
                  </Button>
                </Link>
              )}
              
              {hasAccess('chartbuilder') && (
                <Link to="/chartbuilder">
                  <Button size="lg" className="bg-green-600 hover:bg-green-700">
                    Chart Builder
                  </Button>
                </Link>
              )}
              
              {hasAccess('chartbuilder') && (
                <Link to="/dashboard-builder">
                  <Button size="lg" className="bg-purple-600 hover:bg-purple-700">
                    Dashboard Builder
                  </Button>
                </Link>
              )}
              
              {hasAccess('chartbuilder') && (
                <Link to="/saved-charts">
                  <Button size="lg" variant="greenmind" className="border-2">
                    Market Place
                  </Button>
                </Link>
              )}
              
              {hasAccess('dada') && (
                <Link to="/dada">
                  <Button size="lg" className="bg-amber-600 hover:bg-amber-700">
                    DADA AI
                  </Button>
                </Link>
              )}
            </div>
          )}
          
          {/* Sign in/Sign up buttons for non-authenticated users */}
          {/* {!isAuthenticated && (
            <div 
              className={cn(
                "flex justify-center gap-4 mt-8 transition-all duration-700 delay-300",
                isInView ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
              )}
            >
              <Link to="/signin">
                <Button size="lg" variant="outline" className="border-2">
                  Sign in
                </Button>
              </Link>
              <Link to="/signup">
                <Button size="lg">
                  Sign up
                </Button>
              </Link>
            </div>
          )} */}
        </div>
      </Container>
      
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -right-24 -top-24 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl" />
        <div className="absolute -left-24 top-1/3 w-72 h-72 bg-purple-500/10 rounded-full blur-3xl animate-float" />
        <div className="absolute right-1/4 bottom-24 w-64 h-64 bg-amber-500/10 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }} />
      </div>

    </section>
  );
};

export default Hero;

