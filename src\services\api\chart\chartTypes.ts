/**
 * Interface for database connection parameters
 */
export interface DatabaseConnectionParams {
  db_type: string;
  host: string;
  port: string;
  database: string;
  username: string;
  password: string;
  table_name: string;
}

/**
 * Interface for database connection response
 */
export interface DatabaseConnectionResponse {
  status: string;
  connection_id: string;
}

/**
 * Interface for database disconnect response
 */
export interface DatabaseDisconnectResponse {
  status: string;
  message?: string;
}

/**
 * Column information interface
 */
export interface ColumnInfo {
  name: string;
  type?: string;
  nullable: boolean;
}

/**
 * Interface for chart data items
 */
export interface ChartDataItem {
  [key: string]: string | number;
}

/**
 * Interface for tabular data items
 */
export interface TabularDataItem {
  [key: string]: string | number;
}

/**
 * Interface for tabular data API response
 */
export interface TabularDataResponse {
  chart_response: {
    data: TabularDataItem[];
    status: string;
    metadata: {
      status: string;
      tables: string[];
    };
  };
}

/**
 * Interface for table with columns
 */
export interface TableWithColumns {
  name: string;
  expanded: boolean;
  columns: ColumnInfo[];
  isLoading: boolean;
}

/**
 * Interface for chart configuration
 */
export interface ChartConfig {
  type: 'bar' | 'line' | 'pie' | 'horizontalBar' | 'verticalBar' | 'doughnut';
  title?: string;
  xAxis?: string;
  yAxis?: string[];
  colors?: string[];
}

/**
 * Interface for chart save configuration
 */
export interface ChartSaveConfig {
  chart_type: string;
  x_axis: string;
  y_axis: { column: string; table_name?: string; aggregation?: string }[] | string[] | string;
  tables: {
    table_name: string;
    columns: string[];
  }[];
  chart_name: string;
  db_type: string;
  clipboard?: boolean;
  editable?: boolean;
  refresh?: boolean;
  shareable?: boolean;
  export?: boolean;
  verfied?: boolean;
  published_by?: string;
  group_by?: string | string[];
  filters?: { [key: string]: string };
  chart_response?: {
    status: string;
    data: any[];
    metadata?: {
      tables: string[];
      status: string;
    };
  };
}
