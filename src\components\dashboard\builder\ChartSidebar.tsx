import React, { memo, useMemo, useCallback } from 'react';
import { ChevronDown, ChevronRight, FolderIcon, Loader2 } from 'lucide-react';
import { SavedChart } from '@/types/chartTypes';
import { useDraggable } from '@dnd-kit/core';

interface ChartSidebarProps {
  loading: boolean;
  filteredCharts: SavedChart[];
  foldersExpanded: Record<string, boolean>;
  onToggleFolder: (folderName: string) => void;
  onAddChartToDashboard: (chart: SavedChart) => void;
}

// Draggable Chart Item Component
interface DraggableChartItemProps {
  chart: SavedChart;
}

const DraggableChartItem: React.FC<DraggableChartItemProps> = memo(({
  chart
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging,
  } = useDraggable({
    id: `chart-${chart.chart_id}`,
    data: {
      type: 'chart',
      chart: chart,
    },
  });

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
  } : undefined;

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`p-1 rounded cursor-pointer hover:bg-gray-100 group ${
        isDragging ? 'opacity-50' : ''
      }`}
      {...attributes}
      {...listeners}
    >
      <div className="flex items-center justify-between">
        <div
          className="text-xs truncate flex-1"
          onClick={(e) => {
            e.stopPropagation();
            // onViewChartDetails(chart);
          }}
        >
          {chart.chart_name}
        </div>
      </div>
    </div>
  );
});

const ChartSidebar: React.FC<ChartSidebarProps> = memo(({
  loading,
  filteredCharts,
  foldersExpanded,
  onToggleFolder,
  onAddChartToDashboard
}) => {
  // Memoize expensive computations
  const chartCount = useMemo(() => filteredCharts.length, [filteredCharts]);
  
  // Memoize event handlers
  const handleToggleFolder = useCallback((folderName: string) => {
    onToggleFolder(folderName);
  }, [onToggleFolder]);

  // Memoize rendered chart items to prevent unnecessary re-renders
  const renderedCharts = useMemo(() => {
    if (!foldersExpanded['availableCharts']) return null;
    
    return filteredCharts.map((chart) => (
      <DraggableChartItem
        key={chart.chart_id}
        chart={chart}
      />
    ));
  }, [filteredCharts, foldersExpanded]);
  if (loading) {
    return (
      <div className="flex justify-center items-center p-3">
        <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />
      </div>
    );
  }

  if (filteredCharts.length === 0) {
    return (
      <div className="text-center p-3 text-xs text-gray-500">
        No charts found
      </div>
    );
  }

  return (
    <div className="space-y-1 px-2">
      {/* Available Charts folder */}
      <div className="space-y-1">
        <div 
          className="flex items-center justify-between p-1.5 rounded cursor-pointer hover:bg-gray-100"
          onClick={() => handleToggleFolder('availableCharts')}
        >
          <div className="flex items-center">
            <FolderIcon size={14} className="mr-1.5 text-blue-500" />
            <span className="text-xs font-medium">Available Charts</span>
            <span className="ml-1.5 text-xs text-gray-500">({chartCount})</span>
          </div>
          {foldersExpanded['availableCharts'] ? 
            <ChevronDown size={14} className="text-gray-500" /> : 
            <ChevronRight size={14} className="text-gray-500" />
          }
        </div>
        
        {renderedCharts && (
          <div className="pl-6 space-y-0.5">
            {renderedCharts}
          </div>
        )}
      </div>
    </div>
  );
});

ChartSidebar.displayName = 'ChartSidebar';
DraggableChartItem.displayName = 'DraggableChartItem';

export default ChartSidebar;
