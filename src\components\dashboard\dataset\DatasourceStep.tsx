
import React, { useState, useEffect } from 'react';
import { Search, ArrowRight, ArrowLeft } from 'lucide-react';
import { useAppDispatch, useAppSelector } from '@/hooks/useRedux';
import { 
  addToSelectedConnections,
  removeFromSelectedConnections
} from '@/stores/datasetSlice';
import <PERSON>Header, { NavigationButtons } from './StepHeader';
import { Button } from '@/components/ui/button';
import ConnectionList from './datasource/ConnectionList';
import SelectedConnectionsList from './datasource/SelectedConnectionsList';

interface DatasourceStepProps {
  onClose: () => void;
}

const DatasourceStep: React.FC<DatasourceStepProps> = ({ onClose }) => {
  const dispatch = useAppDispatch();
  const { selectedConnections, availableConnections } = useAppSelector(state => state.dataset);
  const [localSearchQuery, setLocalSearchQuery] = useState('');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  // Clear selected items when component mounts
  useEffect(() => {
    setSelectedItems([]);
  }, []);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalSearchQuery(e.target.value);
  };

  const handleToggleConnection = (connectionName: string) => {
    // Toggle selection in the local state
    if (selectedItems.includes(connectionName)) {
      setSelectedItems(selectedItems.filter(item => item !== connectionName));
    } else {
      setSelectedItems([...selectedItems, connectionName]);
    }
  };

  const handleAddToSelected = () => {
    if (selectedItems.length === 0) {
      return;
    }
    
    dispatch(addToSelectedConnections(selectedItems));
    
    // Clear local selections
    setSelectedItems([]);
  };

  const handleRemoveFromSelected = () => {
    if (selectedItems.length === 0) {
      return;
    }
    
    dispatch(removeFromSelectedConnections(selectedItems));
    
    // Clear local selections
    setSelectedItems([]);
  };

  return (
    <div className="p-4">
      <StepHeader 
        title="Select Data Source" 
        onClose={onClose}
      />
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="border border-gray-200 p-4 rounded-lg">
          <h3 className="text-sm font-medium mb-2">Data Connections</h3>
          <div className="mb-2 relative">
            <input
              type="text"
              placeholder="Search..."
              value={localSearchQuery}
              onChange={handleSearch}
              className="w-full pl-8 pr-4 py-1 border border-gray-300 rounded-md text-sm"
            />
            <Search className="absolute left-2 top-1.5 text-gray-400" size={16} />
          </div>
          
          <ConnectionList 
            connections={availableConnections}
            searchQuery={localSearchQuery}
            selectedItems={selectedItems}
            onToggleConnection={handleToggleConnection}
          />
        </div>

        <div className="flex flex-col items-center justify-center space-y-4">
          <Button 
            onClick={handleAddToSelected} 
            variant="blue" 
            className="w-10 h-10 p-0 flex items-center justify-center"
            disabled={selectedItems.length === 0}
          >
            <ArrowRight size={16} />
          </Button>
          <Button 
            onClick={handleRemoveFromSelected} 
            variant="blue" 
            className="w-10 h-10 p-0 flex items-center justify-center"
            disabled={selectedItems.length === 0}
          >
            <ArrowLeft size={16} />
          </Button>
        </div>

        <div className="border border-gray-200 p-4 rounded-lg">
          <h3 className="text-sm mb-2">Selected Connections</h3>
          <SelectedConnectionsList
            selectedConnections={selectedConnections}
            selectedItems={selectedItems}
            onToggleConnection={handleToggleConnection}
          />
        </div>
      </div>
      
      <div className="mt-6">
        <NavigationButtons 
          showPrevious={false}
          disableNext={selectedConnections.length === 0}
        />
      </div>
    </div>
  );
};

export default DatasourceStep;
