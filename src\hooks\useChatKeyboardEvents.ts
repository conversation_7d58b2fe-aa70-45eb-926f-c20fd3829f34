
import { KeyboardEvent, RefObject } from 'react';

interface UseChatKeyboardEventsProps {
  inputValue: string;
  isLoading: boolean;
  predictions: string[];
  textareaRef: RefObject<HTMLTextAreaElement>;
  handleSubmit: (e: React.FormEvent) => void;
  handlePredictionSelect: (prediction: string, setInputValue: (value: string) => void) => void;
  setInputValue: (value: string) => void;
  setShowPredictions: (show: boolean) => void;
}

export const useChatKeyboardEvents = ({
  inputValue,
  isLoading,
  predictions,
  textareaRef,
  handleSubmit,
  handlePredictionSelect,
  setInputValue,
  setShowPredictions
}: UseChatKeyboardEventsProps) => {
  
  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Tab' && predictions.length > 0) {
      e.preventDefault();
      handlePredictionSelect(predictions[0], setInputValue);
      textareaRef.current?.focus();
      return;
    }
    
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (!isLoading && inputValue.trim()) {
        setShowPredictions(false); // Hide predictions on enter
        handleSubmit(e as unknown as React.FormEvent);
      }
    }

    // Arrow down to navigate to first prediction
    if (e.key === 'ArrowDown' && predictions.length > 0) {
      e.preventDefault();
      // Focus would be handled in PredictionDropdown component
    }
  };

  return { handleKeyDown };
};
