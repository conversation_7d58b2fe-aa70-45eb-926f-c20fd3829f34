
import React, { useState, useCallback } from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import AdminSidebar from '@/components/admin/AdminSidebar';
import Header from '@/components/dashboard/Header';
import AdminBreadcrumb from '@/components/admin/AdminBreadcrumb';

const AdminLayout: React.FC = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
    const location = useLocation();
  const isMainAdminPage = location.pathname === '/Admin' || location.pathname === '/Admin/' || location.pathname === '/Admin/dashboard';
  const isUsersPage = location.pathname === '/Admin/users';
  const isGroupsPage = location.pathname === '/Admin/groups';
  const isServicePrincipalPage = location.pathname === '/Admin/service-principal';
  const isTableRelationshipMapperPage = location.pathname === '/Admin/TableRelationshipMapper';
  const isApprovalSetPage = location.pathname === '/Admin/approval-set';
  const isPermissionSetPage = location.pathname === '/Admin/permission-set';
  const isWorkspacePage = location.pathname.includes('/Admin/workspace');
  const isMarketplacePage = location.pathname.includes('/Admin/marketplace');
  const isDataviewBuilderPage = location.pathname === '/Admin/dataview-builder';

  // Use useCallback to ensure function reference stability
  const toggleSidebar = useCallback(() => {
    setSidebarCollapsed(prev => !prev);
  }, []);

  return (
    <div className="flex flex-col h-screen overflow-hidden">
      <Header
        toggleSidebar={toggleSidebar}
        sidebarCollapsed={sidebarCollapsed}
        dashboardName="Admin"
        rightSidebarVisible={false}
        dashboardType={2}
      />
      <div className="flex flex-1 overflow-hidden bg-gray-50">
        {!isMainAdminPage && !isUsersPage && !isGroupsPage && !isServicePrincipalPage && !isDataviewBuilderPage && (
          <AdminSidebar collapsed={sidebarCollapsed} onToggle={toggleSidebar} />
        )}
        <main className="flex-1 overflow-hidden">
          {isDataviewBuilderPage ? (
            <Outlet />
          ) : isTableRelationshipMapperPage || isApprovalSetPage || isPermissionSetPage || isWorkspacePage || isMarketplacePage ? (
            <div className="p-6 h-full admin-content">
              <div className="max-w-7xl mx-auto">
                {!isMainAdminPage && <AdminBreadcrumb />}
                <Outlet />
              </div>
            </div>
          ) : (
            <div className="p-6 h-full admin-content">
              <div className="max-w-7xl mx-auto">
                {!isMainAdminPage && <AdminBreadcrumb />}
                <Outlet />
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
};

export default React.memo(AdminLayout);
