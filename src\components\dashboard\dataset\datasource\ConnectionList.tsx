
import React from 'react';

interface ConnectionListProps {
  connections: any[];
  searchQuery: string;
  selectedItems: string[];
  onToggleConnection: (name: string) => void;
}

const ConnectionList: React.FC<ConnectionListProps> = ({
  connections,
  searchQuery,
  selectedItems,
  onToggleConnection
}) => {
  const filteredConnections = connections.map(group => ({
    ...group,
    children: group.children.filter(item => 
      item.name.toLowerCase().includes(searchQuery.toLowerCase())
    )
  })).filter(group => group.children.length > 0);

  return (
    <div className="border border-gray-300 rounded-md h-64 overflow-y-auto p-2">
      {filteredConnections.length === 0 ? (
        <div className="text-gray-500 text-sm italic p-2">No connections found</div>
      ) : (
        filteredConnections.map((group) => (
          <div key={group.name} className="mb-2">
            <div className="font-medium">{group.name}</div>
            <ul className="pl-4 text-sm">
              {group.children.map((item: any) => (
                <li 
                  key={item.name}
                  className={`py-1 cursor-pointer hover:bg-blue-50 pl-1 rounded ${
                    selectedItems.includes(item.name) ? 'bg-blue-100' : ''
                  }`}
                  onClick={() => onToggleConnection(item.name)}
                >
                  {item.name}
                </li>
              ))}
            </ul>
          </div>
        ))
      )}
    </div>
  );
};

export default ConnectionList;
