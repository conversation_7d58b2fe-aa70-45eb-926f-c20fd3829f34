
import React from 'react';

interface SelectedConnectionsListProps {
  selectedConnections: string[];
  selectedItems: string[];
  onToggleConnection: (name: string) => void;
}

const SelectedConnectionsList: React.FC<SelectedConnectionsListProps> = ({
  selectedConnections,
  selectedItems,
  onToggleConnection
}) => {
  return (
    <div className="border border-gray-300 rounded-md h-72 overflow-y-auto p-2">
      {selectedConnections.length === 0 ? (
        <div className="text-gray-500 text-sm italic p-2">No connections selected</div>
      ) : (
        <ul className="text-sm">
          {selectedConnections.map((connection) => (
            <li 
              key={connection}
              className={`py-1 cursor-pointer hover:bg-blue-50 pl-1 rounded ${
                selectedItems.includes(connection) ? 'bg-blue-100' : ''
              }`}
              onClick={() => onToggleConnection(connection)}
            >
              {connection}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default SelectedConnectionsList;
