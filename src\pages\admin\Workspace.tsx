import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Autocomplete, AutocompleteOption } from '@/components/ui/autocomplete';
import { Plus, Search, Minus } from 'lucide-react';
import { toast } from 'sonner';

interface Workspace {
  id: string;
  workspace_name: string;
  approval_set_ids: string[];
  approval_set_names?: string[];
  connections_list: DatabaseConnection[];
}

interface DatabaseConnection {
  connection_id: string;
  connection_name: string;
  database_name: string;
}

interface ApprovalSet {
  id: string;
  approval_set_name: string;
  can_approve_insights: boolean;
  can_approve_marketplace: boolean;
  can_approve_pattern: boolean;
  can_approve_predict: boolean;
}

interface CreateWorkspaceRequest {
  workspace_name: string;
  approval_set_id: string;
  connections_list: {
    connection_id: string;
    connection_name: string;
    database_name: string;
  }[];
}

const Workspace: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [workspaces, setWorkspaces] = useState<Workspace[]>([]);
  const [selectedWorkspace, setSelectedWorkspace] = useState<Workspace | null>(null);
  const [workspaceName, setWorkspaceName] = useState('');
  const [selectedApprovalSets, setSelectedApprovalSets] = useState<string[]>([]); // Store approval set IDs
  const [availableConnections, setAvailableConnections] = useState<DatabaseConnection[]>([]);
  const [assignedConnections, setAssignedConnections] = useState<DatabaseConnection[]>([]);
  const [approvalSets, setApprovalSets] = useState<ApprovalSet[]>([]);
  const [isNewWorkspace, setIsNewWorkspace] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [connectionSearchQuery, setConnectionSearchQuery] = useState('');

  // API functions
  const fetchApprovalSets = async (): Promise<ApprovalSet[]> => {
    try {
      const response = await fetch('http://10.100.0.22:8001/account-management/get-all-approval-set', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      });

      if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);

      const data = await response.json();
      console.log('🔍 Raw Approval Sets API Response:', data);
      console.log('🔍 Response type:', typeof data);
      console.log('🔍 Is array:', Array.isArray(data));

      // Handle different response formats
      let approvalSetsData: any[] = [];
      if (Array.isArray(data)) {
        approvalSetsData = data;
      } else if (data && Array.isArray(data.approval_sets)) {
        approvalSetsData = data.approval_sets;
      } else if (data && Array.isArray(data.data)) {
        approvalSetsData = data.data;
      } else if (data && typeof data === 'object') {
        // If it's a single object, wrap it in an array
        approvalSetsData = [data];
      }

      console.log('🔍 Extracted approval sets data:', approvalSetsData);

      // Log each approval set to see the structure
      approvalSetsData.forEach((set, index) => {
        console.log(`🔍 Approval Set ${index}:`, set);
        console.log(`🔍 Available fields:`, Object.keys(set || {}));
      });

      // Map to expected format, handling different field names
      const mappedApprovalSets: ApprovalSet[] = approvalSetsData.map((set, index) => {
        // Try different possible field names for ID
        const id = set?.id || set?.approval_set_id || set?.uuid || set?._id || `temp-${Date.now()}-${index}`;
        const name = set?.approval_set_name || set?.name || set?.title || 'Unnamed Approval Set';

        console.log(`🔍 Mapping approval set ${index} - Original:`, set);
        console.log(`🔍 Mapped ID: "${id}", Name: "${name}"`);

        return {
          id: String(id),
          approval_set_name: String(name),
          can_approve_insights: set?.can_approve_insights || false,
          can_approve_marketplace: set?.can_approve_marketplace || false,
          can_approve_pattern: set?.can_approve_pattern || false,
          can_approve_predict: set?.can_approve_predict || false
        };
      });

      console.log('🔍 Final mapped approval sets:', mappedApprovalSets);
      return mappedApprovalSets;
    } catch (error) {
      console.error('Error fetching approval sets:', error);
      toast.error('Failed to load approval sets');
      return [];
    }
  };

  const fetchConnections = async (): Promise<DatabaseConnection[]> => {
    try {
      const response = await fetch('http://10.100.0.22:8001/account-management/get-all-connections', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      });

      if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);

      const data = await response.json();
      console.log('Database Connections API Response:', data);

      // Handle different response formats
      let connectionsData: DatabaseConnection[] = [];
      if (Array.isArray(data)) {
        connectionsData = data;
      } else if (data && Array.isArray(data.connections)) {
        connectionsData = data.connections;
      } else if (data && Array.isArray(data.data)) {
        connectionsData = data.data;
      } else if (data && Array.isArray(data.connection_list)) {
        connectionsData = data.connection_list;
      }

      console.log('Processed Database Connections:', connectionsData);
      return connectionsData;
    } catch (error) {
      console.error('Error fetching connections:', error);
      toast.error('Failed to load database connections');
      return [];
    }
  };

  const createWorkspace = async (workspaceData: CreateWorkspaceRequest): Promise<boolean> => {
    console.log('\n🚀 === CREATE WORKSPACE API CALL ===');
    console.log('📤 Request URL:', 'http://10.100.0.22:8001/account-management/create-workspace');
    console.log('📤 Request Method:', 'POST');
    console.log('📤 Request Headers:', { 'Content-Type': 'application/json' });
    console.log('📤 Request Body:', JSON.stringify(workspaceData, null, 2));

    try {
      console.log('⏳ Sending request...');
      const response = await fetch('http://10.100.0.22:8001/account-management/create-workspace', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(workspaceData),
      });

      console.log('📥 Response Status:', response.status);
      console.log('📥 Response Status Text:', response.statusText);
      console.log('📥 Response Headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ API Error Response:', errorText);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const result = await response.json();
      console.log('✅ Workspace created successfully:', result);
      return true;
    } catch (error) {
      console.error('❌ Error creating workspace:', error);
      console.error('❌ Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
      toast.error(`Failed to create workspace: ${error.message}`);
      return false;
    }
  };

  // Data initialization
  useEffect(() => {
    const initializeData = async () => {
      setIsLoading(true);

      try {
        const [approvalSetsData, connectionsData] = await Promise.all([
          fetchApprovalSets(),
          fetchConnections()
        ]);

        console.log('\n📦 === APPROVAL SETS DATA LOADED ===');
        console.log('Raw approval sets data:', approvalSetsData);

        // Validate approval sets have names
        const validApprovalSets = approvalSetsData.filter(set => {
          const isValid = set.approval_set_name && set.approval_set_name.trim() !== '';
          console.log(`Approval set validation - ID: ${set.id}, Name: "${set.approval_set_name}", Valid: ${isValid}`);
          return isValid;
        });

        console.log('Valid approval sets:', validApprovalSets);
        setApprovalSets(validApprovalSets);
        setAvailableConnections(connectionsData);

        // Load workspaces from localStorage or use mock data
        const storedWorkspaces = JSON.parse(localStorage.getItem('workspaces') || '[]');
        const mockWorkspaces: Workspace[] = storedWorkspaces.length > 0 ? storedWorkspaces : [
          {
            id: '1',
            workspace_name: 'GlobalWorkspace (In-built)',
            approval_set_id: approvalSetsData[0]?.id || '',
            approval_set_name: approvalSetsData[0]?.approval_set_name || '',
            connections_list: []
          }
        ];
        setWorkspaces(mockWorkspaces);

      } catch (error) {
        console.error('Error initializing data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeData();
  }, []);

  // Handle URL parameters for selecting workspaces
  useEffect(() => {
    if (workspaces.length === 0 || isLoading) return; // Wait for data to load

    const workspaceId = searchParams.get('id');
    const isNew = searchParams.get('new');

    if (isNew === 'true' && !isNewWorkspace) {
      handleNewWorkspace();
    } else if (workspaceId && workspaces.length > 0) {
      const workspace = workspaces.find(w => w.id === workspaceId);
      if (workspace && (!selectedWorkspace || selectedWorkspace.id !== workspace.id)) {
        setSelectedWorkspace(workspace);
        setWorkspaceName(workspace.workspace_name);
        setSelectedApprovalSets(workspace.approval_set_ids || []);
        setAssignedConnections(workspace.connections_list);
        setIsNewWorkspace(false);
      }
    } else if (workspaces.length > 0 && !selectedWorkspace && !isNew) {
      // Auto-select first workspace if none selected and not creating new
      const firstWorkspace = workspaces[0];
      setSelectedWorkspace(firstWorkspace);
      setWorkspaceName(firstWorkspace.workspace_name);
      setSelectedApprovalSets(firstWorkspace.approval_set_ids || []);
      setAssignedConnections(firstWorkspace.connections_list);
      setIsNewWorkspace(false);
    }
  }, [searchParams, workspaces, isLoading]);

  const handleNewWorkspace = () => {
    if (isNewWorkspace) return; // Prevent multiple calls

    const newWorkspace: Workspace = {
      id: `new-${Date.now()}`,
      workspace_name: 'New Workspace',
      approval_set_ids: [],
      approval_set_names: [],
      connections_list: []
    };
    setSelectedWorkspace(newWorkspace);
    setWorkspaceName('New Workspace');
    setSelectedApprovalSets([]);
    setAssignedConnections([]);
    setIsNewWorkspace(true);

    // Update URL to reflect new workspace state
    navigate('/Admin/workspace?new=true', { replace: true });
  };

  const handleSave = async () => {
    console.log('\n🎯 === STARTING WORKSPACE SAVE PROCESS ===');
    console.log('📝 Workspace Name:', workspaceName);
    console.log('📋 Selected Approval Sets (IDs):', selectedApprovalSets);
    console.log('🔗 Assigned Connections:', assignedConnections);
    console.log('🆕 Is New Workspace:', isNewWorkspace);

    if (!workspaceName.trim()) {
      console.log('❌ Validation failed: Empty workspace name');
      toast.error('Please enter a workspace name');
      return;
    }

    if (selectedApprovalSets.length === 0) {
      console.log('❌ Validation failed: No approval sets selected');
      toast.error('Please select at least one approval set');
      return;
    }

    if (approvalSets.length === 0) {
      console.log('❌ Validation failed: No approval sets available');
      toast.error('No approval sets available. Please create an approval set first.');
      return;
    }

    console.log('✅ Validation passed, starting save process...');
    setIsSaving(true);

    try {
      if (isNewWorkspace) {
        console.log('\n📋 === PROCESSING NEW WORKSPACE ===');
        // selectedApprovalSets now contains IDs directly
        const approvalSetIds = selectedApprovalSets;
        console.log('🔍 Approval Set IDs to process:', approvalSetIds);

        // Validate that all selected approval set IDs exist
        const validApprovalSetIds = approvalSetIds.filter(id => {
          const approvalSet = getApprovalSetById(id);
          console.log(`Validating approval set ID "${id}":`, approvalSet ? '✅ Found' : '❌ Not found');
          return approvalSet && approvalSet.id;
        });

        console.log('✅ Valid approval set IDs:', validApprovalSetIds);

        if (validApprovalSetIds.length !== approvalSetIds.length) {
          const invalidIds = approvalSetIds.filter(id => !getApprovalSetById(id));
          console.error('❌ Invalid approval set IDs:', invalidIds);
          toast.error('Some selected approval sets are invalid');
          return;
        }

        // OPTION 1: Create multiple workspaces (one for each approval set)
        // Since API expects single approval_set_id, create separate workspace for each approval set

        // OPTION 2: Use first approval set only (simpler approach)
        // Uncomment this block and comment out OPTION 1 if you prefer single workspace:
        /*
        const primaryApprovalSetId = approvalSetIds[0];
        const workspaceData: CreateWorkspaceRequest = {
          workspace_name: workspaceName,
          approval_set_id: primaryApprovalSetId,
          connections_list: assignedConnections.map(conn => ({
            connection_id: conn.connection_id,
            connection_name: conn.connection_name,
            database_name: conn.database_name
          }))
        };
        const success = await createWorkspace(workspaceData);
        const createdWorkspaces = success ? [{
          id: `workspace-${Date.now()}`,
          workspace_name: workspaceName,
          approval_set_ids: [primaryApprovalSetId],
          approval_set_names: [selectedApprovalSets[0]],
          connections_list: assignedConnections
        }] : [];
        */

        // OPTION 1 Implementation: Create multiple workspaces
        let allSuccessful = true;
        const createdWorkspaces: Workspace[] = [];

        for (let i = 0; i < approvalSetIds.length; i++) {
          const approvalSetId = approvalSetIds[i];
          const approvalSetName = getApprovalSetNameById(approvalSetId);

          console.log(`\n=== Creating Workspace ${i + 1}/${approvalSetIds.length} ===`);
          console.log('Approval Set ID:', approvalSetId);
          console.log('Approval Set Name:', approvalSetName);

          // Create workspace data for this approval set
          const workspaceData: CreateWorkspaceRequest = {
            workspace_name: workspaceName,  // Send exact workspace name without modifications
            approval_set_id: approvalSetId,
            connections_list: assignedConnections.map(conn => ({
              connection_id: conn.connection_id,
              connection_name: conn.connection_name,
              database_name: conn.database_name
            }))
          };

          console.log('=== API Request Payload ===');
          console.log(JSON.stringify(workspaceData, null, 2));

          console.log('=== Calling createWorkspace API ===');
          const success = await createWorkspace(workspaceData);
          console.log('API Response Success:', success);

          if (success) {
            const newWorkspace: Workspace = {
              id: `workspace-${Date.now()}-${i}`, // In real app, this would come from API response
              workspace_name: workspaceName, // Use original workspace name
              approval_set_ids: [approvalSetId],
              approval_set_names: [approvalSetName],
              connections_list: assignedConnections
            };
            createdWorkspaces.push(newWorkspace);
          } else {
            allSuccessful = false;
            break;
          }
        }

        const success = allSuccessful;
        console.log('\n📊 === WORKSPACE CREATION SUMMARY ===');
        console.log('🎯 Overall Success:', success);
        console.log('📦 Created Workspaces:', createdWorkspaces.length);
        console.log('📋 Created Workspace Details:', createdWorkspaces);

        if (success && createdWorkspaces.length > 0) {
          console.log('✅ Processing successful workspace creation...');

          // Update state with all created workspaces
          const updatedWorkspaces = [...workspaces, ...createdWorkspaces];
          setWorkspaces(updatedWorkspaces);
          setSelectedWorkspace(createdWorkspaces[0]); // Select the first created workspace
          setIsNewWorkspace(false);

          // Store in localStorage for persistence
          localStorage.setItem('workspaces', JSON.stringify(updatedWorkspaces));

          // Navigate to the first created workspace
          navigate(`/Admin/workspace?id=${createdWorkspaces[0].id}`, { replace: true });

          if (createdWorkspaces.length === 1) {
            console.log('🎉 Single workspace created successfully');
            toast.success('Workspace created successfully');
          } else {
            console.log(`🎉 Multiple workspaces created: ${createdWorkspaces.length}`);
            toast.success(`${createdWorkspaces.length} workspaces created successfully`);
          }
        } else {
          console.log('❌ Workspace creation failed');
          toast.error('Failed to create workspace(s)');
        }
    } else {
      // Update existing workspace (you can add update API later)
      const approvalSetIds = selectedApprovalSets; // Already IDs
      const approvalSetNames = selectedApprovalSets.map(id => getApprovalSetNameById(id));

      const updatedWorkspaces = workspaces.map(workspace =>
        workspace.id === selectedWorkspace?.id
          ? {
              ...workspace,
              workspace_name: workspaceName,
              approval_set_ids: approvalSetIds,
              approval_set_names: approvalSetNames,
              connections_list: assignedConnections
            }
          : workspace
      );

      setWorkspaces(updatedWorkspaces);
      localStorage.setItem('workspaces', JSON.stringify(updatedWorkspaces));
      toast.success('Workspace updated successfully');
      }
    } catch (error) {
      console.error('Error saving workspace:', error);
      toast.error('Failed to save workspace');
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    if (isNewWorkspace) {
      setSelectedWorkspace(null);
      setWorkspaceName('');
      setSelectedApprovalSets([]);
      setAssignedConnections([]);
      setIsNewWorkspace(false);

      // Navigate back to workspace list
      navigate('/Admin/workspace', { replace: true });
    } else {
      // Reset to original values if editing existing workspace
      if (selectedWorkspace) {
        setWorkspaceName(selectedWorkspace.workspace_name);
        setSelectedApprovalSets(selectedWorkspace.approval_set_ids || []);
        setAssignedConnections(selectedWorkspace.connections_list);
      }
    }
  };

  const addConnection = (connection: DatabaseConnection) => {
    const isAlreadyAssigned = assignedConnections.some(conn => conn.connection_id === connection.connection_id);
    if (!isAlreadyAssigned) {
      setAssignedConnections(prev => [...prev, connection]);
    }
  };

  const removeConnection = (connectionId: string) => {
    setAssignedConnections(prev => prev.filter(conn => conn.connection_id !== connectionId));
  };

  const filteredAvailableConnections = availableConnections.filter(conn => {
    const isAlreadyAssigned = assignedConnections.some(assigned => assigned.connection_id === conn.connection_id);
    return conn.connection_name.toLowerCase().includes(connectionSearchQuery.toLowerCase()) && !isAlreadyAssigned;
  });

  // Helper function to get approval set by ID
  const getApprovalSetById = (approvalSetId: string): ApprovalSet | undefined => {
    if (!approvalSetId) return undefined;
    return approvalSets.find(set => set.id === approvalSetId);
  };

  // Helper function to get approval set name by ID
  const getApprovalSetNameById = (approvalSetId: string): string => {
    console.log('Getting name for approval set ID:', approvalSetId);
    console.log('Available approval sets:', approvalSets.map(set => ({ id: set.id, name: set.approval_set_name })));

    const approvalSet = getApprovalSetById(approvalSetId);
    console.log('Found approval set:', approvalSet);

    const name = approvalSet?.approval_set_name || 'Unknown Approval Set';
    console.log('Returning name:', name);
    return name;
  };

  // Helper function to handle approval set selection
  const handleApprovalSetSelect = (approvalSetId: string) => {
    console.log('\n🎯 === APPROVAL SET SELECTION ===');
    console.log('Selected approval set ID:', approvalSetId);
    console.log('Current selected approval sets:', selectedApprovalSets);

    const approvalSet = getApprovalSetById(approvalSetId);
    console.log('Approval set details:', approvalSet);

    if (!selectedApprovalSets.includes(approvalSetId)) {
      const newSelection = [...selectedApprovalSets, approvalSetId];
      console.log('New selection:', newSelection);
      setSelectedApprovalSets(newSelection);
    } else {
      console.log('Approval set already selected');
    }
  };

  // Helper function to remove approval set
  const removeApprovalSet = (approvalSetId: string) => {
    setSelectedApprovalSets(prev => prev.filter(id => id !== approvalSetId));
  };



  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading workspaces...</div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Navigation Tabs */}
      <div className="border-b border-gray-200 bg-white">
        <nav className="flex space-x-8 px-6">
          <div
            className="py-4 px-1 border-b-2 font-normal text-sm"
            style={{
              borderBottomColor: 'rgb(0, 130, 130)',
              color: 'rgb(0, 130, 130)'
            }}
          >
            DADA - Workspace
          </div>
        </nav>
      </div>

      {/* Content Area */}
      <div className="flex-1 p-6 overflow-auto bg-white">
        {/* Header with New Workspace Button */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h2 className="text-lg font-normal text-gray-700">
              {selectedWorkspace && !isNewWorkspace
                ? `Edit Workspace: ${selectedWorkspace.workspace_name}`
                : isNewWorkspace
                ? 'Create New Workspace'
                : 'Workspaces'
              }
            </h2>
            <p className="text-sm text-gray-500 mt-1">
              {selectedWorkspace && !isNewWorkspace
                ? 'Modify the workspace configuration below'
                : isNewWorkspace
                ? 'Configure a new workspace'
                : 'Select a workspace from the sidebar to edit, or create a new one'
              }
            </p>
          </div>
          <Button
            onClick={handleNewWorkspace}
            className="text-white hover:opacity-90 transition-opacity"
            style={{ backgroundColor: 'rgb(0, 130, 130)' }}
          >
            <Plus className="w-4 h-4 mr-2" />
            New Workspace
          </Button>
        </div>

        {/* Form or Empty State */}
        {selectedWorkspace ? (
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm max-w-4xl">
            {/* Form Content */}
            <div className="p-6 space-y-6">
              {/* Workspace Name */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-normal text-gray-600 mb-2">
                    Workspace Name *
                  </label>
                  <Input
                    value={workspaceName}
                    onChange={(e) => setWorkspaceName(e.target.value)}
                    placeholder="Enter workspace name"
                    className="w-full"
                  />
                </div>
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <p className="text-sm text-yellow-800">
                    Workspace helps group database connections.
                  </p>
                </div>
              </div>

              {/* Approval Set */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-normal text-gray-600 mb-2">
                    Approval Set *
                  </label>
                  <Autocomplete
                    options={(() => {
                      console.log('\n📋 === GENERATING AUTOCOMPLETE OPTIONS ===');
                      console.log('All approval sets:', approvalSets);
                      console.log('Selected approval set IDs:', selectedApprovalSets);

                      const options = approvalSets
                        .filter(set => {
                          const isSelected = selectedApprovalSets.includes(set.id);
                          console.log(`Approval set "${set.approval_set_name}" (ID: ${set.id}) is selected: ${isSelected}`);
                          return !isSelected;
                        })
                        .map(set => {
                          const option = {
                            value: set.id,
                            label: set.approval_set_name || 'Unnamed Approval Set'
                          };
                          console.log('Generated option:', option);
                          return option;
                        });

                      console.log('Final autocomplete options:', options);
                      return options;
                    })()}
                    value=""
                    onValueChange={(value) => handleApprovalSetSelect(value as string)}
                    placeholder={
                      approvalSets.length === 0
                        ? "No approval sets available"
                        : selectedApprovalSets.length === 0
                        ? "Select approval sets"
                        : "Add more approval sets"
                    }
                    searchPlaceholder="Search approval sets..."
                    emptyMessage={selectedApprovalSets.length === approvalSets.length ? "All approval sets selected" : "No approval sets found"}
                    disabled={approvalSets.length === 0 || selectedApprovalSets.length === approvalSets.length}
                    className="w-full"
                  />

                  {/* Show selected approval sets */}
                  {selectedApprovalSets.length > 0 && (
                    <div className="mt-3 flex flex-wrap gap-2">
                      {selectedApprovalSets.map(approvalSetId => {
                        const approvalSetName = getApprovalSetNameById(approvalSetId);
                        return (
                          <div
                            key={approvalSetId}
                            className="flex items-center gap-2 bg-teal-50 border border-teal-200 rounded-lg px-3 py-2"
                          >
                            <span className="text-sm text-teal-800">
                              {approvalSetName}
                            </span>
                            <button
                              type="button"
                              onClick={() => removeApprovalSet(approvalSetId)}
                              className="text-teal-400 hover:text-teal-600 transition-colors"
                              title="Remove"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </button>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>

                {/* Info Box */}
                {/* <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-start gap-2">
                    <svg className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <div className="flex-1">
                      <p className="text-sm text-blue-800">
                        <span className="font-medium">Approval Set:</span> Defines which features require approval in this workspace.
                      </p>
                      {approvalSets.length === 0 ? (
                        <p className="text-xs text-blue-600 mt-2">
                          No approval sets found. Please create an approval set first in the Approval Sets section.
                        </p>
                      ) : (
                        <p className="text-xs text-blue-600 mt-2">
                          Search and select an approval set to configure workspace permissions for Insights, Marketplace, Pattern, and Predict features.
                        </p>
                      )}
                    </div>
                  </div>
                </div> */}
              </div>

              {/* DB Connection Assignment */}
              <div>
                <div className="border border-gray-300 rounded-lg">
                  <div className="bg-gray-50 px-4 py-3 border-b border-gray-300 flex items-center">
                    {/* <Minus className="w-4 h-4 mr-2 text-gray-600" /> */}
                    <span className="font-normal text-gray-600">DBConnection Assignment</span>
                  </div>

                  <div className="p-4 space-y-4">
                    {/* Search Bar */}
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        type="text"
                        placeholder="Search"
                        className="w-full pl-10 pr-3 py-2 text-sm border border-gray-300 rounded-md"
                        value={connectionSearchQuery}
                        onChange={(e) => setConnectionSearchQuery(e.target.value)}
                      />
                    </div>

                    {/* Available Connections */}
                    <div className="space-y-2 max-h-48 overflow-y-auto">
                      {filteredAvailableConnections.length > 0 ? (
                        filteredAvailableConnections.map(connection => (
                          <div key={connection.connection_id} className="flex items-center justify-between py-3 px-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                            <div className="flex flex-col flex-1">
                              <span className="text-sm text-gray-800 font-medium">{connection.connection_name}</span>
                              <span className="text-xs text-gray-500 mt-1">Database: {connection.database_name}</span>
                              <span className="text-xs text-gray-400 mt-1">ID: {connection.connection_id}</span>
                            </div>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => addConnection(connection)}
                              className="text-xs px-4 py-2 bg-teal-50 hover:bg-teal-100 text-teal-700 border-teal-200 hover:border-teal-300 transition-colors"
                            >
                              Add
                            </Button>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          <p className="text-sm">
                            {connectionSearchQuery ? 'No connections match your search' : 'All connections have been added'}
                          </p>
                        </div>
                      )}
                    </div>

                    {/* DB Connections Section */}
                    <div className="mt-6">
                      <h4 className="font-normal text-gray-600 mb-3">DB Connections:</h4>
                      <div className="space-y-3 max-h-48 overflow-y-auto">
                        {assignedConnections.length > 0 ? (
                          assignedConnections.map(connection => (
                            <div
                              key={connection.connection_id}
                              className="flex items-center justify-between py-3 px-4 rounded-lg border-2 border-teal-200 bg-teal-50 hover:bg-teal-100 transition-colors"
                            >
                              <div className="flex flex-col flex-1">
                                <span className="text-sm text-teal-800 font-medium">{connection.connection_name}</span>
                                <span className="text-xs text-teal-600 mt-1">Database: {connection.database_name}</span>
                                <span className="text-xs text-teal-500 mt-1">ID: {connection.connection_id}</span>
                              </div>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => removeConnection(connection.connection_id)}
                                className="text-xs px-4 py-2 bg-red-50 hover:bg-red-100 text-red-700 border-red-200 hover:border-red-300 transition-colors"
                              >
                                Remove
                              </Button>
                            </div>
                          ))
                        ) : (
                          <div className="text-center py-8 text-gray-500 border-2 border-dashed border-gray-200 rounded-lg">
                            <p className="text-sm">No database connections assigned to this workspace</p>
                            <p className="text-xs text-gray-400 mt-1">Add connections from the list above</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                <Button
                  variant="outline"
                  onClick={handleCancel}
                  className="px-6 py-2"
                  disabled={isSaving}
                >
                  Close
                </Button>
                <Button
                  onClick={handleSave}
                  variant='greenmind'
                  disabled={isSaving}
                  className="px-6 py-2"
                >
                  {isSaving ? 'Saving...' : 'Save'}
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-64 text-gray-500">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                <Plus className="w-8 h-8 text-gray-400" />
              </div>
              <p className="text-lg font-normal text-gray-600 mb-2">No workspace selected</p>
              <p className="text-sm text-gray-500 mb-4">
                Choose a workspace from the sidebar to edit, or create a new one using the button above.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Workspace;
