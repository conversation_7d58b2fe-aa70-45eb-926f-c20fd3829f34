
import React from 'react';

export interface DashboardSettingsProps {
  textSize: 'small' | 'medium' | 'large';
  onTextSizeChange: (size: 'small' | 'medium' | 'large') => void;
}

// Text size classes for dynamic sizing
export const textSizeClasses = {
  small: {
    base: 'text-sm',
    heading: 'text-base',
    spacing: 'space-y-3'
  },
  medium: {
    base: 'text-base',
    heading: 'text-lg',
    spacing: 'space-y-4'
  },
  large: {
    base: 'text-lg',
    heading: 'text-xl',
    spacing: 'space-y-5'
  }
};

// Add scrollbar showing styles
export const scrollbarShowStyles = `
  /* Always show scrollbars on hover */
  .scrollbar-show::-webkit-scrollbar,
  .overflow-auto::-webkit-scrollbar,
  div[style*="overflow"]::-webkit-scrollbar,
  div[class*="overflow"]::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    display: initial;
  }
  
  .scrollbar-show::-webkit-scrollbar-thumb,
  .overflow-auto::-webkit-scrollbar-thumb,
  div[style*="overflow"]::-webkit-scrollbar-thumb,
  div[class*="overflow"]::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
  }
  
  .scrollbar-show::-webkit-scrollbar-track,
  .overflow-auto::-webkit-scrollbar-track,
  div[style*="overflow"]::-webkit-scrollbar-track,
  div[class*="overflow"]::-webkit-scrollbar-track {
    background: transparent;
  }
  
  /* Initially hide scrollbars but show on hover */
  .hover-scrollbar {
    overflow: hidden auto;
    overflow-anchor: unset;
  }
  
  .hover-scrollbar:hover {
    overflow: auto;
  }
`;

export default function DashboardSettings({ textSize, onTextSizeChange }: DashboardSettingsProps) {
  return (
    <style>{scrollbarShowStyles}</style>
  );
}
