import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import <PERSON><PERSON>ield from '@/components/admin/FormField';
import PermissionTable, { Permission } from '@/components/admin/PermissionTable';

interface PermissionSetData {
  id: string;
  name: string;
  permissions: Permission[];
}

const PermissionSet: React.FC = () => {
  const [searchParams] = useSearchParams();
  const [selectedPermissionSet, setSelectedPermissionSet] = useState<PermissionSetData | null>(null);
  const [permissionSetName, setPermissionSetName] = useState('');
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [isNewPermissionSet, setIsNewPermissionSet] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Search states for table headers
  const [searchTable, setSearchTable] = useState('');
  const [searchColumn, setSearchColumn] = useState('');
  const [searchAccess, setSearchAccess] = useState('');

  // Mock data initialization
  useEffect(() => {
    const initializeData = async () => {
      setIsLoading(true);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Check URL parameters
      const permissionSetId = searchParams.get('id');
      const isNew = searchParams.get('new') === 'true';

      if (isNew) {
        handleNewPermissionSet();
      } else if (permissionSetId) {
        // Load permission set data based on ID
        const mockPermissionSet = {
          id: permissionSetId,
          name: permissionSetId === '1' ? 'Admin_PermissionSet' : 
                permissionSetId === '2' ? 'User_PermissionSet' : 'ReadOnly_PermissionSet',
          permissions: [
            {
              dbConnection: 'Conn_Databricks_Datawarehouse',
              table: 'Table1',
              column: 'Col1',
              access: 'Y' as const,
              maxRowsRead: '',
              tableLevelFilters: '',
              anonymise: '',
              anonymiseRule: ''
            },
            {
              dbConnection: 'Conn_Databricks_Datawarehouse',
              table: 'Table1',
              column: 'Col2',
              access: 'Y' as const,
              maxRowsRead: '',
              tableLevelFilters: '',
              anonymise: '',
              anonymiseRule: ''
            },
            {
              dbConnection: 'Conn_Databricks_Datawarehouse',
              table: 'Table1',
              column: 'Col3',
              access: 'N' as const,
              maxRowsRead: '',
              tableLevelFilters: '',
              anonymise: '',
              anonymiseRule: ''
            },
            {
              dbConnection: 'Conn_Databricks_Datawarehouse',
              table: 'Table1',
              column: 'Col4',
              access: '' as const,
              maxRowsRead: '',
              tableLevelFilters: '',
              anonymise: '',
              anonymiseRule: ''
            },
            {
              dbConnection: 'Conn_Databricks_Datawarehouse',
              table: 'Table2',
              column: 'Col1',
              access: 'Y' as const,
              maxRowsRead: '',
              tableLevelFilters: '',
              anonymise: '',
              anonymiseRule: ''
            },
            {
              dbConnection: 'Conn_Databricks_Datawarehouse',
              table: 'Table2',
              column: 'Col2',
              access: 'N' as const,
              maxRowsRead: '',
              tableLevelFilters: '',
              anonymise: '',
              anonymiseRule: ''
            },
            {
              dbConnection: 'Conn_Databricks_Datawarehouse',
              table: 'Table2',
              column: 'Col3',
              access: '' as const,
              maxRowsRead: '',
              tableLevelFilters: '',
              anonymise: '',
              anonymiseRule: ''
            },
            {
              dbConnection: 'Conn_Databricks_Datawarehouse',
              table: 'Table2',
              column: 'Col4',
              access: '' as const,
              maxRowsRead: '',
              tableLevelFilters: '',
              anonymise: '',
              anonymiseRule: ''
            }
          ]
        };
        
        setSelectedPermissionSet(mockPermissionSet);
        setPermissionSetName(mockPermissionSet.name);
        setPermissions(mockPermissionSet.permissions);
        setIsNewPermissionSet(false);
      }

      setIsLoading(false);
    };

    initializeData();
  }, [searchParams]);

  const handleNewPermissionSet = () => {
    setSelectedPermissionSet({ id: 'new', name: '', permissions: [] });
    setPermissionSetName('');
    setPermissions([]);
    setIsNewPermissionSet(true);
  };

  const handleSave = async () => {
    if (!permissionSetName.trim()) {
      toast.error('Please enter a permission set name');
      return;
    }

    try {
      if (isNewPermissionSet) {
        toast.success('Permission set created successfully');
      } else {
        toast.success('Permission set updated successfully');
      }
      
      // Update the current permission set
      if (selectedPermissionSet) {
        setSelectedPermissionSet({
          ...selectedPermissionSet,
          name: permissionSetName,
          permissions: permissions
        });
      }
    } catch (error) {
      toast.error('Failed to save permission set');
    }
  };

  const handleSaveAs = async () => {
    if (!permissionSetName.trim()) {
      toast.error('Please enter a permission set name');
      return;
    }

    try {
      toast.success('Permission set saved as new successfully');
    } catch (error) {
      toast.error('Failed to save permission set as new');
    }
  };

  const handlePermissionChange = (index: number, field: keyof Permission, value: string) => {
    const updatedPermissions = [...permissions];
    updatedPermissions[index] = { ...updatedPermissions[index], [field]: value };
    setPermissions(updatedPermissions);
  };

  // Filtering is now handled by the PermissionTable component

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading permission set...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-lg font-normal text-gray-700">
          {selectedPermissionSet && !isNewPermissionSet 
            ? `Edit Permission Set: ${selectedPermissionSet.name}` 
            : isNewPermissionSet 
            ? 'Create New Permission Set'
            : 'Permission Set'
          }
        </h2>
        <p className="text-sm text-gray-500 mt-1">
          {selectedPermissionSet && !isNewPermissionSet 
            ? 'Modify the permission set configuration below'
            : isNewPermissionSet 
            ? 'Configure a new permission set'
            : 'Select a permission set from the sidebar to edit, or create a new one'
          }
        </p>
      </div>

      {/* Form */}
      {selectedPermissionSet ? (
        <div className="space-y-6">
          {/* Permission Set Name */}
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
            <FormField
              label="Permission Set Name"
              value={permissionSetName}
              onChange={setPermissionSetName}
              placeholder="Enter permission set name"
              required
              error=""
              className="max-w-md"
            />
          </div>

          {/* Permissions Table */}
          <PermissionTable
            permissions={permissions}
            onPermissionChange={handlePermissionChange}
            searchTable={searchTable}
            searchColumn={searchColumn}
            searchAccess={searchAccess}
            onSearchTableChange={setSearchTable}
            onSearchColumnChange={setSearchColumn}
            onSearchAccessChange={setSearchAccess}
          />

          {/* Action Buttons */}
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
            <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
              <div className="flex justify-end space-x-3">
                <Button
                  onClick={handleSaveAs}
                  variant="outline"
                  className="px-6 py-2"
                >
                  Save As
                </Button>
                <Button
                  onClick={handleSave}
                  className="px-6 py-2 text-white hover:opacity-90 transition-opacity"
                  style={{ backgroundColor: 'rgb(0, 130, 130)' }}
                >
                  Save
                </Button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center h-64 text-gray-400">
          <div className="text-center">
            <p className="text-lg font-normal text-gray-600 mb-2">No permission set selected</p>
            <p className="text-sm text-gray-500">
              Choose a permission set from the sidebar to edit.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default PermissionSet;
