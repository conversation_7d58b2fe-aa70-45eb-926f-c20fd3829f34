
/**
 * Checks if the selected columns are from related tables
 */
export const checkIfRelationalData = (
  xAxisTable: string,
  yAxisTables: string[],
  xAxisColumn: string,
  yAxisColumns: string[]
): boolean => {
  // If X and Y axis columns are from different tables, it's relational
  if (xAxisTable && yAxisTables.length > 0 && xAxisTable !== yAxisTables[0]) {
    return true;
  }
  
  // If we have specific column types that indicate relational data
  if (xAxisColumn.includes('_id') || yAxisColumns.some(col => col.includes('_id'))) {
    return true;
  }
  
  return false;
};

/**
 * Enhanced function to handle cross-table relationships
 */
export const validateCrossTableRelationships = (
  xAxisTable: string,
  yAxisTables: string[],
  xAxisColumn: string,
  yAxisColumns: string[]
): { isValid: boolean; warnings: string[] } => {
  const warnings: string[] = [];
  let isValid = true;
  
  // Check if we have columns from multiple tables
  const uniqueTables = new Set([xAxisTable, ...yAxisTables]);
  
  if (uniqueTables.size > 1) {
    warnings.push('You are selecting columns from multiple tables. Ensure proper relationships exist.');
    
    // Check for common relationship patterns
    const hasIdColumns = xAxisColumn.includes('_id') || yAxisColumns.some(col => col.includes('_id'));
    if (!hasIdColumns) {
      warnings.push('No ID columns detected for cross-table relationships. Results may be unexpected.');
    }
  }
  
  return { isValid, warnings };
};
