import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';

import { AIButton } from '@/components/ui/ai-button';
import { toast } from 'sonner';
import SimplePagination from '@/components/ui/SimplePagination';
import { ChevronLeft, ChevronRight, Search, ScanLine, Trash2 } from 'lucide-react';
import { DatabaseService, MetadataService, DatabaseHelpers, type UpdateMetadataPayload } from '@/services/api/databaseService';

const MLZDataScanner: React.FC = () => {
  const [searchParams] = useSearchParams();
  const [selectedConnection, setSelectedConnection] = useState('');
  const [selectedConnectionName, setSelectedConnectionName] = useState('');
  const [connectionMetadata, setConnectionMetadata] = useState<any[]>([]);
  const [isLoadingConnection, setIsLoadingConnection] = useState(false);

  // Tables section state
  const [allTables, setAllTables] = useState<any[]>([]);
  const [currentTableIndex, setCurrentTableIndex] = useState(0);
  const [tableName, setTableName] = useState('Table Name');
  const [tableDescription, setTableDescription] = useState('Table Description');
  const [totalTables, setTotalTables] = useState(0);
  const [isLoadingTables, setIsLoadingTables] = useState(false);

  // Autocomplete search state
  const [searchQuery, setSearchQuery] = useState('');
  const [showDropdown, setShowDropdown] = useState(false);
  const [filteredTables, setFilteredTables] = useState<any[]>([]);

  // Column autocomplete states
  const [columnSearchQuery, setColumnSearchQuery] = useState('');
  const [showColumnDropdown, setShowColumnDropdown] = useState(false);
  const [filteredColumns, setFilteredColumns] = useState<any[]>([]);

  // Columns section state
  const [allColumns, setAllColumns] = useState<any[]>([]);
  const [currentColumnIndex, setCurrentColumnIndex] = useState(0);
  const [columnName, setColumnName] = useState('Column Name');
  const [columnDescription, setColumnDescription] = useState('Table Description');
  const [columnAlias, setColumnAlias] = useState('Column Name');
  const [columnDatatype, setColumnDatatype] = useState('String');
  const [isPrimaryKey, setIsPrimaryKey] = useState(false);
  const [isUniqueKey, setIsUniqueKey] = useState(false);

  // Scanner functionality state
  const [isScanning, setIsScanning] = useState(false);
  const [scanningStep, setScanningStep] = useState('');
  const [scanningProgress, setScanningProgress] = useState(0);
  const [storedTableData, setStoredTableData] = useState<any>(null);
  const [availableConnections, setAvailableConnections] = useState<any[]>([]);
  const [totalColumns, setTotalColumns] = useState(0);
  const [isLoadingColumns, setIsLoadingColumns] = useState(false);

  // Save functionality state
  const [isSaving, setIsSaving] = useState(false);





  // Simple pagination state for tables list
  const [tablesListPage, setTablesListPage] = useState(1);
  const tablesPerPage = 5;

  // Handle URL parameters for connection selection or load first connection
  useEffect(() => {
    const connectionId = searchParams.get('connectionId');
    const connectionName = searchParams.get('connectionName');

    console.log('🔍 URL params - connectionId:', connectionId);
    console.log('🔍 URL params - connectionName:', connectionName);

    if (connectionId && connectionName) {
      console.log('✅ Setting connection from URL params:', connectionId);
      setSelectedConnection(connectionId);
      setSelectedConnectionName(decodeURIComponent(connectionName));
      fetchConnectionDetails(connectionId);

      // Fetch tables for the selected connection
      fetchTablesForConnection(parseInt(connectionId));

      // toast.success(`Loaded connection: ${decodeURIComponent(connectionName)}`);
    } else {
      // No URL parameters - load first available connection
      fetchFirstAvailableConnection();
    }
  }, [searchParams]);

  // Fetch first available database connection
  const fetchFirstAvailableConnection = async () => {
    try {
      setIsLoadingConnection(true);
      const userId = '1'; // You may need to get this from auth context

      console.log('🔍 Fetching first available database connection...');
      const result = await DatabaseService.getUserConnections(userId);
      console.log('📊 Available connections:', result);

      if (result.status === 'success' && result.connections && result.connections.length > 0) {
        const firstConnection = result.connections[0];
        setAvailableConnections(result.connections);

        // Set first connection as selected
        console.log('🔍 First connection object:', firstConnection);
        console.log('🔍 First connection ID:', firstConnection.connection_id);
        console.log('🔍 First connection ID type:', typeof firstConnection.connection_id);

        const connectionIdString = firstConnection.connection_id.toString();
        console.log('🔍 Connection ID as string:', connectionIdString);

        setSelectedConnection(connectionIdString);
        setSelectedConnectionName(firstConnection.connection_name);

        console.log(`✅ Auto-loaded first connection: ${firstConnection.connection_name} (ID: ${connectionIdString})`);

        // Fetch connection details and existing metadata
        fetchConnectionDetails(firstConnection.connection_id.toString());

        // Fetch existing tables for the selected connection
        fetchTablesForConnection(firstConnection.connection_id);

        // toast.success(`Auto-loaded connection: ${firstConnection.connection_name}`);
      } else {
        console.log('⚠️ No database connections found');
        toast.info('No database connections available');
      }
    } catch (error) {
      console.error('❌ Error fetching first connection:', error);
      toast.error('Failed to load database connections');
    } finally {
      setIsLoadingConnection(false);
    }
  };

  // Fetch connection details from API
  const fetchConnectionDetails = async (connectionId: string) => {
    try {
      setIsLoadingConnection(true);

      console.log(`Fetching connection details for ID: ${connectionId}`);
      const result = await DatabaseService.getConnectionDetails(connectionId);
      console.log('Connection details fetched:', result);

      if (result.status === 'success' && result.connection) {
        const conn = result.connection;
        setSelectedConnectionName(conn.connection_name || 'Unknown Connection');
        // toast.success(`Connection details loaded: ${conn.connection_name}`);
      } else {
        toast.error('Failed to load connection details');
      }
    } catch (error) {
      console.error('Error fetching connection details:', error);
      toast.error('Failed to load connection details');
    } finally {
      setIsLoadingConnection(false);
    }
  };

  // Fetch tables for the selected connection
  const fetchTablesForConnection = async (connectionId: number) => {
    try {
      setIsLoadingTables(true);

      console.log(`🔍 Fetching tables for connection ID: ${connectionId}`);
      const data = await MetadataService.getMetadataByConnection(connectionId.toString());
      console.log(`📊 Received table data:`, data);

      if (Array.isArray(data) && data.length > 0) {
        setAllTables(data);
        setTotalTables(data.length);

        // Set first table as current
        if (data[0]) {
          setCurrentTableIndex(0);
          updateCurrentTable(0);
        }

        // toast.success(`Found ${data.length} tables`);
      } else {
        setAllTables([]);
        setTotalTables(0);
        setTableName('Table Name');
        setTableDescription('Table Description');
        toast.error('No tables found for this connection');
      }
    } catch (error) {
      console.error('Error fetching tables:', error);
      toast.error('Failed to fetch tables');
      setAllTables([]);
      setTotalTables(0);
    } finally {
      setIsLoadingTables(false);
    }
  };

  // Fetch metadata for selected connection
  const fetchConnectionMetadata = async (connectionId: string) => {
    try {
      setIsLoadingTables(true);
      const API_BASE_URL = 'http://***********:8001';
      console.log(`Fetching metadata for connection ID: ${connectionId}`);

      const response = await fetch(`${API_BASE_URL}/metadata/get_metadata_by_connection/${connectionId}`);

      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error Response:', errorText);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const result = await response.json();
      console.log('Raw API Response:', result);
      console.log('Response type:', typeof result);
      console.log('Is Array:', Array.isArray(result));

      if (result) {
        console.log('Result keys:', Object.keys(result));
        console.log('Result length:', result.length);
      }

      // Check if result has table_metadata field
      let tables = [];
      if (result && result.table_metadata && Array.isArray(result.table_metadata)) {
        tables = result.table_metadata;
        console.log('Found table_metadata array with length:', tables.length);
      } else if (result && Array.isArray(result)) {
        tables = result;
        console.log('Using direct array result with length:', tables.length);
      } else {
        console.log('No valid table data found in response');
      }

      if (tables.length > 0) {
        console.log('First table data:', tables[0]);
        console.log('Table_Name:', tables[0].Table_Name);
        console.log('Table_Description:', tables[0].Table_Description);

        setConnectionMetadata(tables);
        setAllTables(tables);
        setTotalTables(tables.length);

        // Set first table as current if tables exist
        setCurrentTableIndex(0);
        setTableName(tables[0].Table_Name || 'Table Name');
        setTableDescription(tables[0].Table_Description || 'Table Description');
        setSearchQuery(tables[0].Table_Name || ''); // Initialize search with first table name

        // Reset columns when new tables are loaded
        setAllColumns([]);
        setTotalColumns(0);
        setCurrentColumnIndex(0);
        setColumnName('Column Name');
        setColumnDescription('Table Description');
        setColumnAlias('Column Name');
        setColumnDatatype('String');
        setIsPrimaryKey(false);
        setIsUniqueKey(false);

        // Reset search dropdown
        setShowDropdown(false);
        setFilteredTables([]);

        // toast.success(`Loaded ${tables.length} tables. Use search to find tables.`);
      } else {
        console.log('No tables found in response');
        setAllTables([]);
        setTotalTables(0);
        setTableName('No tables found');
        setTableDescription('No description available');
        toast.info('No tables found for this connection');
      }
    } catch (error) {
      console.error('Error fetching connection metadata:', error);
      toast.error(`Failed to load connection metadata: ${error.message}`);
      setAllTables([]);
      setTotalTables(0);
      setTableName('Error loading tables');
      setTableDescription('Failed to load table data');
    } finally {
      setIsLoadingTables(false);
    }
  };

  // Pagination functions for tables
  const goToFirstTable = () => {
    if (allTables.length > 0) {
      setCurrentTableIndex(0);
      updateCurrentTable(0);
    }
  };

  const goToPreviousTable = () => {
    if (currentTableIndex > 0) {
      const newIndex = currentTableIndex - 1;
      setCurrentTableIndex(newIndex);
      updateCurrentTable(newIndex);
    }
  };

  const goToNextTable = () => {
    if (currentTableIndex < allTables.length - 1) {
      const newIndex = currentTableIndex + 1;
      setCurrentTableIndex(newIndex);
      updateCurrentTable(newIndex);
    }
  };

  const goToLastTable = () => {
    if (allTables.length > 0) {
      const lastIndex = allTables.length - 1;
      setCurrentTableIndex(lastIndex);
      updateCurrentTable(lastIndex);
    }
  };

  const goToTableByNumber = (tableNumber: number) => {
    const index = tableNumber - 1; // Convert 1-based to 0-based index
    if (index >= 0 && index < allTables.length) {
      setCurrentTableIndex(index);
      updateCurrentTable(index);
    }
  };

  // Function to patch columns for the current table
  const patchColumnsForCurrentTable = (currentTable: any) => {
    if (!currentTable) {
      // Reset columns when no table
      setAllColumns([]);
      setTotalColumns(0);
      setCurrentColumnIndex(0);
      setColumnName('Column Name');
      setColumnDescription('Table Description');
      setColumnAlias('Column Name');
      setColumnDatatype('String');
      setIsPrimaryKey(false);
      setIsUniqueKey(false);
      return;
    }

    if (!currentTable.Columns_List) {
      // No columns data available for this table
      setAllColumns([]);
      setTotalColumns(0);
      setCurrentColumnIndex(0);
      setColumnName('No columns found');
      setColumnDescription('No description available');
      setColumnAlias('No alias');
      setColumnDatatype('String');
      setIsPrimaryKey(false);
      setIsUniqueKey(false);
      return;
    }

    try {
      setIsLoadingColumns(true);
      console.log('Auto-patching columns for table:', currentTable.Table_Name);
      console.log('Columns_List:', currentTable.Columns_List);

      // Extract columns from Columns_List (which is an indexed object)
      const columnsData = currentTable.Columns_List;
      const columns = [];

      if (typeof columnsData === 'object' && columnsData !== null) {
        // Convert indexed object to array
        for (const key in columnsData) {
          if (columnsData.hasOwnProperty(key)) {
            const column = columnsData[key];
            columns.push({
              column_name: column, // Use the value directly
              data_type: 'String', // Default data type
              column_description: currentTable.Table_Description || 'Table Description',
              column_alias: currentTable.Table_Name || 'Table Name',
              is_nullable: false,
              default_value: null
            });
          }
        }
      }

      console.log('Auto-extracted columns:', columns);

      if (columns.length > 0) {
        setAllColumns(columns);
        setTotalColumns(columns.length);
        setCurrentColumnIndex(0);

        // Set the first column data
        const firstColumn = columns[0];
        setColumnName(firstColumn.column_name);
        setColumnDescription(firstColumn.column_description);
        setColumnAlias(firstColumn.column_alias);
        setColumnDatatype(firstColumn.data_type);

        // Check if first column is primary key or unique key
        const isPrimary = checkIfPrimaryKey(firstColumn.column_name, currentTable);
        const isUnique = checkIfUniqueKey(firstColumn.column_name, currentTable);
        setIsPrimaryKey(isPrimary);
        setIsUniqueKey(isUnique);

        console.log(`Auto-loaded ${columns.length} columns for table: ${currentTable.Table_Name}`);
      } else {
        setAllColumns([]);
        setTotalColumns(0);
        setColumnName('No columns found');
        setColumnDescription('No description available');
        setColumnAlias('No alias');
        setColumnDatatype('String');
        setIsPrimaryKey(false);
        setIsUniqueKey(false);
      }
    } catch (error) {
      console.error('Error auto-patching columns:', error);
      setAllColumns([]);
      setTotalColumns(0);
      setColumnName('Error loading columns');
      setColumnDescription('Failed to load column data');
      setColumnAlias('Error');
      setColumnDatatype('String');
      setIsPrimaryKey(false);
      setIsUniqueKey(false);
    } finally {
      setIsLoadingColumns(false);
    }
  };

  const updateCurrentTable = (index: number) => {
    if (allTables[index]) {
      const currentTable = allTables[index];
      setTableName(currentTable.Table_Name || 'Table Name');
      setTableDescription(currentTable.Table_Description || 'Table Description');
      setSearchQuery(currentTable.Table_Name || ''); // ← Update search query to show current table name

      // Automatically patch columns when table changes
      patchColumnsForCurrentTable(currentTable);
    }
  };

  // Pagination functions for columns
  const goToFirstColumn = () => {
    if (allColumns.length > 0) {
      setCurrentColumnIndex(0);
      updateCurrentColumn(0);
    }
  };

  const goToPreviousColumn = () => {
    if (currentColumnIndex > 0) {
      const newIndex = currentColumnIndex - 1;
      setCurrentColumnIndex(newIndex);
      updateCurrentColumn(newIndex);
    }
  };

  const goToNextColumn = () => {
    if (currentColumnIndex < allColumns.length - 1) {
      const newIndex = currentColumnIndex + 1;
      setCurrentColumnIndex(newIndex);
      updateCurrentColumn(newIndex);
    }
  };

  const goToLastColumn = () => {
    if (allColumns.length > 0) {
      const lastIndex = allColumns.length - 1;
      setCurrentColumnIndex(lastIndex);
      updateCurrentColumn(lastIndex);
    }
  };

  const goToColumnByNumber = (columnNumber: number) => {
    const index = columnNumber - 1; // Convert 1-based to 0-based index
    if (index >= 0 && index < allColumns.length) {
      setCurrentColumnIndex(index);
      updateCurrentColumn(index);
    }
  };

  const updateCurrentColumn = (index: number) => {
    if (allColumns[index]) {
      const column = allColumns[index];
      setColumnName(column.column_name || 'Column Name');
      setColumnDescription(column.column_description || 'Column Description');
      setColumnAlias(column.column_alias || 'Column Alias');
      setColumnDatatype(column.data_type || 'String');

      // Check if column is primary key or unique key
      const currentTable = allTables[currentTableIndex];
      if (currentTable) {
        const isPrimary = checkIfPrimaryKey(column.column_name, currentTable);
        const isUnique = checkIfUniqueKey(column.column_name, currentTable);
        setIsPrimaryKey(isPrimary);
        setIsUniqueKey(isUnique);
      }
    }
  };

  // Helper functions to check key types
  const checkIfPrimaryKey = (columnName: string, table: any): boolean => {
    if (table.Primary_Key_Columns) {
      // Handle both array and object formats
      if (Array.isArray(table.Primary_Key_Columns)) {
        return table.Primary_Key_Columns.includes(columnName);
      } else if (typeof table.Primary_Key_Columns === 'object') {
        return Object.values(table.Primary_Key_Columns).includes(columnName);
      }
    }
    return false;
  };

  const checkIfUniqueKey = (columnName: string, table: any): boolean => {
    if (table.Unique_Key_Columns) {
      // Handle both array and object formats
      if (Array.isArray(table.Unique_Key_Columns)) {
        return table.Unique_Key_Columns.includes(columnName);
      } else if (typeof table.Unique_Key_Columns === 'object') {
        return Object.values(table.Unique_Key_Columns).includes(columnName);
      }
    }
    return false;
  };

  // Autocomplete search functions
  const handleSearchChange = (value: string) => {
    // Update both search query and table name for dual functionality
    setSearchQuery(value);
    setTableName(value);

    if (value.trim() === '') {
      setFilteredTables([]);
      setShowDropdown(false);
      setTablesListPage(1); // Reset pagination
      return;
    }

    // Filter tables based on Table_Name
    const filtered = allTables.filter(table =>
      table.Table_Name &&
      table.Table_Name.toLowerCase().includes(value.toLowerCase())
    );

    setFilteredTables(filtered);
    setShowDropdown(filtered.length > 0);
    setTablesListPage(1); // Reset pagination when search changes
  };

  const handleTableSelect = (table: any, index: number) => {
    setSearchQuery(table.Table_Name);
    setCurrentTableIndex(index);
    setTableName(table.Table_Name);
    setTableDescription(table.Table_Description || 'Table Description');
    setShowDropdown(false);

    // Automatically patch columns when table is selected
    patchColumnsForCurrentTable(table);

    toast.success(`Selected table: ${table.Table_Name}`);
  };

  const handleSearchFocus = () => {
    if (searchQuery.trim() !== '' && filteredTables.length > 0) {
      setShowDropdown(true);
    }
  };

  const handleSearchBlur = () => {
    // Delay hiding dropdown to allow for clicks
    setTimeout(() => setShowDropdown(false), 200);
  };

  // Column search handlers
  const handleColumnSearchChange = (value: string) => {
    setColumnName(value);
    setColumnSearchQuery(value);

    if (value.trim() === '') {
      setFilteredColumns([]);
      setShowColumnDropdown(false);
      return;
    }

    const filtered = allColumns.filter(column =>
      column.name && column.name.toLowerCase().includes(value.toLowerCase())
    );

    setFilteredColumns(filtered);
    setShowColumnDropdown(filtered.length > 0);
  };

  const handleColumnSearchFocus = () => {
    if (allColumns.length > 0) {
      setFilteredColumns(allColumns);
      setShowColumnDropdown(true);
    }
  };

  const handleColumnSearchBlur = () => {
    setTimeout(() => setShowColumnDropdown(false), 200);
  };

  const handleColumnSelect = (column: any, index: number) => {
    setColumnName(column.name);
    setColumnDescription(column.description || 'Column Description');
    setColumnAlias(column.alias || column.name);
    setColumnDatatype(column.datatype || 'String');
    setIsPrimaryKey(column.isPrimaryKey || false);
    setIsUniqueKey(column.isUniqueKey || false);
    setCurrentColumnIndex(index);
    setShowColumnDropdown(false);
    setColumnSearchQuery(column.name);
  };



  const handleSave = async () => {
    // Call the save functionality
    await handleSaveClick();
  };

  const handleCancel = () => {
    console.log('Cancel clicked');
    // Add functionality here
  };

  // Patch MLZ Data Scanner page with GET API response data
  const patchDataFromGetResponse = async (getResult: any, connectionId: string) => {
    try {
      console.log('🔄 Starting data patching process...');
      console.log('📊 GET API response structure:', Object.keys(getResult));
      console.log('📊 GET API response type:', typeof getResult);
      console.log('📊 Is array?', Array.isArray(getResult));
      console.log('📊 Full response sample:', JSON.stringify(getResult, null, 2).substring(0, 500) + '...');

      // Check if response has table metadata
      let tables = [];
      if (Array.isArray(getResult)) {
        tables = getResult;
        console.log(`📋 Direct array response with ${tables.length} tables`);
      } else if (getResult.table_metadata && Array.isArray(getResult.table_metadata)) {
        tables = getResult.table_metadata;
        console.log(`📋 Found ${tables.length} tables in metadata response`);
      } else {
        console.error('❌ Invalid GET API response structure');
        console.log('❌ Expected array or object with table_metadata, got:', typeof getResult);
        toast.error('Invalid response format from server');
        return;
      }

      // Log detailed table structure
      if (tables.length > 0) {
        console.log('📋 First table structure:', {
          keys: Object.keys(tables[0]),
          Table_Id: tables[0].Table_Id,
          Table_Name: tables[0].Table_Name,
          Columns_List_type: typeof tables[0].Columns_List,
          Columns_List_keys: tables[0].Columns_List ? Object.keys(tables[0].Columns_List) : 'null'
        });
      }

      // Update tables data
      setAllTables(tables);
      setTotalTables(tables.length);
      setConnectionMetadata(tables);

      if (tables.length > 0) {
          // Set first table as current
          const firstTable = tables[0];
          setCurrentTableIndex(0);
          setTableName(firstTable.Table_Name || 'Table Name');
          setTableDescription(firstTable.Table_Description || 'Table Description');
          setSearchQuery(firstTable.Table_Name || '');

          console.log(`📋 Set current table: ${firstTable.Table_Name}`);

          // Update columns data if available
          if (firstTable.Columns_List) {
            const columnsData = firstTable.Columns_List;
            const columns = [];

            // Convert indexed object to array
            if (typeof columnsData === 'object' && !Array.isArray(columnsData)) {
              Object.keys(columnsData).forEach(key => {
                columns.push({
                  name: columnsData[key],
                  description: 'Column Description',
                  alias: columnsData[key],
                  datatype: 'String',
                  isPrimaryKey: false,
                  isUniqueKey: false
                });
              });
            }

            setAllColumns(columns);
            setTotalColumns(columns.length);

            if (columns.length > 0) {
              setCurrentColumnIndex(0);
              setColumnName(columns[0].name);
              setColumnDescription(columns[0].description);
              setColumnAlias(columns[0].alias);
              setColumnDatatype(columns[0].datatype);
              setIsPrimaryKey(columns[0].isPrimaryKey);
              setIsUniqueKey(columns[0].isUniqueKey);

            }
          }

          // toast.success(`Loaded ${tables.length} table(s) and updated MLZ Data Scanner`);
        } else {
          console.log('⚠️ No tables found in metadata response');
          toast.info('No tables found in updated metadata');
        }

    } catch (error) {
      toast.error('Failed to update page with metadata');
    }
  };

  // Handle Scanner button click - Call GET API first
  const handleScannerClick = async () => {
    if (!selectedConnection) {
      toast.error('No database connection selected');
      return;
    }

    try {
      setIsScanning(true);
      setScanningStep('Initializing MLZ Data Scanner...');
      setScanningProgress(10);
      toast.info('Starting MLZ data scanning...');

      // Step 1: GET correct table columns from get_tables_columns API
      setScanningStep('Step 1: Fetching table columns from database...');
      setScanningProgress(20);
      console.log('📡 Step 1: Getting correct table columns from get_tables_columns API');
      console.log(`🔍 selectedConnection value:`, selectedConnection);
      console.log(`🔍 selectedConnection type:`, typeof selectedConnection);

      // Validate connection ID (UUID string)
      if (!selectedConnection || selectedConnection === '' || selectedConnection === 'undefined') {
        throw new Error('No valid connection ID available. Please select a connection first.');
      }

      // For UUID connections, use the string directly
      console.log(`🌐 API URL: /metadata/get_tables_columns?connection_id=${selectedConnection}`);
      const getTablesColumnsResult = await MetadataService.getTablesColumnsQuery(selectedConnection);
      console.log('📊 GET tables columns API response:', getTablesColumnsResult);

      // Show success message with data count
      const dataCount = Array.isArray(getTablesColumnsResult) ? getTablesColumnsResult.length :
                       getTablesColumnsResult.table_list ? getTablesColumnsResult.table_list.length : 'unknown';

      setScanningProgress(30);
      toast.success(`✅ Step 1 - GET tables columns successful! Found ${dataCount} tables with columns`);

      // Step 2: DELETE existing metadata
      setScanningStep('Step 2: Clearing existing metadata...');
      setScanningProgress(40);
      console.log('📡 Step 2: Deleting existing metadata');
      const deleteResult = await MetadataService.deleteMetadataContentByConnection(selectedConnection);
      console.log('📊 DELETE API response:', deleteResult);
      setScanningProgress(50);
      toast.success('✅ Step 2 - DELETE API successful! Metadata content deleted');

      // Step 3: POST create AI metadata using correct table columns from Step 1
      setScanningStep('Step 3: Creating AI metadata (this may take 5-10 minutes)...');
      setScanningProgress(60);
      toast.info('🤖 Creating AI metadata - This process may take 5-10 minutes. Please wait...');
      console.log('📡 Step 3: Creating AI metadata using correct table columns from get_tables_columns API');
      console.log('🔄 Using get_tables_columns response as POST payload');
      const postRequestBody = DatabaseHelpers.createAIMetadataPayload(getTablesColumnsResult, selectedConnection);
      console.log('📊 POST API request body (using get_tables_columns response):', postRequestBody);

      // Use extended timeout for AI metadata creation
      const createResult = await MetadataService.createAIDatabaseMetadataWithTimeout(postRequestBody, 600000); // 10 minutes timeout
      setScanningProgress(90);
      toast.success('✅ POST API successful! AI database metadata created');
      try {
        setScanningStep('Step 4: Refreshing metadata display...');
        setScanningProgress(95);
        const metadataResult = await MetadataService.getMetadataByConnection(selectedConnection);
        console.log('✅ GET metadata API call successful:', metadataResult);

        // Update UI with metadata response using existing function
        await patchDataFromGetResponse(metadataResult, selectedConnection);
        setScanningProgress(100);
        setScanningStep('MLZ Scanner completed successfully!');
        toast.success('✅ MLZ Scanner completed! Updated table and column data');
      } catch (metadataError) {
        // Don't throw error, just show empty data
        setAllTables([]);
        setTotalTables(0);
        setTableName('No tables found');
        setTableDescription('No description available');
        setAllColumns([]);
        setTotalColumns(0);
        toast.warning('Could not load updated metadata - showing empty data');
      }

      console.log('🎉 MLZ Data Scanner 4-step workflow completed successfully');

    } catch (error) {
      console.error('❌ MLZ Scanner error:', error);
      setScanningStep('MLZ Scanner failed');
      setScanningProgress(0);

      // More specific error handling
      if (error.message && error.message.includes('timeout')) {
        toast.error('MLZ Scanner timed out. The AI metadata creation is taking longer than expected. Please try again or contact support.');
      } else if (error.message && error.message.includes('network')) {
        toast.error('Network error during MLZ scanning. Please check your connection and try again.');
      } else {
        toast.error(`MLZ Scanner failed: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again.`);
      }
    } finally {
      setIsScanning(false);
      // Reset progress after a delay if scanning failed
      setTimeout(() => {
        setScanningStep('');
        setScanningProgress(0);
      }, 3000);
    }
  };

  // Handle Save button click - Update metadata via PUT API
  const handleSaveClick = async () => {
    if (!selectedConnection) {
      toast.error('No database connection selected');
      return;
    }

    if (!allTables || allTables.length === 0) {
      toast.error('No table data available to save');
      return;
    }

    try {
      setIsSaving(true);
      console.log('💾 Starting save process for dynamically modified metadata...');
      console.log('📡 Connection ID:', selectedConnection);
      console.log('📊 Total tables:', allTables.length);
      console.log('🔄 Current table index:', currentTableIndex);
      console.log('� Current column index:', currentColumnIndex);
      console.log('�📝 Current form data (MODIFIED VALUES):');
      console.log('  - Table Name (MODIFIED):', tableName);
      console.log('  - Table Description (MODIFIED):', tableDescription);
      console.log('  - Column Name (MODIFIED):', columnName);
      console.log('  - Column Description (MODIFIED):', columnDescription);
      console.log('  - Column Alias (MODIFIED):', columnAlias);
      console.log('  - Column Datatype (MODIFIED):', columnDatatype);
      console.log('  - Is Primary Key (MODIFIED):', isPrimaryKey);
      console.log('  - Is Unique Key (MODIFIED):', isUniqueKey);
      console.log('📋 All columns data (ORIGINAL):', allColumns);
      console.log('🔄 Will update current table and column with modified values');

      // Prepare the request body with dynamically modified data from the page
      // Update current table with all form data (user modifications)
      const updatedTables = allTables.map((table, index) => {
        if (index === currentTableIndex) {
          // Update current table with all dynamically modified form data
          let updatedColumnsData = table.Columns_List || {};

          if (allColumns.length > 0) {
            // Update columns with current form data
            updatedColumnsData = allColumns.reduce((acc, col, colIndex) => {
              // Use the current column data from the form
              acc[colIndex.toString()] = col.column_name || col.name || `Column_${colIndex}`;
              return acc;
            }, {} as Record<string, string>);
          } else if (currentColumnIndex >= 0 && columnName && columnName !== 'Column Name') {
            // If we're editing a specific column, update that column
            updatedColumnsData = { ...table.Columns_List };
            updatedColumnsData[currentColumnIndex.toString()] = columnName;
          }

          // Ensure all required fields are present with proper defaults
          return {
            Table_Id: table.Table_Id || 0,
            Table_Name: tableName && tableName !== 'Table Name' ? tableName : table.Table_Name || 'Unnamed_Table',
            Table_Description: tableDescription && tableDescription !== 'Table Description' ? tableDescription : table.Table_Description || 'No description available',
            Primary_Key_Columns: table.Primary_Key_Columns || null,
            Foreign_Key_Columns: table.Foreign_Key_Columns || null,
            Custom_Table_Instructions: table.Custom_Table_Instructions || null,
            Table_Aliases: table.Table_Aliases || null,
            Columns_List: updatedColumnsData,
            Database_Dialect: table.Database_Dialect || 'postgresql',
            Database_Name: table.Database_Name || 'unknown_db',
            connection_id: selectedConnection
          };
        }

        // For non-current tables, ensure all required fields are present
        return {
          Table_Id: table.Table_Id || 0,
          Table_Name: table.Table_Name || 'Unnamed_Table',
          Table_Description: table.Table_Description || 'No description available',
          Primary_Key_Columns: table.Primary_Key_Columns || null,
          Foreign_Key_Columns: table.Foreign_Key_Columns || null,
          Custom_Table_Instructions: table.Custom_Table_Instructions || null,
          Table_Aliases: table.Table_Aliases || null,
          Columns_List: table.Columns_List || {},
          Database_Dialect: table.Database_Dialect || 'postgresql',
          Database_Name: table.Database_Name || 'unknown_db',
          connection_id: selectedConnection
        };
      });

      // Validate and prepare the final request body
      const validatedTables = updatedTables.filter(table => {
        const isValid = table.Table_Id && table.Table_Name && table.Columns_List;
        if (!isValid) {
          console.warn('⚠️ Invalid table data:', table);
        }
        return isValid;
      });

      if (validatedTables.length === 0) {
        toast.error('No valid table data to save');
        return;
      }

      // Prepare column metadata from all columns data
      const columnMetadata: any[] = [];

      // Generate column metadata for all tables
      validatedTables.forEach((table, tableIndex) => {
        if (table.Columns_List) {
          Object.entries(table.Columns_List).forEach(([columnIndex, columnName], colIdx) => {
            // If this is the current table and column being edited, use form values
            const isCurrentTableAndColumn = tableIndex === currentTableIndex && colIdx === currentColumnIndex;

            columnMetadata.push({
              Column_Id: columnMetadata.length + 1,
              Column_Name: isCurrentTableAndColumn ? columnName : columnName,
              Column_Description: isCurrentTableAndColumn ? columnDescription : `Description for ${columnName}`,
              Column_Example_Data: {
                "0": "Sample data 1",
                "1": "Sample data 2",
                "2": "Sample data 3"
              },
              Custom_Column_Instructions: null,
              Column_Aliases: isCurrentTableAndColumn ? columnAlias : null,
              Column_Table_Id: table.Table_Id,
              Column_Table_Name: table.Table_Name
            });
          });
        }
      });

      const requestBody = {
        status: "success",
        table_metadata: validatedTables.map(table => ({
          Table_Id: table.Table_Id,
          Table_Name: table.Table_Name,
          Table_Description: table.Table_Description,
          Primary_Key_Columns: table.Primary_Key_Columns,
          Foreign_Key_Columns: table.Foreign_Key_Columns,
          Custom_Table_Instructions: table.Custom_Table_Instructions,
          Table_Aliases: table.Table_Aliases,
          Columns_List: table.Columns_List,
          Database_Dialect: table.Database_Dialect,
          Database_Name: table.Database_Name,
          connection_id: selectedConnection
        })),
        column_metadata: columnMetadata,
        connection_id: selectedConnection
      };

      console.log('📤 Save request body:', JSON.stringify(requestBody, null, 2));
      console.log('🔍 Detailed payload analysis:');
      console.log('  - Status:', requestBody.status);
      console.log('  - Connection ID (root):', requestBody.connection_id);
      console.log('  - Number of tables:', requestBody.table_metadata.length);
      console.log('  - Number of columns:', requestBody.column_metadata.length);

      requestBody.table_metadata.forEach((table, index) => {
        console.log(`  - Table ${index}:`, {
          Table_Id: table.Table_Id,
          Table_Name: table.Table_Name,
          Table_Description: table.Table_Description?.substring(0, 50) + '...',
          Columns_List_Keys: Object.keys(table.Columns_List || {}),
          Columns_List_Count: Object.keys(table.Columns_List || {}).length,
          Database_Dialect: table.Database_Dialect,
          Database_Name: table.Database_Name,
          connection_id: table.connection_id
        });
      });

      requestBody.column_metadata.forEach((column, index) => {
        console.log(`  - Column ${index}:`, {
          Column_Id: column.Column_Id,
          Column_Name: column.Column_Name,
          Column_Description: column.Column_Description?.substring(0, 30) + '...',
          Column_Table_Id: column.Column_Table_Id,
          Column_Table_Name: column.Column_Table_Name,
          Column_Aliases: column.Column_Aliases
        });
      });

      toast.info('Saving metadata changes...');

      // Call the update metadata API
      const result = await MetadataService.updateMetadata(requestBody);
      console.log('✅ Save API response:', result);

      if (result.status === 'success') {
        toast.success(`✅ Successfully saved metadata for ${allTables.length} tables!`);

        // Call GET API to refresh the data after successful save
        try {
          console.log('🔄 Calling GET API to refresh data after save...');

          // Validate connection ID for GET API (UUID string)
          if (!selectedConnection || selectedConnection === '' || selectedConnection === 'undefined') {
            throw new Error(`Invalid connection ID for save refresh: "${selectedConnection}"`);
          }

          const metadataResult = await MetadataService.getMetadataByConnection(selectedConnection);
          console.log('✅ GET metadata API call successful after save:', metadataResult);

          // Update UI with fresh metadata from GET API
          await patchDataFromGetResponse(metadataResult, selectedConnection);
          console.log('🔄 Successfully refreshed data from GET API after save');
        } catch (getError) {
          console.warn('⚠️ GET API failed after save:', getError);
          // Still show success for save, but warn about refresh failure
          toast.warning('Save successful, but failed to refresh data. Please reload manually.');

          // Fallback: use the save response data if available
          if (result.table_metadata && Array.isArray(result.table_metadata)) {
            setAllTables(result.table_metadata);
            setTotalTables(result.table_metadata.length);
            console.log('🔄 Used save response data as fallback');
          }
        }
      } else {
        toast.error('Failed to save metadata - unexpected response format');
      }

    } catch (error) {
      console.error('❌ Save metadata error:', error);
      toast.error(`Save failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsSaving(false);
    }
  };







  // Pagination calculations for filtered tables dropdown
  const totalTablesPages = Math.ceil(filteredTables.length / tablesPerPage);
  const tablesStartIndex = (tablesListPage - 1) * tablesPerPage;
  const tablesEndIndex = tablesStartIndex + tablesPerPage;
  const paginatedFilteredTables = filteredTables.slice(tablesStartIndex, tablesEndIndex);

  // Pagination handlers
  const goToTablesPage = (page: number) => {
    if (page >= 1 && page <= totalTablesPages) {
      setTablesListPage(page);
    }
  };

  return (
    <div className="bg-white p-8 max-w-6xl mx-auto">
      {/* DB Connection Name Section */}
      <div className="mb-6">
        <div className="flex items-center gap-4 mb-4">
          <Label className="text-sm font-normal text-gray-700 w-40">DB Connection Name:</Label>
          <div className="flex-1 max-w-md">
            <Input
              value={isLoadingConnection ? 'Loading connection...' : selectedConnectionName || 'No connection selected'}
              readOnly
              placeholder="DB Connection Name"
              className={`w-full ${isLoadingConnection ? 'bg-gray-100' : 'bg-white'}`}
            />
          </div>
          {/* MLZ Data Scanner Button */}
          <div className="ml-2">
            <Button
              className="bg-blue-600 text-white hover:bg-blue-700 px-4 py-2 flex items-center gap-2"
              disabled={!selectedConnectionName || isLoadingConnection || isScanning}
              onClick={handleScannerClick}
            >
              <ScanLine className="w-4 h-4" />
              {isScanning ? 'Scanning...' : 'MetaData Scanner'}
            </Button>

            {/* Progress Indicator */}
            {isScanning && (
              <div className="mt-2 w-64">
                <div className="flex justify-between text-xs text-gray-600 mb-1">
                  <span>{scanningStep}</span>
                  <span>{scanningProgress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${scanningProgress}%` }}
                  ></div>
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {scanningProgress >= 60 && scanningProgress < 90 && (
                    <span className="text-orange-600">⏳ AI metadata creation may take 5-10 minutes...</span>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>




      </div>

      {/* Tables Section */}
      <div className="mb-6 border border-gray-300 rounded">
        <div className="bg-gray-100 px-4 py-2 border-b border-gray-300">
          <div className="flex items-center justify-between">
            <span className="text-sm font-normal text-gray-700">Tables</span>
            <Trash2 className="h-4 w-4 text-gray-500 hover:text-red-500 cursor-pointer" />
          </div>
        </div>

        <div className="p-4">
          <div className="grid grid-cols-12 gap-4 items-center mb-6">
            <Label className="col-span-2 text-sm font-normal text-gray-700 flex items-center h-full">Table Name:</Label>
            <div className="col-span-8 relative">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <Input
                  value={isLoadingTables ? 'Loading table...' : tableName}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  onFocus={handleSearchFocus}
                  onBlur={handleSearchBlur}
                  placeholder="Search or edit table name..."
                  className="w-full pl-12 py-3 text-base font-medium border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
                  disabled={isLoadingTables || allTables.length === 0}
                />
              </div>

              {/* Autocomplete Dropdown */}
              {showDropdown && filteredTables.length > 0 && (
                <div className="absolute top-full left-0 right-0 z-50 bg-white border border-gray-300 rounded-md shadow-lg max-h-80 overflow-hidden">
                  <div className="max-h-60 overflow-y-auto">
                    {paginatedFilteredTables.map((table) => (
                      <div
                        key={table.Table_Name}
                        className="px-3 py-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0"
                        onClick={() => handleTableSelect(table, allTables.findIndex(t => t.Table_Name === table.Table_Name))}
                      >
                        <div className="font-medium text-sm">{table.Table_Name}</div>
                        <div className="text-xs text-gray-500 truncate">
                          {table.Table_Description || 'No description'}
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Standard Pagination Controls for Dropdown */}
                  {totalTablesPages > 1 && (
                    <div className="border-t border-gray-200 bg-gray-50">
                      <div className="px-3 py-1">
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-xs text-gray-500">
                            Showing {paginatedFilteredTables.length} of {filteredTables.length} tables
                          </span>
                        </div>
                        <SimplePagination
                          currentPage={tablesListPage}
                          totalPages={totalTablesPages}
                          onPageChange={goToTablesPage}
                          showItemsPerPage={false}
                        />
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

          </div>



          <div className="grid grid-cols-12 gap-4 items-center mb-6">
            <Label className="col-span-2 text-sm font-normal text-gray-700 flex items-center h-full">Table Description:</Label>
            <div className="col-span-9">
              <textarea
                value={isLoadingTables ? 'Loading description...' : tableDescription}
                onChange={(e) => setTableDescription(e.target.value)}
                disabled={isLoadingTables}
                className={`w-full h-24 px-4 py-3 text-base font-medium border-2 border-gray-300 rounded-lg resize-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200 ${isLoadingTables ? 'bg-gray-100' : 'bg-white'}`}
              />
            </div>
            <div className="col-span-1 flex items-center justify-center h-full">
              <AIButton
                onClick={() => toast.info('AI functionality coming soon!')}
                disabled={isLoadingTables}
                size="md"
              />
            </div>
          </div>





          {/* Table Pagination */}
          <div className="flex justify-end items-center">
            <SimplePagination
              currentPage={currentTableIndex + 1}
              totalPages={totalTables}
              onPageChange={(page: number) => {
                const index = page - 1;
                if (index >= 0 && index < totalTables) {
                  setCurrentTableIndex(index);
                  updateCurrentTable(index);
                }
              }}
              showItemsPerPage={false}
            />
          </div>
        </div>
      </div>

      {/* Columns Section */}
      <div className="mb-6 border border-gray-300 rounded">
        <div className="bg-gray-100 px-4 py-2 border-b border-gray-300">
          <div className="flex items-center justify-between">
            <span className="text-sm font-normal text-gray-700">Columns</span>
            <Trash2 className="h-4 w-4 text-gray-500 hover:text-red-500 cursor-pointer" />
          </div>
        </div>

        <div className="p-4">
          <div className="grid grid-cols-12 gap-4 items-center mb-6">
            <Label className="col-span-2 text-sm font-normal text-gray-700 flex items-center h-full">Column Name:</Label>
            <div className="col-span-6 relative">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <Input
                  value={isLoadingColumns ? 'Loading columns...' : columnName}
                  onChange={(e) => handleColumnSearchChange(e.target.value)}
                  onFocus={handleColumnSearchFocus}
                  onBlur={handleColumnSearchBlur}
                  placeholder="Search or edit column name..."
                  className={`w-full pl-12 py-3 text-base font-medium border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200 ${isLoadingColumns ? 'bg-gray-100' : 'bg-white'}`}
                  disabled={isLoadingColumns}
                />
              </div>

              {/* Column Autocomplete Dropdown */}
              {showColumnDropdown && filteredColumns.length > 0 && (
                <div className="absolute top-full left-0 right-0 z-50 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                  {filteredColumns.map((column, index) => (
                    <div
                      key={column.name}
                      className="px-3 py-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0"
                      onClick={() => handleColumnSelect(column, allColumns.findIndex(c => c.name === column.name))}
                    >
                      <div className="font-medium text-sm">{column.name}</div>
                      <div className="text-xs text-gray-500 truncate">
                        {column.description || 'No description'} • {column.datatype || 'String'}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="col-span-3 flex items-center space-x-8">
              <div className="flex items-center space-x-2">
                <div className="relative">
                  <input
                    type="checkbox"
                    checked={isPrimaryKey}
                    disabled
                    className="h-4 w-4 rounded border-2 border-gray-300 bg-white checked:bg-blue-500 checked:border-blue-500 disabled:opacity-50 focus:ring-2 focus:ring-blue-200"
                  />
                  {isPrimaryKey && (
                    <svg className="absolute top-0 left-0 h-4 w-4 text-white pointer-events-none" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
                <Label className="text-sm font-medium text-gray-700">Primary Key</Label>
              </div>
              <div className="flex items-center space-x-2">
                <div className="relative">
                  <input
                    type="checkbox"
                    checked={isUniqueKey}
                    disabled
                    className="h-4 w-4 rounded border-2 border-gray-300 bg-white checked:bg-blue-500 checked:border-blue-500 disabled:opacity-50 focus:ring-2 focus:ring-blue-200"
                  />
                  {isUniqueKey && (
                    <svg className="absolute top-0 left-0 h-4 w-4 text-white pointer-events-none" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
                <Label className="text-sm font-medium text-gray-700">Unique Key</Label>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-12 gap-4 items-center mb-6">
            <Label className="col-span-2 text-sm font-normal text-gray-700 flex items-center h-full">Column Description:</Label>
            <div className="col-span-9">
              <textarea
                value={isLoadingColumns ? 'Loading description...' : columnDescription}
                onChange={(e) => setColumnDescription(e.target.value)}
                className={`w-full h-20 px-4 py-3 text-base font-medium border-2 border-gray-300 rounded-lg resize-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200 ${isLoadingColumns ? 'bg-gray-100' : 'bg-white'}`}
                disabled={isLoadingColumns}
              />
            </div>
            <div className="col-span-1 flex items-center justify-center h-full">
              <AIButton
                onClick={() => toast.info('AI functionality coming soon!')}
                disabled={isLoadingColumns}
                size="md"
              />
            </div>
          </div>

          <div className="grid grid-cols-12 gap-4 items-center mb-6">
            <Label className="col-span-2 text-sm font-normal text-gray-700 flex items-center h-full">Column Alias:</Label>
            <div className="col-span-10">
              <Input
                value={isLoadingColumns ? 'Loading alias...' : columnAlias}
                onChange={(e) => setColumnAlias(e.target.value)}
                className={`w-full py-3 px-4 text-base font-medium border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200 ${isLoadingColumns ? 'bg-gray-100' : 'bg-white'}`}
                disabled={isLoadingColumns}
              />
            </div>
          </div>

          <div className="grid grid-cols-12 gap-4 items-center mb-6">
            <Label className="col-span-2 text-sm font-normal text-gray-700 flex items-center h-full">Column Datatype:</Label>
            <div className="col-span-4">
              <Input
                value={isLoadingColumns ? 'Loading...' : columnDatatype}
                onChange={(e) => setColumnDatatype(e.target.value)}
                className={`w-full py-3 px-4 text-base font-medium border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200 ${isLoadingColumns ? 'bg-gray-100' : 'bg-white'}`}
                disabled={isLoadingColumns}
              />
            </div>

          </div>

          {/* Column Pagination */}
          <div className="flex justify-end items-center">
            <SimplePagination
              currentPage={currentColumnIndex + 1}
              totalPages={totalColumns}
              onPageChange={(page: number) => {
                const index = page - 1;
                if (index >= 0 && index < totalColumns) {
                  setCurrentColumnIndex(index);
                  updateCurrentColumn(index);
                }
              }}
              showItemsPerPage={false}
            />
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end gap-3">
        <Button
          onClick={handleSave}
          disabled={!selectedConnectionName || isLoadingConnection || isSaving || allTables.length === 0}
          className="bg-gray-600 text-white hover:bg-gray-700 px-8 py-2"
        >
          {isSaving ? 'Saving...' : 'Save'}
        </Button>


        <Button
          onClick={handleCancel}
          variant="outline"
          className="px-8 py-2"
        >
          Cancel
        </Button>
      </div>


    </div>
  );
};

export default MLZDataScanner;
