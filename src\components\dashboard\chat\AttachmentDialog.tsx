
import React from "react"
import { <PERSON>clip, FileSpreadsheet, Loader2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"

interface AttachmentDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  handleFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  isFileUploadMode?: boolean;
  isUploading?: boolean;
}

const AttachmentDialog = ({
  isOpen,
  onOpenChange,
  handleFileChange,
  isFileUploadMode = false,
  isUploading = false
}: AttachmentDialogProps) => {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{isFileUploadMode ? 'Upload Data Files' : 'Upload File'}</DialogTitle>
          {isFileUploadMode && (
            <DialogDescription>
              Upload CSV or Excel files for data analysis. Supported formats: .csv, .xlsx, .xls
            </DialogDescription>
          )}
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-lg p-12">
            {isUploading ? (
              <>
                <Loader2 size={48} className="text-blue-500 mb-4 animate-spin" />
                <p className="text-sm text-blue-600 mb-2">
                  {isFileUploadMode ? "Uploading files..." : "Processing files..."}
                </p>
              </>
            ) : (
              <>
                {isFileUploadMode ? (
                  <FileSpreadsheet className="mb-4 text-blue-400" size={32} />
                ) : (
                  <Paperclip className="mb-4 text-gray-400" size={24} />
                )}
                <p className="text-sm text-gray-500 mb-2">
                  {isFileUploadMode
                    ? "Drag and drop CSV or Excel files here"
                    : "Drag and drop files here or click to browse"}
                </p>
                {isFileUploadMode && (
                  <p className="text-xs text-gray-400 mb-2">
                    Files will be uploaded automatically after selection
                  </p>
                )}
              </>
            )}
            <input
              type="file"
              id="file-upload"
              multiple
              className="hidden"
              onChange={handleFileChange}
              accept={isFileUploadMode ? ".csv,.xlsx,.xls" : undefined}
              disabled={isUploading}
            />
            <Button
              type="button"
              variant="outline"
              onClick={() => document.getElementById('file-upload')?.click()}
              disabled={isUploading}
            >
              {isUploading ? (
                <>
                  <Loader2 size={16} className="mr-2 animate-spin" />
                  Uploading...
                </>
              ) : (
                "Browse Files"
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AttachmentDialog;



