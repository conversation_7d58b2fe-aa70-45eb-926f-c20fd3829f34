import React from 'react';
import KPIPreview from './KPIPreview';
import { KPIFormData, KPIPreviewData } from './types/kpiTypes';

interface KPIBuilderPreviewProps {
  formData: KPIFormData;
  previewData?: KPIPreviewData;
  isLoading: boolean;
}

const KPIBuilderPreview: React.FC<KPIBuilderPreviewProps> = ({
  formData,
  previewData,
  isLoading
}) => {
  return (
    <div className="space-y-4">
      {/* Preview */}
      <KPIPreview
        formData={formData}
        previewData={previewData}
        isLoading={isLoading}
      />
    </div>
  );
};

export default KPIBuilderPreview;
