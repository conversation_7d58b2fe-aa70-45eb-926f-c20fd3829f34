# KPI Save Functionality

## Overview
The KPI Builder now supports saving KPIs with different request body formats based on the selected KPI type.

## API Endpoint
- **URL**: `/kpi/save`
- **Method**: `POST`
- **Content-Type**: `application/json`

## Request Body Formats

### 1. KPI Only
```json
{
  "name": "string",
  "kpi_label": "string", 
  "sql": "string",
  "additional_info": "string"
}
```

### 2. Current vs Prior
```json
{
  "name": "string",
  "additional_info": "string",
  "kpi_prior_label": "string",
  "kpi_current_label": "string", 
  "kpi_current_sql": "string",
  "kpi_prior_sql": "string"
}
```

### 3. Target Based
```json
{
  "name": "string",
  "kpi_label": "string",
  "sql": "string", 
  "additional_info": "string",
  "kpi_target": 0
}
```

## Response Format
```json
{
  "success": boolean,
  "message": "string",
  "kpi_id": "string" // optional
}
```

## Usage

### 1. User Flow
1. User fills out KPI form fields
2. User clicks "Execute" to test the KPI
3. After successful execution, "Save" button becomes enabled
4. User clicks "Save" button
5. Save dialog opens asking for KPI name
6. User enters name and clicks "Save KPI"
7. API call is made with appropriate request body format
8. Success/error message is shown

### 2. Component Integration
The save functionality is integrated into:
- `KPIBuilder.tsx` - Main component with save dialog
- `KPISaveDialog.tsx` - Modal dialog for entering KPI name
- `useKPIExecution.ts` - Hook handling save logic
- `kpiService.ts` - API service for save calls

### 3. Validation
- KPI name is required (3-100 characters)
- KPI must be successfully executed before saving
- All required form fields must be filled
- SQL validation must pass

## Files Modified
- `src/services/api/chart/kpiService.ts` - Added save API function
- `src/components/dashboard/kpi/KPISaveDialog.tsx` - New save dialog component
- `src/components/dashboard/kpi/hooks/useKPIExecution.ts` - Added save functionality
- `src/components/dashboard/kpi/KPIBuilder.tsx` - Integrated save dialog
