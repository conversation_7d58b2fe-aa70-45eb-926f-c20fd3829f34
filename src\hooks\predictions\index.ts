
import { useState } from 'react';
import { usePowerModeState } from './usePowerModeState';
import { usePredictionFetching } from './usePredictionFetching';
import { usePredictionSelection } from './usePredictionSelection';
import { UsePredictionsProps, UsePredictionsReturn } from './types';

/**
 * Main predictions hook that combines power mode state, prediction fetching, and prediction selection
 */
export const usePredictions = ({ 
  inputValue, 
  dashboardType = 3, 
  powerModeEnabled = false 
}: UsePredictionsProps): UsePredictionsReturn => {
  // Determine if predictions should be enabled based on dashboard type (only for DADA - type 3)
  const isPredictionsEnabled = dashboardType === 3;
  
  // Manage power mode state
  const { isPowerModeActive, enablePowerMode, togglePowerMode } = usePowerModeState(powerModeEnabled);
  
  // Manage predictions fetching
  const [predictions, setPredictions] = useState<string[]>([]);
  const {
    predictions: fetchedPredictions,
    showPredictions,
    setShowPredictions,
    isChainSequence,
    chainStage,
    setChainStage
  } = usePredictionFetching({
    inputValue,
    isPredictionsEnabled,
    isPowerModeActive
  });
  
  // Use fetched predictions
  if (fetchedPredictions !== predictions) {
    setPredictions(fetchedPredictions);
  }
  
  // Manage prediction selection
  const {
    lastSelectedPrediction,
    handlePredictionSelect
  } = usePredictionSelection({
    isChainSequence,
    chainStage,
    setChainStage,
    setPredictions,
    setShowPredictions
  });
  
  return {
    predictions,
    showPredictions,
    setShowPredictions,
    isChainSequence,
    chainStage,
    lastSelectedPrediction,
    handlePredictionSelect,
    isPredictionsEnabled,
    enablePowerMode,
    togglePowerMode
  };
};

export * from './types';
