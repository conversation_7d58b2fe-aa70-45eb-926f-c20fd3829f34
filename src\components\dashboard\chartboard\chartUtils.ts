
// Re-export all functions from the new utility modules for backward compatibility
export { 
  identify<PERSON><PERSON><PERSON><PERSON><PERSON>,
  getExistingChartData
} from '@/utils/chart/dataProcessing';

export { 
  createTablesArray,
  createChartConfig
} from '@/utils/chart/configGeneration';

export { 
  checkIfRelationalData,
  validateCrossTableRelationships
} from '@/utils/chart/validation';

export { 
  getChartTitle,
  prepareChartData,
  getBaseChartOptions
} from '@/utils/chart/rendering';

// Keep the original fetchChartDataHelper for backward compatibility
import { fetchChartData } from '@/services/api/chart/chartService';
import { getExistingChartData } from '@/utils/chart/dataProcessing';
import { toast } from 'sonner';

export const fetchChartDataHelper = async (
  connectionId: string,
  tables: any[],
  setLoading?: (loading: boolean) => void,
  xAxisColumn?: string,
  yAxisParams?: any[],
  groupBy?: string
) => {
  if (!connectionId) {
    console.error('fetchChartDataHelper: No connection ID provided');
    toast.error('No connection selected', {
      description: 'Please select a database connection'
    });
    return null;
  }
  
  if (!tables || tables.length === 0) {
    console.error('fetchChartDataHelper: No tables provided');
    toast.error('No tables selected', {
      description: 'Please select tables to fetch data from'
    });
    return null;
  }
  
  try {
    if (setLoading) setLoading(true);
    
    console.log('fetchChartDataHelper: Checking for existing chart data');
    
    // Check if we have existing chart data
    const existingData = getExistingChartData();
    if (existingData) {
      return existingData;
    }
    
    console.log('fetchChartDataHelper: Executing chart with params:', {
      connectionId,
      tables,
      xAxisColumn,
      yAxisParams,
      groupBy
    });
    
    // Call the service function with all parameters
    const data = await fetchChartData(
      connectionId, 
      tables,
      xAxisColumn,
      yAxisParams,
      groupBy
    );
    
    if (!data) {
      console.error('fetchChartDataHelper: No data returned from API');
      toast.error('No data returned from server');
      return null;
    }
    
    if (!Array.isArray(data)) {
      console.error('fetchChartDataHelper: Invalid data format - not an array:', data);
      
      // Try to extract data if it's in a nested structure
      if (data && typeof data === 'object' && 'data' in data && Array.isArray((data as any).data)) {
        console.log('fetchChartDataHelper: Extracted data from nested structure');
        return (data as any).data;
      }
      
      toast.error('Invalid data format', {
        description: 'The data returned from the server is not in the expected format'
      });
      return null;
    }
    
    console.log('fetchChartDataHelper: Data fetched successfully:', data.length, 'items');
    return data;
  } catch (error) {
    console.error('Error fetching chart data:', error);
    toast.error('Failed to fetch chart data', {
      description: error instanceof Error ? error.message : 'Unknown error'
    });
    return null;
  } finally {
    if (setLoading) setLoading(false);
  }
};
