
import { useState, useEffect, useCallback, useMemo } from 'react';
import { ChatTopic, Meeting, Project } from '@/components/dashboard/sidebar/types';
import { 
  getInitialChatTopics, 
  toggleChatTopic, 
  addChatToChatTopic, 
  addNewChatTopic, 
  filterChatTopics 
} from './sidebar/chatTopicsUtils';
import { 
  getInitialMeetings, 
  toggleMeeting, 
  addNewMeeting, 
  filterMeetings 
} from './sidebar/meetingsUtils';
import { 
  getInitialProjects, 
  toggleProject, 
  addChatToProject, 
  addNewProject, 
  filterProjects 
} from './sidebar/projectsUtils';

interface UseSidebarItemsHook {
  chatTopics: ChatTopic[];
  meetings: Meeting[];
  projects: Project[];
  filteredItems: ChatTopic[] | Meeting[] | Project[];
  setChatTopics: React.Dispatch<React.SetStateAction<ChatTopic[]>>;
  setMeetings: React.Dispatch<React.SetStateAction<Meeting[]>>;
  setProjects: React.Dispatch<React.SetStateAction<Project[]>>;
  setFilteredItems: React.Dispatch<React.SetStateAction<ChatTopic[] | Meeting[] | Project[]>>;
  toggleItem: (itemId: string) => void;
  addChatToItem: (itemId: string) => void;
  addNewItem: () => void;
  filterItems: (searchQuery: string, dashboardType: 1 | 2 | 3) => void;
}

export function useSidebarItems(dashboardType: 1 | 2 | 3): UseSidebarItemsHook {
  // Use lazy state initialization to avoid unnecessary re-computation
  const [chatTopics, setChatTopics] = useState<ChatTopic[]>(() => getInitialChatTopics());
  const [meetings, setMeetings] = useState<Meeting[]>(() => getInitialMeetings());
  const [projects, setProjects] = useState<Project[]>(() => getInitialProjects());
  const [filteredItems, setFilteredItems] = useState<ChatTopic[] | Meeting[] | Project[]>([]);
  
  // Initialize filtered items based on dashboard type only once on mount
  useEffect(() => {
    // Set initial items based on dashboard type
    switch (dashboardType) {
      case 1:
        setFilteredItems(chatTopics);
        break;
      case 2:
        setFilteredItems(meetings);
        break;
      case 3:
        setFilteredItems(projects);
        break;
      default:
        setFilteredItems([]);
    }
  }, [dashboardType]);

  // Memoized toggle function to maintain stable reference
  const toggleItem = useCallback((itemId: string) => {
    switch (dashboardType) {
      case 1:
        setChatTopics(prevTopics => toggleChatTopic(prevTopics, itemId));
        break;
      case 2:
        setMeetings(prevMeetings => toggleMeeting(prevMeetings, itemId));
        break;
      case 3:
        setProjects(prevProjects => toggleProject(prevProjects, itemId));
        break;
    }
  }, [dashboardType]);

  // Memoized addChat function
  const addChatToItem = useCallback((itemId: string) => {
    switch (dashboardType) {
      case 1:
        setChatTopics(prevTopics => addChatToChatTopic(prevTopics, itemId));
        break;
      case 3:
        setProjects(prevProjects => addChatToProject(prevProjects, itemId));
        break;
    }
  }, [dashboardType]);

  // Memoized addNewItem function
  const addNewItem = useCallback(() => {
    switch (dashboardType) {
      case 1:
        setChatTopics(prevTopics => addNewChatTopic(prevTopics));
        break;
      case 2:
        setMeetings(prevMeetings => addNewMeeting(prevMeetings));
        break;
      case 3:
        setProjects(prevProjects => addNewProject(prevProjects));
        break;
    }
  }, [dashboardType]);

  // Debounce search with useCallback
  const filterItems = useCallback((searchQuery: string, type: 1 | 2 | 3) => {
    // Don't re-filter if there's no search query
    if (!searchQuery.trim()) {
      switch (type) {
        case 1:
          setFilteredItems(chatTopics);
          break;
        case 2:
          setFilteredItems(meetings);
          break;
        case 3:
          setFilteredItems(projects);
          break;
        default:
          setFilteredItems([]);
      }
      return;
    }

    // Apply appropriate filter based on dashboard type
    switch (type) {
      case 1:
        setFilteredItems(filterChatTopics(chatTopics, searchQuery));
        break;
      case 2:
        setFilteredItems(filterMeetings(meetings, searchQuery));
        break;
      case 3:
        setFilteredItems(filterProjects(projects, searchQuery));
        break;
    }
  }, [chatTopics, meetings, projects]);

  return {
    chatTopics,
    meetings,
    projects,
    filteredItems,
    setChatTopics,
    setMeetings,
    setProjects,
    setFilteredItems,
    toggleItem,
    addChatToItem,
    addNewItem,
    filterItems
  };
}
