
import React from "react"
import { Image, Paperclip } from "lucide-react"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { But<PERSON> } from "@/components/ui/button"

interface AttachmentButtonProps {
  onOpenAttachmentDialog: () => void;
}

const AttachmentButton = ({ onOpenAttachmentDialog }: AttachmentButtonProps) => {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button 
          type="button" 
          size="icon" 
          variant="ghost" 
          className="h-8 w-8"
        >
          <Paperclip size={18} />
        </Button>
      </PopoverTrigger>
      <PopoverContent side="top" className="w-48 p-2">
        <div className="flex flex-col gap-2">
          <Button 
            type="button" 
            variant="outline" 
            className="justify-start" 
            onClick={onOpenAttachmentDialog}
          >
            <Paperclip size={16} className="mr-2" /> 
            Attach file
          </Button>
          <Button 
            type="button" 
            variant="outline" 
            className="justify-start"
            onClick={onOpenAttachmentDialog}
          >
            <Image size={16} className="mr-2" /> 
            Add image
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default AttachmentButton;
