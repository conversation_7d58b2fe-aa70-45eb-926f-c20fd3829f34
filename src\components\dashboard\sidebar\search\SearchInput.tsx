
import React from 'react';
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";

interface SearchInputProps {
  searchQuery: string;
  handleSearch: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder: string;
}

const SearchInput: React.FC<SearchInputProps> = ({
  searchQuery,
  handleSearch,
  placeholder
}) => {
  return (
    <div className="relative flex-1">
      <Search className="absolute left-2 top-1.5 h-4 w-4 text-gray-500" />
      <Input
        placeholder={placeholder}
        value={searchQuery}
        onChange={handleSearch}
        className="pl-8 pr-2 h-8 min-h-[32px] text-sm"
      />
    </div>
  );
};

export default SearchInput;
