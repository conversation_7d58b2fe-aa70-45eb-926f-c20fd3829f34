import React from 'react';
import StandardSQLInput from '@/components/shared/StandardSQLInput';
import { SQLValidationResult } from '@/hooks/shared/useSQLInput';

interface KPISQLInputProps {
  value: string;
  onChange: (value: string) => void;
  onValidate?: (sql: string) => Promise<SQLValidationResult>;
  onBlur?: () => void;
  label: string;
  required?: boolean;
  placeholder?: string;
  error?: string;
  viewType?: string;
  fieldKey?: string;
}

const KPISQLInput: React.FC<KPISQLInputProps> = ({
  value,
  onChange,
  onValidate,
  onBlur,
  label,
  required = false,
  placeholder = "Enter your SQL query here...",
  error,
  viewType,
  fieldKey
}) => {
  // Create unique key to force component remount when view changes
  const componentKey = `${viewType}-${fieldKey}`;
  
  return (
    <StandardSQLInput
      key={componentKey}
      value={value}
      onChange={onChange}
      onValidate={onValidate}
      onBlur={onBlur}
      label={label}
      required={required}
      placeholder={placeholder}
      rows={4}
      className="font-mono text-sm"
      error={error}
    />
  );
};

export default KPISQLInput;