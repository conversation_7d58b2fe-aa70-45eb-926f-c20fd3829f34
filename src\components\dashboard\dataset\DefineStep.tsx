
import React from 'react';
import { useAppDispatch, useAppSelector } from '@/hooks/useRedux';
import { updateDatasetDefinition } from '@/stores/datasetSlice';
import StepHeader, { NavigationButtons } from './StepHeader';

interface DefineStepProps {
  onClose: () => void;
}

const DefineStep: React.FC<DefineStepProps> = ({ onClose }) => {
  const dispatch = useAppDispatch();
  const { datasetName, datasetDescription, destinationConnection, schemaName } = useAppSelector(state => state.dataset);

  return (
    <div className="p-4">
      <StepHeader 
        title="Define Dataset" 
        onClose={onClose}
      />
      
      <div className="border border-gray-200 p-4 rounded-lg mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium mb-1">Dataset Name:</label>
            <input
              type="text"
              className="w-full p-2 border border-gray-300 rounded-md"
              value={datasetName}
              onChange={(e) => dispatch(updateDatasetDefinition({ name: e.target.value }))}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Destination Connection:</label>
            <select
              className="w-full p-2 border border-gray-300 rounded-md"
              value={destinationConnection}
              onChange={(e) => dispatch(updateDatasetDefinition({ connection: e.target.value }))}
            >
              <option value="">Select Connection</option>
              <option value="Connection1">Connection1</option>
              <option value="Connection2">Connection2</option>
              <option value="Connection3">Connection3</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Schema Name:</label>
            <select
              className="w-full p-2 border border-gray-300 rounded-md"
              value={schemaName}
              onChange={(e) => dispatch(updateDatasetDefinition({ schema: e.target.value }))}
            >
              <option value="">Select Schema</option>
              <option value="Schema1">Schema1</option>
              <option value="Schema2">Schema2</option>
              <option value="Schema3">Schema3</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Dataset Description:</label>
            <textarea
              className="w-full p-2 border border-gray-300 rounded-md"
              rows={4}
              value={datasetDescription}
              onChange={(e) => dispatch(updateDatasetDefinition({ description: e.target.value }))}
            />
          </div>
        </div>
      </div>
      
      {/* Add navigation buttons at the bottom */}
      <div className="mt-6">
        <NavigationButtons
          showPrevious={true}
          showNext={true}
          disableNext={!datasetName || !destinationConnection || !schemaName}
        />
      </div>
    </div>
  );
};

export default DefineStep;
