
import React from "react"
import { Paperclip, X } from "lucide-react"

interface FilePreviewProps {
  files: File[];
  removeFile: (index: number) => void;
}

const FilePreview = ({ files, removeFile }: FilePreviewProps) => {
  if (files.length === 0) return null;
  
  return (
    <div className="w-full mb-2 flex flex-wrap gap-2">
      {files.map((file, index) => (
        <div key={index} className="flex items-center bg-gray-100 rounded-md p-1 pr-2">
          <Paperclip size={14} className="mr-1" />
          <span className="text-xs mr-1 max-w-[150px] truncate">{file.name}</span>
          <button 
            type="button" 
            onClick={() => removeFile(index)}
            className="text-gray-500 hover:text-gray-700 text-xs"
          >
            <X size={14} />
          </button>
        </div>
      ))}
    </div>
  );
};

export default FilePreview;
