
import React, { useState, useEffect } from 'react';
import { Database, ChevronDown, PanelLeftClose, Loader2 } from 'lucide-react';
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { useAppSelector, useAppDispatch } from '@/hooks/useRedux';
import { 
  setSelectedDatabase, 
  setConnectionId, 
  setIsConnecting 
} from '@/stores/chartSlice';
import { fetchDatabases, connectToDatabase } from '@/services/api/chart/databaseService';
import { DatabaseConnectionParams } from '@/services/api/chart/chartTypes';
import { toast } from 'sonner';
import { useConnectionManager } from '@/hooks/useConnectionManager';

interface DatabaseConnectionDropdownProps {
  onToggleSidebar: () => void;
}

const DatabaseConnectionDropdown: React.FC<DatabaseConnectionDropdownProps> = ({ 
  onToggleSidebar 
}) => {
  // Redux state and dispatch
  const dispatch = useAppDispatch();
  const selectedDatabase = useAppSelector(state => (state as any).chart?.selectedDatabase || '');
  const isConnecting = useAppSelector(state => (state as any).chart?.isConnecting || false);
  
  // Connection manager hook
  const { getOrCreateConnection, hasValidConnectionForDatabase } = useConnectionManager();
  
  // Local state
  const [databases, setDatabases] = useState<string[]>([]);
  const [isLoadingDatabases, setIsLoadingDatabases] = useState(false);

  // Fetch available databases on component mount
  useEffect(() => {
    const loadDatabases = async () => {
      try {
        setIsLoadingDatabases(true);
        const dbList = await fetchDatabases();
        setDatabases(dbList);
      } catch (error) {
        console.error('Failed to load databases:', error);
        toast.error('Failed to load databases', {
          description: 'Could not retrieve available databases'
        });
      } finally {
        setIsLoadingDatabases(false);
      }
    };
    
    loadDatabases();
  }, []);

  // Handle database selection - now reuses existing connections
  const handleDatabaseSelect = async (db: string) => {
    console.log("Selected database:", db);
    
    try {
      dispatch(setIsConnecting(true));
      dispatch(setSelectedDatabase(db));
      
      // Check if we have a valid existing connection for this database
      if (hasValidConnectionForDatabase(db)) {
        console.log('Reusing existing connection for database:', db);
        // Get the existing connection - this will reuse it
        const connectionId = await getOrCreateConnection(db, true);
        if (connectionId) {
          toast.success(`Connected to ${db}`, {
            description: `Using existing connection: ${connectionId.substring(0, 8)}...`
          });
        }
      } else {
        console.log('Creating new connection for database:', db);
        // Create new connection
        const connectionId = await getOrCreateConnection(db, false);
        if (connectionId) {
          toast.success(`Connected to ${db}`, {
            description: `New connection created: ${connectionId.substring(0, 8)}...`
          });
        } else {
          toast.error(`Failed to connect to ${db}`);
        }
      }
    } catch (error) {
      console.error('Error connecting to database:', error);
      toast.error('Connection error', {
        description: error instanceof Error ? error.message : 'Failed to connect to database'
      });
    } finally {
      dispatch(setIsConnecting(false));
    }
  };

  return (
    <div className="mb-2 flex items-center gap-2 group">
      <DropdownMenu.Root>
        <DropdownMenu.Trigger asChild>
          <button
            className="flex-1 flex items-center text-xs text-white bg-blue-500 hover:bg-blue-600 rounded px-2 py-1 w-full justify-between"
            disabled={isConnecting}
            data-testid="database-dropdown-trigger"
          >
            <div className="flex items-center">
              <Database size={12} className="mr-1" />
              {isConnecting ? (
                <span className="flex items-center">
                  <Loader2 size={12} className="mr-1 animate-spin" />
                  Connecting...
                </span>
              ) : (
                <span>{selectedDatabase || "Select Database"}</span>
              )}
            </div>
            <ChevronDown size={12} className="ml-1" />
          </button>
        </DropdownMenu.Trigger>

        <DropdownMenu.Content
          className="mt-1 w-36 bg-white border border-gray-200 rounded shadow-lg z-10"
          sideOffset={5}
        >
          {isLoadingDatabases ? (
            <div className="flex items-center justify-center p-2">
              <Loader2 size={14} className="animate-spin text-blue-500 mr-2" />
              <span className="text-xs text-gray-500">Loading...</span>
            </div>
          ) : databases.length > 0 ? (
            databases.map((db) => (
              <DropdownMenu.Item
                key={db}
                onSelect={() => handleDatabaseSelect(db)}
                className={`block w-full text-left px-3 py-1.5 text-xs cursor-pointer ${
                  selectedDatabase === db ? "bg-gray-100 font-medium" : "hover:bg-gray-50"
                }`}
                data-testid={`database-option-${db}`}
              >
                {db}
              </DropdownMenu.Item>
            ))
          ) : (
            <DropdownMenu.Item className="block w-full text-left px-3 py-1.5 text-xs text-gray-500">
              No databases available
            </DropdownMenu.Item>
          )}
        </DropdownMenu.Content>
      </DropdownMenu.Root>
      
      {/* Toggle sidebar button - now with opacity transition */}
      <button 
        onClick={onToggleSidebar}
        className="flex-shrink-0 w-8 h-8 rounded-md flex items-center justify-center hover:bg-gray-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
        aria-label="Toggle sidebar"
        data-testid="toggle-sidebar-button"
      >
        <PanelLeftClose size={16} />
      </button>
    </div>
  );
};

export default DatabaseConnectionDropdown;
