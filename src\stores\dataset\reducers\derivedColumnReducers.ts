
import { PayloadAction } from '@reduxjs/toolkit';
import { DatasetState, DerivedColumn } from '../types';

export const derivedColumnReducers = {
  addDerivedColumn: (state: DatasetState, action: PayloadAction<DerivedColumn>) => {
    state.derivedColumns.push(action.payload);
  },
  
  updateDerivedColumn: (state: DatasetState, action: PayloadAction<{index: number; column: DerivedColumn}>) => {
    const { index, column } = action.payload;
    if (index >= 0 && index < state.derivedColumns.length) {
      state.derivedColumns[index] = column;
    }
  },
  
  removeDerivedColumn: (state: DatasetState, action: PayloadAction<number>) => {
    state.derivedColumns.splice(action.payload, 1);
  },
};
