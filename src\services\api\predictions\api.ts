
import { debounce } from "lodash";
import { PredictionParams } from "./types";
import { store } from '@/stores/store';
import { setError } from '@/stores/errorSlice';
import { getMockPredictions } from "./mockData";
import { formatQueryParams } from "./utils";

// Configure the base URL for the API
const API_BASE_URL = import.meta.env.VITE_DADA_API_URL;

// Function to get text predictions from the backend API
export const getPredictions = async ({
  query,
  powerKeyword,
  command,
  parameter
}: PredictionParams): Promise<string[]> => {
  if (!query) {
    return [];
  }

  try {
    // Build query parameters using our formatter
    const params = formatQueryParams({
      query,
      powerKeyword,
      command,
      parameter
    });

    console.log("Fetching predictions with URL params:", params.toString());
    
    const response = await fetch(`${API_BASE_URL}/predict?${params.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Server responded with status ${response.status}`);
    }

    const data = await response.json();
    console.log("API prediction response:", data);
    
    // Ensure we always return an array
    return Array.isArray(data) ? data : [];
  } catch (error) {
    console.error("Error fetching predictions:", error);
    
    // Dispatch error to show in ErrorDialog
    store.dispatch(setError({
      message: "Failed to fetch predictions. Please try again later.",
      statusCode: 500
    }));
    
    return [];
  }
};

// Create a debounced version of the getPredictions function
// This prevents the infinite recursion by properly using lodash debounce without custom extensions
export const debouncedGetPredictions = debounce(
  (params: PredictionParams) => getPredictions(params),
  100
);

// We're not adding any custom methods to the debounced function
// as they were causing the infinite recursion
