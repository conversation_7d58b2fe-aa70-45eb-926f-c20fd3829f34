
import datasetReducer, {
  // Navigation actions
  setShowDatasetScreen, 
  setCurrentStep,
  setStepNumber,
  setSearchQuery,
  resetDatasetState,
  nextStep,
  previousStep,
  
  // Connection actions
  toggleConnectionSelection,
  addToSelectedConnections,
  removeFromSelectedConnections,
  
  // Table actions
  toggleTableSelection,
  addToSelectedTables,
  removeFromSelectedTables,
  
  // Column actions
  addColumn,
  removeColumn,
  
  // Derived column actions
  addDerivedColumn,
  updateDerivedColumn,
  removeDerivedColumn,
  
  // Filter actions
  addFilterCondition,
  updateFilterCondition,
  removeFilterCondition,
  
  // Dataset definition actions
  updateDatasetDefinition,
} from './dataset/actions';

// Re-export types
export type { 
  DatasetStep, 
  DatasetState,
  ColumnDefinition,
  DerivedColumn,
  FilterCondition
} from './dataset/types';

// Re-export actions
export {
  // Navigation actions
  setShowDatasetScreen, 
  setCurrentStep,
  setStepNumber,
  setSearch<PERSON>uery,
  resetDatasetState,
  nextStep,
  previousStep,
  
  // Connection actions
  toggleConnectionSelection,
  addToSelectedConnections,
  removeFromSelectedConnections,
  
  // Table actions
  toggleTableSelection,
  addToSelectedTables,
  removeFromSelectedTables,
  
  // Column actions
  addColumn,
  removeColumn,
  
  // Derived column actions
  addDerivedColumn,
  updateDerivedColumn,
  removeDerivedColumn,
  
  // Filter actions
  addFilterCondition,
  updateFilterCondition,
  removeFilterCondition,
  
  // Dataset definition actions
  updateDatasetDefinition,
};

export default datasetReducer;
