import { QueryResultData } from '@/components/dashboard/models';
import { toast } from 'sonner';

// Configure the base URL for the API
const API_BASE_URL = import.meta.env.VITE_DADA_API_URL;

export const sendQueryRequest = async (prompt: string): Promise<QueryResultData> => {
  try {
    console.log(`Sending query request to ${API_BASE_URL}/api/query with prompt: ${prompt}`);

    // Call the backend API
    const response = await fetch(`${API_BASE_URL}/api/query?nl_question=${encodeURIComponent(prompt)}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
      },
    });

    // Check if the response is OK
    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      const errorMessage = errorData?.detail || `Server responded with status ${response.status}`;
      throw new Error(errorMessage);
    }

    // Parse the response
    const data = await response.json();
    console.log("Received API response:", data);

    // Return the data in the format expected by the frontend
    return {
      query: data.query,
      tableData: {
        columns: data.columns,
        rows: data.rows,
      },
    };
  } catch (error) {
    let errorMessage = "Failed to process your query";

    if (error instanceof Error) {
      errorMessage = error.message;
    }

    console.error("API Error:", errorMessage);
    throw error; // Let the error propagate to be handled by the thunk
  }
};

// New function to export query results to CSV
export const exportToCSV = async (prompt: string): Promise<string> => {
  try {
      // Configure the base URL for the API
    const API_BASE_URL =  import.meta.env.VITE_DADA_API_URL;
    console.log(`Sending export request to ${API_BASE_URL}/api/export with prompt: ${prompt}`);

    const response = await fetch(`${API_BASE_URL}/api/export?nl_question=${encodeURIComponent(prompt)}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Accept: "text/csv",
      },
    });

    if (!response.ok) {
      const errorData = await response.text();
      const errorMessage = errorData || `Server responded with status ${response.status}`;
      throw new Error(errorMessage);
    }

    const blob = await response.blob();
    
    const contentDisposition = response.headers.get('Content-Disposition');
    let filename = 'query_result.csv';
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
      if (filenameMatch && filenameMatch[1]) {
        filename = filenameMatch[1].replace(/['"]/g, '');
      }
    }
    
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    window.URL.revokeObjectURL(url);
    
    return filename;
  } catch (error) {
    let errorMessage = "Failed to export data";
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    console.error("Export API Error:", errorMessage);
    throw error;
  }
};

// New function to regenerate query
export const regenerateQuery = async (prompt: string): Promise<QueryResultData> => {
  try {
   // Configure the base URL for the API
     const API_BASE_URL =  import.meta.env.VITE_DADA_API_URL;
    console.log(`Sending regenerate request to ${API_BASE_URL}/api/regenerate with prompt: ${prompt}`);

    // Call the backend API
    const response = await fetch(`${API_BASE_URL}/api/regenerate?nl_question=${encodeURIComponent(prompt)}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
      },
    });

    // Check if the response is OK
    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      const errorMessage = errorData?.detail || `Server responded with status ${response.status}`;
      throw new Error(errorMessage);
    }

    // Parse the response
    const data = await response.json();

    console.log("Received API regenerate response:", data);

    // Return the data in the format expected by the frontend
    return {
      query: data.query,
      tableData: {
        columns: data.columns,
        rows: data.rows,
      },
    };
  } catch (error) {
    let errorMessage = "Failed to regenerate query";

    if (error instanceof Error) {
      errorMessage = error.message;
    }

    console.error("Regenerate API Error:", errorMessage);
    throw error; // Let the error propagate
  }
};
