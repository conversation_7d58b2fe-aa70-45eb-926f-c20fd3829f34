import React from 'react';
import { Shield, Check } from 'lucide-react';
import { cn } from '@/lib/utils'; // Assuming you have shadcn's cn utility
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface VerifiedShieldIconProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * The overall size of the icon (width and height).
   * @default 24
   */
  size?: number;
  /**
   * Tailwind CSS class for the fill color of the shield.
   * @default "fill-green-500"
   */
  shieldColor?: string;
  /**
   * Tailwind CSS class for the color of the checkmark.
   * @example "text-white"
   * @default "text-white"
   */
  checkColor?: string;
  /**
   * Tailwind CSS class for the stroke color of the shield.
   * @example "stroke-green-700"
   * @default "stroke-green-700"
   */
  strokeColor?: string; // New prop for stroke color
  /**
   * Optional className for the root div element.
   */
  className?: string;
  /**
   * The text to display in the tooltip.
   * @default "Verified"
   */
  tooltipText?: string; // New prop for tooltip text
}

const VerifiedIcon: React.FC<VerifiedShieldIconProps> = ({
  size = 20,
  shieldColor = 'fill-green-500',
  checkColor = 'text-white',
  strokeColor = 'stroke-green-700', // Default stroke color to a darker green
  className,
  tooltipText = 'Verified', // Default tooltip text
  ...props
}) => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          {/* The div that contains the icon and triggers the tooltip */}
          <div
            className={cn("relative inline-flex items-center justify-center", className)}
            style={{ width: size, height: size }}
            {...props}
          >
            {/* Shield icon */}
            <Shield
              size={size}
              // Apply both fill and stroke colors
              className={cn(shieldColor, strokeColor)}
              strokeWidth={0} // Adjust stroke width for the outline
            />

            {/* Checkmark icon - position it absolutely on top of the shield */}
            <Check
              size={size * 0.6} // Make the checkmark smaller relative to the shield
              className={cn("absolute", checkColor)}
              strokeWidth={3} // Make the checkmark bolder
              style={{
                // Fine-tune positioning if necessary
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
              }}
            />
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>{tooltipText}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default VerifiedIcon;