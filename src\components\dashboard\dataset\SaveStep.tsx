
import React, { useState } from 'react';
import { Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useAppDispatch, useAppSelector } from '@/hooks/useRedux';
import { resetDatasetState, setShowDatasetScreen } from '@/stores/datasetSlice';
import StepHeader, { NavigationButtons } from './StepHeader';
import { SuccessDialog } from '@/components/ui/success-dialog';

interface SaveStepProps {
  onClose: () => void;
}

const SaveStep: React.FC<SaveStepProps> = ({ onClose }) => {
  const dispatch = useAppDispatch();
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const { datasetName } = useAppSelector(state => state.dataset);

  const handleComplete = () => {
    // Show success dialog
    setShowSuccessDialog(true);
  };

  const handleCloseSuccessDialog = () => {
    setShowSuccessDialog(false);
    
    // Reset the dataset state and close the screen
    dispatch(resetDatasetState());
    dispatch(setShowDatasetScreen(false));
  };

  return (
    <div className="p-4">
      <StepHeader 
        title="Save Dataset" 
        onClose={onClose}
      />
      
      <div className="max-w-md mx-auto my-12">
        <Card>
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                <Check className="text-green-600" size={32} />
              </div>
            </div>
            <CardTitle>Dataset Ready</CardTitle>
            <CardDescription>
              Your dataset "{datasetName}" is ready to be created.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-gray-600">
              Click the button below to finalize and save your dataset. Once saved,
              it will be available for querying and analysis.
            </p>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button 
              onClick={handleComplete}
              className="w-50"
            >
              Export As Python code
            </Button>
            <Button 
              onClick={handleComplete}
              className="w-50"
            >
              Export As Sql code
            </Button>
          </CardFooter>
        </Card>
      </div>
      
      {/* Add navigation buttons at the bottom */}
      <div className="mt-6">
        <NavigationButtons
          showPrevious={true}
          showNext={false}
        />
      </div>
      
      {/* Success Dialog */}
      <SuccessDialog
        open={showSuccessDialog}
        onOpenChange={setShowSuccessDialog}
        onClose={handleCloseSuccessDialog}
        title="Dataset Created Successfully!"
        message={`Your dataset "${datasetName}" has been successfully created and is ready to use. You can access it from the Datasets section.`}
      />
    </div>
  );
};

export default SaveStep;
