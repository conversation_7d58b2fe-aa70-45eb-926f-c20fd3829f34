import React, { useState, useEffect } from 'react';
import { Edit, Eraser } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useDataGrid } from '@/hooks/datagrid';
import { useConnectionManager } from '@/hooks/useConnectionManager';
import { SelectedColumn } from '@/services/api/chart/datagrid/datagridTypes';
import { FilterCondition } from '@/components/dashboard/chartboard/FilterSection';
import DataGridTable from './DataGridTable';
import DataGridColumnSelector from './DataGridColumnSelector';
import DataGridSQLMode from './DataGridSQLMode';
import DataGridFilters from './DataGridFilters';

interface DataGridBuilderProps {
  // No props needed - DataGrid manages its own state
}


const DataGridBuilder: React.FC<DataGridBuilderProps> = () => {
  const [selectedColumns, setSelectedColumns] = useState<SelectedColumn[]>([]);
  const [filterConditions, setFilterConditions] = useState<FilterCondition[]>([{ column: '', operator: '=', value: '' }]);
  const [showSQLMode, setShowSQLMode] = useState(false);
  const [sqlQuery, setSqlQuery] = useState<string>('');
  const [sqlValidationResult, setSqlValidationResult] = useState<{ isValid: boolean; message?: string } | null>(null);
  const [isValidating, setIsValidating] = useState(false);
  const { data, loading, error, executeDataGrid, executeDataGridSQL, validateSQL, clearData, clearError } = useDataGrid();
  const { getCurrentConnection } = useConnectionManager('chart');

  // Clear any existing data when component mounts to prevent showing chart data
  useEffect(() => {
    clearData();
    clearError();
  }, [clearData, clearError]);

  // Clear validation result when SQL query is cleared
  useEffect(() => {
    if (sqlQuery.trim().length === 0 && sqlValidationResult) {
      setSqlValidationResult(null);
    }
  }, [sqlQuery, sqlValidationResult]);

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    
    // Try to get column data from different formats
    let columnName = '';
    let columnType = '';
    let tableName = '';
    
    // First try to get individual properties
    columnName = e.dataTransfer.getData('column-name');
    columnType = e.dataTransfer.getData('column-type');
    tableName = e.dataTransfer.getData('table-name');
    
    // If that fails, try to get JSON data
    if (!columnName) {
      try {
        const jsonData = e.dataTransfer.getData('application/json');
        if (jsonData) {
          const columnData = JSON.parse(jsonData);
          columnName = columnData.name;
          columnType = columnData.type;
          tableName = columnData.tableName;
        }
      } catch (error) {
        console.error('Error parsing JSON data:', error);
      }
    }
    
    // If all else fails, use plain text
    if (!columnName) {
      columnName = e.dataTransfer.getData('text/plain');
    }
    
    if (columnName && !selectedColumns.some(col => col.name === columnName)) {
      setSelectedColumns(prev => [...prev, {
        name: columnName,
        type: columnType,
        tableName: tableName
      }]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };





  const handleRemoveColumn = (columnName: string) => {
    setSelectedColumns(prev => prev.filter(col => col.name !== columnName));
    // Clear data when columns are modified
    if (data.length > 0) {
      clearData();
    }
  };

  const handleValidateSQL = async (sql: string) => {
    setIsValidating(true);
    try {
      const result = await validateSQL(sql);
      setSqlValidationResult(result);
      return result;
    } catch (error) {
      const errorResult = {
        isValid: false,
        message: error instanceof Error ? error.message : 'Validation error occurred'
      };
      setSqlValidationResult(errorResult);
      return errorResult;
    } finally {
      setIsValidating(false);
    }
  };

  // Helper function to get connection ID
  const getConnectionId = () => {
    // Try to get connection from hook first
    let connectionId = getCurrentConnection();

    // Fallback: try to get from session storage directly
    if (!connectionId) {
      try {
        connectionId = sessionStorage.getItem('chartbuilder_session_connection');
        console.log('DataGrid: Using session storage connection:', connectionId);
      } catch (error) {
        console.error('DataGrid: Failed to get session connection:', error);
      }
    }

    return connectionId;
  };

  // Handle preview data for column-based mode
  const handlePreviewDataColumns = async () => {
    const connectionId = getConnectionId();

    if (!connectionId) {
      console.error('No active connection found');
      return;
    }

    if (selectedColumns.length === 0) {
      console.error('No columns selected');
      return;
    }

    try {
      clearError();
      // Use column-based execution with filter support
      await executeDataGrid(
        selectedColumns,
        connectionId,
        filterConditions
      );
    } catch (error) {
      console.error('Error executing DataGrid:', error);
    }
  };

  // Handle preview data for SQL mode
  const handlePreviewDataSQL = async () => {
    const connectionId = getConnectionId();

    if (!connectionId) {
      console.error('No active connection found');
      return;
    }

    if (!sqlQuery.trim()) {
      console.error('No SQL query provided');
      return;
    }

    if (!sqlValidationResult?.isValid) {
      console.error('SQL query is not valid');
      return;
    }

    try {
      clearError();
      // Use SQL-based execution
      await executeDataGridSQL(sqlQuery, connectionId);
    } catch (error) {
      console.error('Error executing SQL DataGrid:', error);
    }
  };

  return (
    <div className="space-y-3">
      {/* Show SQL Mode or Normal Mode */}
      {showSQLMode ? (
        /* SQL Query Mode - Only show SQL input */
        <DataGridSQLMode
          sqlQuery={sqlQuery}
          onSQLChange={setSqlQuery}
          onValidateSQL={handleValidateSQL}
          validationResult={sqlValidationResult}
          isValidating={isValidating}
          onExitSQLMode={() => setShowSQLMode(false)}
        />
      ) : (
        /* Normal Mode - Show drag & drop and Write SQL Query button */
        <>
          {/* Write SQL Query Button - Outside drag area */}
          <div className="flex justify-end mb-3">
            <Button
              size="sm"
              onClick={() => {
                setShowSQLMode(true);
                clearData(); // Clear existing table data when switching to SQL mode
                clearError(); // Clear any existing errors
              }}
              className="text-sm px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white"
            >
              <Edit className="w-4 h-4 mr-2" />
              Write SQL Query
            </Button>
          </div>

          {/* Drag and Drop Area */}
          <DataGridColumnSelector
            selectedColumns={selectedColumns}
            onColumnAdd={(column) => setSelectedColumns(prev => [...prev, column])}
            onColumnRemove={handleRemoveColumn}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
          />
        </>
      )}



      {/* Filter Section - Only show when columns are selected and not in SQL mode */}
      <DataGridFilters
        filterConditions={filterConditions}
        onFilterChange={setFilterConditions}
        onDragOver={handleDragOver}
        visible={!showSQLMode && selectedColumns.length > 0}
      />

      {/* Clear and Preview Data Buttons - Only show in normal mode */}
      {!showSQLMode && (
        <div className="flex justify-start items-center gap-3 mt-4">
          {/* Clear button - only show when there are selected columns */}
          {selectedColumns.length > 0 && (
            <Button
              onClick={() => {
                setSelectedColumns([]);
                clearData();
                clearError();
              }}
              variant="outline"
              size="default"
              className="px-4 py-2 text-sm font-medium border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              Clear
            </Button>
          )}

          <Button
            onClick={handlePreviewDataColumns}
            disabled={selectedColumns.length === 0 || loading}
            className={`w-auto px-6 py-2 text-base font-medium ${
              selectedColumns.length === 0 || loading
                ? 'bg-gray-400 cursor-not-allowed text-gray-600'
                : 'bg-blue-600 hover:bg-blue-700 text-white'
            }`}
            size="default"
          >
            {loading ? 'Loading...' : '👁 Preview Data'}
          </Button>
        </div>
      )}

      {/* Clear and Preview Data Buttons for SQL Mode */}
      {showSQLMode && (
        <div className="flex justify-start items-center gap-3 mt-4">
          {/* Clear button - only show when there's SQL content */}
          {sqlQuery.trim() && (
            <Button
              onClick={() => {
                setSqlQuery('');
                setSqlValidationResult(null);
                clearData();
                clearError();
              }}
              variant="outline"
              size="default"
              className="px-4 py-2 text-sm font-medium border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              <Eraser size={16} className="mr-1.5" />
              Clear
            </Button>
          )}

          <Button
            onClick={handlePreviewDataSQL}
            disabled={loading || !sqlQuery.trim() || !sqlValidationResult?.isValid}
            className={`w-auto px-6 py-2 text-base font-medium ${
              loading || !sqlQuery.trim() || !sqlValidationResult?.isValid
                ? 'bg-gray-400 cursor-not-allowed text-gray-600'
                : 'bg-blue-600 hover:bg-blue-700 text-white'
            }`}
            size="default"
          >
            {loading ? 'Loading...' : '👁 Preview Data'}
          </Button>
        </div>
      )}

      {/* DataGrid Results Table */}
      {(data.length > 0 || error) && (
        <div className="mt-6">
          <DataGridTable
            data={data}
            loading={loading}
            error={error}
          />
        </div>
      )}
    </div>
  );
};

export default DataGridBuilder;
