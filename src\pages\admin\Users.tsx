import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import SimplePagination from '@/components/ui/SimplePagination';
import { Search, Plus, Trash2, ArrowLeft, Edit } from 'lucide-react';
import { toast } from 'sonner';
import UserForm from '@/components/admin/UserForm';
import {
  createUser,
  updateUser,
  deleteMultipleUsers,
  getUsers,
  transformFormDataToPayload,
  transformUserToFormData,
  type User
} from '@/services/api/admin/userService';

const Users: React.FC = () => {
  const navigate = useNavigate();
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [selectedUsers, setSelectedUsers] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(true);
  const [itemsPerPage, setItemsPerPage] = useState(20);

  // Fetch users from API
  const fetchUsersData = async () => {
    try {
      setIsLoading(true);
      const usersData = await getUsers();
      setUsers(usersData);
      setFilteredUsers(usersData);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast.error('Failed to load users');
      // Set empty array on error
      setUsers([]);
      setFilteredUsers([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchUsersData();
  }, []);

  // Filter users based on search term
  useEffect(() => {
    const filtered = users.filter(user =>
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredUsers(filtered);
    setCurrentPage(1);
  }, [searchTerm, users]);

  // Pagination logic
  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentUsers = filteredUsers.slice(startIndex, endIndex);

  const handleSelectUser = (userId: string) => {
    const newSelected = new Set(selectedUsers);
    if (newSelected.has(userId)) {
      newSelected.delete(userId);
    } else {
      newSelected.add(userId);
    }
    setSelectedUsers(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedUsers.size === currentUsers.length) {
      setSelectedUsers(new Set());
    } else {
      setSelectedUsers(new Set(currentUsers.map(user => user.id)));
    }
  };

  const handleDeleteSelected = async () => {
    if (selectedUsers.size === 0) {
      toast.error('Please select users to delete');
      return;
    }

    try {
      const userIdsToDelete = Array.from(selectedUsers);
      await deleteMultipleUsers(userIdsToDelete);

      // Update local state after successful deletion
      const updatedUsers = users.filter(user => !selectedUsers.has(user.id));
      setUsers(updatedUsers);
      setSelectedUsers(new Set());
      toast.success(`${userIdsToDelete.length} user(s) deleted successfully`);
    } catch (error) {
      console.error('Error deleting users:', error);
      toast.error('Failed to delete users. Please try again.');
    }
  };

  const handleCreateUser = async (userData: any) => {
    try {
      // Transform form data to API payload
      const payload = transformFormDataToPayload(userData);

      // Create user via API
      const newUser = await createUser(payload);

      // Update local state with the new user
      setUsers(prev => [...prev, newUser]);
      setIsCreateDialogOpen(false);
      toast.success('User created successfully');

      // Optionally refresh the list to get the latest data from server
      // await fetchUsersData();
    } catch (error) {
      console.error('Error creating user:', error);
      toast.error('Failed to create user. Please try again.');
      // Don't close the dialog on error so user can retry
    }
  };

  const handleEditUser = (user: User) => {
    setEditingUser(user);
    setIsEditDialogOpen(true);
  };

  const handleUpdateUser = async (userData: any) => {
    if (!editingUser) return;

    try {
      // Transform form data to API payload
      const payload = transformFormDataToPayload(userData);

      // Remove password if it's empty (don't update password)
      if (!userData.password) {
        delete payload.password;
      }

      // Update user via API
      const updatedUser = await updateUser(editingUser.id, payload);

      // Update local state with the updated user
      setUsers(prev => prev.map(user =>
        user.id === editingUser.id ? updatedUser : user
      ));
      setIsEditDialogOpen(false);
      setEditingUser(null);
      toast.success('User updated successfully');

      // Optionally refresh the list to get the latest data from server
      // await fetchUsersData();
    } catch (error) {
      console.error('Error updating user:', error);
      toast.error('Failed to update user. Please try again.');
      // Don't close the dialog on error so user can retry
    }
  };

  const handleCancelEdit = () => {
    setIsEditDialogOpen(false);
    setEditingUser(null);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading users...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Back to Admin Button */}
      <div className="flex items-center mb-4">
        <Button
          variant="ghost"
          onClick={() => navigate('/Admin')}
          className="p-0 text-green-600 hover:text-green-800 hover:bg-transparent"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Admin
        </Button>
      </div>

      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-normal text-gray-700">Users</h1>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button
              className="text-white hover:opacity-90 transition-opacity"
              style={{ backgroundColor: 'rgb(0, 130, 130)' }}
            >
              <Plus className="w-4 h-4 mr-2" />
              Add User
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Create New User</DialogTitle>
            </DialogHeader>
            <UserForm onSubmit={handleCreateUser} onCancel={() => setIsCreateDialogOpen(false)} />
          </DialogContent>
        </Dialog>

        {/* Edit User Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Edit User</DialogTitle>
            </DialogHeader>
            <UserForm
              onSubmit={handleUpdateUser}
              onCancel={handleCancelEdit}
              initialData={editingUser ? transformUserToFormData(editingUser) : undefined}
              isEditMode={true}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Search and Actions */}
      <div className="flex items-center justify-between gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search users..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        {selectedUsers.size > 0 && (
          <Button
            variant="destructive"
            onClick={handleDeleteSelected}
            className="bg-gray-600 hover:bg-gray-700"
          >
            <Trash2 className="w-4 h-4 mr-2" />
            Delete User
          </Button>
        )}
      </div>

      {/* Users Table */}
      <div className="bg-white rounded-lg border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={selectedUsers.size === currentUsers.length && currentUsers.length > 0}
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Source</TableHead>
              <TableHead>Admin</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {currentUsers.map((user) => (
              <TableRow key={user.id}>
                <TableCell>
                  <Checkbox
                    checked={selectedUsers.has(user.id)}
                    onCheckedChange={() => handleSelectUser(user.id)}
                  />
                </TableCell>
                <TableCell className="font-normal">{user.email}</TableCell>
                <TableCell>{user.name}</TableCell>
                <TableCell>
                  <span className={`px-2 py-1 rounded-full text-xs ${
                    user.source === 'DADA' 
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {user.source}
                  </span>
                </TableCell>
                <TableCell>
                  <Checkbox checked={user.isAdmin} disabled />
                </TableCell>
                <TableCell>
                  <span className={`px-2 py-1 rounded-full text-xs ${
                    user.status === 'Active'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {user.status}
                  </span>
                </TableCell>
                <TableCell>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleEditUser(user)}
                    className="h-8 w-8 p-0 hover:bg-gray-100"
                  >
                    <Edit className="w-4 h-4 text-gray-600" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-500">
            Showing {startIndex + 1} to {Math.min(endIndex, filteredUsers.length)} of {filteredUsers.length} users
          </div>
          <SimplePagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={setItemsPerPage}
            showItemsPerPage={true}
          />
        </div>
      )}
    </div>
  );
};

export default Users;
