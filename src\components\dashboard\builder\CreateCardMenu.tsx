import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Plus, BarChart3, Table, ChevronDown } from 'lucide-react';

interface CreateCardMenuProps {
  onAddCard: (cardType: 'chart' | 'table') => void;
  className?: string;
}

const CreateCardMenu: React.FC<CreateCardMenuProps> = ({ onAddCard, className = '' }) => {
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleMenuToggle = () => {
    setIsOpen(!isOpen);
  };

  const handleAddCard = (cardType: 'chart' | 'table') => {
    onAddCard(cardType);
    setIsOpen(false);
  };

  return (
    <div className={`relative inline-block ${className}`} ref={menuRef}>
      {/* Create Button */}
      <Button
        ref={buttonRef}
        onClick={handleMenuToggle}
        className="bg-green-500 hover:bg-green-600 text-white flex items-center gap-2"
      >
        <Plus size={16} />
        Create
        <ChevronDown size={16} className={`transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </Button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute top-full right-0 mt-2 w-56 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-max">
          <div className="py-1">
            {/* Add Chart Card Option */}
            <button
              onClick={() => handleAddCard('chart')}
              className="w-full px-4 py-3 text-left hover:bg-blue-50 flex items-center gap-3 transition-colors"
            >
              <BarChart3 size={18} className="text-blue-500" />
              <div>
                <div className="font-medium text-gray-900">Add Chart Card</div>
                <div className="text-xs text-gray-500">Create a card for charts and visualizations</div>
              </div>
            </button>

            {/* Divider */}
            <div className="border-t border-gray-100 my-1"></div>

            {/* Add Table Card Option */}
            <button
              onClick={() => handleAddCard('table')}
              className="w-full px-4 py-3 text-left hover:bg-gray-50 flex items-center gap-3 transition-colors"
            >
              <Table size={18} className="text-gray-500" />
              <div>
                <div className="font-medium text-gray-900">Add Table Card</div>
                <div className="text-xs text-gray-500">Create a card for data tables</div>
              </div>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default CreateCardMenu;
