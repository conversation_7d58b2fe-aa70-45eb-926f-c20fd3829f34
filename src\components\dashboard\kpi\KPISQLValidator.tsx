import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Check<PERSON>ircle, XCircle, AlertCircle, Loader2 } from 'lucide-react';
// import { KPISQLValidatorProps } from './types/kpiTypes';

// const KPISQLValidator: React.FC<KPISQLValidatorProps> = ({
//   sql,
//   onValidate,
//   isValidating = false,
//   validationResult
// }) => {
//   const [localValidationResult, setLocalValidationResult] = useState<{
//     isValid: boolean;
//     error?: string;
//   } | null>(null);

//   const handleValidate = async () => {
//     if (!sql.trim()) {
//       setLocalValidationResult({
//         isValid: false,
//         error: 'SQL query is required'
//       });
//       return;
//     }

//     try {
//       const isValid = await onValidate(sql);
//       setLocalValidationResult({
//         isValid,
//         error: isValid ? undefined : 'SQL validation failed'
//       });
//     } catch (error) {
//       setLocalValidationResult({
//         isValid: false,
//         error: error instanceof Error ? error.message : 'Validation error occurred'
//       });
//     }
//   };

//   const currentResult = validationResult || localValidationResult;

//   const getValidationIcon = () => {
//     if (isValidating) {
//       return <Loader2 className="w-4 h-4 animate-spin" />;
//     }
    
//     if (!currentResult) {
//       return <AlertCircle className="w-4 h-4" />;
//     }
    
//     return currentResult.isValid 
//       ? <CheckCircle className="w-4 h-4 text-green-500" />
//       : <XCircle className="w-4 h-4 text-red-500" />;
//   };

//   const getValidationMessage = () => {
//     if (isValidating) {
//       return 'Validating SQL...';
//     }
    
//     if (!currentResult) {
//       return 'Click to validate SQL query';
//     }
    
//     return currentResult.isValid 
//       ? 'SQL query is valid'
//       : currentResult.error || 'SQL validation failed';
//   };

//   const getValidationColor = () => {
//     if (isValidating) {
//       return 'text-blue-600';
//     }
    
//     if (!currentResult) {
//       return 'text-gray-600';
//     }
    
//     return currentResult.isValid ? 'text-green-600' : 'text-red-600';
//   };

//   return (
//     <div className="flex items-center space-x-4">
//       <Button
//         onClick={handleValidate}
//         disabled={isValidating || !sql.trim()}
//         variant="outline"
//         size="sm"
//         className="flex items-center space-x-2"
//       >
//         {getValidationIcon()}
//         <span>Validate SQL</span>
//       </Button>
      
//       <div className={`flex items-center space-x-2 text-sm ${getValidationColor()}`}>
//         <span>{getValidationMessage()}</span>
//       </div>
//     </div>
//   );
// };

// export default KPISQLValidator;
