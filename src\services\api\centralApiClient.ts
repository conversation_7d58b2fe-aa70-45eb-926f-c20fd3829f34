
import { store } from '@/stores/store';
import { setError } from '@/stores/errorSlice';

// API Configuration
interface ApiEndpointConfig {
  baseUrl: string;
  apiKey?: string;
  timeout: number;
  maxRetries: number;
}

interface ApiConfig {
  dada: ApiEndpointConfig;
  sso: ApiEndpointConfig;
  dashboard1: ApiEndpointConfig;
  chart: ApiEndpointConfig;
  database: ApiEndpointConfig;
}

// Custom error type for API errors
interface ApiError extends Error {
  status?: number;
}

// Request options interface
interface RequestOptions {
  method?: string;
  headers?: Record<string, string>;
  body?: any;
  timeout?: number;
  retryCount?: number;
  skipDeduplication?: boolean;
  skipErrorHandling?: boolean;
  skipContentType?: boolean;
  signal?: AbortSignal;
}

// Get configuration from environment variables
const getApiConfig = (): ApiConfig => {
  // Get URLs from environment variables
  let dadaUrl = import.meta.env.VITE_DADA_API_URL;
  let ssoUrl = import.meta.env.VITE_SSO_API_URL;
  let dashboard1Url = import.meta.env.VITE_DASHBOARD1_API_URL;
  let chartUrl = import.meta.env.VITE_CHART_API_URL;
  const apiKey = import.meta.env.VITE_API_KEY;

  // Fallback to working IP if environment variables are missing
  if (!dashboard1Url) {
    dashboard1Url = 'http://***********:8000';
    console.warn('Using fallback URL for dashboard1:', dashboard1Url);
  }
  const databaseUrl = 'http://***********:8001';

  if (!dadaUrl) {
    dadaUrl = 'http://***********:8001';
    console.warn('Using fallback URL for DADA:', dadaUrl);
  }

  return {
    dada: {
      baseUrl: dadaUrl,
      apiKey,
      timeout: 30000,
      maxRetries: 2
    },
    sso: {
      baseUrl: ssoUrl || 'http://***********:8002',
      timeout: 10000,
      maxRetries: 2
    },
    dashboard1: {
      baseUrl: dashboard1Url,
      timeout: 30000,
      maxRetries: 2
    },
    chart: {
      baseUrl: chartUrl || dashboard1Url,
      timeout: 30000,
      maxRetries: 2
    },
    database: {
      baseUrl: databaseUrl,
      timeout: 30000,
      maxRetries: 2
    }
  };
};

// Utility function for sleep
const sleep = (ms: number): Promise<void> => new Promise(resolve => setTimeout(resolve, ms));

// Central API Client Class
class CentralApiClient {
  private config: ApiConfig;
  private ongoingRequests = new Map<string, Promise<any>>();
  private abortControllers = new Map<string, AbortController>();

  constructor() {
    this.config = getApiConfig();
  }

  // Get default headers
  private getDefaultHeaders(includeContentType = true, isFormData = false): Record<string, string> {
    const headers: Record<string, string> = {
      'Accept': 'application/json',
    };

    if (includeContentType && !isFormData) {
      headers['Content-Type'] = 'application/json';
    }

    return headers;
  }

  // Create request key for deduplication
  private createRequestKey(endpoint: keyof ApiConfig, path: string, options: RequestOptions): string {
    const method = options.method || 'GET';
    const bodyKey = options.body ? JSON.stringify(options.body) : '';
    return `${endpoint}:${method}:${path}:${bodyKey}`;
  }

  // Main request method
  async makeRequest<T = any>(
    endpoint: keyof ApiConfig,
    path: string,
    options: RequestOptions = {}
  ): Promise<T> {
    const {
      method = 'GET',
      headers = {},
      body,
      timeout = this.config[endpoint]?.timeout || 30000,
      retryCount = 0,
      skipDeduplication = false,
      skipErrorHandling = false,
      skipContentType = false
    } = options;

    const apiEndpoint = this.config[endpoint];
    if (!apiEndpoint) {
      throw new Error(`Unknown endpoint: ${endpoint}`);
    }

    // Build full URL
    const fullUrl = `${apiEndpoint.baseUrl}${path}`;

    // Determine endpoint types
    const isDashboard2 = endpoint === 'chart' || endpoint === 'database';
    const isDadaEndpoint = endpoint === 'dada';

    // Log the request details for debugging
    console.log(`API Request: ${method} ${fullUrl}`, {
      headers,
      body: body instanceof FormData ? 'FormData' : body,
      timeout,
      retryCount,
      isDashboard2,
      isDadaEndpoint
    });

    // Request deduplication
    if (!skipDeduplication) {
      const requestKey = this.createRequestKey(endpoint, path, options);
      const existingRequest = this.ongoingRequests.get(requestKey);

      if (existingRequest) {
        console.log(`Using existing request for ${requestKey}`);
        return existingRequest as Promise<T>;
      }
    }

    // Set up abort controller for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      controller.abort();
    }, timeout);

    // Set up headers with proper merging
    const isFormData = body instanceof FormData;
    const requestHeaders = {
      ...this.getDefaultHeaders(!skipContentType, isFormData),
      ...headers, // User headers override defaults
    };

    // Add API key if available
    if (apiEndpoint.apiKey) {
      requestHeaders['X-API-Key'] = apiEndpoint.apiKey;
    }

    // Create fetch options with conditional credentials
    const fetchOptions: RequestInit = {
      method,
      headers: requestHeaders,
      body: body instanceof FormData ? body : (body ? JSON.stringify(body) : undefined),
      signal: options.signal || controller.signal,
      mode: 'cors',
      // Only include credentials for endpoints that support them (exclude dashboard2 and dada)
      ...(isDashboard2 || isDadaEndpoint ? {} : { credentials: 'include' })
    };

    // Create the request promise
    const requestPromise = (async () => {
      try {
        // For dashboard2 and DADA endpoints, use direct fetch without credentials
        if ((isDashboard2 && !path.includes('tracker')) || isDadaEndpoint) {
          console.log(`Using direct fetch without credentials for ${isDadaEndpoint ? 'DADA' : 'dashboard2'} endpoint: ${fullUrl}`);

          // If this is a dashboard2 endpoint (but not tracker) or DADA endpoint, use direct fetch
          const directResponse = await fetch(fullUrl, {
            method,
            headers: requestHeaders,
            body: body instanceof FormData ? body : (body ? JSON.stringify(body) : undefined),
            signal: options.signal || controller.signal,
            mode: 'cors'
            // Explicitly omit credentials
          });

          // Clear timeout
          clearTimeout(timeoutId);

          // Handle non-200 responses
          if (!directResponse.ok) {
            const errorText = await directResponse.text();
            console.error(`API error (${directResponse.status}): ${errorText}`);

            // Create and throw error
            const error = new Error(errorText || `HTTP error ${directResponse.status}`) as ApiError;
            error.status = directResponse.status;
            throw error;
          }

          // Parse response based on content type
          const contentType = directResponse.headers.get('content-type');
          let data: any;

          if (contentType?.includes('application/json')) {
            data = await directResponse.json();
          } else if (contentType?.includes('text/')) {
            data = await directResponse.text();
          } else {
            // For binary data, return the response directly
            return directResponse as unknown as T;
          }

          return data;
        }

        // For all other endpoints, use normal fetch with conditional credentials
        const response = await fetch(fullUrl, fetchOptions);

        // Clear timeout
        clearTimeout(timeoutId);

        // Handle non-200 responses
        if (!response.ok) {
          const errorText = await response.text();
          console.error(`API error (${response.status}): ${errorText}`);

          // Create and throw error
          const error = new Error(errorText || `HTTP error ${response.status}`) as ApiError;
          error.status = response.status;
          throw error;
        }

        // Parse response based on content type
        const contentType = response.headers.get('content-type');
        let data: any;

        if (contentType?.includes('application/json')) {
          data = await response.json();
        } else if (contentType?.includes('text/')) {
          data = await response.text();
        } else {
          // For binary data, return the response directly
          return response as unknown as T;
        }

        return data;
      } catch (error: any) {
        // Clear timeout
        clearTimeout(timeoutId);

        // Don't record aborted requests as failures
        if (error.name === 'AbortError') {
          console.log(`Request to ${fullUrl} was aborted`);
          throw error;
        }

        // Retry network errors
        if (error.message.includes('Failed to fetch') && retryCount < 2) {
          console.log(`Retrying request to ${fullUrl} after network error (attempt ${retryCount + 1})`);
          await sleep(1000 * (retryCount + 1)); // Exponential backoff
          return this.makeRequest(endpoint, path, {
            ...options,
            retryCount: retryCount + 1,
          });
        }

        // Handle errors
        if (!skipErrorHandling) {
          console.error(`API request failed: ${error.message}`);

          // Dispatch error to Redux store with correct property names
          store.dispatch(setError({
            message: `API request failed: ${error.message}`,
            statusCode: error.status || 500
          }));
        }

        throw error;
      } finally {
        // Remove from ongoing requests map
        if (!skipDeduplication) {
          const requestKey = this.createRequestKey(endpoint, path, options);
          this.ongoingRequests.delete(requestKey);
        }
      }
    })();

    // Store the request promise for deduplication
    if (!skipDeduplication) {
      const requestKey = this.createRequestKey(endpoint, path, options);
      this.ongoingRequests.set(requestKey, requestPromise);
    }

    return requestPromise;
  }

  // Improved health check that handles missing endpoints
  async healthCheck(endpoint: keyof ApiConfig): Promise<boolean> {
    try {
      // Try the dedicated health endpoint first
      await this.makeRequest(endpoint, '/health', {
        skipDeduplication: true,
        skipErrorHandling: true,
        timeout: 3000 // Short timeout for health checks
      });

      // If we get here, the request succeeded
      console.log(`Health check succeeded for ${endpoint}`);

      // Dispatch connected event
      window.dispatchEvent(new CustomEvent('backend-connected'));

      return true;
    } catch (error) {
      console.warn(`Health check failed for ${endpoint}:`, error);

      // Try an alternative endpoint if health check fails
      try {
        // Try a different endpoint that should be available
        await this.makeRequest(endpoint, '/files/personal', {
          skipDeduplication: true,
          skipErrorHandling: true,
          timeout: 3000
        });

        // If we get here, the API is actually working despite health check failure
        console.log(`Alternative health check succeeded for ${endpoint}`);

        // Dispatch connected event
        window.dispatchEvent(new CustomEvent('backend-connected'));

        return true;
      } catch (altError) {
        console.error(`Alternative health check also failed for ${endpoint}:`, altError);
        return false;
      }
    }
  }

  // Cancel all pending requests
  cancelAllRequests() {
    this.abortControllers.forEach((controller, key) => {
      console.log(`Cancelling request: ${key}`);
      controller.abort();
    });
    this.abortControllers.clear();
  }
}

// Export singleton instance
export const centralApiClient = new CentralApiClient();

