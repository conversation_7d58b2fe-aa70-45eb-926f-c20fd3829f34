import React, { createContext, useContext, ReactNode } from 'react';
import { ChartContext as ChartContextType, ChartSize } from '@/constants/chartConstants';

interface ChartContextValue {
  context: ChartContextType;
  size: ChartSize;
  colors?: string[];
  theme?: 'light' | 'dark';
}

interface ChartProviderProps {
  children: ReactNode;
  context?: ChartContextType;
  size?: ChartSize;
  colors?: string[];
  theme?: 'light' | 'dark';
}

const ChartContext = createContext<ChartContextValue>({
  context: 'dashboard',
  size: 'default',
  theme: 'light'
});

export const useChartContext = () => {
  return useContext(ChartContext);
};

export const ChartProvider: React.FC<ChartProviderProps> = ({
  children,
  context = 'dashboard',
  size = 'default',
  colors,
  theme = 'light'
}) => {
  const value: ChartContextValue = {
    context,
    size,
    colors,
    theme
  };

  return (
    <ChartContext.Provider value={value}>
      {children}
    </ChartContext.Provider>
  );
};

export default ChartContext;