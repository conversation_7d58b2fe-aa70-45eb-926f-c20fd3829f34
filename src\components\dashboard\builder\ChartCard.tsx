import React, { memo, useMemo, useCallback } from 'react';
import { <PERSON><PERSON>onte<PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { BarChart3, Loader2 } from 'lucide-react';
import { useDroppable } from '@dnd-kit/core';
import { SavedChart, ChartDataPoint } from '@/types/chartTypes';
import { LazyChartRenderer } from './LazyComponents';
import { ResizableCard } from './ResizableCard';

interface CardState {
  id: string;
  type: 'chart' | 'table';
  chart: SavedChart | null;
  data: ChartDataPoint[] | null;
  loading: boolean;
  createdAt: number;
}

interface ChartCardProps {
  cardId: string;
  cardData?: CardState;
  hasCardContent: boolean;
  chartZoneChart?: SavedChart | null;
  chartData?: ChartDataPoint[] | null;
  loadingChartData?: boolean;
  isOverChart: boolean;
  onUpdateCard?: (cardId: string, updates: Partial<CardState>) => void;
  onRemoveChart?: (zoneType: 'chart' | 'table') => void;
  onRemoveCard?: (cardId: string) => void;
}

export const ChartCard: React.FC<ChartCardProps> = memo(({
  cardId,
  cardData,
  hasCardContent,
  chartZoneChart,
  chartData,
  loadingChartData,
  isOverChart,
  onUpdateCard,
  onRemoveChart,
  onRemoveCard
}) => {
  // Memoize expensive computations
  const chartName = useMemo(() => 
    cardData?.chart?.chart_name || chartZoneChart?.chart_name,
    [cardData?.chart?.chart_name, chartZoneChart?.chart_name]
  );

  const hasChartData = useMemo(() => 
    (cardData?.chart && cardData?.data) || (chartZoneChart && chartData && chartData.length > 0),
    [cardData?.chart, cardData?.data, chartZoneChart, chartData]
  );

  const isLoading = useMemo(() => 
    cardData?.loading || loadingChartData,
    [cardData?.loading, loadingChartData]
  );

  // Memoize event handlers
  const handleRemove = useCallback(() => {
    if (hasCardContent) {
      onUpdateCard?.(cardId, { chart: null, data: null });
    } else if (chartZoneChart && onRemoveChart) {
      onRemoveChart('chart');
    } else {
      onRemoveCard?.(cardId);
    }
  }, [hasCardContent, cardId, onUpdateCard, chartZoneChart, onRemoveChart, onRemoveCard]);

  // Create unique droppable for this specific card
  const { isOver: isOverThisCard, setNodeRef: setThisCardNodeRef } = useDroppable({
    id: `chart-${cardId}`,
    data: {
      type: 'chart-zone',
      cardId: cardId,
    },
  });

  return (
    <div ref={setThisCardNodeRef}>
      <ResizableCard
        initialWidth={400}
        initialHeight={350}
        minWidth={300}
        minHeight={250}
        hasContent={hasCardContent || !!chartZoneChart}
        onRemove={handleRemove}
        showCloseIcon={!hasCardContent && !chartZoneChart}
        className={`transition-colors ${
          isOverThisCard || isOverChart
            ? 'border-2 border-dashed border-primary bg-primary/10'
            : hasCardContent || chartZoneChart
              ? 'border-none bg-card shadow-sm'
              : 'border-2 border-dashed border-primary/30 bg-primary/5 hover:border-primary/50'
        }`}
      >
        <CardHeader className="p-3 pb-2 flex-shrink-0">
          <CardTitle className="text-lg font-medium text-foreground flex items-center">
            <BarChart3 size={20} className="mr-2 text-primary" />
            Chart
            {chartName && (
              <span className="ml-2 text-sm font-normal text-muted-foreground truncate">
                - {chartName}
              </span>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="flex-1 p-0 overflow-hidden flex flex-col min-h-0">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <Loader2 className="h-6 w-6 text-primary animate-spin" />
              <span className="ml-2 text-sm text-muted-foreground">Loading chart...</span>
            </div>
          ) : hasChartData ? (
            <div className="flex-1 p-3 min-h-0">
              <div className="w-full h-full relative">
                <LazyChartRenderer
                  chart={cardData?.chart || chartZoneChart}
                  chartData={(cardData?.data as ChartDataPoint[]) || chartData}
                />
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center h-full text-center text-muted-foreground">
              <div>
                <BarChart3 size={48} className="mx-auto mb-3 text-muted-foreground/50" />
                <p className="text-sm">Drop chart here</p>
              </div>
            </div>
          )}
        </CardContent>
      </ResizableCard>
    </div>
  );
});

ChartCard.displayName = 'ChartCard';