
import * as React from "react"
import { Column } from "@tanstack/react-table"
import { ChevronsUpDown, EyeOff, SortAsc, SortDesc, FilterX } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { ColumnFilter } from "./column-filter"

interface ColumnHeaderProps<TData, TValue> extends React.HTMLAttributes<HTMLDivElement> {
  column: Column<TData, TValue>
  title: string
  className?: string
}

export function ColumnHeader<TData, TValue>({
  column,
  title,
  className,
}: ColumnHeaderProps<TData, TValue>) {
  const [showFilterInput, setShowFilterInput] = React.useState(false);
  
  // Function to toggle filter input visibility
  const toggleFilterInput = () => {
    setShowFilterInput(!showFilterInput);
  };
  
  // Function to hide filter input
  const hideFilterInput = () => {
    setShowFilterInput(false);
  };

  // Function to clear filter and sorting
  const clearFilterAndSort = () => {
    column.setFilterValue(undefined);
    column.clearSorting();
  };

  // Check if column has an active filter or sorting
  const hasFilter = Boolean(column.getFilterValue());
  const hasSorting = column.getIsSorted() !== false;
  const hasFilterOrSort = hasFilter || hasSorting;

  // If filter input should be shown, don't show the column title
  if (showFilterInput) {
    return (
      <div className={cn("flex items-center justify-between", className)}>
        <ColumnFilter 
          column={column} 
          title={title} 
          onFilterClear={hideFilterInput}
        />
      </div>
    );
  }

  // Default view with column title and filter icon
  if (!column.getCanSort()) {
    return (
      <div className={cn("flex items-center justify-between", className)}>
        <span className="text-white font-medium">{title}</span>
        <ColumnFilter 
          column={column} 
          title={title} 
          onFilterShow={toggleFilterInput}
        />
      </div>
    );
  }

  // Rest of the existing code for sortable columns
  return (
    <div className={cn("flex items-center justify-between py-1", className)}>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 text-white hover:bg-teal-700 hover:text-white data-[state=open]:bg-teal-700 flex items-center gap-1 px-2"
          >
            <span className="font-medium whitespace-normal text-left">{title}</span>
            {column.getIsSorted() === "desc" ? (
              <SortDesc className="h-4 w-4 flex-shrink-0" />
            ) : column.getIsSorted() === "asc" ? (
              <SortAsc className="h-4 w-4 flex-shrink-0" />
            ) : (
              <ChevronsUpDown className="h-4 w-4 flex-shrink-0" />
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-40">
          <DropdownMenuItem onClick={() => column.toggleSorting(false)}>
            <SortAsc className="mr-2 h-3.5 w-3.5" />
            Sort Ascending
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => column.toggleSorting(true)}>
            <SortDesc className="mr-2 h-3.5 w-3.5" />
            Sort Descending
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem 
            onClick={clearFilterAndSort}
            disabled={!hasFilterOrSort}
            className={!hasFilterOrSort ? "opacity-50 cursor-not-allowed" : ""}
          >
            <FilterX className="mr-2 h-3.5 w-3.5" />
            Clear Filter
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => column.toggleVisibility(false)}>
            <EyeOff className="mr-2 h-3.5 w-3.5" />
            Hide Column
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      
      <ColumnFilter 
        column={column} 
        title={title} 
        onFilterShow={toggleFilterInput}
      />
    </div>
  );
}
