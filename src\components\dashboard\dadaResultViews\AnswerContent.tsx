


import { useState, useEffect } from "react"
import { TabNavigation } from "./TabNavigation"
import { TabContent } from "./TabContent"
import { ActionButtons } from "./ActionButtons"
import { Loader2 } from "lucide-react"
import type { QueryResultData } from "@/components/dashboard/models"

interface AnswerContentProps {
  size?: "default" | "large"
  queryResult: any // Replace 'any' with a more specific type if possible
  isLoading?: boolean
  error?: any // Replace 'any' with a more specific type if possible
  originalQuery?: string // Add originalQuery prop
  isPowerQuery?: boolean // Prop to check if it's a power query
}

export const AnswerContent = ({
  size = "default",
  queryResult,
  isLoading = false,
  error = null,
  originalQuery,
  isPowerQuery = false,
}: AnswerContentProps) => {
  const [activeTab, setActiveTab] = useState("table")
  const [selectedDatabase, setSelectedDatabase] = useState("SQL")

  // Debug logging to help troubleshoot
  useEffect(() => {
    console.log("AnswerContent received queryResult:", queryResult, "isPowerQuery:", isPowerQuery)
  }, [queryResult, isPowerQuery])

  // Reset to table tab if SQL tab is selected for a power query
  useEffect(() => {
    if (isPowerQuery && activeTab === "sql") {
      console.log("Resetting activeTab to 'table' for power query in dialog");
      setActiveTab("table")
    }
  }, [isPowerQuery, activeTab])

  const handleDatabaseChange = (database: string) => {
    setSelectedDatabase(database)
    // In the future, this could trigger different data loading based on the database
    console.log(`Database changed to: ${database}`)
  }

  const headerTextSize = size === "large" ? "text-base" : "text-sm"
  const headerPadding = size === "large" ? "px-4 py-2" : "px-2 py-1"

  return (
    <div>
     

      {isLoading ? (
        <div className="flex items-center justify-center p-8">
          <Loader2 className="h-8 w-8 text-blue-500 animate-spin" />
          <span className="ml-2 text-gray-600">Processing your query...</span>
        </div>
      ) : error ? (
        <div className="p-4 bg-red-50 text-red-700">
          <p className="font-medium">Error processing query</p>
          <p className="text-sm mt-1">{error}</p>
        </div>
      ) : (
        <>
          <div className="tabs-container border-b border-gray-200 mb-4">
            <TabNavigation 
              activeTab={activeTab} 
              setActiveTab={setActiveTab} 
              size={size} 
              isPowerQuery={isPowerQuery} 
            />
          </div>

          <TabContent 
            activeTab={activeTab} 
            size={size} 
            database={selectedDatabase} 
            queryResult={queryResult}
            isPowerQuery={isPowerQuery}
          />

          <ActionButtons size={size} queryResult={queryResult} originalQuery={originalQuery} />
        </>
      )}
    </div>
  )
}
