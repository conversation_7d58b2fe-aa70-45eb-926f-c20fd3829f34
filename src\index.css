@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

/* postcss-ignore-next-line */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;

    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;

    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;

    --radius: 0.75rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;

    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  html {
    font-family: 'Inter', sans-serif;
    scroll-behavior: smooth;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-medium tracking-tight;
  }

  h1 {
    @apply text-4xl sm:text-5xl md:text-6xl;
  }

  h2 {
    @apply text-3xl sm:text-4xl;
  }

  h3 {
    @apply text-2xl sm:text-3xl;
  }

  .glass {
    @apply bg-white/5 backdrop-blur-lg border border-white/10 shadow-xl;
  }

  .section {
    @apply py-16 md:py-24;
  }
}

/* Custom utility classes */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  .text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-500;
  }
}

/* Consolidated scrollbar styles - single definition */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #94a3b8;
  border-radius: 4px;
  border: 1px solid #f1f5f9;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

::-webkit-scrollbar-corner {
  background: #f1f5f9;
}

/* Firefox scrollbars */
* {
  scrollbar-width: thin;
  scrollbar-color: #94a3b8 #f1f5f9;
}

/* Custom scrollbar for file list and specific components */
.file-list-virtuoso::-webkit-scrollbar {
  width: 6px;
}

.file-list-virtuoso::-webkit-scrollbar-track {
  background: transparent;
}

.file-list-virtuoso::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.file-list-virtuoso::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* Textarea scrollbar styles */
textarea::-webkit-scrollbar {
  width: 6px;
}

textarea::-webkit-scrollbar-track {
  background: transparent;
}

textarea::-webkit-scrollbar-thumb {
  background-color: #CBD5E1;
  border-radius: 3px;
}

textarea {
  scrollbar-width: thin;
  scrollbar-color: #CBD5E1 transparent;
}

/* Dialog scrollbar styles */
.DialogContent {
  max-height: 85vh;
  overflow-y: auto;
}

.DialogContent::-webkit-scrollbar {
  width: 4px;
}

.DialogContent::-webkit-scrollbar-track {
  background: transparent;
}

.DialogContent::-webkit-scrollbar-thumb {
  background-color: #CBD5E1;
  border-radius: 3px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .tracker-container {
    height: calc(100vh - 56px);
  }
}

/* Tablet Responsive */
@media (max-width: 1024px) and (min-width: 769px) {
  .tracker-container {
    height: calc(100vh - 60px);
  }
}

/* Desktop */
@media (min-width: 1025px) {
  .tracker-container {
    height: calc(100vh - 64px);
  }
}

/* Modify this to allow scrolling on specific pages */
html, body {
  height: 100%;
}

/* Default body behavior - no scroll */
body {
  overflow: hidden;
}

/* Allow scrolling for landing page */
body.landing-page {
  overflow: auto;
}

/* Ensure admin content scrolls properly */
.admin-content {
  overflow: auto;
  height: 100%;
  scrollbar-width: thin;
  scrollbar-color: #94a3b8 #f1f5f9;
}

.admin-content::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.admin-content::-webkit-scrollbar-track {
  background: transparent;
}

.admin-content::-webkit-scrollbar-thumb {
  background-color: #94a3b8;
  border-radius: 4px;
}

