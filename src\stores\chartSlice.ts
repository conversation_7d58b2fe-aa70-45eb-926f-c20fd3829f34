import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { TableWithColumns, ChartDataItem } from '@/services/api/chart/chartTypes';

// Define the chart state type
export interface ChartState {
  selectedDatabase: string;
  connectionId: string;
  tables: TableWithColumns[];
  isLoadingTables: boolean;
  isConnecting: boolean;
  // Add these new properties
  tableData: {
    [tableName: string]: ChartDataItem[];
  };
  selectedColumns: {
    [tableName: string]: string[];
  };
  isLoadingTableData: boolean;
  // Add editing chart data for navigation state management
  editingChartData: any | null;
  selectedConnectionId: number | null;
}

const initialState: ChartState = {
  selectedDatabase: '',
  connectionId: '',
  tables: [],
  isLoadingTables: false,
  isConnecting: false,
  // Initialize new properties
  tableData: {},
  selectedColumns: {},
  isLoadingTableData: false,
  editingChartData: null,
  selectedConnectionId: null
};

// Create the chart slice
const chartSlice = createSlice({
  name: 'chart',
  initialState,
  reducers: {
    setSelectedDatabase: (state, action: PayloadAction<string>) => {
      // Log the action for debugging
      console.log('Redux: Setting selected database to:', action.payload);
      state.selectedDatabase = action.payload;
      // Clear connection ID when database changes
      state.connectionId = '';
      state.tables = [];
    },
    setConnectionId: (state, action: PayloadAction<string>) => {
      console.log('Redux: Setting connection ID to:', action.payload);
      state.connectionId = action.payload;
    },
    setIsConnecting: (state, action: PayloadAction<boolean>) => {
      state.isConnecting = action.payload;
    },
    setIsLoadingTables: (state, action: PayloadAction<boolean>) => {
      state.isLoadingTables = action.payload;
    },
    setTables: (state, action: PayloadAction<TableWithColumns[]>) => {
      state.tables = action.payload;
    },
    updateTable: (state, action: PayloadAction<TableWithColumns>) => {
      console.log('Redux: Updating table:', action.payload.name, 'expanded:', action.payload.expanded);
      
      const index = state.tables.findIndex(table => table.name === action.payload.name);
      if (index !== -1) {
        // Create a new array with the updated table
        const updatedTables = [...state.tables];
        updatedTables[index] = action.payload;
        state.tables = updatedTables;
        
        console.log('Redux: Table updated, new expanded state:', action.payload.expanded);
      } else {
        console.warn('Redux: Table not found in state:', action.payload.name);
      }
    },
    // Add a reset action to clear the state
    resetChartState: (state) => {
      state.selectedDatabase = '';
      state.connectionId = '';
      state.tables = [];
      state.isLoadingTables = false;
      state.isConnecting = false;
      state.tableData = {};
      state.selectedColumns = {};
      state.isLoadingTableData = false;
      state.editingChartData = null;
    },
    // Add these new reducers
    setTableData: (state, action: PayloadAction<{ tableName: string, data: ChartDataItem[] }>) => {
      console.log('Redux: Setting table data for:', action.payload.tableName);
      state.tableData[action.payload.tableName] = action.payload.data;
    },
    
    setSelectedColumns: (state, action: PayloadAction<{ tableName: string, columns: string[] }>) => {
      console.log('Redux: Setting selected columns for:', action.payload.tableName, action.payload.columns);
      state.selectedColumns[action.payload.tableName] = action.payload.columns;
    },
    
    setIsLoadingTableData: (state, action: PayloadAction<boolean>) => {
      state.isLoadingTableData = action.payload;
    },
    
    clearTableData: (state) => {
      state.tableData = {};
      state.selectedColumns = {};
    },
    
    // Add new action for editing chart data
    setEditingChartData: (state, action: PayloadAction<any>) => {
      console.log('Redux: Setting editing chart data:', action.payload);
      state.editingChartData = action.payload;
    },
    
    clearEditingChartData: (state) => {
      console.log('Redux: Clearing editing chart data');
      state.editingChartData = null;
    },
    setSelectedConnectionId: (state, action: PayloadAction<number | null>) => {
      state.selectedConnectionId = action.payload;
    },
  },
});

// Export actions
export const { 
  setSelectedDatabase, 
  setConnectionId, 
  setIsConnecting,
  setIsLoadingTables,
  setTables,
  updateTable,
  resetChartState,
  // Export new actions
  setTableData,
  setSelectedColumns,
  setIsLoadingTableData,
  clearTableData,
  setEditingChartData,
  clearEditingChartData,
  setSelectedConnectionId
} = chartSlice.actions;

// Define selector types without importing RootState
// This avoids circular dependencies
export const selectSelectedDatabase = (state: any) => state.chart?.selectedDatabase || '';
export const selectConnectionId = (state: any) => state.chart?.connectionId || '';
export const selectIsConnecting = (state: any) => state.chart?.isConnecting || false;
export const selectTables = (state: any) => state.chart?.tables || [];
export const selectTableData = (state: any) => state.chart?.tableData || {};
export const selectSelectedColumns = (state: any) => state.chart?.selectedColumns || {};
export const selectIsLoadingTableData = (state: any) => state.chart?.isLoadingTableData || false;
export const selectEditingChartData = (state: any) => state.chart?.editingChartData || null;
export const selectSelectedConnectionId = (state: any) => state.chart?.selectedConnectionId || null;

export default chartSlice.reducer;
