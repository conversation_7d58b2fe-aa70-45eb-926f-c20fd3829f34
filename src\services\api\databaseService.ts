import { centralApiClient } from './centralApiClient';

// Types for database connection
export interface DatabaseConnection {
  connection_id: number;
  connection_name: string;
  database_dialect: string;
  database_name: string;
  host?: string;
  port?: number;
  username?: string;
}

export interface DatabaseConnectionForm {
  connectionName: string;
  host: string;
  port: string;
  databaseName: string;
  username: string;
  password: string;
  driverDialect: string;
}

export interface CreateConnectionPayload {
  user_id: string;
  connection_name: string;
  db_dialect: string;
  database_name: string;
  username: string;
  password: string;
  host: string;
  port: number;
  ssl_mode: string;
}

export interface TableColumn {
  table_name: string;
  table_columns: string[];
}

export interface SaveSelectedTablesPayload {
  connection_id: string;
  selected_tables_columns: TableColumn[];
}

export interface CreateAIMetadataPayload {
  success: string;
  table_list: TableColumn[];
  connection_id: string;
}

export interface UpdateMetadataPayload {
  status: string;
  connection_id: string;
  table_metadata: Array<{
    Table_Id: number;
    Table_Name: string;
    Table_Description: string;
    Primary_Key_Columns: Record<string, string> | null;
    Foreign_Key_Columns: Record<string, string> | null;
    Custom_Table_Instructions: string | null;
    Table_Aliases: string | null;
    Columns_List: Record<string, string>;
    Database_Dialect: string;
    Database_Name: string;
    connection_id: string;
  }>;
  column_metadata: Array<{
    Column_Id: number;
    Column_Name: string;
    Column_Description: string;
    Column_Example_Data: Record<string, any>;
    Custom_Column_Instructions: string | null;
    Column_Aliases: string | null;
    Column_Table_Id: number;
    Column_Table_Name: string;
  }>;
}

// Database Connection APIs
export class DatabaseService {
  
  /**
   * Get user database connections
   */
  static async getUserConnections(userId: string): Promise<{ status: string; connections: DatabaseConnection[] }> {
    return centralApiClient.makeRequest('database', `/database/user-connections/${userId}`);
  }

  /**
   * Get detailed user database connections
   */
  static async getUserConnectionsDetailed(userId: string): Promise<{ status: string; connections: DatabaseConnection[] }> {
    return centralApiClient.makeRequest('database', `/database/user-connections-detailed/${userId}`);
  }

  /**
   * Get connection details by ID
   */
  static async getConnectionDetails(connectionId: string): Promise<{ status: string; connection: DatabaseConnection }> {
    return centralApiClient.makeRequest('database', `/database/connection-details/${connectionId}`);
  }

  /**
   * Test database connection
   */
  static async testConnection(payload: CreateConnectionPayload): Promise<{ status: string; message: string }> {
    return centralApiClient.makeRequest('database', '/database/test-connection', {
      method: 'POST',
      body: payload
    });
  }

  /**
   * Create new database connection
   */
  static async createConnection(payload: CreateConnectionPayload): Promise<{ status: string; message: string; connection_id?: number }> {
    return centralApiClient.makeRequest('database', '/database/add-connection', {
      method: 'POST',
      body: payload
    });
  }

  /**
   * Create database connection (alternative endpoint)
   */
  static async createConnectionAlt(payload: CreateConnectionPayload): Promise<{ status: string; message: string }> {
    return centralApiClient.makeRequest('database', '/database/create-connection', {
      method: 'POST',
      body: payload
    });
  }
}

// Metadata APIs
export class MetadataService {
  
  /**
   * Get metadata by connection ID
   */
  static async getMetadataByConnection(connectionId: string): Promise<any[]> {
    return centralApiClient.makeRequest('database', `/metadata/get_metadata_by_connection/${connectionId}`);
  }

  /**
   * Get tables and columns for a connection
   */
  static async getTablesColumns(connectionId: string | number): Promise<{ table_list: { table_name: string; table_columns: string[] }[] }> {
    return centralApiClient.makeRequest('database', `/metadata/get_tables_columns/${connectionId}`);
  }

  /**
   * Get tables and columns with query parameter
   */
  static async getTablesColumnsQuery(connectionId: string): Promise<{ table_list: { table_name: string; table_columns: string[] }[] }> {
    return centralApiClient.makeRequest('database', `/metadata/get_tables_columns?connection_id=${connectionId}`);
  }

  /**
   * Get selected tables and columns
   */
  static async getSelectedTablesColumns(connectionId: string): Promise<any> {
    return centralApiClient.makeRequest('database', `/metadata/get_selected_tables_columns?connection_id=${connectionId}`);
  }

  /**
   * Save selected tables and columns
   */
  static async saveSelectedTablesColumns(payload: SaveSelectedTablesPayload): Promise<any> {
    return centralApiClient.makeRequest('database', '/metadata/save_selected_tables_columns', {
      method: 'POST',
      body: payload
    });
  }

  /**
   * Delete metadata content by connection
   */
  static async deleteMetadataContentByConnection(connectionId: string): Promise<any> {
    return centralApiClient.makeRequest('database', `/metadata/delete_metadata_content_by_connection/${connectionId}`, {
      method: 'DELETE'
    });
  }

  /**
   * Create AI database metadata
   */
  static async createAIDatabaseMetadata(payload: CreateAIMetadataPayload): Promise<any> {
    return centralApiClient.makeRequest('database', '/metadata/create_ai_database_metadata', {
      method: 'POST',
      body: payload
    });
  }

  /**
   * Create AI database metadata with extended timeout
   */
  static async createAIDatabaseMetadataWithTimeout(payload: CreateAIMetadataPayload, timeoutMs: number = 600000): Promise<any> {
    return centralApiClient.makeRequest('database', '/metadata/create_ai_database_metadata', {
      method: 'POST',
      body: payload,
      timeout: timeoutMs
    });
  }

  /**
   * Update metadata for tables
   */
  static async updateMetadata(payload: UpdateMetadataPayload): Promise<any> {
    return centralApiClient.makeRequest('database', '/metadata/update_metadata', {
      method: 'PUT',
      body: payload
    });
  }

  /**
   * Create database metadata embedding by connection ID
   */
  static async createDatabaseMetadataEmbedding(connectionId: string): Promise<any> {
    return centralApiClient.makeRequest('database', `/embedding/create_database_metadata_embedding_by_connection_id/${connectionId}`, {
      method: 'GET'
    });
  }
}

// Helper functions for form data conversion
export class DatabaseHelpers {
  
  /**
   * Convert form data to API payload
   */
  static formToPayload(formData: DatabaseConnectionForm, userId: string): CreateConnectionPayload {
    return {
      user_id: userId,
      connection_name: formData.connectionName,
      db_dialect: formData.driverDialect,
      database_name: formData.databaseName,
      username: formData.username,
      password: formData.password,
      host: formData.host,
      port: parseInt(formData.port),
      ssl_mode: 'prefer'
    };
  }

  /**
   * Create selected tables payload
   */
  static createSelectedTablesPayload(connectionId: string, selectedTables: string[], getTableColumns: (tableName: string) => string[]): SaveSelectedTablesPayload {
    const tableList = selectedTables.map((tableName) => ({
      table_name: tableName,
      table_columns: getTableColumns(tableName)
    }));

    return {
      connection_id: connectionId,
      selected_tables_columns: tableList
    };
  }

  /**
   * Create AI metadata payload from selected tables response
   */
  static createAIMetadataPayload(selectedTablesResponse: any, connectionId: string): CreateAIMetadataPayload {
    let selectedTables = [];
    
    if (selectedTablesResponse.selected_tables_columns) {
      selectedTables = selectedTablesResponse.selected_tables_columns;
    } else if (selectedTablesResponse.table_list) {
      selectedTables = selectedTablesResponse.table_list;
    } else if (Array.isArray(selectedTablesResponse)) {
      selectedTables = selectedTablesResponse;
    }

    return {
      success: "success",
      table_list: selectedTables,
      connection_id: connectionId
    };
  }
}
