
import { ChartSaveConfig } from '@/services/api/chart/chartTypes';

/**
 * Interface for normalized navigation payload
 */
export interface NormalizedChartData {
  chart_type: string;
  x_axis: string;
  y_axis: string;
  table_name: string;
  tables: { table_name: string; columns: string[] }[];
  chart_name: string;
  db_type: string;
  aggregation?: string;
  group_by?: string | string[];
}

/**
 * Normalizes y_axis data to extract column and aggregation information
 */
export const normalizeYAxisData = (yAxis: any): { column: string; aggregation: string | null } => {
  console.log('Normalizing y_axis data:', yAxis);
  
  // If it's already a string, return it with no aggregation
  if (typeof yAxis === 'string') {
    return { column: yAxis, aggregation: null };
  }
  
  // If it's an array, process the first item
  if (Array.isArray(yAxis) && yAxis.length > 0) {
    const firstItem = yAxis[0];
    
    // If the first item is a string
    if (typeof firstItem === 'string') {
      return { column: firstItem, aggregation: null };
    }
    
    // If the first item is an object with column property
    if (typeof firstItem === 'object' && firstItem !== null && 'column' in firstItem) {
      return {
        column: firstItem.column,
        aggregation: firstItem.aggregation || null
      };
    }
  }
  
  console.error('Unknown y_axis format:', yAxis);
  return { column: '', aggregation: null };
};

/**
 * Extracts table_name from chart data with fallback logic
 */
export const extractTableName = (savedChart: any): string => {
  console.log('Extracting table name from chart:', savedChart);
  
  // First, try to get table_name directly
  if (savedChart.table_name && typeof savedChart.table_name === 'string') {
    console.log('Found direct table_name:', savedChart.table_name);
    return savedChart.table_name;
  }
  
  // If no direct table_name, try to extract from tables array
  if (savedChart.tables && Array.isArray(savedChart.tables) && savedChart.tables.length > 0) {
    const firstTable = savedChart.tables[0];
    if (firstTable && firstTable.table_name) {
      console.log('Extracted table_name from tables array:', firstTable.table_name);
      return firstTable.table_name;
    }
  }
  
  // As a last resort, try to use a default or empty string
  console.warn('Could not extract table_name from chart data, using fallback');
  return 'default_table'; // Provide a fallback instead of empty string
};

/**
 * Creates a normalized navigation payload from saved chart data
 */
export const createNavigationPayload = (
  savedChart: any,
  connectionId: string
): { chartData: NormalizedChartData; connectionId: string } => {
  console.log('Creating navigation payload for chart:', savedChart.chart_name);
  
  // Handle y_axis data based on chart type and structure
  let yAxisData = savedChart.y_axis;
  let aggregation = null;
  
  // Extract y-axis column and aggregation
  if (typeof yAxisData === 'string') {
    // Simple string y-axis
    yAxisData = savedChart.y_axis;
  } else if (Array.isArray(yAxisData) && yAxisData.length > 0) {
    // Array of y-axis values
    if (typeof yAxisData[0] === 'string') {
      // Array of strings
      yAxisData = yAxisData;
    } else if (typeof yAxisData[0] === 'object' && yAxisData[0].column) {
      // Array of objects with column property
      aggregation = yAxisData[0].aggregation || null;
      yAxisData = yAxisData.map(item => item.column);
    }
  } else if (typeof yAxisData === 'object' && yAxisData !== null && yAxisData.column) {
    // Single object with column property
    aggregation = yAxisData.aggregation || null;
    yAxisData = yAxisData.column;
  }
  
  // Use simplified y-axis if available
  if (savedChart.simplifiedYAxis) {
    yAxisData = savedChart.simplifiedYAxis;
  }
  
  // Use stored aggregation type if available
  if (savedChart.aggregationType) {
    aggregation = savedChart.aggregationType;
  }
  
  // Extract table name with fallback logic
  const tableName = extractTableName(savedChart);
  
  // Create tables array with fallback
  const tables = savedChart.tables && savedChart.tables.length > 0 
    ? savedChart.tables 
    : [{
        table_name: tableName,
        columns: [savedChart.x_axis].concat(Array.isArray(yAxisData) ? yAxisData : [yAxisData]).filter(Boolean)
      }];
  
  // Handle group_by field
  let groupBy = savedChart.group_by;
  
  // For pie charts, ensure we have the correct structure
  if (savedChart.chart_type === 'pie') {
    console.log('Creating navigation payload for pie chart');
    
    // If we don't have a group_by but have an x_axis, use that
    if (!groupBy && savedChart.x_axis) {
      groupBy = savedChart.x_axis;
    }
  }
  
  // If we have aggregation, ensure we have group_by set to x_axis
  if (aggregation && !groupBy && savedChart.x_axis) {
    groupBy = savedChart.x_axis;
  }
  
  // Create normalized payload
  const normalizedData: NormalizedChartData = {
    chart_type: savedChart.chart_type || 'bar',
    x_axis: savedChart.x_axis || '',
    y_axis: yAxisData || '',
    table_name: tableName,
    tables: tables,
    chart_name: savedChart.chart_name || 'Untitled Chart',
    db_type: savedChart.db_type || 'postgres',
    aggregation: aggregation || undefined,
    group_by: groupBy || undefined
  };
  
  console.log('Created normalized navigation payload:', normalizedData);
  
  return {
    chartData: normalizedData,
    connectionId: connectionId
  };
};

/**
 * Validates navigation payload to ensure all required data is present
 */
export const validateNavigationPayload = (payload: any): boolean => {
  if (!payload) {
    console.error('Navigation payload is null or undefined');
    return false;
  }
  
  if (!payload.chartData) {
    console.error('Navigation payload missing chartData property');
    return false;
  }
  
  const { chartData } = payload;
  
  // Check required fields
  const requiredFields = ['chart_type', 'x_axis', 'y_axis'];
  for (const field of requiredFields) {
    if (!chartData[field] && chartData[field] !== 0) {
      console.error(`Navigation payload missing required field: ${field}`, chartData);
      return false;
    }
  }
  
  // Check for chart_name and db_type with more flexible validation
  if (!chartData.chart_name) {
    console.log('Chart name missing, using default');
    chartData.chart_name = 'Untitled Chart';
  }
  
  if (!chartData.db_type) {
    console.log('Database type missing, using default');
    chartData.db_type = 'postgres';
  }
  
  // Check table_name with more flexible validation
  if (!chartData.table_name) {
    // Try to extract table name from tables array
    if (chartData.tables && chartData.tables.length > 0) {
      chartData.table_name = chartData.tables[0].table_name;
      console.log('Extracted table_name from tables array:', chartData.table_name);
    } else {
      console.error('Navigation payload missing table_name and cannot extract from tables');
      return false;
    }
  }
  
  console.log('Navigation payload validation passed:', chartData);
  return true;
};
