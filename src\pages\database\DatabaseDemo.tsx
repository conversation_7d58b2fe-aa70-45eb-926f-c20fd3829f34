import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Database, Table, Plus, Eye, Edit } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const DatabaseDemo: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Database Management System</h1>
          <p className="text-lg text-gray-600 mb-6">
            Complete database connection and table management interface
          </p>
          <Button 
            onClick={() => navigate('/Admin/database')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 text-lg"
          >
            <Database className="mr-2 h-5 w-5" />
            Open Database Management
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {/* Database Connections Card */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center mb-4">
              <Database className="h-8 w-8 text-blue-600 mr-3" />
              <h3 className="text-xl font-semibold">Database Connections</h3>
            </div>
            <p className="text-gray-600 mb-4">
              Manage multiple database connections including PostgreSQL, MySQL, SQL Server, and more.
            </p>
            <div className="space-y-2">
              <div className="flex items-center text-sm">
                <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                <span>DB_Connection1 (PostgreSQL)</span>
              </div>
              <div className="flex items-center text-sm">
                <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                <span>DB_Connection2 (MySQL)</span>
              </div>
              <div className="flex items-center text-sm">
                <div className="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                <span>DB_Connection3 (SQL Server)</span>
              </div>
            </div>
          </div>

          {/* Table Management Card */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center mb-4">
              <Table className="h-8 w-8 text-green-600 mr-3" />
              <h3 className="text-xl font-semibold">Table Management</h3>
            </div>
            <p className="text-gray-600 mb-4">
              Create, edit, and manage database tables with an intuitive interface.
            </p>
            <div className="space-y-2">
              <div className="flex items-center text-sm">
                <Table className="w-4 h-4 text-gray-500 mr-2" />
                <span>Table1 (BASE TABLE)</span>
              </div>
              <div className="flex items-center text-sm">
                <Eye className="w-4 h-4 text-gray-500 mr-2" />
                <span>View1 (VIEW)</span>
              </div>
              <div className="flex items-center text-sm">
                <Table className="w-4 h-4 text-gray-500 mr-2" />
                <span>users (BASE TABLE)</span>
              </div>
            </div>
          </div>

          {/* Features Card */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center mb-4">
              <Plus className="h-8 w-8 text-purple-600 mr-3" />
              <h3 className="text-xl font-semibold">Key Features</h3>
            </div>
            <p className="text-gray-600 mb-4">
              Comprehensive database management with advanced features.
            </p>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center">
                <div className="w-2 h-2 bg-purple-500 rounded-full mr-2"></div>
                <span>Real-time table data viewing</span>
              </li>
              <li className="flex items-center">
                <div className="w-2 h-2 bg-purple-500 rounded-full mr-2"></div>
                <span>Column management & metadata</span>
              </li>
              <li className="flex items-center">
                <div className="w-2 h-2 bg-purple-500 rounded-full mr-2"></div>
                <span>Connection testing</span>
              </li>
              <li className="flex items-center">
                <div className="w-2 h-2 bg-purple-500 rounded-full mr-2"></div>
                <span>Search & filtering</span>
              </li>
            </ul>
          </div>
        </div>

        {/* Sample Data Preview */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-semibold mb-4">Sample Table Data Preview</h3>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-gray-50">
                  <th className="border border-gray-300 px-4 py-2 text-left">ID</th>
                  <th className="border border-gray-300 px-4 py-2 text-left">Name</th>
                  <th className="border border-gray-300 px-4 py-2 text-left">Email</th>
                  <th className="border border-gray-300 px-4 py-2 text-left">Created At</th>
                  <th className="border border-gray-300 px-4 py-2 text-left">Status</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td className="border border-gray-300 px-4 py-2">1</td>
                  <td className="border border-gray-300 px-4 py-2">John Doe</td>
                  <td className="border border-gray-300 px-4 py-2"><EMAIL></td>
                  <td className="border border-gray-300 px-4 py-2">2024-01-15</td>
                  <td className="border border-gray-300 px-4 py-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">active</span>
                  </td>
                </tr>
                <tr>
                  <td className="border border-gray-300 px-4 py-2">2</td>
                  <td className="border border-gray-300 px-4 py-2">Jane Smith</td>
                  <td className="border border-gray-300 px-4 py-2"><EMAIL></td>
                  <td className="border border-gray-300 px-4 py-2">2024-01-16</td>
                  <td className="border border-gray-300 px-4 py-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">active</span>
                  </td>
                </tr>
                <tr>
                  <td className="border border-gray-300 px-4 py-2">3</td>
                  <td className="border border-gray-300 px-4 py-2">Bob Johnson</td>
                  <td className="border border-gray-300 px-4 py-2"><EMAIL></td>
                  <td className="border border-gray-300 px-4 py-2">2024-01-17</td>
                  <td className="border border-gray-300 px-4 py-2">
                    <span className="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs">inactive</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <p className="text-sm text-gray-500 mt-4">Showing 3 of 150 total rows</p>
        </div>

        {/* Navigation Buttons */}
        <div className="flex justify-center gap-4 mt-8">
          <Button 
            onClick={() => navigate('/Admin/menu')}
            variant="outline"
            className="px-6 py-3"
          >
            Create New Connection
          </Button>
          <Button 
            onClick={() => navigate('/Admin/database')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3"
          >
            <Database className="mr-2 h-4 w-4" />
            Manage Databases
          </Button>
        </div>
      </div>
    </div>
  );
};

export default DatabaseDemo;
