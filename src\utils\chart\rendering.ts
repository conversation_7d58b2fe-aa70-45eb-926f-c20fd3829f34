
import { ChartDataItem } from '@/services/api/chart/chartTypes';

interface ColumnOption {
  id: string;
  name: string;
  tableName: string;
}

/**
 * Generates a chart title based on the selected columns
 */
export const getChartTitle = (
  xAxisColumn: string,
  yAxisColumns: string[],
  availableColumns: ColumnOption[]
): string => {
  const xAxisName = availableColumns.find(c => c.id === xAxisColumn)?.name || xAxisColumn;
  const yAxisName = yAxisColumns.map(col => 
    availableColumns.find(c => c.id === col)?.name || col
  ).join(' & ');
  
  return `${yAxisName} by ${xAxisName}`;
};

/**
 * Prepares data for chart rendering
 */
export const prepareChartData = (
  combinedChartData: ChartDataItem[],
  xAxisColumn: string,
  yAxisColumns: string[],
  chartXAxisKey: string,
  chartYAxisKey: string,
  chartStyle: 'bar' | 'line' | 'pie'
) => {
  if (!combinedChartData || combinedChartData.length === 0) {
    return {
      labels: [],
      datasets: []
    };
  }
  
  // Use the dynamically identified keys or fall back to defaults
  const xKey = chartXAxisKey || xAxisColumn || 'x';
  const yKey = chartYAxisKey || (yAxisColumns.length > 0 ? yAxisColumns[0] : 'y');
  
  // Extract unique x-axis values
  const xAxisValues = [...new Set(combinedChartData.map(item => item[xKey]))];
  
  // Determine which columns to use for the y-axis
  const columnsToUse = yAxisColumns.length > 0 ? yAxisColumns : [yKey];
  
  // Generate colors for the chart
  const baseColors = ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6'];
  const enhancedColors = baseColors.map(color => `${color}cc`); // Add transparency
  const borderColors = baseColors.map(color => `${color}ff`); // Full opacity for borders
  
  // Process data for the chart
  const processedData = xAxisValues.map(xValue => {
    const matchingItems = combinedChartData.filter(item => item[xKey] === xValue);
    
    // Create an object with the x value
    const dataPoint: any = { [xKey]: xValue };
    
    // Add y values for each column
    columnsToUse.forEach(columnId => {
      // If we have matching items, use their values
      if (matchingItems.length > 0) {
        // For aggregated data, use the aggregated value
        if (matchingItems[0][columnId] !== undefined) {
          dataPoint[columnId] = matchingItems[0][columnId];
        } else {
          // For non-aggregated data, calculate the sum
          dataPoint[columnId] = matchingItems.reduce((sum, item) => sum + (Number(item[columnId]) || 0), 0);
        }
      } else {
        dataPoint[columnId] = 0;
      }
    });
    
    return dataPoint;
  });
  
  // For bar and line charts
  return {
    labels: xAxisValues,
    datasets: columnsToUse.map((columnId, index) => {
      return {
        label: columnId,
        data: processedData.map(item => item[columnId]),
        backgroundColor: enhancedColors[0],
        borderColor: chartStyle === 'line' ? borderColors[0] : undefined,
        borderWidth: 1,
        borderRadius: 6,
        barPercentage: 0.4,
        categoryPercentage: 0.5,
        hoverBackgroundColor: borderColors[0],
        hoverBorderWidth: 2
      };
    })
  };
};

/**
 * Creates base chart options
 */
export const getBaseChartOptions = (
  xAxisColumn: string,
  yAxisColumns: string[],
  availableColumns: ColumnOption[]
) => {
  return {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        align: 'center' as const,
        labels: {
          boxWidth: 20,
          padding: 15,
          font: {
            size: 12
          }
        }
      },
      title: {
        display: true,
        text: getChartTitle(xAxisColumn, yAxisColumns, availableColumns),
        font: {
          size: 16,
          weight: 'bold' as const
        },
        padding: {
          top: 10,
          bottom: 20
        }
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        titleColor: '#333',
        bodyColor: '#333',
        borderColor: '#ddd',
        borderWidth: 1,
        padding: 10,
        displayColors: true
      }
    }
  };
};
