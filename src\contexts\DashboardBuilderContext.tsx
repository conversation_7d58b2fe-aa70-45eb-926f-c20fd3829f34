import React, { createContext, useContext, ReactNode } from 'react';
import { useDashboardState, CardState, DashboardState } from '@/hooks/dashboard/useDashboardState';
import { useDragAndDrop } from '@/hooks/dashboard/useDragAndDrop';
import { useChartDataLoader } from '@/hooks/dashboard/useChartDataLoader';
import { useDashboardPersistence } from '@/hooks/dashboard/useDashboardPersistence';
import { SavedChart } from '@/types/chartTypes';
import { toast } from 'sonner';

interface DashboardBuilderContextType {
  // State and actions from useDashboardState
  state: DashboardState;
  cardArray: CardState[];
  chartCards: CardState[];
  tableCards: CardState[];
  
  // Card management
  addCard: (type: 'chart' | 'table') => string;
  removeCard: (cardId: string) => void;
  updateCard: (cardId: string, updates: Partial<CardState>) => void;
  reorderCards: (newOrder: string[]) => void;
  
  // UI state management
  setSearchQuery: (query: string) => void;
  toggleSidebar: () => void;
  toggleFolder: (folderName: string) => void;
  
  // Chart management
  addSelectedChart: (chart: SavedChart) => void;
  removeSelectedChart: (chartId: string) => void;
  
  // Zone management (legacy)
  setChartZone: (chart: SavedChart | null) => void;
  setTableZone: (chart: SavedChart | null) => void;
  setZoneChartData: (data: any[] | null) => void;
  setTabularData: (data: any[] | null) => void;
  setLoadingZoneData: (loading: boolean) => void;
  setLoadingTabularData: (loading: boolean) => void;
  
  // Cache management
  cacheChartData: (chartId: string, data: any) => void;
  getCachedData: (chartId: string) => any;
  clearCache: () => void;
  
  // Reset
  resetDashboard: () => void;
  
  // Drag and Drop
  sensors: any;
  handleDragEnd: (event: any) => Promise<void>;
  
  // Data loading
  loadChartDataForCard: (chart: SavedChart, cardId: string) => Promise<void>;
  loadTabularDataForCard: (chart: SavedChart, cardId: string) => Promise<void>;
  
  // Persistence
  saveDashboard: (name: string) => Promise<string | null>;
  loadDashboard: (dashboardId: string) => Promise<void>;
  getSavedDashboards: () => Promise<Array<{id: string; name: string; updatedAt: number}>>;
  deleteDashboard: (dashboardId: string) => Promise<boolean>;
  exportDashboard: () => void;
  importDashboard: (file: File) => Promise<void>;
}

const DashboardBuilderContext = createContext<DashboardBuilderContextType | undefined>(undefined);

interface DashboardBuilderProviderProps {
  children: ReactNode;
}

export const DashboardBuilderProvider: React.FC<DashboardBuilderProviderProps> = ({ children }) => {
  const dashboardState = useDashboardState();
  const dataLoader = useChartDataLoader();
  const persistence = useDashboardPersistence();

  // Enhanced data loading functions that integrate with state
  const loadChartDataForCard = async (chart: SavedChart, cardId: string) => {
    dashboardState.updateCard(cardId, { loading: true });
    await dataLoader.loadChartDataForCard(chart, cardId, dashboardState.updateCard);
  };

  const loadTabularDataForCard = async (chart: SavedChart, cardId: string) => {
    dashboardState.updateCard(cardId, { loading: true });
    await dataLoader.loadTabularDataForCard(chart, cardId, dashboardState.updateCard);
  };

  // Drag and drop handlers
  const handleCardDrop = async (draggedChart: SavedChart, cardId: string, cardType: 'chart' | 'table') => {
    dashboardState.updateCard(cardId, {
      chart: draggedChart,
      loading: true
    });

    if (cardType === 'chart') {
      await loadChartDataForCard(draggedChart, cardId);
    } else {
      await loadTabularDataForCard(draggedChart, cardId);
    }
  };

  const handleZoneDrop = async (draggedChart: SavedChart, zoneType: 'chart' | 'table') => {
    if (zoneType === 'chart') {
      dashboardState.setChartZone(draggedChart);
      await dataLoader.loadChartDataForZone(
        draggedChart,
        dashboardState.setZoneChartData,
        dashboardState.setLoadingZoneData
      );
    } else {
      dashboardState.setTableZone(draggedChart);
      await dataLoader.loadTabularDataForZone(
        draggedChart,
        dashboardState.setTabularData,
        dashboardState.setLoadingTabularData
      );
    }
  };

  const dragAndDrop = useDragAndDrop({
    onCardDrop: handleCardDrop,
    onZoneDrop: handleZoneDrop,
  });

  // Enhanced persistence functions
  const saveDashboard = async (name: string) => {
    return await persistence.saveDashboard(
      name,
      dashboardState.cardArray,
      dashboardState.state.selectedCharts,
      dashboardState.state.cardOrder,
      dashboardState.state.sidebarCollapsed
    );
  };

  const loadDashboard = async (dashboardId: string) => {
    const config = await persistence.loadDashboard(dashboardId);
    if (config) {
      // Reset current state
      dashboardState.resetDashboard();
      
      // Load the configuration
      config.cards.forEach(card => {
        const cardId = dashboardState.addCard(card.type);
        dashboardState.updateCard(cardId, card);
      });
      
      config.selectedCharts.forEach(chart => {
        dashboardState.addSelectedChart(chart);
      });
      
      dashboardState.reorderCards(config.layout.cardOrder);
      
      if (config.layout.sidebarCollapsed) {
        dashboardState.toggleSidebar();
      }
    }
  };

  const exportDashboard = () => {
    const config = {
      name: `Dashboard-${Date.now()}`,
      cards: dashboardState.cardArray,
      selectedCharts: dashboardState.state.selectedCharts,
      layout: {
        cardOrder: dashboardState.state.cardOrder,
        sidebarCollapsed: dashboardState.state.sidebarCollapsed,
      },
      metadata: {
        createdAt: Date.now(),
        updatedAt: Date.now(),
        version: '1.0.0',
      },
    };
    persistence.exportDashboard(config);
  };

  const importDashboard = async (file: File) => {
    const config = await persistence.importDashboard(file);
    if (config) {
      await loadDashboard(config.id || 'imported');
    }
  };

  const contextValue: DashboardBuilderContextType = {
    // State
    ...dashboardState,
    
    // Drag and Drop
    sensors: dragAndDrop.sensors,
    handleDragEnd: dragAndDrop.handleDragEnd,
    
    // Enhanced data loading
    loadChartDataForCard,
    loadTabularDataForCard,
    
    // Persistence
    saveDashboard,
    loadDashboard,
    getSavedDashboards: persistence.getSavedDashboards,
    deleteDashboard: persistence.deleteDashboard,
    exportDashboard,
    importDashboard,
  };

  return (
    <DashboardBuilderContext.Provider value={contextValue}>
      {children}
    </DashboardBuilderContext.Provider>
  );
};

export const useDashboardBuilder = (): DashboardBuilderContextType => {
  const context = useContext(DashboardBuilderContext);
  if (context === undefined) {
    throw new Error('useDashboardBuilder must be used within a DashboardBuilderProvider');
  }
  return context;
};