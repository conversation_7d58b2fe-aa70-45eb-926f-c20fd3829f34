
import React, { useState, useEffect } from 'react';
import { Search, ArrowRight, ArrowLeft } from 'lucide-react';
import { useAppDispatch, useAppSelector } from '@/hooks/useRedux';
import { 
  addColumn,
  removeColumn
} from '@/stores/datasetSlice';
import StepHeader, { NavigationButtons } from './StepHeader';
import { Button } from '@/components/ui/button';
import { ColumnSelector } from './ColumnSelector';
import { SelectedColumnsList } from './SelectedColumnsList';

interface ColumnsStepProps {
  onClose: () => void;
}

const ColumnsStep: React.FC<ColumnsStepProps> = ({ onClose }) => {
  const dispatch = useAppDispatch();
  const { selectedTables, selectedColumns } = useAppSelector(state => state.dataset);
  const [localSearchQuery, setLocalSearchQuery] = useState('');
  const [localSelectedColumns, setLocalSelectedColumns] = useState<any[]>([]);

  // Clear local selected columns when component mounts
  useEffect(() => {
    setLocalSelectedColumns([]);
    console.log('Selected tables:', selectedTables);
  }, []);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalSearchQuery(e.target.value);
  };

  const handleToggleColumn = (column: any, tableName: string) => {
    console.log('Toggling column:', column, 'from table:', tableName);
    
    const isLocallySelected = localSelectedColumns.some(c => 
      c.name === column.name && c.tableOrigin === tableName
    );
    
    if (isLocallySelected) {
      setLocalSelectedColumns(localSelectedColumns.filter(c => 
        !(c.name === column.name && c.tableOrigin === tableName)
      ));
    } else {
      setLocalSelectedColumns([...localSelectedColumns, {
        ...column,
        tableOrigin: tableName
      }]);
    }
  };

  const handleAddToSelected = () => {
    // Add all locally selected columns to the global state
    if (localSelectedColumns.length === 0) {
      return;
    }
    
    localSelectedColumns.forEach(column => {
      const columnExists = selectedColumns.some(c => 
        c.name === column.name && c.tableOrigin === column.tableOrigin
      );
      
      if (!columnExists) {
        dispatch(addColumn(column));
      }
    });
    
    // Clear local selections
    setLocalSelectedColumns([]);
  };

  const handleRemoveFromSelected = () => {
    // Remove all locally selected columns from the global state
    if (localSelectedColumns.length === 0) {
      return;
    }
    
    localSelectedColumns.forEach(column => {
      dispatch(removeColumn({
        name: column.name,
        tableOrigin: column.tableOrigin
      }));
    });
    
    // Clear local selections
    setLocalSelectedColumns([]);
  };

  return (
    <div className="p-4">
      <StepHeader 
        title="Select Columns" 
        onClose={onClose}
      />
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <ColumnSelector 
          selectedTables={selectedTables}
          localSearchQuery={localSearchQuery}
          handleSearch={handleSearch}
          localSelectedColumns={localSelectedColumns}
          handleToggleColumn={handleToggleColumn}
        />

        <div className="flex flex-col items-center justify-center space-y-4">
          <Button 
            onClick={handleAddToSelected} 
            variant="blue" 
            className="w-10 h-10 p-0 flex items-center justify-center"
            disabled={localSelectedColumns.length === 0}
          >
            <ArrowRight size={16} />
          </Button>
          <Button 
            onClick={handleRemoveFromSelected} 
            variant="blue" 
            className="w-10 h-10 p-0 flex items-center justify-center"
            disabled={localSelectedColumns.length === 0}
          >
            <ArrowLeft size={16} />
          </Button>
        </div>

        <SelectedColumnsList 
          selectedColumns={selectedColumns}
          localSelectedColumns={localSelectedColumns}
          handleToggleColumn={handleToggleColumn}
        />
      </div>
      
      {/* Navigation buttons at the bottom */}
      <div className="mt-6">
        <NavigationButtons 
          disableNext={selectedColumns.length === 0}
        />
      </div>
    </div>
  );
};

export default ColumnsStep;
