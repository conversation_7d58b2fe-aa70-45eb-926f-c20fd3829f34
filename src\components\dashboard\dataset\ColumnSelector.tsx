
import React from 'react';
import { Search } from 'lucide-react';

// Sample data for columns - structured by table name for easier lookup
const tableColumns = {
  'Table1': [
    { name: 'Col1', dataType: 'Int', description: 'xxx' },
    { name: 'Col2', dataType: 'Text', description: 'yyy' },
    { name: 'ColDate1', dataType: 'Date', description: 'zzz' }
  ],
  'Table2': [
    { name: 'Id', dataType: 'Int', description: 'Primary key' },
    { name: 'Name', dataType: 'Text', description: 'Customer name' },
    { name: 'CreateDate', dataType: 'Date', description: 'Record creation date' }
  ],
  'Table3': [
    { name: 'Col1', dataType: 'Int', description: 'xxx' },
    { name: 'Col2', dataType: 'Text', description: 'yyy' },
    { name: 'ColDate5', dataType: 'Date', description: 'zzz' }
  ],
  'Table4': [
    { name: 'Id', dataType: 'Int', description: 'Primary key' },
    { name: 'Amount', dataType: 'Decimal', description: 'Transaction amount' },
    { name: 'TransactionDate', dataType: 'Date', description: 'Transaction date' }
  ],
  'Athena_Prod': [
    { name: 'Prod1', dataType: 'Int', description: 'Product ID' },
    { name: 'Prod2', dataType: 'Text', description: 'Product Name' },
    { name: 'Prod3', dataType: 'Decimal', description: 'Product Price' }
  ],
  'Quickcap_Prod': [
    { name: 'QC1', dataType: 'Int', description: 'Quickcap ID' },
    { name: 'QC2', dataType: 'Text', description: 'Quickcap Name' },
    { name: 'QC3', dataType: 'Date', description: 'Quickcap Date' }
  ]
};

interface ColumnSelectorProps {
  selectedTables: string[];
  localSearchQuery: string;
  handleSearch: (e: React.ChangeEvent<HTMLInputElement>) => void;
  localSelectedColumns: any[];
  handleToggleColumn: (column: any, tableName: string) => void;
}

export const ColumnSelector: React.FC<ColumnSelectorProps> = ({
  selectedTables,
  localSearchQuery,
  handleSearch,
  localSelectedColumns,
  handleToggleColumn
}) => {
  // Helper function to extract table name from tableId
  const getTableName = (tableId: string): string => {
    // Split by underscore and get the last part as the table name
    const parts = tableId.split('_');
    return parts.length > 1 ? parts[parts.length - 1] : tableId;
  };

  // Helper function to get connection name from tableId
  const getConnectionName = (tableId: string): string => {
    const parts = tableId.split('_');
    return parts.length > 1 ? parts[0] : '';
  };

  return (
    <div className="border border-gray-200 p-4 rounded-lg">
      <h3 className="font-medium text-sm mb-2">Columns</h3>
      <div className="mb-2 relative">
        <input
          type="text"
          placeholder="Search..."
          value={localSearchQuery}
          onChange={handleSearch}
          className="w-full pl-8 pr-4 py-1 border border-gray-300 rounded-md text-sm"
        />
        <Search className="absolute left-2 top-1.5 text-gray-400" size={16} />
      </div>
      <div className="border border-gray-300 rounded-md h-64 overflow-y-auto p-2">
        {selectedTables.length === 0 ? (
          <div className="text-gray-500 text-sm italic p-2">No tables selected</div>
        ) : (
          selectedTables.map((tableId) => {
            const tableName = getTableName(tableId);
            const connName = getConnectionName(tableId);
            
            // Try to find columns for the table
            let columnsList = tableColumns[tableName] || [];
            
            // If no columns found for table name, try with the connection name
            if (columnsList.length === 0 && connName) {
              columnsList = tableColumns[connName] || [];
            }
            
            return (
              <div key={tableId} className="mb-2">
                <div className="font-medium">{tableName} ({connName})</div>
                {columnsList.length === 0 ? (
                  <div className="text-gray-500 text-sm italic pl-4">No columns available</div>
                ) : (
                  <ul className="pl-4 text-sm">
                    {columnsList
                      .filter((col: any) => 
                        col.name.toLowerCase().includes(localSearchQuery.toLowerCase())
                      )
                      .map((col: any) => {
                        const isLocallySelected = localSelectedColumns.some(c => 
                          c.name === col.name && c.tableOrigin === tableName
                        );
                        
                        return (
                          <li 
                            key={`${tableName}_${col.name}`}
                            className={`py-1 cursor-pointer hover:bg-blue-50 pl-1 rounded ${
                              isLocallySelected ? 'bg-blue-100' : ''
                            }`}
                            onClick={() => handleToggleColumn({...col, tableOrigin: tableName}, tableName)}
                          >
                            {col.name} ({col.dataType})
                          </li>
                        );
                      })
                    }
                  </ul>
                )}
              </div>
            );
          })
        )}
      </div>
    </div>
  );
};
