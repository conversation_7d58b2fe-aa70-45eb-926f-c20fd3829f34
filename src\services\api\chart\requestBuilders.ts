
import { ChartSaveConfig } from './chartTypes';

export class RequestBuilders {
  static buildFetchDataRequest(
    connectionId: string,
    tables: { table_name: string; columns: string[] }[],
    xAxis?: string,
    yAxis?: string[] | { column: string; table_name?: string; aggregation?: string }[],
    groupBy?: string,
    filters?: { [key: string]: any } // Updated type to handle complex filter objects
  ) {
    const isAggregatedQuery = Boolean(xAxis && yAxis && groupBy);
    
    console.log('buildFetchDataRequest called with params:', { 
      connectionId, 
      tables,
      xAxis,
      yAxis,
      groupBy,
      filters, // Log filters
      isAggregatedQuery
    });
    
    // Validate input parameters
    if (!connectionId) {
      throw new Error('Connection ID is required');
    }
    
    if (!tables || tables.length === 0) {
      throw new Error('At least one table must be specified');
    }
    
    // Build the request body
    const requestBody: any = {
      connection_id: connectionId,
      tables: tables
    };
    
    // Only include x_axis, y_axis, and group_by for aggregated queries
    if (isAggregatedQuery) {
      requestBody.x_axis = xAxis;
      requestBody.y_axis = yAxis;
      requestBody.group_by = [groupBy];
    }
    
    // Add filters as a separate property if provided
    if (filters && Object.keys(filters).length > 0) {
      // No transformation needed - pass the filters object directly
      requestBody.filters = filters;
      console.log('Including filters in request:', filters);
    }
    
    console.log('Final request body:', JSON.stringify(requestBody, null, 2));
    
    return requestBody;
  }

  static buildSaveChartRequest(
    connectionId: string,
    config: ChartSaveConfig,
    chartData: {
      status: string;
      data: any[];
      metadata: {
        tables: string[];
        status: string;
      };
    },
    chartImage?: string,
    userEmail?: string
  ) {
    console.log('buildSaveChartRequest called with config:', config);
    
    // Validate required fields
    if (!connectionId) {
      throw new Error('Connection ID is required for saving chart');
    }
    
    if (!config.chart_name || !config.chart_name.trim()) {
      throw new Error('Chart name is required');
    }
    
    if (!config.tables || config.tables.length === 0) {
      throw new Error('At least one table must be specified in chart config');
    }
    
    // Format group_by as an array if it's a string
    const groupBy = config.group_by 
      ? Array.isArray(config.group_by) ? config.group_by : [config.group_by]
      : null;
    
    const saveConfig = {
      chart_type: config.chart_type,
      tables: config.tables,
      x_axis: config.x_axis,
      y_axis: config.y_axis,
      chart_name: config.chart_name,
      db_type: config.db_type,
      clipboard: config.clipboard,
      editable: config.editable,
      refresh: config.refresh,
      shareable: config.shareable,
      export: config.export,
      group_by: groupBy,
      chart_image: chartImage,
      chart_response: chartData,
      verified: config.verfied,
      published_by: userEmail || 'anonymous',
      filters: config.filters // Include filters in the save config
    };
    
    console.log('Built save config:', saveConfig);
    
    return {
      connection_id: connectionId,
      config: saveConfig
    };
  }

  static buildUpdateChartRequest(
    chartId: string,
    connectionId: string,
    config: ChartSaveConfig,
    chartData: {
      status: string;
      data: any[];
      metadata: {
        tables: string[];
        status: string;
      };
    },
    chartImage?: string,
    userEmail?: string
  ) {
    console.log('buildUpdateChartRequest called with chartId:', chartId);
    
    // Validate chart ID
    if (!chartId || !chartId.trim()) {
      throw new Error('Chart ID is required for updating chart');
    }
    
    const saveRequest = this.buildSaveChartRequest(connectionId, config, chartData, chartImage, userEmail);
    
    // Create a new config object with chart_id for update requests
    const updateConfig = {
      ...saveRequest.config,
      chart_id: chartId
    };
    
    console.log('Built update config with chart_id:', updateConfig);
    
    return {
      connection_id: saveRequest.connection_id,
      config: updateConfig
    };
  }
}
