import { centralApiClient } from '@/services/api/centralApiClient';

let isCheckingConnection = false;

/**
 * Initialize connection monitoring
 */
export const initConnectionMonitoring = () => {
  // For local development, just return a no-op cleanup function
  return () => {};
};

/**
 * Check connection to backend
 */
export const checkConnection = async () => {
  // For local development, always return true
  window.dispatchEvent(new CustomEvent('backend-connected'));
  return true;
};

/**
 * Force a connection check
 */
export const forceConnectionCheck = () => {
  return checkConnection();
};

