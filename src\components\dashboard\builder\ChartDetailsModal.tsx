import React from 'react';
import { X, LayoutDashboard, Table, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { SavedChart } from '@/types/chartTypes';
import { NoDataDisplay } from '@/components/dashboard/dadaResultViews/chart/NoDataDisplay';
import ChartRenderer from '@/components/charts/ChartRenderer';

interface ChartDetailsModalProps {
  chart: SavedChart | null;
  isVisible: boolean;
  chartData: any[] | null;
  loadingChartData: boolean;
  onClose: () => void;
  onAddChartToDashboard: (chart: SavedChart) => void;
  onAddChartComponentToDashboard: (chart: SavedChart, componentType: 'chart' | 'table') => void;
}

const ChartDetailsModal: React.FC<ChartDetailsModalProps> = ({
  chart,
  isVisible,
  chartData,
  loadingChartData,
  onClose,
  onAddChartToDashboard,
  onAddChartComponentToDashboard
}) => {
  if (!chart || !isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-5xl max-h-[90vh] overflow-hidden flex flex-col">
        <div className="p-4 border-b flex items-center justify-between">
          <h3 className="text-lg font-medium">{chart.chart_name}</h3>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X size={18} />
          </Button>
        </div>
        
        <div className="p-4 grid grid-cols-1 md:grid-cols-2 gap-4 overflow-auto">
          {/* Chart Visualization Card */}
          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium">
                  <div className="flex items-center">
                    <LayoutDashboard size={16} className="mr-2" />
                    Chart Visualization
                  </div>
                </CardTitle>
                <Button 
                  size="sm" 
                  className="text-xs h-7"
                  onClick={() => {
                    onAddChartComponentToDashboard(chart, 'chart');
                    onClose();
                  }}
                >
                  Add Chart Only
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {loadingChartData ? (
                <div className="h-64 flex items-center justify-center">
                  <Loader2 className="h-6 w-6 text-blue-500 animate-spin" />
                </div>
              ) : chartData && chartData.length > 0 ? (
                <div className="h-64">
                  <ChartRenderer chart={chart} chartData={chartData} />
                </div>
              ) : (
                <NoDataDisplay 
                  height="h-64"
                  textSize="text-base"
                  message="No chart data available"
                  subMessage="No data available for this chart visualization."
                />
              )}
            </CardContent>
          </Card>
          
          {/* Table Data Card */}
          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium">
                  <div className="flex items-center">
                    <Table size={16} className="mr-2" />
                    Table Data
                  </div>
                </CardTitle>
                <Button  
                  size="sm" 
                  className="text-xs h-7"
                  onClick={() => {
                    onAddChartComponentToDashboard(chart, 'table');
                    onClose();
                  }}
                >
                  Add Table Only
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {loadingChartData ? (
                <div className="h-64 flex items-center justify-center">
                  <Loader2 className="h-6 w-6 text-blue-500 animate-spin" />
                </div>
              ) : chartData && chartData.length > 0 ? (
                <div className="h-64 overflow-auto">
                  <table className="w-full text-xs border-collapse">
                    <thead>
                      <tr className="bg-gray-100">
                        {Object.keys(chartData[0]).map((key) => (
                          <th key={key} className="p-2 text-left border">{key}</th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      {chartData.slice(0, 10).map((row, i) => (
                        <tr key={i} className={i % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                          {Object.values(row).map((value, j) => (
                            <td key={j} className="p-2 border">
                              {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                            </td>
                          ))}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                  {chartData.length > 10 && (
                    <div className="text-center text-xs text-gray-500 mt-2">
                      Showing 10 of {chartData.length} rows
                    </div>
                  )}
                </div>
              ) : (
                <NoDataDisplay 
                  height="h-64"
                  textSize="text-base"
                  message="No table data available"
                  subMessage="No data available for this chart."
                />
              )}
            </CardContent>
          </Card>
        </div>

        {/* Add to Dashboard button at the bottom */}
        <div className="p-4 border-t flex justify-end">
          <Button 
            onClick={() => {
              onAddChartToDashboard(chart);
              onClose();
            }}
            variant="blue"
          >
            Add to Dashboard
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ChartDetailsModal;
