import type { QueryResultData } from "@/components/dashboard/models";
import { SavedChart, ChartDataPoint } from '@/types/chartTypes';

/**
 * Data adapter to convert between different chart data formats
 */

export interface UnifiedChartData {
  chart: SavedChart;
  chartData: ChartDataPoint[];
  isConverted: boolean;
  originalFormat: 'queryResult' | 'chartBuilder';
}

/**
 * Convert QueryResultData to ChartBuilder format for unified rendering
 */
export const convertQueryResultToChartBuilder = (
  queryResult: QueryResultData
): UnifiedChartData | null => {
  if (!queryResult?.tableData?.rows?.length) {
    return null;
  }

  const { rows, columns } = queryResult.tableData;
  const viz = queryResult.visualization;

  // Determine chart configuration
  let xAxis = 'category';
  let yAxis = 'value';
  let chartType = 'bar';

  if (viz) {
    chartType = viz.best_chart_type?.toLowerCase() || 'bar';
    xAxis = viz.x_axis_key_name || 'category';
    yAxis = viz.y_axis_key_name || 'value';
  }

  // Convert data to ChartBuilder format
  let chartData: ChartDataPoint[];

  // Handle single aggregate values
  if (rows.length === 1 && columns.length === 1) {
    const key = columns[0];
    const value = rows[0][key];
    const label = viz?.chart_title || key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    
    chartData = [{
      [xAxis]: label,
      [yAxis]: Number(value) || 0
    }];
  } else {
    // Handle multi-row data
    chartData = rows.map(row => {
      const converted: ChartDataPoint = {};
      
      // Map columns to chart data
      columns.forEach(col => {
        const value = row[col];
        if (typeof value === 'number') {
          converted[col] = value;
        } else if (typeof value === 'string') {
          const numValue = Number(value);
          converted[col] = !isNaN(numValue) ? numValue : value;
        } else {
          converted[col] = value;
        }
      });
      
      return converted;
    });
  }

  // Create SavedChart object matching the interface
  const chart: SavedChart = {
    chart_id: `generated-${Date.now()}`,
    chart_name: viz?.chart_title || 'Generated Chart',
    chart_image: '',
    description: null,
    chart_type: chartType,
    table_name: '',
    db_type: 'postgres',
    x_axis: xAxis,
    y_axis: yAxis,
    tables: [],
    clipboard: false,
    editable: true,
    refresh: false,
    shareable: false,
    export: false,
    filters: {},
    sort_order: null,
    group_by: null,
    verified: false,
    views: 0,
    published_by: '',
    created_at: new Date().toISOString(),
    connection_id: ''
  };

  return {
    chart,
    chartData,
    isConverted: true,
    originalFormat: 'queryResult'
  };
};

/**
 * Validate ChartBuilder data format
 */
export const validateChartBuilderData = (
  chart: SavedChart | null,
  chartData: ChartDataPoint[] | null
): boolean => {
  if (!chart || !chartData || chartData.length === 0) {
    return false;
  }

  // Check if required chart properties exist
  if (!chart.chart_type || !chart.x_axis || !chart.y_axis) {
    return false;
  }

  // Check if data has the required columns
  const firstDataPoint = chartData[0];
  if (!firstDataPoint) {
    return false;
  }

  return true;
};

/**
 * Normalize chart data for consistent rendering
 */
export const normalizeChartData = (
  chart: SavedChart,
  chartData: ChartDataPoint[]
): { chart: SavedChart; chartData: ChartDataPoint[] } => {
  // Ensure chart data has the expected structure
  const normalizedData = chartData.map(item => {
    const normalized: ChartDataPoint = { ...item };
    
    // Ensure numeric values are properly typed
    Object.keys(normalized).forEach(key => {
      const value = normalized[key];
      if (typeof value === 'string') {
        const numValue = Number(value);
        if (!isNaN(numValue) && isFinite(numValue)) {
          normalized[key] = numValue;
        }
      }
    });
    
    return normalized;
  });

  return {
    chart: { ...chart },
    chartData: normalizedData
  };
};

/**
 * Create unified chart props from either data format
 */
export const createUnifiedChartProps = (
  queryResult?: QueryResultData,
  chart?: SavedChart,
  chartData?: ChartDataPoint[]
): UnifiedChartData | null => {
  // If we have queryResult, convert it
  if (queryResult) {
    return convertQueryResultToChartBuilder(queryResult);
  }
  
  // If we have chart builder data, validate and normalize it
  if (chart && chartData) {
    if (!validateChartBuilderData(chart, chartData)) {
      return null;
    }
    
    const normalized = normalizeChartData(chart, chartData);
    return {
      chart: normalized.chart,
      chartData: normalized.chartData,
      isConverted: false,
      originalFormat: 'chartBuilder'
    };
  }
  
  return null;
};
