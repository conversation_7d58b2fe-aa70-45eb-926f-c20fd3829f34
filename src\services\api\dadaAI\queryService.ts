import { toast } from 'sonner';
import { QueryResultData } from '@/components/dashboard/models';
import { centralApiClient } from '@/services/api/centralApiClient';

/**
 * Processes a query using the DADA AI API
 * @param connectionId The database connection ID
 * @param queryText The natural language query text
 * @returns Promise with query result data
 */
export const processQuery = async (connectionId: number, queryText: string): Promise<QueryResultData> => {
  try {
    // Validate inputs
    if (!connectionId) {
      throw new Error('No database connection selected. Please select a database connection first.');
    }

    if (!queryText || queryText.trim() === '') {
      throw new Error('Query text cannot be empty.');
    }

    console.log(`Processing query for connection ID: ${connectionId}, query: ${queryText}`);

    const requestBody = {
      connection_id: connectionId,
      query_text: queryText
    };

    console.log('Request payload:', requestBody);

    const data = await centralApiClient.makeRequest<any>('dada', '/agent/query', {
      method: 'POST',
      body: requestBody
    });

    console.log('Query API response:', data);

    // Transform the API response to match the expected QueryResultData format
    return transformQueryResponse(data);
  } catch (error) {
    console.error('Error processing query:', error);
    toast.error('Query processing failed', {
      description: error instanceof Error ? error.message : 'Unknown error occurred'
    });
    throw error;
  }
};

/**
 * Transforms the DADA AI API response to the application's QueryResultData format
 * @param apiResponse The raw API response
 * @returns Formatted QueryResultData
 */
function transformQueryResponse(apiResponse: any): QueryResultData {
  try {
    // Check if the response has the expected structure
    if (apiResponse && apiResponse.result) {
      const result = apiResponse.result;
      
      // Extract column names from the first result row if available
      const columns = result.sql_result && result.sql_result.length > 0
        ? Object.keys(result.sql_result[0]).map(key => key)
        : [];

      console.log('[QueryService] Transforming API response:', {
        sql_result_length: result.sql_result?.length || 0,
        columns: columns,
        has_visualization: !!result.visualization_dict,
        can_visualize: result.visualization_dict?.can_visualize
      });

      return {
        query: result.sql_query || '',
        sql_result: result.sql_result || [], // Add the sql_result field for chart processing
        tableData: {
          columns: columns,
          rows: result.sql_result || []
        },
        // Add visualization data if available
        visualization: result.visualization_dict ? {
          can_visualize: result.visualization_dict.can_visualize,
          best_chart_type: result.visualization_dict.best_chart_type,
          chart_title: result.visualization_dict.chart_title,
          x_axis_key_name: result.visualization_dict.x_axis_key_name,
          y_axis_key_name: result.visualization_dict.y_axis_key_name,
          reasoning: result.visualization_dict.reasoning
        } : undefined
      };
    }
    
    // Fallback for other response formats
    return {
      query: apiResponse.sql || apiResponse.query || '',
      tableData: {
        columns: apiResponse.columns || [],
        rows: apiResponse.rows || []
      }
    };
  } catch (error) {
    console.error('Error transforming query response:', error);
    return {
      query: '',
      tableData: {
        columns: [],
        rows: []
      }
    };
  }
}



