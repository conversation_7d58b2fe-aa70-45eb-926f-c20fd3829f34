import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { ArrowLeft } from 'lucide-react';

interface DatabaseConnection {
  connection_id: number;
  connection_name: string;
  database_dialect: string;
  database_name: string;
  last_used: string;
  config: {
    host: string;
    port: number;
    user_id: number;
    ssl_mode: string;
    username: string;
    db_dialect: string;
    database_name: string;
    connection_name: string;
    database_dialect: string;
    password?: string;
  };
}

// Configuration mapping for dynamic data patching from config object
interface ConfigFieldMapping {
  formField: string;
  configKey: string;
  type: 'string' | 'number';
  defaultValue?: any;
}

const CONFIG_FIELD_MAPPINGS: ConfigFieldMapping[] = [
  { formField: 'connectionName', configKey: 'connection_name', type: 'string' },
  { formField: 'host', configKey: 'host', type: 'string' },
  { formField: 'port', configKey: 'port', type: 'number', defaultValue: 5432 },
  { formField: 'databaseName', configKey: 'database_name', type: 'string' },
  { formField: 'username', configKey: 'username', type: 'string' },
  { formField: 'driverDialect', configKey: 'db_dialect', type: 'string', defaultValue: 'postgresql' },
  { formField: 'sslMode', configKey: 'ssl_mode', type: 'string', defaultValue: 'prefer' }
];

const EditDatabaseConnection: React.FC = () => {
  const { connectionId } = useParams<{ connectionId: string }>();
  const navigate = useNavigate();
  
  // Database connection form state
  const [dbConnection, setDbConnection] = useState({
    connectionName: '',
    host: '',
    port: '5432',
    databaseName: '',
    username: '',
    password: '',
    driverDialect: 'postgresql',
    sslMode: 'prefer'
  });

  // Add form errors state
  const [formErrors, setFormErrors] = useState({
    connectionName: '',
    host: '',
    databaseName: '',
    username: '',
    password: '',
    driverDialect: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [connectionTested, setConnectionTested] = useState(false);
  const [connectionValid, setConnectionValid] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const [lastApiResponse, setLastApiResponse] = useState<any>(null);

  // Fetch connection details when component mounts
  useEffect(() => {
    if (connectionId) {
      console.log('🚀 Component mounted, fetching connection details for ID:', connectionId);
      fetchConnectionDetails(connectionId);
    }
  }, [connectionId]);

  // Monitor dbConnection state changes
  useEffect(() => {
    console.log('🔄 dbConnection state updated:', dbConnection);
  }, [dbConnection]);

  // Test function to verify config patching with your exact data
  const testConfigPatching = () => {
    const testConfig = {
      "host": "***********",
      "port": 5454,
      "user_id": "1d9f3bef-f399-42c9-8cfb-867c1a6ba176",
      "ssl_mode": "prefer",
      "username": "svc_postgresql",
      "db_dialect": "postgresql",
      "database_name": "postgres",
      "connection_name": "postgres data",
      "database_dialect": "postgresql"
    };

    console.log('🧪 Testing config patching with your exact data...');
    const result = patchFormDataFromConfig(testConfig);
    console.log('🎯 Test result:', result);

    // Apply the test result to see if it works
    setDbConnection(result);
    toast.success('Applied test config data to form');
  };

  // Manual patch function using the last API response
  const manualPatchFromApi = () => {
    if (!lastApiResponse) {
      toast.error('No API response available. Please refresh data first.');
      return;
    }

    console.log('🔧 Manual patching from stored API response:', lastApiResponse);

    if (lastApiResponse.status === 'success' && lastApiResponse.connection) {
      const conn = lastApiResponse.connection;

      if (conn.config) {
        console.log('📦 Manually patching from config:', conn.config);
        const patchedData = patchFormDataFromConfig(conn.config);
        console.log('🎯 Manual patch result:', patchedData);
        setDbConnection(patchedData);
        toast.success('Manually applied API config data to form');
      } else {
        toast.error('No config object found in API response');
      }
    } else {
      toast.error('Invalid API response format');
    }
  };

  // Dynamic data patching function based on config object
  const patchFormDataFromConfig = (config: any) => {
    console.log('🔧 Starting config patching with data:', config);
    const patchedData: any = {};

    CONFIG_FIELD_MAPPINGS.forEach(mapping => {
      const configValue = config[mapping.configKey];
      console.log(`📋 Mapping ${mapping.configKey} → ${mapping.formField}:`, configValue);

      if (configValue !== undefined && configValue !== null) {
        if (mapping.type === 'number') {
          patchedData[mapping.formField] = configValue.toString();
          console.log(`🔢 Number converted: ${configValue} → "${configValue.toString()}"`);
        } else {
          patchedData[mapping.formField] = configValue;
          console.log(`📝 String assigned: "${configValue}"`);
        }
      } else if (mapping.defaultValue !== undefined) {
        patchedData[mapping.formField] = mapping.defaultValue.toString();
        console.log(`⚙️ Default value used: ${mapping.defaultValue}`);
      } else {
        console.log(`❌ No value found for ${mapping.configKey}`);
      }
    });

    // Don't populate password for security reasons
    patchedData.password = '';

    console.log('✅ Final patched form data:', patchedData);
    return patchedData;
  };

  // Generate request body for API from form data
  const generateRequestBodyFromForm = (userId: string) => {
    const requestBody: any = {
      user_id: userId
    };

    CONFIG_FIELD_MAPPINGS.forEach(mapping => {
      const formValue = (dbConnection as any)[mapping.formField];

      if (formValue !== undefined && formValue !== null && formValue !== '') {
        if (mapping.type === 'number') {
          requestBody[mapping.configKey] = parseInt(formValue) || mapping.defaultValue || 0;
        } else {
          requestBody[mapping.configKey] = formValue;
        }
      } else if (mapping.defaultValue !== undefined) {
        requestBody[mapping.configKey] = mapping.defaultValue;
      }
    });

    // Always include password from form
    // requestBody.password = dbConnection.password;

    console.log('Generated request body from form:', requestBody);
    return requestBody;
  };

  // Handle PUT API response and update form data
  const handlePutApiResponse = (responseData: any) => {
    console.log('Processing PUT API response:', responseData);

    if (responseData.connection) {
      const updatedConnection = responseData.connection;

      // Check if the response has config object
      if (updatedConnection.config) {
        console.log('Updating form with config data from PUT response:', updatedConnection.config);
        const patchedData = patchFormDataFromConfig(updatedConnection.config);
        setDbConnection(patchedData);
      } else {
        // Fallback to direct mapping
        console.log('No config in PUT response, using direct mapping');
        setDbConnection({
          connectionName: updatedConnection.connection_name || dbConnection.connectionName,
          host: updatedConnection.host || dbConnection.host,
          port: updatedConnection.port?.toString() || dbConnection.port,
          databaseName: updatedConnection.database_name || dbConnection.databaseName,
          username: updatedConnection.username || dbConnection.username,
          password: '', // Don't populate password for security
          driverDialect: updatedConnection.db_dialect || dbConnection.driverDialect,
          sslMode: updatedConnection.ssl_mode || dbConnection.sslMode
        });
      }

      // Reset connection test status since data has been updated
      setConnectionTested(false);
      setConnectionValid(false);

      toast.info('Form updated with latest connection data');
    }
  };

  const fetchConnectionDetails = async (id: string) => {
    try {
      setIsLoading(true);
      const API_BASE_URL = 'http://***********:8001';
      
      console.log(`🌐 Fetching connection details for ID: ${id}`);
      console.log(`🔗 API URL: ${API_BASE_URL}/database/connection-details/${id}`);

      const response = await fetch(`${API_BASE_URL}/database/connection-details/${id}`);

      console.log(`📡 Response status: ${response.status}`);
      console.log(`📡 Response headers:`, response.headers);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`❌ API Error Response (${response.status}):`, errorText);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const result = await response.json();
      console.log('✅ Connection details fetched:', result);

      // Store the API response for debugging
      setLastApiResponse(result);

      // Check if result is the connection object directly or wrapped in a response
      let conn;
      if (result.connection) {
        // Wrapped format: { status: 'success', connection: {...} }
        conn = result.connection;
        console.log('📦 Using wrapped connection format');
      } else if (result.connection_id) {
        // Direct format: { connection_id: 2, connection_name: '...', config: {...} }
        conn = result;
        console.log('📦 Using direct connection format');
      } else {
        throw new Error('Invalid response format: no connection data found');
      }

      console.log('🔍 Full connection data received:', JSON.stringify(conn, null, 2));

      // Check if config object exists and patch data from it
      if (conn.config) {
        console.log('✅ Config object found, starting patching process...');
        console.log('📦 Config object contents:', JSON.stringify(conn.config, null, 2));

        const patchedData = patchFormDataFromConfig(conn.config);
        console.log('🎯 About to set form data with patched data:', patchedData);
        console.log('📋 Current dbConnection state before update:', dbConnection);

        setDbConnection(patchedData);

        // Verify the state was set (this will trigger the useEffect)
        console.log('✅ setDbConnection called with:', patchedData);

        toast.success(`Loaded connection: ${conn.config.connection_name || 'Unknown'}`);
      } else {
        // Fallback to direct mapping if no config object
        console.log('⚠️ No config object found, using direct mapping fallback');
        const fallbackData = {
          connectionName: conn.connection_name || '',
          host: conn.host || '',
          port: conn.port?.toString() || '5432',
          databaseName: conn.database_name || '',
          username: conn.username || '',
          password: '', // Don't populate password for security
          driverDialect: conn.db_dialect || 'postgresql',
          sslMode: conn.ssl_mode || 'prefer'
        };
        console.log('📋 Fallback data:', fallbackData);
        setDbConnection(fallbackData);

        toast.info(`Loaded connection: ${conn.connection_name || 'Unknown'} (fallback mode)`);
      }
    } catch (error) {
      console.error('❌ Error fetching connection details:', error);

      // Use mock data for testing if API fails
      console.log('🔄 Using mock data for testing...');
      const mockResponse = {
        status: 'success',
        connection: {
          connection_id: parseInt(id),
          connection_name: 'postgres data',
          database_dialect: 'postgresql',
          database_name: 'postgres',
          last_used: '2025-07-02T12:32:31.327900+00:00',
          config: {
            host: '***********',
            port: 5454,
            user_id: '1d9f3bef-f399-42c9-8cfb-867c1a6ba176',
            ssl_mode: 'prefer',
            username: 'svc_postgresql',
            db_dialect: 'postgresql',
            database_name: 'postgres',
            connection_name: 'postgres data',
            database_dialect: 'postgresql'
          }
        }
      };

      console.log('📦 Using mock config data:', mockResponse.connection.config);
      setLastApiResponse(mockResponse);

      const patchedData = patchFormDataFromConfig(mockResponse.connection.config);
      console.log('🎯 Mock data patched result:', patchedData);
      setDbConnection(patchedData);

      toast.warning('API failed, using mock data for testing. Check console for details.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDbChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setDbConnection(prev => ({ ...prev, [name]: value }));
  };

  const handleDbSelectChange = (name: string, value: string) => {
    setDbConnection(prev => ({ ...prev, [name]: value }));
  };

  // Add validation function
  const validateForm = (): boolean => {
    const newErrors = {
      connectionName: '',
      host: '',
      databaseName: '',
      username: '',
      password: '',
      driverDialect: ''
    };
    
    // Validate required fields
    if (!dbConnection.connectionName.trim()) {
      newErrors.connectionName = 'Connection name is required';
    }
    
    if (!dbConnection.host.trim()) {
      newErrors.host = 'Host is required';
    }
    
    if (!dbConnection.databaseName.trim()) {
      newErrors.databaseName = 'Database name is required';
    }
    
    if (!dbConnection.username.trim()) {
      newErrors.username = 'Username is required';
    }
    
    if (!dbConnection.password.trim()) {
      newErrors.password = 'Password is required';
    }
    
    setFormErrors(newErrors);
    return !Object.values(newErrors).some(error => error !== '');
  };

  const handleTestDbConnection = async () => {
    // Validate form first
    if (!validateForm()) {
      toast.error('Please fill in all required fields');
      return;
    }

    setIsTestingConnection(true);
    try {
      const userId = 'a6e3020d-984a-4394-ac73-da7ec5393314'; // Admin user ID
      
      // Create payload dynamically from form values using config mappings
      const payload = generateRequestBodyFromForm(userId);

      console.log('Testing connection with payload:', payload);

      // Use direct fetch with the specific URL
      const response = await fetch('http://***********:8001/database/test-connection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result.status === 'success') {
        toast.success('Database connection test successful!');
        setConnectionValid(true);
      } else {
        toast.error(result.message || 'Failed to connect to database');
        setConnectionValid(false);
      }
    } catch (error) {
      console.error('Error testing connection:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to connect to database');
      setConnectionValid(false);
    } finally {
      setIsTestingConnection(false);
      setConnectionTested(true);
    }
  };

  const handleSubmitDbConnection = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form first
    if (!validateForm()) {
      toast.error('Please fill in all required fields');
      return;
    }

    // No connection testing required - proceed directly to update

    setIsSubmitting(true);
    try {
      const userId = 'a6e3020d-984a-4394-ac73-da7ec5393314'; // Admin user ID
      const API_BASE_URL = 'http://***********:8001';

      // Create dynamic request body from form data using config mappings
      const requestBody = generateRequestBodyFromForm(userId);

      console.log('Updating database connection with payload:', requestBody);
      console.log(`PUT API URL: ${API_BASE_URL}/database/update-connection/${connectionId}`);

      // Make PUT API call to update connection
      const response = await fetch(`${API_BASE_URL}/database/update-connection/${connectionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorData.detail || errorMessage;
          console.error('PUT API Error Response:', errorData);
        } catch (parseError) {
          console.error('Failed to parse error response:', parseError);
        }
        throw new Error(errorMessage);
      }

      const result = await response.json();
      console.log('PUT API Update response:', result);

      if (result.status === 'success') {
        toast.success('Database connection updated successfully!');

        // Handle the PUT API response and update form data
        handlePutApiResponse(result);

        // Navigate back to admin menu after a short delay
        setTimeout(() => {
          navigate('/Admin/menu');
        }, 1500);
      } else {
        toast.error(result.message || 'Failed to update database connection');
      }

    } catch (error) {
      console.error('Error updating database connection:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update database connection');
    } finally {
      setIsSubmitting(false);
    }
  };


      console.log('� Step 1: Getting selected tables and columns...');



      console.log('�️ Step 2: Deleting existing metadata content...');


  if (isLoading) {
    return (
      <div className="bg-white p-8 max-w-5xl mx-auto">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Loading connection details...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white p-8 max-w-5xl mx-auto">
      {/* Header with back button */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Button
            variant="ghost"
            onClick={() => navigate('/Admin/menu')}
            className="mr-4 p-2"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Edit Database Connection</h1>
            <p className="text-gray-600">Update connection details for: {dbConnection.connectionName}</p>
          </div>
        </div>

        <div className="flex gap-2">
     
        </div>
      </div>

      <form onSubmit={handleSubmitDbConnection} className="space-y-8">
        
        {/* DB Connection Name */}
        <div className="flex items-center gap-4">
          <Label className="text-sm font-medium w-40">DB Connection Name</Label>
          <div className="flex-1 max-w-md flex flex-col">
            <Input
              name="connectionName"
              value={dbConnection.connectionName}
              onChange={handleDbChange}
              placeholder="DB Connection Name"
              className={`w-full ${formErrors.connectionName ? 'border-red-500' : ''}`}
            />
            {formErrors.connectionName && (
              <p className="text-red-500 text-xs mt-1">{formErrors.connectionName}</p>
            )}
          </div>
        </div>

        {/* Parameters Table */}
        <div className="mt-8">
          {/* Table Header */}
          <div className="grid grid-cols-3 gap-8 border-b pb-3 mb-6">
            <div className="text-sm font-medium">Parameter</div>
            <div className="text-sm font-medium">Description</div>
            <div></div>
          </div>

          {/* Host/Server Row */}
          <div className="grid grid-cols-3 gap-8 py-4 border-b items-center">
            <div className="text-sm">Host / Server</div>
            <div className="text-sm text-gray-600">IP address or domain name of the DB server</div>
            <div className="flex flex-col">
              <Input
                name="host"
                value={dbConnection.host}
                onChange={handleDbChange}
                placeholder=""
                className={`w-full ${formErrors.host ? 'border-red-500' : ''}`}
              />
              {formErrors.host && (
                <p className="text-red-500 text-xs mt-1">{formErrors.host}</p>
              )}
            </div>
          </div>

          {/* Port Row */}
          <div className="grid grid-cols-3 gap-8 py-4 border-b items-center">
            <div className="text-sm">Port</div>
            <div className="text-sm text-gray-600">Port number on which the DB is listening</div>
            <Input
              name="port"
              value={dbConnection.port}
              onChange={handleDbChange}
              placeholder=""
              className="w-full"
            />
          </div>

          {/* Database Name Row */}
          <div className="grid grid-cols-3 gap-8 py-4 border-b items-center">
            <div className="text-sm">Database Name</div>
            <div className="text-sm text-gray-600">The specific database to connect to on the server</div>
            <Input
              name="databaseName"
              value={dbConnection.databaseName}
              onChange={handleDbChange}
              placeholder=""
              className="w-full"
            />
          </div>

          {/* Username Row */}
          <div className="grid grid-cols-3 gap-8 py-4 border-b items-center">
            <div className="text-sm">Username</div>
            <div className="text-sm text-gray-600">Database username with access permissions</div>
            <Input
              name="username"
              value={dbConnection.username}
              onChange={handleDbChange}
              placeholder=""
              className="w-full"
            />
          </div>

          {/* Password Row */}
          <div className="grid grid-cols-3 gap-8 py-4 border-b items-center">
            <div className="text-sm">Password</div>
            <div className="text-sm text-gray-600">Associated password for authentication</div>
            <Input
              name="password"
              type="password"
              value={dbConnection.password}
              onChange={handleDbChange}
              placeholder="Enter new password"
              className="w-full"
            />
          </div>

          {/* Driver/Dialect Row */}
          <div className="grid grid-cols-3 gap-8 py-4 border-b items-center">
            <div className="text-sm">Driver / Dialect</div>
            <div className="text-sm text-gray-600">The connector/driver name used in client libraries</div>
            <Select value={dbConnection.driverDialect} onValueChange={(value) => handleDbSelectChange('driverDialect', value)}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select driver/dialect" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="postgresql">PostgreSQL</SelectItem>
                <SelectItem value="mysql">MySQL</SelectItem>
                <SelectItem value="sqlserver">SQL Server</SelectItem>
                <SelectItem value="oracle">Oracle</SelectItem>
                <SelectItem value="sqlite">SQLite</SelectItem>
                <SelectItem value="snowflake">Snowflake</SelectItem>
                <SelectItem value="databricks">Databricks</SelectItem>
                <SelectItem value="salesforce">Salesforce</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* SSL Mode Row */}
          <div className="grid grid-cols-3 gap-8 py-4 items-center">
            <div className="text-sm">SSL Mode</div>
            <div className="text-sm text-gray-600">SSL connection mode for secure database connections</div>
            <Select value={dbConnection.sslMode} onValueChange={(value) => handleDbSelectChange('sslMode', value)}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select SSL mode" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="disable">Disable</SelectItem>
                <SelectItem value="allow">Allow</SelectItem>
                <SelectItem value="prefer">Prefer</SelectItem>
                <SelectItem value="require">Require</SelectItem>
                <SelectItem value="verify-ca">Verify CA</SelectItem>
                <SelectItem value="verify-full">Verify Full</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>



        {/* Action Buttons */}
        <div className="flex justify-end gap-3 pt-4">
          <Button
            type="submit"
            disabled={isSubmitting}
            variant='greenmind'
          >
            {isSubmitting ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Updating...
              </span>
            ) : 'Update Connection'}
          </Button>
          <Button
            type="button"
            variant="outline"
            className="px-6"
            onClick={() => navigate('/Admin/menu')}
          >
            Cancel
          </Button>
        </div>
      </form>
    </div>
  );
};

export default EditDatabaseConnection;
