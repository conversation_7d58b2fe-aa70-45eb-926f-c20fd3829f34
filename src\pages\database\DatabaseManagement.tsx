import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, Database, Table, Eye, Edit, Trash, Plus } from 'lucide-react';
import { toast } from 'sonner';
import {
  getDatabaseConnections,
  getDatabaseTables,
  getTableData,
  DatabaseConnection,
  DatabaseTable,
  TableData
} from '@/services/api/admin/databaseConnectionService';

const DatabaseManagement: React.FC = () => {
  // Static data for demonstration
  const staticConnections: DatabaseConnection[] = [
    {
      id: 1,
      connection_name: "DB_Connection1",
      db_dialect: "postgresql",
      database_name: "production_db",
      username: "admin",
      password: "****",
      host: "localhost",
      port: 5432,
      user_id: 'a6e3020d-984a-4394-ac73-da7ec5393314',
      ssl_mode: "prefer"
    },
    {
      id: 2,
      connection_name: "DB_Connection2",
      db_dialect: "mysql",
      database_name: "analytics_db",
      username: "user",
      password: "****",
      host: "db.company.com",
      port: 3306,
      user_id: 'a6e3020d-984a-4394-ac73-da7ec5393314',
      ssl_mode: "prefer"
    },
    {
      id: 3,
      connection_name: "DB_Connection3",
      db_dialect: "sqlserver",
      database_name: "warehouse_db",
      username: "sa",
      password: "****",
      host: "sql.server.com",
      port: 1433,
      user_id: 'a6e3020d-984a-4394-ac73-da7ec5393314',
      ssl_mode: "prefer"
    }
  ];

  const staticTables: DatabaseTable[] = [
    { table_name: "Table1", table_type: "BASE TABLE", table_schema: "public" },
    { table_name: "View1", table_type: "VIEW", table_schema: "public" },
    { table_name: "users", table_type: "BASE TABLE", table_schema: "public" },
    { table_name: "orders", table_type: "BASE TABLE", table_schema: "public" },
    { table_name: "products", table_type: "BASE TABLE", table_schema: "public" },
    { table_name: "customers", table_type: "BASE TABLE", table_schema: "public" }
  ];

  const staticTableData: TableData = {
    columns: ["id", "name", "email", "created_at", "status"],
    rows: [
      [1, "John Doe", "<EMAIL>", "2024-01-15", "active"],
      [2, "Jane Smith", "<EMAIL>", "2024-01-16", "active"],
      [3, "Bob Johnson", "<EMAIL>", "2024-01-17", "inactive"],
      [4, "Alice Brown", "<EMAIL>", "2024-01-18", "active"],
      [5, "Charlie Wilson", "<EMAIL>", "2024-01-19", "pending"],
      [6, "Diana Davis", "<EMAIL>", "2024-01-20", "active"],
      [7, "Edward Miller", "<EMAIL>", "2024-01-21", "inactive"],
      [8, "Fiona Garcia", "<EMAIL>", "2024-01-22", "active"],
      [9, "George Martinez", "<EMAIL>", "2024-01-23", "pending"],
      [10, "Helen Rodriguez", "<EMAIL>", "2024-01-24", "active"]
    ],
    total_rows: 150
  };

  const [connections, setConnections] = useState<DatabaseConnection[]>(staticConnections);
  const [selectedConnection, setSelectedConnection] = useState<DatabaseConnection | null>(staticConnections[0]);
  const [tables, setTables] = useState<DatabaseTable[]>(staticTables);
  const [selectedTable, setSelectedTable] = useState<DatabaseTable | null>(staticTables[0]);
  const [tableData, setTableData] = useState<TableData | null>(staticTableData);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Table management states
  const [isCreatingTable, setIsCreatingTable] = useState(false);
  const [newTable, setNewTable] = useState({
    tableName: 'Table Name',
    tableDescription: 'Table Description'
  });

  // Column management states with sample data
  const [columns, setColumns] = useState([
    { name: 'Column Name', description: 'Table Description', alias: 'Column Name', dataType: 'String', isPrimaryKey: false, isUniqueKey: false }
  ]);

  useEffect(() => {
    // Using static data, no need to fetch
    console.log('Database Management page loaded with static data');
  }, []);

  const fetchConnections = async () => {
    // Using static data for now
    console.log('Using static connections data');
  };

  const handleConnectionSelect = async (connection: DatabaseConnection) => {
    setSelectedConnection(connection);
    setSelectedTable(staticTables[0]); // Select first table by default
    setTableData(staticTableData);
    toast.success(`Connected to ${connection.connection_name}`);
  };

  const handleTableSelect = async (table: DatabaseTable) => {
    setSelectedTable(table);
    setTableData(staticTableData);
    toast.success(`Viewing table: ${table.table_name}`);
  };

  const handleAddColumn = () => {
    setColumns([...columns, { 
      name: '', 
      description: '', 
      alias: '', 
      dataType: 'String', 
      isPrimaryKey: false, 
      isUniqueKey: false 
    }]);
  };

  const handleColumnChange = (index: number, field: string, value: any) => {
    const updatedColumns = [...columns];
    updatedColumns[index] = { ...updatedColumns[index], [field]: value };
    setColumns(updatedColumns);
  };

  const handleRemoveColumn = (index: number) => {
    if (columns.length > 1) {
      setColumns(columns.filter((_, i) => i !== index));
    }
  };

  const filteredTables = tables.filter(table =>
    table.table_name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar - Database Connections */}
      <div className="w-64 bg-white border-r border-gray-200 p-4">
        <div className="flex items-center gap-2 mb-4">
          <Database className="h-5 w-5 text-blue-600" />
          <h2 className="font-semibold text-gray-900">Database Connections</h2>
        </div>
        
        <div className="space-y-2">
          {connections.map((connection) => (
            <div
              key={connection.id}
              onClick={() => handleConnectionSelect(connection)}
              className={`p-3 rounded-lg cursor-pointer transition-colors ${
                selectedConnection?.id === connection.id
                  ? 'bg-blue-100 border border-blue-300'
                  : 'hover:bg-gray-100'
              }`}
            >
              <div className="font-medium text-sm">{connection.connection_name}</div>
              <div className="text-xs text-gray-500">{connection.db_dialect}</div>
              <div className="text-xs text-gray-400">{connection.database_name}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {selectedConnection ? (
          <>
            {/* Header */}
            <div className="bg-white border-b border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-xl font-semibold text-gray-900">
                    {selectedConnection.connection_name}
                  </h1>
                  <p className="text-sm text-gray-500">
                    {selectedConnection.db_dialect} • {selectedConnection.database_name}
                  </p>
                </div>
                <Button onClick={() => setIsCreatingTable(true)} className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  New Table
                </Button>
              </div>
            </div>

            {/* Content Area */}
            <div className="flex-1 flex">
              {/* Tables List */}
              <div className="w-80 bg-white border-r border-gray-200 p-4">
                <div className="flex items-center gap-2 mb-4">
                  <Table className="h-4 w-4 text-green-600" />
                  <h3 className="font-medium text-gray-900">Tables</h3>
                </div>
                
                {/* Search */}
                <div className="relative mb-4">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search tables..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>

                {/* Tables List */}
                <div className="space-y-1">
                  {filteredTables.map((table) => (
                    <div
                      key={table.table_name}
                      onClick={() => handleTableSelect(table)}
                      className={`p-3 rounded-lg cursor-pointer transition-colors ${
                        selectedTable?.table_name === table.table_name
                          ? 'bg-green-100 border border-green-300'
                          : 'hover:bg-gray-100'
                      }`}
                    >
                      <div className="font-medium text-sm">{table.table_name}</div>
                      <div className="text-xs text-gray-500">{table.table_type}</div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Table Data View */}
              <div className="flex-1 p-6">
                {isCreatingTable ? (
                  /* Table Creation Form */
                  <div className="max-w-4xl">
                    <div className="flex items-center justify-between mb-6">
                      <h3 className="text-lg font-medium text-gray-900">Create New Table</h3>
                      <Button variant="outline" onClick={() => setIsCreatingTable(false)}>
                        Cancel
                      </Button>
                    </div>

                    <form className="space-y-6">
                      {/* DB Connection Name */}
                      <div className="flex items-center gap-4">
                        <Label className="text-sm font-medium w-40">DB Connection Name</Label>
                        <Input
                          value={selectedConnection?.connection_name || ''}
                          disabled
                          className="flex-1 max-w-md bg-gray-50"
                        />
                      </div>

                      {/* Search Table View */}
                      <div className="flex items-center gap-4">
                        <Label className="text-sm font-medium w-40">Search Table View</Label>
                        <Input
                          placeholder="Search Table View"
                          className="flex-1 max-w-md"
                        />
                      </div>

                      {/* Table View Access Section */}
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="font-medium mb-4">Table View Access</h4>

                        {/* SQL Server DB1 */}
                        <div className="mb-4">
                          <div className="font-medium text-sm mb-2">SQL_server_DB1</div>
                          <div className="ml-4 space-y-2">
                            <div className="flex items-center gap-2">
                              <span className="text-sm">Table1</span>
                              <Select defaultValue="Select All">
                                <SelectTrigger className="w-32">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="Select All">Select All</SelectItem>
                                  <SelectItem value="Select">Select</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="flex items-center gap-2">
                              <span className="text-sm">View1</span>
                              <Select defaultValue="Select">
                                <SelectTrigger className="w-32">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="Select All">Select All</SelectItem>
                                  <SelectItem value="Select">Select</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        </div>

                        <Button className="w-full bg-gray-600 text-white hover:bg-gray-700">
                          Start column metadata
                        </Button>
                      </div>

                      {/* Tables Section */}
                      <div className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-4">
                          <h4 className="font-medium">TABLES</h4>
                        </div>

                        <div className="grid grid-cols-2 gap-4 mb-4">
                          <div>
                            <Label className="text-sm font-medium">Table Name</Label>
                            <Input
                              value={newTable.tableName}
                              onChange={(e) => setNewTable(prev => ({ ...prev, tableName: e.target.value }))}
                              placeholder="Table Name"
                            />
                          </div>
                          <div></div>
                        </div>

                        <div className="mb-4">
                          <Label className="text-sm font-medium">Table Description</Label>
                          <Input
                            value={newTable.tableDescription}
                            onChange={(e) => setNewTable(prev => ({ ...prev, tableDescription: e.target.value }))}
                            placeholder="Table Description"
                            className="w-full"
                          />
                        </div>

                        {/* Pagination */}
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center gap-2">
                            <Button variant="outline" size="sm">←</Button>
                            <Button variant="outline" size="sm">→</Button>
                            <span className="text-sm text-gray-500">10</span>
                            <Button variant="outline" size="sm">←</Button>
                            <Button variant="outline" size="sm">→</Button>
                            <span className="text-sm text-gray-500">500</span>
                          </div>
                        </div>
                      </div>

                      {/* Columns Section */}
                      <div className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-4">
                          <h4 className="font-medium">Columns</h4>
                        </div>

                        {columns.map((column, index) => (
                          <div key={index} className="grid grid-cols-6 gap-4 mb-4 items-end">
                            <div>
                              <Label className="text-sm font-medium">Column Name</Label>
                              <Input
                                value={column.name}
                                onChange={(e) => handleColumnChange(index, 'name', e.target.value)}
                                placeholder="Column Name"
                              />
                            </div>
                            <div className="flex items-center gap-2">
                              <input
                                type="checkbox"
                                checked={column.isPrimaryKey}
                                onChange={(e) => handleColumnChange(index, 'isPrimaryKey', e.target.checked)}
                              />
                              <Label className="text-sm">Primary Key</Label>
                            </div>
                            <div className="flex items-center gap-2">
                              <input
                                type="checkbox"
                                checked={column.isUniqueKey}
                                onChange={(e) => handleColumnChange(index, 'isUniqueKey', e.target.checked)}
                              />
                              <Label className="text-sm">Unique Key</Label>
                            </div>
                            <div>
                              <Label className="text-sm font-medium">Column Description</Label>
                              <Input
                                value={column.description}
                                onChange={(e) => handleColumnChange(index, 'description', e.target.value)}
                                placeholder="Table Description"
                              />
                            </div>
                            <div>
                              <Label className="text-sm font-medium">Column Alias</Label>
                              <Input
                                value={column.alias}
                                onChange={(e) => handleColumnChange(index, 'alias', e.target.value)}
                                placeholder="Column Name"
                              />
                            </div>
                            <div>
                              <Label className="text-sm font-medium">Column Datatype</Label>
                              <Select value={column.dataType} onValueChange={(value) => handleColumnChange(index, 'dataType', value)}>
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="String">String</SelectItem>
                                  <SelectItem value="Integer">Integer</SelectItem>
                                  <SelectItem value="Float">Float</SelectItem>
                                  <SelectItem value="Boolean">Boolean</SelectItem>
                                  <SelectItem value="Date">Date</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        ))}

                        <div className="flex items-center justify-between">
                          <Button type="button" onClick={handleAddColumn} variant="outline">
                            <Plus className="h-4 w-4 mr-2" />
                            Add Column
                          </Button>
                          <div className="flex items-center gap-2">
                            <Button variant="outline" size="sm">←</Button>
                            <Button variant="outline" size="sm">→</Button>
                            <span className="text-sm text-gray-500">10</span>
                            <Button variant="outline" size="sm">←</Button>
                            <Button variant="outline" size="sm">→</Button>
                            <span className="text-sm text-gray-500">500</span>
                          </div>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex justify-end gap-3">
                        <Button type="submit" className="bg-gray-600 text-white hover:bg-gray-700">
                          Save
                        </Button>
                        <Button type="button" variant="outline" onClick={() => setIsCreatingTable(false)}>
                          Cancel
                        </Button>
                      </div>
                    </form>
                  </div>
                ) : selectedTable && tableData ? (
                  <div>
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-medium text-gray-900">
                        {selectedTable.table_name}
                      </h3>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-2" />
                          View
                        </Button>
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </Button>
                      </div>
                    </div>

                    {/* Table Data */}
                    <div className="border rounded-lg overflow-hidden">
                      <div className="overflow-x-auto">
                        <table className="w-full">
                          <thead className="bg-gray-50">
                            <tr>
                              {tableData.columns.map((column, index) => (
                                <th key={index} className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  {column}
                                </th>
                              ))}
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {tableData.rows.slice(0, 10).map((row, rowIndex) => (
                              <tr key={rowIndex}>
                                {row.map((cell, cellIndex) => (
                                  <td key={cellIndex} className="px-4 py-3 text-sm text-gray-900">
                                    {cell?.toString() || ''}
                                  </td>
                                ))}
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>

                    {tableData.total_rows && (
                      <div className="mt-4 text-sm text-gray-500">
                        Showing 10 of {tableData.total_rows} rows
                      </div>
                    )}
                  </div>
                ) : selectedTable ? (
                  <div className="flex items-center justify-center h-64">
                    <div className="text-gray-500">Loading table data...</div>
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-64">
                    <div className="text-gray-500">Select a table to view its data</div>
                  </div>
                )}
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <Database className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Database Selected</h3>
              <p className="text-gray-500">Select a database connection from the sidebar to get started</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DatabaseManagement;
