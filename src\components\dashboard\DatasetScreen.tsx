
import React from 'react';
import { X } from 'lucide-react';
import { useAppSelector } from '@/hooks/useRedux';
import DatasetStepIndicator from './dataset/DatasetStepIndicator';
import DatasourceStep from './dataset/DatasourceStep';
import TablesStep from './dataset/TablesStep';
import ColumnsStep from './dataset/ColumnsStep';
import DerivedColumnsStep from './dataset/DerivedColumnsStep';
import FilterStep from './dataset/FilterStep';
import DefineStep from './dataset/DefineStep';
import GenerateStep from './dataset/GenerateStep';
import SaveStep from './dataset/SaveStep';

interface DatasetScreenProps {
  onClose: () => void;
}

const DatasetScreen: React.FC<DatasetScreenProps> = ({ onClose }) => {
  const { currentStep } = useAppSelector(state => state.dataset);
  
  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'datasource':
        return <DatasourceStep onClose={onClose} />;
      case 'tables':
        return <TablesStep onClose={onClose} />;
      case 'columns':
        return <ColumnsStep onClose={onClose} />;
      case 'derived-columns':
        return <DerivedColumnsStep onClose={onClose} />;
      case 'filter':
        return <FilterStep onClose={onClose} />;
      case 'define':
        return <DefineStep onClose={onClose} />;
      case 'generate':
        return <GenerateStep onClose={onClose} />;
      case 'save':
        return <SaveStep onClose={onClose} />;
      default:
        return <DatasourceStep onClose={onClose} />;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg overflow-auto max-h-[90vh]">
      <div className="p-6">
        <div className="mb-6">
          <DatasetStepIndicator />
        </div>
        <div>
          {renderCurrentStep()}
        </div>
      </div>
    </div>
  );
};

export default DatasetScreen;
