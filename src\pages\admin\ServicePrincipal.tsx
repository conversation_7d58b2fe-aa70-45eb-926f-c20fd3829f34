import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { ArrowLeft, Plus } from 'lucide-react';
import { toast } from 'sonner';
import {
  createServicePrincipal,
  getServicePrincipals,
  type ServicePrincipal as ServicePrincipalType,
  type CreateServicePrincipalPayload
} from '@/services/api/admin/servicePrincipalService';

interface ServicePrincipalFormData {
  name: string;
  source: 'Azure' | 'DADA';
  applicationId: string;
}

const ServicePrincipal: React.FC = () => {
  const navigate = useNavigate();
  const [servicePrincipals, setServicePrincipals] = useState<ServicePrincipalType[]>([]);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [formData, setFormData] = useState<ServicePrincipalFormData>({
    name: '',
    source: 'Azure',
    applicationId: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch service principals from API
  useEffect(() => {
    const fetchServicePrincipalsData = async () => {
      try {
        setIsLoading(true);
        const data = await getServicePrincipals();
        setServicePrincipals(data);
      } catch (error) {
        console.error('Error fetching service principals:', error);
        toast.error('Failed to load service principals');
        // Set empty array on error
        setServicePrincipals([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchServicePrincipalsData();
  }, []);

  const handleInputChange = (field: keyof ServicePrincipalFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCreateServicePrincipal = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim() || !formData.applicationId.trim()) {
      toast.error('Please fill in all required fields');
      return;
    }

    setIsSubmitting(true);

    try {
      // Create service principal via API
      const payload: CreateServicePrincipalPayload = {
        name: formData.name,
        source: formData.source,
        applicationId: formData.applicationId
      };

      const newServicePrincipal = await createServicePrincipal(payload);

      // Update local state with the new service principal
      setServicePrincipals(prev => [...prev, newServicePrincipal]);
      setIsCreateDialogOpen(false);
      setFormData({ name: '', source: 'Azure', applicationId: '' });
      toast.success('Service Principal created successfully');
    } catch (error) {
      toast.error('Failed to create Service Principal. Please try again.');
      console.error('Service Principal creation error:', error);
      // Don't close the dialog on error so user can retry
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    setFormData({ name: '', source: 'Azure', applicationId: '' });
    setIsCreateDialogOpen(false);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg font-normal text-gray-600">Loading service principals...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Back to Admin Button */}
      <div className="flex items-center mb-4">
        <Button
          variant="ghost"
          onClick={() => navigate('/Admin')}
          className="p-0 text-green-600 hover:text-green-800 hover:bg-transparent font-normal"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Admin
        </Button>
      </div>

      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-normal text-gray-700">Service Principal</h1>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button
              className="text-white hover:opacity-90 transition-opacity font-normal"
              style={{ backgroundColor: 'rgb(0, 130, 130)' }}
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Service Principal
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle className="font-normal">Create New Service Principal</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleCreateServicePrincipal} className="space-y-4">
              {/* Name */}
              <div className="space-y-2">
                <Label htmlFor="name" className="font-normal">Name *</Label>
                <Input
                  id="name"
                  type="text"
                  placeholder="Enter service principal name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className="font-normal"
                />
              </div>

              {/* Source */}
              <div className="space-y-2">
                <Label htmlFor="source" className="font-normal">Source</Label>
                <select
                  id="source"
                  value={formData.source}
                  onChange={(e) => handleInputChange('source', e.target.value as 'Azure' | 'DADA')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm font-normal focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="Azure">Azure</option>
                  <option value="DADA">DADA</option>
                </select>
              </div>

              {/* Application ID */}
              <div className="space-y-2">
                <Label htmlFor="applicationId" className="font-normal">Application ID *</Label>
                <Input
                  id="applicationId"
                  type="text"
                  placeholder="Enter application ID"
                  value={formData.applicationId}
                  onChange={(e) => handleInputChange('applicationId', e.target.value)}
                  className="font-normal"
                />
              </div>

              <DialogFooter className="gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancel}
                  disabled={isSubmitting}
                  className="font-normal"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-blue-600 hover:bg-blue-700 font-normal"
                >
                  {isSubmitting ? 'Creating...' : 'Create Service Principal'}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Service Principals Table */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
        <Table>
          <TableHeader>
            <TableRow className="border-b border-gray-200">
              <TableHead className="font-normal text-black">Name</TableHead>
              <TableHead className="font-normal text-black">Source</TableHead>
              <TableHead className="font-normal text-black">ApplicationId</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {servicePrincipals.map((sp) => (
              <TableRow key={sp.id} className="border-b border-gray-200 hover:bg-gray-50">
                <TableCell className="font-normal text-gray-700">{sp.name}</TableCell>
                <TableCell>
                  <span className={`px-2 py-1 rounded-full text-xs font-normal ${
                    sp.source === 'Azure' 
                      ? 'bg-blue-100 text-blue-800'
                      : 'bg-green-100 text-green-800'
                  }`}>
                    {sp.source}
                  </span>
                </TableCell>
                <TableCell className="font-normal text-gray-700">{sp.applicationId}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {servicePrincipals.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500 font-normal">No service principals found. Create your first one!</p>
        </div>
      )}

      {/* MLZ Package Section */}
      {servicePrincipals.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
          {servicePrincipals.map((sp) => (
            <div key={sp.id} className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
              <div className="text-center mb-4">
                <h3 className="text-lg font-normal text-gray-700 mb-2">{sp.name}</h3>
                <p className="text-sm text-gray-500 font-normal">
                  {sp.source} ({sp.source === 'Azure' ? '15 DB connections' : '50 DB connections'})
                </p>
              </div>

              <div className="text-center mb-4">
                <p className="text-sm font-normal text-gray-600">MLZ Package</p>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <div className="text-center">
                  <h4 className="text-sm font-normal text-gray-700 mb-2">Application DBS</h4>
                  <div className="flex justify-center space-x-4 text-xs font-normal text-gray-600">
                    <span className="px-2 py-1 bg-white rounded border">Postgres</span>
                    <span className="px-2 py-1 bg-white rounded border">Databricks</span>
                    <span className="px-2 py-1 bg-white rounded border">MongoDB</span>
                  </div>
                </div>
              </div>

              <div className="mt-4 text-center">
                <p className="text-xs font-normal text-gray-500">
                  Application DB connection details in Environment Variable
                </p>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ServicePrincipal;
