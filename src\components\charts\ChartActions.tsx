
import React, { useState } from 'react';
import { 
  Copy, Edit, RefreshCw, Share2, Download, Check,
  FileImage, FileText
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { SavedChart } from '@/types/chartTypes';
import { ChartExportService } from '@/services/api/chart/chartExportService';

interface ChartActionsProps {
  chart: SavedChart;
  onEdit: () => void;
  onRefresh: () => void;
}

const ChartActions: React.FC<ChartActionsProps> = ({ 
  chart, 
  onEdit, 
  onRefresh 
}) => {
  const [copyingChart, setCopyingChart] = useState<string | null>(null);
  const [exportingChart, setExportingChart] = useState<string | null>(null);

  const handleCopyChart = async (e: React.MouseEvent) => {
    e.stopPropagation();
    setCopyingChart(chart.chart_name);
    
    const success = await ChartExportService.copyChartToClipboard(chart.chart_name);
    
    if (success) {
      setTimeout(() => {
        setCopyingChart(null);
      }, 2000);
    } else {
      setCopyingChart(null);
    }
  };

  const handleExportPNG = async (e: React.MouseEvent) => {
    e.stopPropagation();
    setExportingChart(chart.chart_name);
    
    const success = await ChartExportService.exportChartAsPNG(chart.chart_name);
    
    if (success) {
      setTimeout(() => {
        setExportingChart(null);
      }, 2000);
    } else {
      setExportingChart(null);
    }
  };

  const handleExportPDF = async (e: React.MouseEvent) => {
    e.stopPropagation();
    setExportingChart(chart.chart_name);
    
    const success = await ChartExportService.exportChartAsPDF(chart.chart_name);
    
    if (success) {
      setTimeout(() => {
        setExportingChart(null);
      }, 2000);
    } else {
      setExportingChart(null);
    }
  };

  const handleRefreshChart = (e: React.MouseEvent) => {
    e.stopPropagation();
    onRefresh();
  };

  return (
    <div className="flex space-x-2">
      {/* Copy Chart - Always show but disable if clipboard is false */}
      <button 
        className={`p-2 rounded-full transition-colors ${
          chart.clipboard !== false 
            ? "text-gray-600 hover:text-blue-600 hover:bg-blue-50" 
            : "text-gray-300 cursor-not-allowed"
        }`}
        title={chart.clipboard !== false ? "Copy Chart to Clipboard" : "Copy not available"}
        onClick={chart.clipboard !== false ? handleCopyChart : undefined}
        disabled={copyingChart === chart.chart_name || chart.clipboard === false}
      >
        {copyingChart === chart.chart_name ? (
          <Check size={18} className="text-green-500" />
        ) : (
          <Copy size={18} />
        )}
      </button>

      {/* Edit Chart - Always show but disable if editable is false */}
      <button 
        className={`p-2 rounded-full transition-colors ${
          chart.editable !== false 
            ? "text-gray-600 hover:text-blue-600 hover:bg-blue-50" 
            : "text-gray-300 cursor-not-allowed"
        }`}
        title={chart.editable !== false ? "Edit Chart" : "Edit not available"}
        onClick={chart.editable !== false ? (e) => {
          e.stopPropagation();
          onEdit();
        } : undefined}
        disabled={chart.editable === false}
      >
        <Edit size={18} />
      </button>

      {/* Refresh Chart - Always show but disable if refresh is false */}
      <button 
        className={`p-2 rounded-full transition-colors ${
          chart.refresh !== false 
            ? "text-gray-600 hover:text-blue-600 hover:bg-blue-50" 
            : "text-gray-300 cursor-not-allowed"
        }`}
        title={chart.refresh !== false ? "Refresh Data" : "Refresh not available"}
        onClick={chart.refresh !== false ? handleRefreshChart : undefined}
        disabled={chart.refresh === false}
      >
        <RefreshCw size={18} />
      </button>

      {/* Share Chart - Always show but disable if shareable is false */}
      <button 
        className={`p-2 rounded-full transition-colors ${
          chart.shareable !== false 
            ? "text-gray-600 hover:text-blue-600 hover:bg-blue-50" 
            : "text-gray-300 cursor-not-allowed"
        }`}
        title={chart.shareable !== false ? "Share Chart" : "Share not available"}
        onClick={chart.shareable !== false ? undefined : undefined}
        disabled={chart.shareable === false}
      >
        <Share2 size={18} />
      </button>

      {/* Export Chart - Always show but disable if export is false */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild disabled={chart.export === false}>
          <button 
            className={`p-2 rounded-full transition-colors ${
              chart.export !== false 
                ? "text-gray-600 hover:text-blue-600 hover:bg-blue-50" 
                : "text-gray-300 cursor-not-allowed"
            }`}
            title={chart.export !== false ? "Export Chart" : "Export not available"}
            disabled={exportingChart === chart.chart_name || chart.export === false}
          >
            {exportingChart === chart.chart_name ? (
              <Check size={18} className="text-green-500" />
            ) : (
              <Download size={18} />
            )}
          </button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuItem onClick={handleExportPNG}>
            <FileImage className="mr-2 h-4 w-4" />
            <span>Export as PNG</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleExportPDF}>
            <FileText className="mr-2 h-4 w-4" />
            <span>Export as PDF</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export default ChartActions;
