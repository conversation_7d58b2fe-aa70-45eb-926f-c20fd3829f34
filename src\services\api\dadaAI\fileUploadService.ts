import { toast } from 'sonner';
import { centralApiClient } from '@/services/api/centralApiClient';

// Define types for the API responses
export interface FileUploadResponse {
  file_session_id: string;
}

export interface FileQueryResponse {
  // Define the expected response structure for file query
  // This will be updated based on the actual API response
  data: any;
  status: string;
  message?: string;
}

/**
 * Default user ID for file operations
 * This matches the user ID used in connectionService.ts
 */
const DEFAULT_USER_ID = "a6e3020d-984a-4394-ac73-da7ec5393314";

/**
 * Uploads multiple CSV or Excel files to the DADA AI API
 * @param files Array of files to upload
 * @param userId The user ID (defaults to the standard user ID)
 * @returns Promise with file session ID
 */
export const uploadFiles = async (
  files: File[], 
  userId: string = DEFAULT_USER_ID
): Promise<string> => {
  try {
    console.log(`Uploading ${files.length} files for user ID: ${userId}`);

    // Create FormData for multipart/form-data request
    const formData = new FormData();
    
    // Add user_id parameter
    formData.append('user_id', userId);
    
    // Add files to FormData
    files.forEach((file, index) => {
      formData.append('files', file);
      console.log(`Added file ${index + 1}: ${file.name} (${file.size} bytes)`);
    });

    // Log FormData contents for debugging
    console.log('FormData contents:');
    for (const [key, value] of formData.entries()) {
      if (value instanceof File) {
        console.log(`${key}: File - ${value.name} (${value.size} bytes)`);
      } else {
        console.log(`${key}: ${value}`);
      }
    }

    const response = await centralApiClient.makeRequest<FileUploadResponse>(
      'dada', 
      '/file-analysis/upload', 
      {
        method: 'POST',
        body: formData,
        skipContentType: true // Let the browser set the content-type with boundary for FormData
      }
    );

    console.log('File upload API response:', response);

    if (!response.file_session_id) {
      throw new Error('No file session ID received from server');
    }

    toast.success(`Successfully uploaded ${files.length} file(s)`);
    return response.file_session_id;

  } catch (error) {
    console.error('Error uploading files:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    toast.error(`Failed to upload files: ${errorMessage}`);
    
    throw error;
  }
};

/**
 * Queries files using the file session ID
 * @param query The user's query string
 * @param fileSessionId The file session ID from upload
 * @param userId The user ID (defaults to the standard user ID)
 * @returns Promise with query response data
 */
export const queryFiles = async (
  query: string,
  fileSessionId: string,
  userId: string = DEFAULT_USER_ID
): Promise<FileQueryResponse> => {
  try {
    console.log(`Querying files with session ID: ${fileSessionId}, query: ${query}, user ID: ${userId}`);

    const requestBody = {
      query: query,
      file_session_id: fileSessionId,
      user_id: userId
    };

    console.log('File query request body:', requestBody);

    const response = await centralApiClient.makeRequest<FileQueryResponse>(
      'dada', 
      '/file-analysis/files/query', 
      {
        method: 'POST',
        body: requestBody
      }
    );

    console.log('File query API response:', response);

    toast.success('File query executed successfully');
    return response;

  } catch (error) {
    console.error('Error querying files:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    toast.error(`Failed to query files: ${errorMessage}`);
    
    throw error;
  }
};

/**
 * Validates if files are supported formats (CSV or Excel)
 * @param files Array of files to validate
 * @returns boolean indicating if all files are valid
 */
export const validateFileFormats = (files: File[]): boolean => {
  const supportedTypes = [
    'text/csv',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ];

  const supportedExtensions = ['.csv', '.xls', '.xlsx'];

  return files.every(file => {
    const hasValidType = supportedTypes.includes(file.type);
    const hasValidExtension = supportedExtensions.some(ext => 
      file.name.toLowerCase().endsWith(ext)
    );
    
    if (!hasValidType && !hasValidExtension) {
      toast.error(`Unsupported file format: ${file.name}. Please upload CSV or Excel files only.`);
      return false;
    }
    
    return true;
  });
};

/**
 * Validates file size limits
 * @param files Array of files to validate
 * @param maxSizePerFile Maximum size per file in bytes (default: 10MB)
 * @param maxTotalSize Maximum total size in bytes (default: 50MB)
 * @returns boolean indicating if all files are within size limits
 */
export const validateFileSizes = (
  files: File[], 
  maxSizePerFile: number = 10 * 1024 * 1024, // 10MB
  maxTotalSize: number = 50 * 1024 * 1024    // 50MB
): boolean => {
  const totalSize = files.reduce((sum, file) => sum + file.size, 0);

  // Check individual file sizes
  for (const file of files) {
    if (file.size > maxSizePerFile) {
      toast.error(`File ${file.name} is too large. Maximum size per file is ${Math.round(maxSizePerFile / 1024 / 1024)}MB.`);
      return false;
    }
  }

  // Check total size
  if (totalSize > maxTotalSize) {
    toast.error(`Total file size is too large. Maximum total size is ${Math.round(maxTotalSize / 1024 / 1024)}MB.`);
    return false;
  }

  return true;
};

/**
 * Comprehensive file validation
 * @param files Array of files to validate
 * @returns boolean indicating if all files are valid
 */
export const validateFiles = (files: File[]): boolean => {
  if (files.length === 0) {
    toast.error('Please select at least one file to upload.');
    return false;
  }

  return validateFileFormats(files) && validateFileSizes(files);
};
