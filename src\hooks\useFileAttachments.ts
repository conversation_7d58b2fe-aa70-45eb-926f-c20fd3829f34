
import { useState, useEffect } from 'react';
import { useAppDispatch } from '@/hooks/useRedux';
import { uploadFiles, validateFiles } from '@/services/api/dadaAI/fileUploadService';
import { setFileSessionId, setUploadedFiles, setIsFileMode, clearFileSession } from '@/stores/dadaSlice';
import { toast } from 'sonner';

interface UseFileAttachmentsReturn {
  files: File[];
  isAttachmentDialogOpen: boolean;
  setIsAttachmentDialogOpen: (isOpen: boolean) => void;
  handleFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  removeFile: (index: number) => void;
  isUploading: boolean;
}

export const useFileAttachments = (isFileUploadMode: boolean = false): UseFileAttachmentsReturn => {
  const [files, setFiles] = useState<File[]>([]);
  const [isAttachmentDialogOpen, setIsAttachmentDialogOpen] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const dispatch = useAppDispatch();

  // Clear file session when switching away from file upload mode
  useEffect(() => {
    if (!isFileUploadMode) {
      dispatch(clearFileSession());
    }
  }, [isFileUploadMode, dispatch]);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFiles = Array.from(e.target.files);

      // For file upload mode (DADA), automatically upload files
      if (isFileUploadMode) {
        // Validate files first
        if (!validateFiles(newFiles)) {
          return;
        }

        setIsUploading(true);

        try {
          // Call the upload API automatically
          const fileSessionId = await uploadFiles(newFiles);

          // Update Redux state
          dispatch(setFileSessionId(fileSessionId));
          dispatch(setUploadedFiles(newFiles));
          dispatch(setIsFileMode(true));

          // Update local state
          setFiles(newFiles);
          setIsAttachmentDialogOpen(false);

          console.log('Files uploaded successfully, session ID:', fileSessionId);

        } catch (error) {
          console.error('Failed to upload files:', error);
          // Don't add files to state if upload failed
        } finally {
          setIsUploading(false);
        }
      } else {
        // For regular file attachments (non-upload mode), just add to state
        setFiles((prevFiles) => [...prevFiles, ...newFiles]);
        setIsAttachmentDialogOpen(false);
      }
    }
  };

  const removeFile = (index: number) => {
    const updatedFiles = files.filter((_, i) => i !== index);
    setFiles(updatedFiles);

    // If in file upload mode and removing files, update Redux state
    if (isFileUploadMode) {
      dispatch(setUploadedFiles(updatedFiles));

      // If no files left, clear file session
      if (updatedFiles.length === 0) {
        dispatch(setFileSessionId(null));
        dispatch(setIsFileMode(false));
      }
    }
  };

  return {
    files,
    isAttachmentDialogOpen,
    setIsAttachmentDialogOpen,
    handleFileChange,
    removeFile,
    isUploading
  };
};
