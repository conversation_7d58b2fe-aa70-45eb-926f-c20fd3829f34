
// Enhanced Constants for unified chart system
export const CHART_COLORS = [
  { bg: 'rgba(59, 130, 246, 0.8)', border: 'rgb(59, 130, 246)', hex: '#3b82f6' }, // Blue
  { bg: 'rgba(239, 68, 68, 0.8)', border: 'rgb(239, 68, 68)', hex: '#ef4444' },   // Red
  { bg: 'rgba(16, 185, 129, 0.8)', border: 'rgb(16, 185, 129)', hex: '#10b981' }, // Green
  { bg: 'rgba(245, 158, 11, 0.8)', border: 'rgb(245, 158, 11)', hex: '#f59e0b' }, // Amber
  { bg: 'rgba(139, 92, 246, 0.8)', border: 'rgb(139, 92, 246)', hex: '#8b5cf6' }, // Purple
  { bg: 'rgba(236, 72, 153, 0.8)', border: 'rgb(236, 72, 153)', hex: '#ec4899' }, // Pink
  { bg: 'rgba(20, 184, 166, 0.8)', border: 'rgb(20, 184, 166)', hex: '#14b8a6' }, // Teal
  { bg: 'rgba(249, 115, 22, 0.8)', border: 'rgb(249, 115, 22)', hex: '#f97316' }, // Orange
  { bg: 'rgba(99, 102, 241, 0.8)', border: 'rgb(99, 102, 241)', hex: '#6366f1' }, // Indigo
  { bg: 'rgba(132, 204, 22, 0.8)', border: 'rgb(132, 204, 22)', hex: '#84cc16' }  // Lime
];

// Legacy export for backward compatibility
export const chartColors = CHART_COLORS;

export type ChartType = 'bar' | 'line' | 'pie' | 'doughnut' | 'horizontalBar' | 'verticalBar';

export type ChartSize = 'small' | 'default' | 'large';

export type ChartContext = 'dashboard' | 'builder' | 'results' | 'thumbnail' | 'chartboard';

// Base chart options configurations
export const BASE_CHART_OPTIONS = {
  responsive: true,
  maintainAspectRatio: false,
  datasets: {
    bar: {
      barThickness: 40,
      maxBarThickness: 60,
      borderWidth: 1,
      borderSkipped: false
    }
  },
  plugins: {
    legend: {
      position: 'top' as const,
      align: 'center' as const,
      labels: {
        padding: 15,
        boxWidth: 12,
        font: {
          size: 11
        }
      },
    },
    title: {
      display: false,
      font: {
        size: 13,
        weight: 'bold' as const
      },
      padding: {
        bottom: 8
      }
    },
    tooltip: {
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      titleColor: '#333',
      bodyColor: '#333',
      borderColor: '#ddd',
      borderWidth: 1,
      padding: 8,
      displayColors: true,
      titleFont: {
        size: 11
      },
      bodyFont: {
        size: 11
      }
    }
  },
  scales: {
    x: {
      grid: {
        display: false
      },
      ticks: {
        font: {
          size: 10
        }
      }
    },
    y: {
      grid: {
        color: '#f0f0f0',
        drawBorder: false
      },
      ticks: {
        font: {
          size: 10
        }
      },
      beginAtZero: true
    }
  }
};

// Thumbnail/minimal chart options
export const MINIMAL_CHART_OPTIONS = {
  responsive: true,
  maintainAspectRatio: true,
  plugins: {
    legend: {
      display: false
    },
    title: {
      display: false
    },
    tooltip: {
      enabled: false
    }
  },
  scales: {
    x: {
      display: false,
      grid: {
        display: false
      }
    },
    y: {
      display: false,
      grid: {
        display: false
      }
    }
  },
  elements: {
    point: {
      radius: 0
    }
  }
};

// Color utilities
export const getChartColor = (index: number) => CHART_COLORS[index % CHART_COLORS.length];

export const getChartColors = (count: number) => 
  Array.from({ length: count }, (_, i) => getChartColor(i));

export const generateColorPalette = (count: number, withTransparency: boolean = true) => {
  return Array.from({ length: count }, (_, i) => {
    const color = getChartColor(i);
    return withTransparency ? color.bg : color.border;
  });
};
