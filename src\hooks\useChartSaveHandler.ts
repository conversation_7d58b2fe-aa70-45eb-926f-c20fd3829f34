
import { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { saveChart, updateChart } from '@/services/api/chart';
import { createChartConfig } from '@/components/dashboard/chartboard/chartUtils';
import { ChartDataItem } from '@/services/api/chart/chartTypes';
import { toast } from 'sonner';
import html2canvas from 'html2canvas';
import { useAuth } from '@/contexts/AuthContext';
import { FilterCondition } from '@/components/dashboard/chartboard/FilterSection';

export const useChartSaveHandler = (
  isEditMode: boolean,
  chartId?: string
) => {
  const navigate = useNavigate();
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const { user } = useAuth(); // Get current user from auth context

  const captureChartAsBase64 = async (): Promise<string | null> => {
    try {
      const chartElement = document.querySelector('.chart-display-container');
      if (!chartElement) {
        console.error('Chart element not found');
        return null;
      }
      
      const canvas = await html2canvas(chartElement as HTMLElement);
      return canvas.toDataURL('image/png');
    } catch (error) {
      console.error('Error capturing chart as base64:', error);
      return null;
    }
  };

  const handleSave = useCallback(async (
    chartName: string,
    chartStyle: string,
    xAxisColumn: string,
    yAxisColumns: string[],
    xAxisTable: string,
    yAxisTables: string[],
    selectedDatabase: string,
    connectionId: string,
    combinedChartData: ChartDataItem[],
    aggregationType: string | null,
    filterConditions?: FilterCondition[] // Add filter conditions parameter
  ) => {
    if (!chartName.trim()) {
      toast.error('Chart name is required');
      return;
    }
    
    try {
      setIsSaving(true);
      
      // Convert filter conditions to the new format expected by the API
      const filters = {};
      if (filterConditions && filterConditions.length > 0) {
        filterConditions.forEach(condition => {
          if (condition.column && condition.value) {
            // Format based on operator type
            if (condition.operator === 'in' || condition.operator === 'not_in') {
              // Handle array values for 'in' operator
              const values = condition.value.split(',').map(v => v.trim());
              filters[condition.column] = { [condition.operator]: values };
            } else if (condition.operator === '=') {
              // Handle equals operator
              filters[condition.column] = condition.value;
            } else {
              // Handle all other operators (gt, lt, like, etc.)
              filters[condition.column] = { [condition.operator]: condition.value };
            }
          }
        });
      }
      
      const config = createChartConfig(
        chartStyle,
        xAxisColumn,
        yAxisColumns,
        xAxisTable,
        yAxisTables,
        chartName,
        selectedDatabase,
        aggregationType,
        filters // Pass filters to createChartConfig
      );
      
      const chartImage = await captureChartAsBase64();
      
      const formattedChartData = {
        status: "success",
        data: combinedChartData || [],
        metadata: {
          tables: config.tables.map(t => t.table_name),
          status: "success"
        }
      };
      
      // Get current user's email
      const userEmail = user?.email || '';
      
      if (isEditMode && chartId) {
        await updateChart(chartId, connectionId, config, formattedChartData, chartImage || undefined, userEmail);
        toast.success('Chart updated successfully');
      } else {
        await saveChart(connectionId, config, formattedChartData, chartImage || undefined, userEmail);
        toast.success('Chart saved successfully');
      }
      
      setShowSaveDialog(false);
      navigate('/saved-charts');
    } catch (error) {
      console.error('Error saving chart:', error);
      toast.error(isEditMode ? 'Failed to update chart' : 'Failed to save chart');
    } finally {
      setIsSaving(false);
    }
  }, [isEditMode, chartId, navigate, user]); // Add user to dependencies

  return {
    showSaveDialog,
    setShowSaveDialog,
    isSaving,
    handleSave
  };
};
