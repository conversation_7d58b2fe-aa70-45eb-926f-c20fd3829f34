
// import React, { useMemo, useCallback } from 'react';
// import { PanelLeftClose } from "lucide-react";
// import { Separator } from "@/components/ui/separator";
// import { useAppSelector, useAppDispatch } from '@/hooks/useRedux';
// import { useNavigate } from 'react-router-dom';
// import FileSection from '../FileSection';
// import SearchInput from '../search/SearchInput';
// import { FolderType } from '@/services/api/audioTranscript/types';
// import { toast } from 'sonner';
// import { toggleTrackerExpanded } from '@/stores/fileSlice';

// interface FilesDashboardViewProps {
//   searchQuery: string;
//   handleSearch: (e: React.ChangeEvent<HTMLInputElement>) => void;
//   personalFiles: { file_id: string; filename: string }[];
//   projectFiles: { file_id: string; filename: string }[];
//   isPersonalExpanded: boolean;
//   isProjectExpanded: boolean;
//   togglePersonalExpanded: () => void;
//   toggleProjectExpanded: () => void;
//   isLoadingFiles: boolean;
//   handleFileClick: (fileId: string, folderType: FolderType, fileName?: string) => void;
//   handleDeleteClick: (fileId: string, folderType: FolderType) => void;
//   onToggle: () => void;
// }

// const FilesDashboardView: React.FC<FilesDashboardViewProps> = ({
//   searchQuery,
//   handleSearch,
//   personalFiles,
//   projectFiles,
//   isPersonalExpanded,
//   isProjectExpanded,
//   togglePersonalExpanded,
//   toggleProjectExpanded,
//   isLoadingFiles,
//   handleFileClick,
//   handleDeleteClick,
//   onToggle
// }) => {
//   const navigate = useNavigate();
//   const dispatch = useAppDispatch();
  
//   // Get tracker files, expansion state, and projects from Redux
//   const { 
//     trackerFiles, 
//     isTrackerExpanded,
//     trackerProjects 
//   } = useAppSelector(state => state.file);
  
//   const fetchStatus = useAppSelector(state => state.file.fetchStatus);
//   const isLoadingTracker = fetchStatus.trackerProjects === 'loading';

//   // Add handler for toggling tracker expanded state
//   const handleToggleTrackerExpanded = useCallback(() => {
//     dispatch(toggleTrackerExpanded());
//   }, [dispatch]);

//   // Filter files based on search query - memoized for performance
//   const filteredPersonalFiles = useMemo(() => {
//     return personalFiles.filter(file => 
//       file.filename.toLowerCase().includes(searchQuery.toLowerCase())
//     );
//   }, [personalFiles, searchQuery]);

//   const filteredProjectFiles = useMemo(() => {
//     return projectFiles.filter(file => 
//       file.filename.toLowerCase().includes(searchQuery.toLowerCase())
//     );
//   }, [projectFiles, searchQuery]);

//   // Filter tracker projects based on search query
//   const filteredTrackerProjects = useMemo(() => {
//     if (!searchQuery.trim()) {
//       return trackerProjects;
//     }
    
//     const query = searchQuery.toLowerCase();
//     return trackerProjects.filter(project => 
//       project.name.toLowerCase().includes(query)
//     );
//   }, [trackerProjects, searchQuery]);

//   // Handle tracker project click - memoize to prevent re-renders
//   const handleTrackerProjectClick = useCallback((projectId: string, folderType: FolderType) => {
//     // Find the project by ID from our projects list
//     const project = trackerProjects.find(p => p.id === projectId);
//     if (project) {
//       console.log(`Navigating to tracker project: ${project.name}`);
//       navigate(`/transcript/tracker/${encodeURIComponent(project.name)}`);
//     } else {
//       console.error(`Project with ID ${projectId} not found`);
//       toast.error('Project not found');
//     }
//   }, [trackerProjects, navigate]);

//   return (
//     <div className="flex h-full">
//       <div className="w-64 flex flex-col h-full bg-gray-200 border group">
//         <div className="p-3">
//           <div className="flex items-center space-x-2 group">
//             <SearchInput
//               searchQuery={searchQuery}
//               handleSearch={handleSearch}
//               placeholder="Search files..."
//             />
//             <button 
//               onClick={onToggle}
//               className="flex-shrink-0 w-8 h-8 rounded-md flex items-center justify-center hover:bg-gray-100 transition-colors duration-200 opacity-0 group-hover:opacity-100"
//               aria-label="Collapse sidebar"
//             >
//               <PanelLeftClose size={20} className="h-5 w-5 text-gray-600 hover:text-gray-900" />
//             </button>
//           </div>
//         </div>

//         {/* Add a wrapper div with hidden scrollbar */}
//         <div className="flex-1 overflow-y-auto py-2 [scrollbar-width:none] [-ms-overflow-style:none] [&::-webkit-scrollbar]:hidden">
//           <div className="space-y-1">
//             <FileSection
//               title="Personal Files"
//               folderType="personal"
//               files={filteredPersonalFiles}
//               isExpanded={isPersonalExpanded}
//               toggleExpanded={togglePersonalExpanded}
//               onFileClick={handleFileClick}
//               onDeleteClick={(fileId) => handleDeleteClick(fileId, 'personal')}
//               isLoading={isLoadingFiles}
//               iconColor="text-blue-600"
//             />

//             <Separator className="my-2" />

//             <FileSection
//               title="Project Files"
//               folderType="project"
//               files={filteredProjectFiles}
//               isExpanded={isProjectExpanded}
//               toggleExpanded={toggleProjectExpanded}
//               onFileClick={handleFileClick}
//               onDeleteClick={(fileId) => handleDeleteClick(fileId, 'project')}
//               isLoading={isLoadingFiles}
//               iconColor="text-green-600"
//             />

//             <Separator className="my-2" />

//             <FileSection
//               title="Tracker"
//               folderType="tracker"
//               files={filteredTrackerProjects.map(project => ({
//                 file_id: project.id,
//                 filename: project.name
//               }))}
//               isExpanded={isTrackerExpanded}
//               toggleExpanded={handleToggleTrackerExpanded}
//               onFileClick={handleTrackerProjectClick}
//               onDeleteClick={() => {}}
//               isLoading={isLoadingTracker}
//               iconColor="text-purple-600"
//               isTracker={true}
//               trackerProjects={filteredTrackerProjects}
//             />
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default React.memo(FilesDashboardView);
