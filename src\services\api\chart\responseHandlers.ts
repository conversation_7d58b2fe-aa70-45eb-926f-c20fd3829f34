
import { ChartDataItem, TabularDataItem, TabularDataResponse } from './chartTypes';

export class ResponseHandlers {
  static handleChartDataResponse(data: any): ChartDataItem[] {
    // Handle different response formats
    if (data && data.status === "success" && Array.isArray(data.data)) {
      // Format 1: {status: "success", data: [...]}
      console.log('Received success response with data array');
      return data.data;
    } else if (data && Array.isArray(data)) {
      // Format 2: Direct array
      console.log('Received direct data array');
      return data;
    } else if (data && typeof data === 'object' && data.data && Array.isArray(data.data)) {
      // Format 3: {data: [...]} without status
      console.log('Received data object with data array');
      return data.data;
    } else if (data && typeof data === 'object' && data.metadata) {
      // Format 4: {metadata: {...}, data: [...]} or similar structure
      console.log('Received data with metadata');
      return Array.isArray(data.data) ? data.data : [];
    } else {
      // Try to extract any array from the response
      const possibleArrays = Object.values(data || {}).filter(val => Array.isArray(val));
      if (possibleArrays.length > 0) {
        console.log('Found array in response:', possibleArrays[0]);
        return possibleArrays[0];
      }
      
      console.error('Unexpected response format:', data);
      throw new Error('Unexpected response format from server');
    }
  }

  static handleChartSaveResponse(data: any): any {
    // For now, return the data as-is
    // Can add specific handling for save responses if needed
    return data;
  }

  static handleChartsListResponse(data: any): { charts: any[] } {
    // Ensure we return the expected format
    return data;
  }

  static handleTabularDataResponse(data: any): TabularDataItem[] {
    console.log('handleTabularDataResponse called with data:', data);

    // Handle the specific response format: {chart_response: {data: [...], status: "success", metadata: {...}}}
    if (data && data.chart_response && data.chart_response.status === "success" && Array.isArray(data.chart_response.data)) {
      console.log('Received tabular data response with success status');
      return data.chart_response.data;
    } else if (data && data.chart_response && Array.isArray(data.chart_response.data)) {
      // Handle case where status might not be present but data exists
      console.log('Received tabular data response with data array');
      return data.chart_response.data;
    } else if (data && Array.isArray(data.data)) {
      // Fallback: direct data array
      console.log('Received direct data array for tabular data');
      return data.data;
    } else if (data && Array.isArray(data)) {
      // Fallback: response is directly an array
      console.log('Received direct array for tabular data');
      return data;
    } else {
      console.error('Unexpected tabular data response format:', data);
      throw new Error('Unexpected tabular data response format from server');
    }
  }
}
