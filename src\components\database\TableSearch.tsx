import React, { useState, useEffect } from 'react';
import { Search, Check } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

interface TableMetadata {
  Table_Id: number;
  Table_Name: string;
  Table_Description: string;
  Database_Name: string;
  connection_id: number;
  Columns_List: Record<string, string>;
}

interface ColumnMetadata {
  Column_Id: number;
  Column_Name: string;
  Column_Description: string;
  Column_Table_Id: number;
  Column_Table_Name: string;
}

interface TableSearchProps {
  connectionId: number;
}

const TableSearch: React.FC<TableSearchProps> = ({ connectionId }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [tables, setTables] = useState<TableMetadata[]>([]);
  const [columns, setColumns] = useState<ColumnMetadata[]>([]);
  const [selectedTables, setSelectedTables] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showTableList, setShowTableList] = useState(false);

  // Fetch tables and columns when connection ID changes
  useEffect(() => {
    if (connectionId) {
      fetchMetadata(connectionId);
    }
  }, [connectionId]);

  const fetchMetadata = async (connId: number) => {
    try {
      setIsLoading(true);
      // In a real implementation, replace with actual API call
      // const response = await fetch(`/api/metadata/${connId}`);
      // const data = await response.json();
      
      // Using the sample data from your JSON response
      const data = {
        "status": "success",
        "table_metadata": [
          {
            "Table_Id": 77,
            "Table_Name": "claim_icd",
            "Table_Description": "The claim_icd table in the health_db database stores ICD codes associated with claims...",
            "Columns_List": {"0": "Patient_Id", "1": "Claim_Dt", "2": "Claim_Line_Id", "3": "ICD_CD"},
            "Database_Name": "health_db",
            "connection_id": 8
          },
          {
            "Table_Id": 76,
            "Table_Name": "claims",
            "Table_Description": "The 'claims' table in the 'health_db' PostgreSQL database contains healthcare claim records...",
            "Columns_List": {"0": "Patient_Id", "1": "PCP_Id", "2": "Claim_Dt", "3": "Claim_Line_Id", "4": "Claim_Line_Amount"},
            "Database_Name": "health_db",
            "connection_id": 8
          },
          {
            "Table_Id": 75,
            "Table_Name": "patient_data",
            "Table_Description": "Contains basic patient information including Patient_Id, Patient_First_Name, Patient_Last_Name...",
            "Columns_List": {"0": "Patient_Id", "1": "Patient_First_Name", "2": "Patient_Last_Name", "3": "Active_Record", "4": "ETL_DT"},
            "Database_Name": "health_db",
            "connection_id": 8
          }
        ],
        "column_metadata": [
          // Column metadata from your JSON
        ]
      };
      
      if (data.status === "success") {
        setTables(data.table_metadata);
        setColumns(data.column_metadata || []);
        setShowTableList(true);
        toast.success(`Found ${data.table_metadata.length} tables`);
      } else {
        toast.error(data.message || "Failed to fetch metadata");
      }
    } catch (error) {
      console.error('Error fetching metadata:', error);
      toast.error('Failed to fetch database metadata');
    } finally {
      setIsLoading(false);
    }
  };

  const handleScanTables = () => {
    if (connectionId) {
      fetchMetadata(connectionId);
    } else {
      toast.error('No connection selected');
    }
  };

  const handleSelectAll = () => {
    if (selectedTables.length === filteredTables.length) {
      setSelectedTables([]);
    } else {
      setSelectedTables(filteredTables.map(table => table.Table_Name));
    }
  };

  const handleSelectTable = (tableName: string) => {
    if (selectedTables.includes(tableName)) {
      setSelectedTables(selectedTables.filter(t => t !== tableName));
    } else {
      setSelectedTables([...selectedTables, tableName]);
    }
  };

  // Filter tables based on search query
  const filteredTables = tables.filter(table => 
    table.Table_Name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="p-4 max-w-2xl">
      {/* Scan Tables Button */}
      <Button
        onClick={handleScanTables}
        disabled={isLoading}
        className="w-full bg-gray-600 text-white hover:bg-gray-700 mb-4"
      >
        {isLoading ? 'Scanning...' : 'Scan Tables/Views'}
      </Button>

      {/* Search Table View */}
      {showTableList && (
        <>
          <div className="mb-2 relative">
            <Input
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search Table View"
              className="pl-8"
            />
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          </div>

          {/* Table/View Access Box */}
          <div className="border border-gray-300 rounded mb-4">
            <div className="bg-gray-100 border-b border-gray-300 px-4 py-2 font-medium text-sm">
              Table/View Access
            </div>
            <div className="max-h-64 overflow-y-auto">
              <table className="w-full">
                <tbody>
                  {filteredTables.map((table, index) => (
                    <tr key={table.Table_Id} className="border-b border-gray-200 last:border-0">
                      <td className="px-4 py-2 w-1/2">{table.Table_Name}</td>
                      <td className="px-4 py-2 text-right">
                        {index === 0 && (
                          <button 
                            onClick={handleSelectAll}
                            className="text-amber-600 hover:text-amber-700 font-medium text-sm"
                          >
                            Select All
                          </button>
                        )}
                        <button 
                          onClick={() => handleSelectTable(table.Table_Name)}
                          className="text-amber-600 hover:text-amber-700 font-medium text-sm ml-4"
                        >
                          {selectedTables.includes(table.Table_Name) ? (
                            <span className="flex items-center">
                              <Check className="h-4 w-4 mr-1" />
                              Selected
                            </span>
                          ) : (
                            'Select'
                          )}
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Selected Tables Information */}
          {selectedTables.length > 0 && (
            <div className="border border-gray-300 rounded mb-4">
              <div className="bg-gray-100 border-b border-gray-300 px-4 py-2 font-medium text-sm">
                Selected Tables
              </div>
              <div className="p-4">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left pb-2">Table Name</th>
                      <th className="text-left pb-2">Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    {selectedTables.map(tableName => {
                      const tableInfo = tables.find(t => t.Table_Name === tableName);
                      return tableInfo ? (
                        <tr key={tableInfo.Table_Id} className="border-b border-gray-200 last:border-0">
                          <td className="py-2 pr-4">{tableInfo.Table_Name}</td>
                          <td className="py-2 text-sm text-gray-600">
                            {tableInfo.Table_Description.length > 100 
                              ? tableInfo.Table_Description.substring(0, 100) + '...' 
                              : tableInfo.Table_Description}
                          </td>
                        </tr>
                      ) : null;
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Scan Column Metadata Button */}
          <Button
            className="w-full bg-gray-600 text-white hover:bg-gray-700 mb-4"
            disabled={selectedTables.length === 0}
          >
            Scan column metadata
          </Button>
        </>
      )}
    </div>
  );
};

export default TableSearch;