
import React from 'react';
import UniversalChart from './UniversalChart';
import { SavedChart, ChartDataPoint } from '@/types/chartTypes';

interface ChartRendererProps {
  chart: SavedChart;
  chartData: ChartDataPoint[];
  size?: 'small' | 'default' | 'large';
  context?: 'dashboard' | 'builder' | 'results' | 'thumbnail' | 'chartboard';
  className?: string;
  onSave?: () => void;
}

const ChartRenderer: React.FC<ChartRendererProps> = ({ 
  chart, 
  chartData, 
  size = 'default',
  context = 'builder',
  className,
  onSave
}) => {
  return (
    <UniversalChart
      savedChart={chart}
      chartData={chartData}
      size={size}
      context={context}
      className={className}
      onSave={onSave}
    />
  );
};

export default ChartRenderer;
