
import { useState, useEffect } from 'react';

/**
 * Hook to manage power mode state
 */
export const usePowerModeState = (initialPowerMode: boolean = false) => {
  const [isPowerModeActive, setIsPowerModeActive] = useState(initialPowerMode);
  
  // Update internal power mode state when prop changes
  useEffect(() => {
    setIsPowerModeActive(initialPowerMode);
  }, [initialPowerMode]);

  // Function to enable power mode
  const enablePowerMode = () => {
    setIsPowerModeActive(true);
  };

  // Function to toggle power mode
  const togglePowerMode = (enabled: boolean) => {
    setIsPowerModeActive(enabled);
  };

  return {
    isPowerModeActive,
    enablePowerMode,
    togglePowerMode
  };
};
