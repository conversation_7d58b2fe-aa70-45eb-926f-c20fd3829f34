
import React from 'react';
import { DatabaseZap, FileSpreadsheet } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from 'sonner';
import { useAppDispatch } from '@/hooks/useRedux';
import { clearFileSession } from '@/stores/dadaSlice';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface PowerModeButtonProps {
  enabled: boolean;
  onToggle: () => void;
  selectedMode: string;
  onModeSelect: (mode: string) => void;
}

export const PowerModeButton: React.FC<PowerModeButtonProps> = ({ 
  enabled, 
  onToggle,
  selectedMode,
  onModeSelect
}) => {
  const dispatch = useAppDispatch();

  const handleModeSelect = (mode: string) => {
    onModeSelect(mode);

    // Clear file session when switching away from Upload Files mode
    if (mode !== 'Upload Files') {
      dispatch(clearFileSession());
    }

    // Handle mode-specific actions and notifications
    switch (mode) {
      case 'Default Query':
        if (enabled) {
          onToggle(); // Disable power mode
          toast.info('Mode Changed', {
            description: 'Switched to Default Query mode. Power mode disabled.',
            // style: { backgroundColor: '#F2FCE2', color: '#505050', border: '1px solid #86c555' }
          });
        } else {
          toast.info('Mode Changed', {
            description: 'Switched to Default Query mode.',
            // style: { backgroundColor: '#F2FCE2', color: '#505050', border: '1px solid #86c555' }
          });
        }
        break;

      case 'Power Query':
        if (!enabled) {
          onToggle(); // Enable power mode
          toast.success('Power Mode Activated', {
            description: 'Power Query mode enabled. Type @ to use power keywords.',
            // style: { backgroundColor: '#F2FCE2', color: '#505050', border: '1px solid #86c555' }
          });
        } else {
          toast.info('Mode Changed', {
            description: 'Switched to Power Query mode.',
            // style: { backgroundColor: '#F2FCE2', color: '#505050', border: '1px solid #86c555' }
          });
        }
        break;

      case 'Meta Data':
        toast.info('Mode Changed', {
          description: 'Switched to Meta Data mode.',
          // style: { backgroundColor: '#F2FCE2', color: '#505050', border: '1px solid #86c555' }
        });
        break;
        
      case 'Upload Files':
        toast.info('Mode Changed', {
          description: 'Switched to Upload Files mode. You can upload CSV or Excel files for analysis.',
          // style: { backgroundColor: '#F2FCE2', color: '#505050', border: '1px solid #86c555' }
        });
        break;
    }
  };

  return (
    <div className="absolute left-1 z-10">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            type="button"
            size="icon"
            className={`rounded-full ${enabled 
              ? "bg-green-500 text-white hover:bg-green-600" 
              : "bg-gray-300 text-gray-600 hover:bg-gray-400"}`}
          >
            <DatabaseZap 
              size={18} 
              className="transform rotate-45 transition-colors duration-200"
            />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-40">
          <DropdownMenuItem 
            onClick={() => handleModeSelect('Default Query')}
            className={selectedMode === 'Default Query' ? 'bg-gray-100' : ''}
          >
            Default Query
          </DropdownMenuItem>
          <DropdownMenuItem 
            onClick={() => handleModeSelect('Power Query')}
            className={selectedMode === 'Power Query' ? 'bg-gray-100' : ''}
          >
            Power Query
          </DropdownMenuItem>
          <DropdownMenuItem 
            onClick={() => handleModeSelect('Meta Data')}
            className={selectedMode === 'Meta Data' ? 'bg-gray-100' : ''}
          >
            Meta Data
          </DropdownMenuItem>
          <DropdownMenuItem 
            onClick={() => handleModeSelect('Upload Files')}
            className={selectedMode === 'Upload Files' ? 'bg-gray-100' : ''}
          >
            <FileSpreadsheet size={16} className="mr-2" />
            Upload Files
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};


