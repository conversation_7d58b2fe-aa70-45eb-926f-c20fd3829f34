import { useEffect } from "react"
import { useAppDispatch, useAppSelector } from "@/hooks/useRedux"
import { 
  setSearchQuery, 
  setHeaderSearchQuery, 
  setInputValue, 
  setUploadedFile,
  deleteMessage,
  toggleMinimizeMessage,
  activateVoiceInput,
  loadMessages,
  submitQuery
} from "@/stores/messageSlice"
import { setShowDatasetScreen } from "@/stores/datasetSlice"
import { ContentProps, textSizeStyles } from "./models"
import MessageItem from "./MessageItem"
import SqlResultView from "./SqlResultView"
import { ChatInput } from "./chat"
import DatasetScreen from "./DatasetScreen"
import { useLocation } from 'react-router-dom';


interface ExtendedContentProps extends ContentProps {
  sidebarCollapsed?: boolean;
  rightSidebarVisible?: boolean;
  shouldExpandContent?: boolean;
}

const Content = ({
  dashboardType,
  textSize = "medium",
  searchQuery,
  headerSearchQuery,
  showMetricScreen,
  showDatasetScreen,
  setShowDatasetScreen: propSetShowDatasetScreen,
  sidebarCollapsed,
  rightSidebarVisible,
  shouldExpandContent
}: ExtendedContentProps) => {
  const location = useLocation();
  const isTrackerView = location.pathname.includes('/transcript/tracker/');
  const dispatch = useAppDispatch();
  const { 
    messages, 
    filteredMessages,
    isLoading 
  } = useAppSelector(state => state.message);

  // Get dashboard-specific input value based on dashboard type
  const inputValue = useAppSelector(state => {
    const messageState = state.message;
    switch (dashboardType) {
      case 1: return messageState.chatbotInputValue || '';
      case 2: return messageState.transcriptInputValue || '';
      case 3: return messageState.dadaInputValue || '';
      default: return '';
    }
  });
  
  // Set up effects for search queries and screen states
  useEffect(() => {
    dispatch(setSearchQuery(searchQuery || ''));
  }, [dispatch, searchQuery]);

  useEffect(() => {
    dispatch(setHeaderSearchQuery(headerSearchQuery || ''));
  }, [dispatch, headerSearchQuery]);

  useEffect(() => {
    dispatch(setShowDatasetScreen(showDatasetScreen));
  }, [dispatch, showDatasetScreen]);

  useEffect(() => {
    console.log("Loading messages for dashboard type:", dashboardType);
    dispatch(loadMessages(dashboardType as 1 | 2 | 3));
  }, [dispatch, dashboardType]);

  // Event handlers
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!inputValue.trim()) return;
    
    // Ensure dashboardType is properly typed as 1 | 2 | 3
    dispatch(submitQuery({ inputValue, dashboardType: dashboardType as 1 | 2 | 3 }));
  };

  const handleDeleteMessage = (index: number) => {
    dispatch(deleteMessage(index));
  };

  const toggleMinimize = (index: number) => {
    dispatch(toggleMinimizeMessage(index));
  };

  const handleVoiceInput = () => {
    // This is now fixed to dispatch the thunk action correctly
    dispatch(activateVoiceInput());
  };

  // Set input value specific to current dashboard
  const setDashboardInputValue = (value: string) => {
    dispatch(setInputValue({ value, dashboardType: dashboardType as 1 | 2 | 3 }));
  };

  // Get container classes based on expansion state
  const getContainerClasses = () => {
    if (shouldExpandContent) {
      return "w-full flex flex-col h-full pt-4";
    }
    return "max-w-4xl mx-auto w-full flex flex-col h-full pt-4";
  };

  // For TrackerTableView, render it with flex-based container that works within dashboard layout
  // if (dashboardType === 2 && isTrackerView) {
  //   return (
  //     <div className="flex-1 flex flex-col h-full overflow-hidden bg-white">
  //       <div className="flex-1 overflow-hidden">
  //         <TrackerTableView />
  //       </div>
  //     </div>
  //   );
  // }


  if (showDatasetScreen) {
    return (
      <div className="flex-1 flex flex-col h-full overflow-hidden">
        <div className="w-full flex flex-col h-full">
          <div className="flex-1 overflow-y-auto hover-scrollbar">
            <DatasetScreen onClose={() => propSetShowDatasetScreen(false)} />
          </div>
        </div>
      </div>
    );
  }

  // Render standard content with messages
  return (
    <div className="flex-1 flex flex-col h-full overflow-hidden">
      <div className={getContainerClasses()}>
        <div className={`flex-1 overflow-y-auto px-4 hover-scrollbar ${textSizeStyles[textSize].spacing}`}>
          {filteredMessages.map((message, index) => (
            <div key={index} className={textSizeStyles[textSize].spacing}>
              {dashboardType === 3 ? (
                <SqlResultView
                  message={message}
                  index={index}
                  toggleMinimizeMessage={toggleMinimize}
                  handleDeleteMessage={handleDeleteMessage}
                  messages={messages}
                />
              ) : (
                <MessageItem
                  message={message}
                  index={index}
                  textSizeStyles={textSizeStyles}
                  toggleMinimizeMessage={toggleMinimize}
                  handleDeleteMessage={handleDeleteMessage}
                />
              )}
            </div>
          ))}

          {isLoading && (
            <div className="flex justify-center items-center py-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              <span className="ml-2 text-gray-600">Processing your query...</span>
            </div>
          )}
        </div>

        <div className="sticky bottom-0 bg-white pt-2 pb-4 border-t">
          <ChatInput
            inputValue={inputValue}
            setInputValue={setDashboardInputValue}
            handleSubmit={handleSubmit}
            activateVoiceInput={handleVoiceInput}
            isLoading={isLoading}
            dashboardType={dashboardType as 1 | 2 | 3} // Ensure dashboardType is properly typed
          />
        </div>
      </div>
    </div>
  );
};

export default Content;
