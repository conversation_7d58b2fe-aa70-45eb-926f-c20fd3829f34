
import { ReactNode } from 'react';

export interface SidebarNavProps {
  activeDashboard?: 1 | 2 | 3;
  textSize: 'small' | 'medium' | 'large';
  handleTextSizeChange: (size: 'small' | 'medium' | 'large') => void;
  onDatasetIconClick?: () => void;
  isDatasetScreenOpen?: boolean;
}

export interface ChatTopic {
  id: string;
  name: string;
  chats: string[];
  expanded: boolean;
}

export interface MeetingItem {
  name: string;
  type: 'transcript' | 'summary' | 'action' | 'question';
}

export interface Meeting {
  id: string;
  name: string;
  items: MeetingItem[];
  expanded: boolean;
}

export interface Project {
  id: string;
  name: string;
  chats: string[];
  expanded: boolean;
}

// Add the missing FolderItem interface
export interface FolderItem {
  name: string;
  type: 'folder';
  files: string[];
  subFolders?: FolderItem[];
}
