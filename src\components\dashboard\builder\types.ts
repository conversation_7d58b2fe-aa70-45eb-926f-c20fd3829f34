import { SavedChart, ChartDataPoint } from '@/types/chartTypes';
import { TabularDataItem } from '@/services/api/chart/chartTypes';

export interface CardState {
  id: string;
  type: 'chart' | 'table';
  chart: SavedChart | null;
  data: ChartDataPoint[] | TabularDataItem[] | null;
  loading: boolean;
  createdAt: number;
}

export interface ChartTableGridProps {
  className?: string;
  chartZoneChart?: SavedChart | null;
  tableZoneChart?: SavedChart | null;
  chartData?: ChartDataPoint[] | null;
  loadingChartData?: boolean;
  onRemoveChart?: (zoneType: 'chart' | 'table') => void;
  tabularData?: TabularDataItem[] | null;
  loadingTabularData?: boolean;
  chartCards?: string[];
  tableCards?: string[];
  onRemoveCard?: (cardId: string) => void;
  cards?: CardState[];
  onUpdateCard?: (cardId: string, updates: Partial<CardState>) => void;
}