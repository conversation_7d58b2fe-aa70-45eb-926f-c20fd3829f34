
import React, { useEffect } from 'react';
import { useLocation, useParams, useNavigate } from 'react-router-dom';
import DashboardLayout from '@/components/dashboard/layout/DashboardLayout';
import { useAppDispatch, useAppSelector } from '@/hooks/useRedux';
import { ChartState, selectSelectedDatabase, selectConnectionId } from '@/stores/chartSlice';

interface DashboardProps {
  initialDashboard?: 1 | 2 | 3;
}

const Dashboard: React.FC<DashboardProps> = ({ initialDashboard }) => {
  const location = useLocation();
  const { folderType, fileName } = useParams();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  
  // Get files from Redux store
  // const personalFiles = useAppSelector(state => state.file.personalFiles);
  // const projectFiles = useAppSelector(state => state.file.projectFiles);
  
  // Use selectors to get chart state
  const selectedDatabase = useAppSelector(selectSelectedDatabase);
  const connectionId = useAppSelector(selectConnectionId);

  const getDashboardType = () => {
    if (initialDashboard) {
      return initialDashboard;
    } else if (location.state && location.state.dashboard) {
      const dashboardType = Number(location.state.dashboard);
      if ([1, 2, 3].includes(dashboardType)) {
        return dashboardType as 1 | 2 | 3;
      }
    }
    return 1;
  };

  const dashboardType = getDashboardType();

  const isDatabaseSelected = () => {
    return !!selectedDatabase && !!connectionId;
  };

  useEffect(() => {
    console.log("Dashboard - selectedDatabase from Redux:", selectedDatabase);
    console.log("Dashboard - connectionId from Redux:", connectionId);
  }, [selectedDatabase, connectionId]);

  // Remove the useEffect for transcript access
  // useEffect(() => {
  //   const handleTranscriptAccess = async () => {
  //     // Only proceed if we have both folderType and fileName in the URL
  //     if (folderType && fileName) {
  //       // ...transcript handling code
  //     }
  //   };
  //   
  //   handleTranscriptAccess();
  // }, [folderType, fileName, dispatch, navigate, location.pathname]);

  return (
    <DashboardLayout initialDashboard={dashboardType}>
      {!isDatabaseSelected() && dashboardType === 2 && (
        <div className="text-red-500 p-2 bg-red-50 rounded border border-red-200 mb-4">
          No database selected
        </div>
      )}
    </DashboardLayout>
  );
};

export default Dashboard;



