// DataGrid API Types

export interface DataGridTableRequest {
  table_name: string;
  columns: string[];
}



// Column-based DataGrid request
export interface DataGridRequest {
  connection_id: string;
  tables: DataGridTableRequest[];
  filters?: { [key: string]: any }; // Support both single values and complex filter objects
}

// SQL-based DataGrid request
export interface DataGridSQLRequest {
  connection_id: string;
  sql: string;
}

export interface DataGridResponse {
  status: string;
  data: Record<string, any>[];
}

export interface DataGridError {
  message: string;
  status?: number;
}

// Column data from drag and drop
export interface SelectedColumn {
  name: string;
  type?: string;
  tableName?: string;
}

// Grouped columns by table for API request
export interface GroupedColumns {
  [tableName: string]: string[];
}


