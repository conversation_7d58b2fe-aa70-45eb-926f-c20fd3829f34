import React from 'react';
import { cn } from '@/lib/utils';

interface FormSectionProps {
  title: string;
  children: React.ReactNode;
  className?: string;
  headerClassName?: string;
}

interface FormFieldProps {
  label: string;
  children: React.ReactNode;
  className?: string;
  labelClassName?: string;
  required?: boolean;
}

// Form Section Component - Microsoft-style section container
export const FormSection: React.FC<FormSectionProps> = ({
  title,
  children,
  className,
  headerClassName
}) => {
  return (
    <div className={cn("mb-6 border border-gray-200 rounded-lg shadow-sm bg-white", className)}>
      <div className={cn("bg-gray-50 px-4 py-3 border-b border-gray-200", headerClassName)}>
        <h3 className="text-sm font-normal text-gray-700">{title}</h3>
      </div>
      <div className="p-4">
        {children}
      </div>
    </div>
  );
};

// Form Field Component - Microsoft-style field layout
export const FormField: React.FC<FormFieldProps> = ({
  label,
  children,
  className,
  labelClassName,
  required = false
}) => {
  return (
    <div className={cn("grid grid-cols-12 gap-4 items-center mb-6", className)}>
      <label className={cn("col-span-2 text-sm font-normal text-gray-700 flex items-center h-full", labelClassName)}>
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}:
      </label>
      <div className="col-span-10">
        {children}
      </div>
    </div>
  );
};

// Form Field with AI Button - Microsoft-style field with AI assistance
export const FormFieldWithAI: React.FC<FormFieldProps & { onAIClick?: () => void; aiDisabled?: boolean }> = ({
  label,
  children,
  className,
  labelClassName,
  required = false,
  onAIClick,
  aiDisabled = false
}) => {
  return (
    <div className={cn("grid grid-cols-12 gap-4 items-center mb-6", className)}>
      <label className={cn("col-span-2 text-sm font-normal text-gray-700 flex items-center h-full", labelClassName)}>
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}:
      </label>
      <div className="col-span-9">
        {children}
      </div>
      {onAIClick && (
        <div className="col-span-1 flex items-center justify-center h-full">
          <button
            onClick={onAIClick}
            disabled={aiDisabled}
            className="p-2 text-blue-600 hover:text-blue-800 disabled:text-gray-400 disabled:cursor-not-allowed"
            title="AI Assistance"
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </button>
        </div>
      )}
    </div>
  );
};

// Microsoft-style Input Component
export const MSInput = React.forwardRef<
  HTMLInputElement,
  React.InputHTMLAttributes<HTMLInputElement> & {
    error?: boolean;
  }
>(({ className, error, ...props }, ref) => {
  return (
    <input
      ref={ref}
      className={cn(
        "w-full py-2 px-3 text-sm border rounded-md transition-all duration-200",
        "focus:border-blue-500 focus:ring-1 focus:ring-blue-200 focus:outline-none",
        error 
          ? "border-red-300 focus:border-red-500 focus:ring-red-200" 
          : "border-gray-300",
        "disabled:bg-gray-100 disabled:cursor-not-allowed",
        className
      )}
      {...props}
    />
  );
});
MSInput.displayName = "MSInput";

// Microsoft-style Textarea Component
export const MSTextarea = React.forwardRef<
  HTMLTextAreaElement,
  React.TextareaHTMLAttributes<HTMLTextAreaElement> & {
    error?: boolean;
  }
>(({ className, error, ...props }, ref) => {
  return (
    <textarea
      ref={ref}
      className={cn(
        "w-full px-3 py-2 text-sm border rounded-md resize-none transition-all duration-200",
        "focus:border-blue-500 focus:ring-1 focus:ring-blue-200 focus:outline-none",
        error 
          ? "border-red-300 focus:border-red-500 focus:ring-red-200" 
          : "border-gray-300",
        "disabled:bg-gray-100 disabled:cursor-not-allowed",
        className
      )}
      {...props}
    />
  );
});
MSTextarea.displayName = "MSTextarea";

// Microsoft-style Select Component
export const MSSelect = React.forwardRef<
  HTMLSelectElement,
  React.SelectHTMLAttributes<HTMLSelectElement> & {
    error?: boolean;
  }
>(({ className, error, children, ...props }, ref) => {
  return (
    <select
      ref={ref}
      className={cn(
        "w-full py-2 px-3 text-sm border rounded-md transition-all duration-200",
        "focus:border-blue-500 focus:ring-1 focus:ring-blue-200 focus:outline-none",
        error 
          ? "border-red-300 focus:border-red-500 focus:ring-red-200" 
          : "border-gray-300",
        "disabled:bg-gray-100 disabled:cursor-not-allowed",
        "bg-white",
        className
      )}
      {...props}
    >
      {children}
    </select>
  );
});
MSSelect.displayName = "MSSelect";

// Microsoft-style Checkbox Component
export const MSCheckbox = React.forwardRef<
  HTMLInputElement,
  React.InputHTMLAttributes<HTMLInputElement> & {
    label?: string;
    error?: boolean;
  }
>(({ className, label, error, ...props }, ref) => {
  return (
    <div className="flex items-center space-x-2">
      <input
        ref={ref}
        type="checkbox"
        className={cn(
          "h-4 w-4 rounded border text-blue-600 transition-colors",
          "focus:ring-1 focus:ring-blue-200 focus:ring-offset-0",
          error 
            ? "border-red-300 focus:border-red-500" 
            : "border-gray-300",
          "disabled:opacity-50 disabled:cursor-not-allowed",
          className
        )}
        {...props}
      />
      {label && (
        <label className="text-sm font-normal text-gray-700 cursor-pointer">
          {label}
        </label>
      )}
    </div>
  );
});
MSCheckbox.displayName = "MSCheckbox";
