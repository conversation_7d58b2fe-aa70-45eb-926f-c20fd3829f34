
import { ChartNoAxesCombined } from "lucide-react";
import React from "react";

interface NoDataDisplayProps {
  height: string;
  textSize: string;
  message?: string;
  subMessage?: string;
  icon?: React.ReactNode;
}

export const NoDataDisplay: React.FC<NoDataDisplayProps> = ({
  height,
  textSize,
  message = "No Chart Displayed",
  subMessage,
  icon = <div className="flex justify-center mb-3">
    {/* <svg width="36" height="36" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M8 13V17M12 9V17M16 5V17M5 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89543 20.1046 3 19 3H5C3.89543 3 3 3.89543 3 5V19C3 20.1046 3.89543 21 5 21Z" 
        stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg> */}
    <ChartNoAxesCombined  size={36} />
  </div>
}) => (
  <div className={`${height} bg-white rounded flex flex-col items-center justify-center`}>
    <div className="text-center w-full">
      {icon}
      <p className={`text-gray-700 font-medium ${textSize}`}>{message}</p>
      {subMessage && (
        <p className="text-gray-500 text-sm mt-2 max-w-md mx-auto">
          {subMessage}
        </p>
      )}
    </div>
  </div>
);




