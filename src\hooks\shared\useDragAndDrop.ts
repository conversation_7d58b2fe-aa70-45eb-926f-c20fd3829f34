import { useCallback, useState } from 'react';

export interface DragData {
  name: string;
  type?: string;
  tableName?: string;
  metadata?: Record<string, any>;
}

export interface UseDragAndDropReturn {
  isDragging: boolean;
  dragOverTarget: string | null;
  handleDragStart: (data: DragData) => (e: React.DragEvent) => void;
  handleDragEnd: () => void;
  handleDragOver: (targetId?: string) => (e: React.DragEvent) => void;
  handleDragLeave: () => void;
  handleDrop: (onDropCallback: (data: DragData) => void, targetId?: string) => (e: React.DragEvent) => void;
  parseDragData: (e: React.DragEvent) => DragData | null;
}

export const useDragAndDrop = (): UseDragAndDropReturn => {
  const [isDragging, setIsDragging] = useState(false);
  const [dragOverTarget, setDragOverTarget] = useState<string | null>(null);

  const handleDragStart = useCallback((data: DragData) => (e: React.DragEvent) => {
    setIsDragging(true);
    
    // Set multiple data formats for compatibility
    e.dataTransfer.setData('application/json', JSON.stringify(data));
    e.dataTransfer.setData('text/plain', data.name);
    
    // Set individual properties for legacy compatibility
    e.dataTransfer.setData('column-name', data.name);
    if (data.type) e.dataTransfer.setData('column-type', data.type);
    if (data.tableName) e.dataTransfer.setData('table-name', data.tableName);
    
    // Set drag effect
    e.dataTransfer.effectAllowed = 'copy';
  }, []);

  const handleDragEnd = useCallback(() => {
    setIsDragging(false);
    setDragOverTarget(null);
  }, []);

  const handleDragOver = useCallback((targetId?: string) => (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
    
    if (targetId) {
      setDragOverTarget(targetId);
    }
  }, []);

  const handleDragLeave = useCallback(() => {
    setDragOverTarget(null);
  }, []);

  const parseDragData = useCallback((e: React.DragEvent): DragData | null => {
    try {
      // Try to get JSON data first (modern approach)
      const jsonData = e.dataTransfer.getData('application/json');
      if (jsonData) {
        return JSON.parse(jsonData);
      }
    } catch (error) {
      console.warn('Failed to parse JSON drag data:', error);
    }

    // Fallback to individual properties
    const name = e.dataTransfer.getData('column-name') || e.dataTransfer.getData('text/plain');
    if (!name) return null;

    return {
      name,
      type: e.dataTransfer.getData('column-type') || undefined,
      tableName: e.dataTransfer.getData('table-name') || undefined
    };
  }, []);

  const handleDrop = useCallback((onDropCallback: (data: DragData) => void, targetId?: string) => (e: React.DragEvent) => {
    e.preventDefault();
    setDragOverTarget(null);
    
    const dragData = parseDragData(e);
    if (dragData) {
      onDropCallback(dragData);
    }
  }, [parseDragData]);

  return {
    isDragging,
    dragOverTarget,
    handleDragStart,
    handleDragEnd,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    parseDragData
  };
};