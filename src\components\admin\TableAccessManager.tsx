import React, { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { useTableManagement } from '@/hooks/useTableManagement';
import SimplePagination from '@/components/admin/SimplePagination';

interface TableAccessManagerProps {
  onTablesApplied?: () => void;
}

export const TableAccessManager: React.FC<TableAccessManagerProps> = ({ 
  onTablesApplied 
}) => {
  const {
    connections,
    selectedConnection,
    isLoadingConnections,
    availableTables,
    selectedTables,
    isLoadingTables,
    tableSearchQuery,
    filteredTables,

    // Available tables pagination
    availableTablesPage,
    availableTablesTotalPages,
    paginatedAvailableTables,

    // Selected tables search and pagination
    selectedTableSearchQuery,
    filteredSelectedTables,
    selectedTablesPage,
    selectedTablesTotalPages,
    paginatedSelectedTables,

    isLoadingTableContent,
    fetchConnections,
    fetchSelectedTables,
    handleConnectionSelect,
    handleTableSelect,
    applySelectedTables,
    clearSelections,
    selectAllTables,
    removeAllTables,
    setTableSearchQuery,
    setSelectedTableSearchQuery,
    nextAvailablePage,
    prevAvailablePage,
    nextSelectedPage,
    prevSelectedPage
  } = useTableManagement();

  // Fetch connections on component mount
  useEffect(() => {
    fetchConnections();
  }, []);

  // Fetch selected tables when connection changes
  useEffect(() => {
    if (selectedConnection) {
      fetchSelectedTables(selectedConnection.connection_id);
    }
  }, [selectedConnection, fetchSelectedTables]);

  const handleApply = async () => {
    try {
      const success = await applySelectedTables();
      if (success && onTablesApplied) {
        onTablesApplied();
      }
    } catch (error) {
      console.error('Error in handleApply:', error);
    }
  };



  return (
    <div className="max-w-4xl mx-auto space-y-6 bg-white p-6 rounded-lg shadow-sm">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-semibold text-gray-800 mb-2">Table/ View Access Management</h2>
        <p className="text-gray-600">Configure access permissions for database tables and views</p>
      </div>

      {/* Connection Selection */}
      <div className="mb-6">
        <Label className="text-sm font-medium mb-3 block">Select Database Connection</Label>

        {isLoadingConnections ? (
          <div className="text-center py-4">
            <div className="text-gray-500">Loading connections...</div>
          </div>
        ) : connections.length === 0 ? (
          <div className="text-center py-4">
            <div className="text-gray-500">No database connections found</div>
            <div className="text-sm text-gray-400 mt-1">
              Please create a database connection first
            </div>
          </div>
        ) : (
          <div className="grid gap-3">
            {connections.map((connection) => (
              <div
                key={connection.connection_id}
                className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                  selectedConnection?.connection_id === connection.connection_id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'
                }`}
                onClick={() => handleConnectionSelect(connection)}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium text-gray-800">{connection.connection_name}</div>
                    <div className="text-sm text-gray-500">
                      {connection.database_dialect} • {connection.database_name}
                    </div>
                  </div>
                  {selectedConnection?.connection_id === connection.connection_id && (
                    <div className="text-blue-600 font-medium text-sm">Selected</div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Table/View Access Configuration */}
      {selectedConnection && (
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Available Tables */}
            <div className="border border-gray-300 rounded-lg">
              <div className="bg-gray-100 border-b border-gray-300 px-4 py-3">
                <div className="flex justify-between items-center">
                  <h3 className="font-medium text-gray-800">Available Tables</h3>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={selectAllTables}
                    disabled={!selectedConnection || availableTables.length === 0}
                    className="text-xs px-2 py-1 text-blue-600 border-blue-300 hover:bg-blue-50"
                  >
                    Select All
                  </Button>
                </div>
              </div>
              <div className="p-4 max-h-64 overflow-y-auto">
                {/* Search */}
                <div className="mb-3">
                  <Input
                    placeholder="Search tables..."
                    value={tableSearchQuery}
                    onChange={(e) => setTableSearchQuery(e.target.value)}
                    className="w-full text-sm"
                  />
                </div>





                {isLoadingTables ? (
                  <div className="text-center py-4">
                    <div className="text-gray-500 text-sm">Loading tables...</div>
                  </div>
                ) : paginatedAvailableTables.length > 0 ? (
                  <>
                    <div className="space-y-2">
                      {paginatedAvailableTables.map((tableName) => (
                        <div key={tableName} className="border border-gray-200 rounded p-3 hover:bg-gray-50">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium">{tableName}</span>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                if (!selectedTables.includes(tableName)) {
                                  handleTableSelect(tableName);
                                }
                              }}
                              disabled={selectedTables.includes(tableName)}
                              className={`text-xs px-2 py-1 ${
                                selectedTables.includes(tableName)
                                  ? 'text-gray-500 border-gray-300 cursor-not-allowed'
                                  : 'text-blue-600 border-blue-300 hover:bg-blue-50'
                              }`}
                            >
                              {selectedTables.includes(tableName) ? 'Added' : 'Add'}
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                    <SimplePagination
                      currentPage={availableTablesPage}
                      totalPages={availableTablesTotalPages}
                      onPageChange={(page) => {
                        if (page > availableTablesPage) {
                          nextAvailablePage();
                        } else {
                          prevAvailablePage();
                        }
                      }}
                      label={`${filteredTables.length} tables`}
                      totalItems={filteredTables.length}
                      itemsPerPage={3}
                    />
                  </>
                ) : (
                  <div className="text-center py-4">
                    <div className="text-gray-500 text-sm">
                      {tableSearchQuery ? 'No tables match your search' : 'No tables found'}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Selected Tables */}
            <div className="border border-gray-300 rounded-lg">
              <div className="bg-gray-100 border-b border-gray-300 px-4 py-3">
                <div className="flex justify-between items-center">
                  <h3 className="font-medium text-gray-800">Selected Tables ({selectedTables.length})</h3>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={removeAllTables}
                    disabled={selectedTables.length === 0}
                    className="text-xs px-2 py-1 text-red-600 border-red-300 hover:bg-red-50"
                  >
                    Remove All
                  </Button>
                </div>
              </div>
              <div className="p-4 max-h-64 overflow-y-auto">
                {selectedTables.length > 0 ? (
                  <>
                    {/* Search for Selected Tables */}
                    <div className="mb-3">
                      <Input
                        placeholder="Search selected tables..."
                        value={selectedTableSearchQuery}
                        onChange={(e) => setSelectedTableSearchQuery(e.target.value)}
                        className="w-full text-sm"
                      />
                    </div>



                    {paginatedSelectedTables.length > 0 ? (
                      <>
                        <div className="space-y-2">
                          {paginatedSelectedTables.map((tableName) => (
                            <div key={tableName} className="border border-gray-200 rounded p-3 bg-gray-50">
                              <div className="flex items-center justify-between">
                                <span className="text-sm font-medium">{tableName}</span>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleTableSelect(tableName)}
                                  className="text-xs px-2 py-1 text-red-600 border-red-300 hover:bg-red-50"
                                >
                                  Remove
                                </Button>
                              </div>
                            </div>
                          ))}
                        </div>
                        <SimplePagination
                          currentPage={selectedTablesPage}
                          totalPages={selectedTablesTotalPages}
                          onPageChange={(page) => {
                            if (page > selectedTablesPage) {
                              nextSelectedPage();
                            } else {
                              prevSelectedPage();
                            }
                          }}
                          label={`${filteredSelectedTables.length} selected`}
                          totalItems={filteredSelectedTables.length}
                          itemsPerPage={3}
                        />
                      </>
                    ) : (
                      <div className="text-center py-4">
                        <div className="text-gray-500 text-sm">No selected tables match your search</div>
                      </div>
                    )}
                  </>
                ) : (
                  <div className="text-center py-8">
                    <div className="text-gray-500 text-sm">No tables selected</div>
                    <div className="text-gray-400 text-xs mt-1">Add tables from the left panel</div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-center gap-4 pt-6">
        <Button
          onClick={clearSelections}
          variant="outline"
          disabled={selectedTables.length === 0}
          className="px-6 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50"
        >
          Clear All
        </Button>

        <Button
          onClick={handleApply}
          disabled={!selectedConnection || selectedTables.length === 0 || isLoadingTableContent}
          className="px-6 py-2 bg-blue-600 text-white hover:bg-blue-700 border border-blue-600"
        >
          {isLoadingTableContent ? 'Saving...' : 'Save'}
        </Button>
      </div>
    </div>
  );
};
