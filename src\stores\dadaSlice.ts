
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface DadaConnection {
  connection_id: number;
  connection_name: string;
  database_dialect: string;
  database_name: string;
}

interface DadaState {
  selectedDatabase: string;
  selectedConnectionId: number | null;
  connections: DadaConnection[];
  isLoading: boolean;
  error: string | null;
  // Add connection change tracking
  lastConnectionChange: number;
  // Add file session management
  fileSessionId: string | null;
  uploadedFiles: File[];
  isFileMode: boolean;
}

const initialState: DadaState = {
  selectedDatabase: '',
  selectedConnectionId: null,
  connections: [],
  isLoading: false,
  error: null,
  lastConnectionChange: 0,
  fileSessionId: null,
  uploadedFiles: [],
  isFileMode: false
};

const dadaSlice = createSlice({
  name: 'dada',
  initialState,
  reducers: {
    setSelectedDatabase: (state, action: PayloadAction<string>) => {
      console.log('DADA Redux: Setting selected database to:', action.payload);
      state.selectedDatabase = action.payload;
      state.lastConnectionChange = Date.now();
    },
    setSelectedConnectionId: (state, action: PayloadAction<number | null>) => {
      console.log('DADA Redux: Setting selected connection ID to:', action.payload);
      state.selectedConnectionId = action.payload;
      state.lastConnectionChange = Date.now();
    },
    setConnections: (state, action: PayloadAction<DadaConnection[]>) => {
      console.log('DADA Redux: Setting connections:', action.payload.length, 'connections');
      state.connections = action.payload;
    },
    setIsLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    // Add file session management actions
    setFileSessionId: (state, action: PayloadAction<string | null>) => {
      console.log('DADA Redux: Setting file session ID to:', action.payload);
      state.fileSessionId = action.payload;
    },
    setUploadedFiles: (state, action: PayloadAction<File[]>) => {
      console.log('DADA Redux: Setting uploaded files:', action.payload.length, 'files');
      state.uploadedFiles = action.payload;
    },
    setIsFileMode: (state, action: PayloadAction<boolean>) => {
      console.log('DADA Redux: Setting file mode to:', action.payload);
      state.isFileMode = action.payload;
      // Clear file session when switching modes
      if (!action.payload) {
        state.fileSessionId = null;
        state.uploadedFiles = [];
      }
    },
    clearFileSession: (state) => {
      console.log('DADA Redux: Clearing file session');
      state.fileSessionId = null;
      state.uploadedFiles = [];
      state.isFileMode = false;
    },
    // Add a reset action to clear the DADA state
    resetDadaState: (state) => {
      console.log('DADA Redux: Resetting DADA state');
      state.selectedDatabase = '';
      state.selectedConnectionId = null;
      state.connections = [];
      state.isLoading = false;
      state.error = null;
      state.lastConnectionChange = 0;
      state.fileSessionId = null;
      state.uploadedFiles = [];
      state.isFileMode = false;
    }
  }
});

export const {
  setSelectedDatabase,
  setSelectedConnectionId,
  setConnections,
  setIsLoading,
  setError,
  setFileSessionId,
  setUploadedFiles,
  setIsFileMode,
  clearFileSession,
  resetDadaState
} = dadaSlice.actions;

// Selectors for DADA state
export const selectDadaSelectedDatabase = (state: any) => state.dada?.selectedDatabase || '';
export const selectDadaSelectedConnectionId = (state: any) => state.dada?.selectedConnectionId || null;
export const selectDadaConnections = (state: any) => state.dada?.connections || [];
export const selectDadaIsLoading = (state: any) => state.dada?.isLoading || false;
export const selectDadaError = (state: any) => state.dada?.error || null;
export const selectDadaLastConnectionChange = (state: any) => state.dada?.lastConnectionChange || 0;
// File session selectors
export const selectDadaFileSessionId = (state: any) => state.dada?.fileSessionId || null;
export const selectDadaUploadedFiles = (state: any) => state.dada?.uploadedFiles || [];
export const selectDadaIsFileMode = (state: any) => state.dada?.isFileMode || false;

export default dadaSlice.reducer;
