
import React, { useState, useEffect } from 'react';
import { Search, ArrowRight, ArrowLeft } from 'lucide-react';
import { useAppDispatch, useAppSelector } from '@/hooks/useRedux';
import { 
  addToSelectedTables,
  removeFromSelectedTables
} from '@/stores/datasetSlice';
import <PERSON>Header, { NavigationButtons } from './StepHeader';
import { Button } from '@/components/ui/button';
import TablesList from './tables/TablesList';
import SelectedTablesList from './tables/SelectedTablesList';

interface TablesStepProps {
  onClose: () => void;
}

const TablesStep: React.FC<TablesStepProps> = ({ onClose }) => {
  const dispatch = useAppDispatch();
  const { selectedConnections, selectedTables } = useAppSelector(state => state.dataset);
  const [localSearchQuery, setLocalSearchQuery] = useState('');
  const [localSelectedTables, setLocalSelectedTables] = useState<string[]>([]);

  // Clear local selected tables when component mounts
  useEffect(() => {
    setLocalSelectedTables([]);
  }, []);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalSearchQuery(e.target.value);
  };

  const handleToggleTable = (tableId: string) => {
    if (localSelectedTables.includes(tableId)) {
      setLocalSelectedTables(localSelectedTables.filter(t => t !== tableId));
    } else {
      setLocalSelectedTables([...localSelectedTables, tableId]);
    }
  };

  const handleAddToSelected = () => {
    if (localSelectedTables.length === 0) {
      return;
    }
    
    dispatch(addToSelectedTables(localSelectedTables));
    
    // Clear local selections
    setLocalSelectedTables([]);
  };

  const handleRemoveFromSelected = () => {
    if (localSelectedTables.length === 0) {
      return;
    }
    
    dispatch(removeFromSelectedTables(localSelectedTables));
    
    // Clear local selections
    setLocalSelectedTables([]);
  };

  return (
    <div className="p-4">
      <StepHeader 
        title="Select Tables/Views" 
        onClose={onClose}
      />
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="border border-gray-200 p-4 rounded-lg">
          <h3 className="text-sm font-medium mb-2">Tables/Views</h3>
          <div className="mb-2 relative">
            <input
              type="text"
              placeholder="Search..."
              value={localSearchQuery}
              onChange={handleSearch}
              className="w-full pl-8 pr-4 py-1 border border-gray-300 rounded-md text-sm"
            />
            <Search className="absolute left-2 top-1.5 text-gray-400" size={16} />
          </div>
          
          <TablesList
            connections={selectedConnections}
            searchQuery={localSearchQuery}
            selectedTables={localSelectedTables}
            onToggleTable={handleToggleTable}
          />
        </div>

        <div className="flex flex-col items-center justify-center space-y-4">
          <Button 
            onClick={handleAddToSelected} 
            variant="blue" 
            className="w-10 h-10 p-0 flex items-center justify-center"
            disabled={localSelectedTables.length === 0}
          >
            <ArrowRight size={16} />
          </Button>
          <Button 
            onClick={handleRemoveFromSelected} 
            variant="blue" 
            className="w-10 h-10 p-0 flex items-center justify-center"
            disabled={localSelectedTables.length === 0}
          >
            <ArrowLeft size={16} />
          </Button>
        </div>

        <div className="border border-gray-200 p-4 rounded-lg">
          <h3 className="font-medium text-sm mb-2">Selected Tables</h3>
          <SelectedTablesList
            selectedTables={selectedTables}
            localSelectedTables={localSelectedTables}
            onToggleTable={handleToggleTable}
          />
        </div>
      </div>
      
      <div className="mt-6">
        <NavigationButtons 
          disableNext={selectedTables.length === 0}
        />
      </div>
    </div>
  );
};

export default TablesStep;
