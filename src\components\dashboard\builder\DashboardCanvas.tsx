import React from 'react';
import { useDashboardBuilder } from '@/contexts/DashboardBuilderContext';
import MetricCards from './MetricCards';
import ChartTableGrid from './ChartTableGrid';

const DashboardCanvas: React.FC = () => {
  const {
    state,
    cardArray,
    removeCard,
    updateCard,
  } = useDashboardBuilder();

  return (
    <div className="space-y-6">
      {/* Top 3 Metric Cards */}
      <MetricCards />

      {/* Chart and Table Cards */}
      <ChartTableGrid
        chartZoneChart={state.chartZoneChart}
        tableZoneChart={state.tableZoneChart}
        chartData={state.zoneChartData}
        loadingChartData={state.loadingZoneData}
        onRemoveChart={(zoneType) => {
          if (zoneType === 'chart') {
            // Legacy zone removal logic would go here
          } else {
            // Legacy zone removal logic would go here
          }
        }}
        tabularData={state.tabularData}
        loadingTabularData={state.loadingTabularData}
        chartCards={cardArray.filter(card => card.type === 'chart').map(card => card.id)}
        tableCards={cardArray.filter(card => card.type === 'table').map(card => card.id)}
        onRemoveCard={removeCard}
        cards={cardArray}
        onUpdateCard={updateCard}
      />

      {/* Show selected charts below if any (for backwards compatibility) */}
      {state.selectedCharts.length > 0 && (
        <div className="mt-8">
          <h3 className="text-lg font-medium text-foreground mb-4">Added Charts</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {state.selectedCharts.map((chart) => (
              <div key={`${chart.chart_id}-${chart.displayComponent || 'both'}`} 
                   className="bg-card rounded-lg shadow border p-4">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-medium text-sm text-foreground truncate">
                    {chart.chart_name}
                  </h3>
                  <button
                    onClick={() => {
                      // Remove from selected charts logic would go here
                    }}
                    className="text-muted-foreground hover:text-destructive transition-colors"
                  >
                    ×
                  </button>
                </div>

                {/* Chart preview */}
                <div className="h-48 mb-3 border rounded bg-card">
                  <div className="h-full flex items-center justify-center text-muted-foreground text-sm">
                    {chart.chart_type || 'Chart'} Preview
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default DashboardCanvas;