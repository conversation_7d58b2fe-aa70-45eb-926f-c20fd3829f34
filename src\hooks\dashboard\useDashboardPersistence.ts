import { useCallback } from 'react';
import { SavedChart } from '@/types/chartTypes';
import { CardState } from './useDashboardState';
import { toast } from 'sonner';

interface DashboardConfig {
  id?: string;
  name: string;
  cards: CardState[];
  selectedCharts: SavedChart[];
  layout: {
    cardOrder: string[];
    sidebarCollapsed: boolean;
  };
  metadata: {
    createdAt: number;
    updatedAt: number;
    version: string;
  };
}

export const useDashboardPersistence = () => {
  // Save dashboard configuration
  const saveDashboard = useCallback(async (
    name: string,
    cards: CardState[],
    selectedCharts: SavedChart[],
    cardOrder: string[],
    sidebarCollapsed: boolean
  ): Promise<string | null> => {
    try {
      const dashboardConfig: DashboardConfig = {
        name,
        cards,
        selectedCharts,
        layout: {
          cardOrder,
          sidebarCollapsed,
        },
        metadata: {
          createdAt: Date.now(),
          updatedAt: Date.now(),
          version: '1.0.0',
        },
      };

      // TODO: Replace with actual API call
      const savedDashboardId = `dashboard-${Date.now()}`;
      
      // Simulate API save
      localStorage.setItem(`dashboard-${savedDashboardId}`, JSON.stringify(dashboardConfig));
      
      toast.success(`Dashboard "${name}" saved successfully`);
      return savedDashboardId;
    } catch (error) {
      console.error('Error saving dashboard:', error);
      toast.error('Failed to save dashboard');
      return null;
    }
  }, []);

  // Load dashboard configuration
  const loadDashboard = useCallback(async (dashboardId: string): Promise<DashboardConfig | null> => {
    try {
      // TODO: Replace with actual API call
      const savedData = localStorage.getItem(`dashboard-${dashboardId}`);
      
      if (!savedData) {
        toast.error('Dashboard not found');
        return null;
      }

      const dashboardConfig: DashboardConfig = JSON.parse(savedData);
      toast.success(`Dashboard "${dashboardConfig.name}" loaded successfully`);
      return dashboardConfig;
    } catch (error) {
      console.error('Error loading dashboard:', error);
      toast.error('Failed to load dashboard');
      return null;
    }
  }, []);

  // Get list of saved dashboards
  const getSavedDashboards = useCallback(async (): Promise<Array<{id: string; name: string; updatedAt: number}>> => {
    try {
      // TODO: Replace with actual API call
      const dashboards: Array<{id: string; name: string; updatedAt: number}> = [];
      
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key?.startsWith('dashboard-')) {
          const data = localStorage.getItem(key);
          if (data) {
            const config = JSON.parse(data);
            dashboards.push({
              id: key.replace('dashboard-', ''),
              name: config.name,
              updatedAt: config.metadata.updatedAt,
            });
          }
        }
      }
      
      return dashboards.sort((a, b) => b.updatedAt - a.updatedAt);
    } catch (error) {
      console.error('Error getting saved dashboards:', error);
      return [];
    }
  }, []);

  // Delete dashboard
  const deleteDashboard = useCallback(async (dashboardId: string): Promise<boolean> => {
    try {
      // TODO: Replace with actual API call
      localStorage.removeItem(`dashboard-${dashboardId}`);
      toast.success('Dashboard deleted successfully');
      return true;
    } catch (error) {
      console.error('Error deleting dashboard:', error);
      toast.error('Failed to delete dashboard');
      return false;
    }
  }, []);

  // Export dashboard as JSON
  const exportDashboard = useCallback((dashboardConfig: DashboardConfig) => {
    try {
      const dataStr = JSON.stringify(dashboardConfig, null, 2);
      const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
      
      const exportFileDefaultName = `dashboard-${dashboardConfig.name}-${Date.now()}.json`;
      
      const linkElement = document.createElement('a');
      linkElement.setAttribute('href', dataUri);
      linkElement.setAttribute('download', exportFileDefaultName);
      linkElement.click();
      
      toast.success('Dashboard exported successfully');
    } catch (error) {
      console.error('Error exporting dashboard:', error);
      toast.error('Failed to export dashboard');
    }
  }, []);

  // Import dashboard from JSON
  const importDashboard = useCallback(async (file: File): Promise<DashboardConfig | null> => {
    try {
      const text = await file.text();
      const dashboardConfig: DashboardConfig = JSON.parse(text);
      
      // Validate the imported data structure
      if (!dashboardConfig.name || !dashboardConfig.cards || !dashboardConfig.metadata) {
        throw new Error('Invalid dashboard file format');
      }
      
      toast.success('Dashboard imported successfully');
      return dashboardConfig;
    } catch (error) {
      console.error('Error importing dashboard:', error);
      toast.error('Failed to import dashboard: Invalid file format');
      return null;
    }
  }, []);

  return {
    saveDashboard,
    loadDashboard,
    getSavedDashboards,
    deleteDashboard,
    exportDashboard,
    importDashboard,
  };
};