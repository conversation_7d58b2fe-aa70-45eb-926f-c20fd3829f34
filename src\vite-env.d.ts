/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_API_URL: string
  readonly VITE_API_KEY: string
  readonly VITE_DADA_API_URL: string
  readonly VITE_SSO_API_URL: string
  readonly VITE_AZURE_CLIENT_ID: string
  readonly VITE_AZURE_TENANT_ID: string
  readonly VITE_ALLOWED_REDIRECT_URL: string
  readonly VITE_DASHBOARD1_API_URL: string
  readonly VITE_CHART_API_URL: string
}


interface ImportMeta {
  readonly env: ImportMetaEnv
}