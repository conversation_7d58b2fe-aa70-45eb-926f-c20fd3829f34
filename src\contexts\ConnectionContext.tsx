import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { centralApiClient } from '@/services/api/centralApiClient';

// Define the context type
type ConnectionContextType = {
  status: 'connected' | 'disconnected' | 'connecting';
  lastChecked: Date | null;
  checkConnection: () => Promise<boolean>;
  isChecking: boolean;
};

// Create the context with default values
const ConnectionContext = createContext<ConnectionContextType>({
  status: 'connecting',
  lastChecked: null,
  checkConnection: async () => false,
  isChecking: false,
});

// Custom hook to use the connection context
export const useConnection = () => useContext(ConnectionContext);

// Singleton for tracking if a check is in progress
let globalCheckInProgress = false;

// Provider component
export const ConnectionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [status, setStatus] = useState<'connected' | 'disconnected' | 'connecting'>('connecting');
  const [lastChecked, setLastChecked] = useState<Date | null>(null);
  const [isChecking, setIsChecking] = useState(false);
  
  // Function to check connection status
  const checkConnection = useCallback(async (): Promise<boolean> => {
    // Prevent multiple simultaneous checks
    if (globalCheckInProgress) {
      console.log('Connection check already in progress, skipping');
      return status === 'connected';
    }
    
    globalCheckInProgress = true;
    setIsChecking(true);
    
    try {
      // First check if browser is online
      if (!navigator.onLine) {
        setStatus('disconnected');
        // Dispatch global event
        window.dispatchEvent(new CustomEvent('backend-disconnected'));
        return false;
      }
      
      // Then check backend health
      const isHealthy = await centralApiClient.healthCheck('dada');
      
      // Update status
      const newStatus = isHealthy ? 'connected' : 'disconnected';
      setStatus(newStatus);
      setLastChecked(new Date());
      
      // Dispatch global event
      window.dispatchEvent(new CustomEvent(
        isHealthy ? 'backend-connected' : 'backend-disconnected'
      ));
      
      return isHealthy;
    } catch (error) {
      console.error('Error checking connection:', error);
      setStatus('disconnected');
      
      // Dispatch global event
      window.dispatchEvent(new CustomEvent('backend-disconnected'));
      
      return false;
    } finally {
      setIsChecking(false);
      globalCheckInProgress = false;
    }
  }, [status]);
  
  // Check connection on mount and when online status changes
  useEffect(() => {
    // Initial check
    checkConnection();
    
    // Set up polling with exponential backoff
    let pollInterval = 60000; // Start with 60 seconds
    const maxInterval = 300000; // Max 5 minutes
    
    const scheduleNextCheck = () => {
      return setTimeout(async () => {
        const isConnected = await checkConnection();
        
        // Adjust interval based on result
        if (isConnected) {
          pollInterval = 60000; // Reset to 1 minute on success
        } else {
          pollInterval = Math.min(pollInterval * 1.5, maxInterval); // Increase on failure
        }
        
        // Schedule next check
        timeoutId = scheduleNextCheck();
      }, pollInterval);
    };
    
    // Start polling
    let timeoutId = scheduleNextCheck();
    
    // Listen for online/offline events
    const handleOnline = () => {
      console.log('Browser online event received');
      checkConnection();
    };
    
    const handleOffline = () => {
      console.log('Browser offline event received');
      setStatus('disconnected');
      window.dispatchEvent(new CustomEvent('backend-disconnected'));
    };
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [checkConnection]);
  
  // Provide the connection context to children
  return (
    <ConnectionContext.Provider value={{ status, lastChecked, checkConnection, isChecking }}>
      {children}
    </ConnectionContext.Provider>
  );
};
