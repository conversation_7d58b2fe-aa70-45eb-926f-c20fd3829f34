import React from 'react';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import StandardSQLInput from '@/components/shared/StandardSQLInput';
import { SQLValidationResult } from '@/hooks/shared/useSQLInput';

interface DataGridSQLModeProps {
  sqlQuery: string;
  onSQLChange: (value: string) => void;
  onValidateSQL: (sql: string) => Promise<SQLValidationResult>;
  validationResult: SQLValidationResult | null;
  isValidating: boolean;
  onExitSQLMode: () => void;
}

const DataGridSQLMode: React.FC<DataGridSQLModeProps> = ({
  sqlQuery,
  onSQLChange,
  onValidateSQL,
  validationResult,
  isValidating,
  onExitSQLMode
}) => {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">Write SQL Query</h3>
        <Button
          size="sm"
          onClick={onExitSQLMode}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          <X className="w-4 h-4 mr-2" />
          Back to Builder
        </Button>
      </div>

      <StandardSQLInput
        value={sqlQuery}
        onChange={onSQLChange}
        onValidate={onValidateSQL}
        placeholder="Enter your SQL query to fetch data..."
        required={true}
      />
    </div>
  );
};

export default DataGridSQLMode;