
import React from 'react';

// Sample data for tables
const connectionTables = {
  'Athena_Prod': ['Table1', 'Table2', 'View1', 'View2'],
  'Quickcap_Prod': ['SalesTable', 'CustomerTable'],
  'Databricks_Prod': ['Table3', 'Table4', 'View3']
};

interface TablesListProps {
  connections: string[];
  searchQuery: string;
  selectedTables: string[];
  onToggleTable: (tableId: string) => void;
}

const TablesList: React.FC<TablesListProps> = ({
  connections,
  searchQuery,
  selectedTables,
  onToggleTable
}) => {
  return (
    <div className="border border-gray-300 rounded-md h-64 overflow-y-auto p-2">
      {connections.length === 0 ? (
        <div className="text-gray-500 text-sm italic p-2">No connections selected</div>
      ) : (
        connections.map((conn) => {
          const tables = (connectionTables as any)[conn] || [];
          
          if (tables.length === 0) {
            return (
              <div key={conn} className="mb-2">
                <div className="font-medium">{conn}</div>
                <div className="text-gray-500 text-sm italic pl-4">No tables available</div>
              </div>
            );
          }
          
          return (
            <div key={conn} className="mb-2">
              <div className="font-medium">{conn}</div>
              <ul className="pl-4 text-sm">
                {tables.filter((table: string) => 
                  table.toLowerCase().includes(searchQuery.toLowerCase())
                ).map((table: string) => {
                  const tableId = `${conn}_${table}`;
                  return (
                    <li 
                      key={tableId}
                      className={`py-1 cursor-pointer hover:bg-blue-50 pl-1 rounded ${
                        selectedTables.includes(tableId) ? 'bg-blue-100' : ''
                      }`}
                      onClick={() => onToggleTable(tableId)}
                    >
                      {table}
                    </li>
                  );
                })}
              </ul>
            </div>
          );
        })
      )}
    </div>
  );
};

export default TablesList;
