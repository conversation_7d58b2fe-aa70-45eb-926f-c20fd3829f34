
import { useState, useEffect } from 'react';

type DashboardType = 1 | 2 | 3;
type TextSizeType = 'small' | 'medium' | 'large';

interface DashboardStateHook {
  sidebarCollapsed: boolean;
  rightSidebarVisible: boolean;
  searchQuery: string;
  headerSearchQuery: string;
  textSize: TextSizeType;
  showMetricScreen: boolean;
  showDatasetScreen: boolean;
  activeDashboard: DashboardType;
  toggleSidebar: () => void;
  toggleRightSidebar: () => void;
  handleSearch: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleHeaderSearch: (query: string) => void;
  handleTextSizeChange: (size: TextSizeType) => void;
  setShowMetricScreen: (show: boolean) => void;
  setShowDatasetScreen: (show: boolean) => void;
  getDashboardName: () => string;
}

export function useDashboardState(initialDashboard: DashboardType = 1): DashboardStateHook {
  const [activeDashboard] = useState<DashboardType>(initialDashboard);
  // Set initial sidebar state based on dashboard type
  const [sidebarCollapsed, setSidebarCollapsed] = useState(initialDashboard === 2 ? true : false);
  const [rightSidebarVisible, setRightSidebarVisible] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [headerSearchQuery, setHeaderSearchQuery] = useState('');
  const [textSize, setTextSize] = useState<TextSizeType>('medium');
  const [showMetricScreen, setShowMetricScreen] = useState(false);
  const [showDatasetScreen, setShowDatasetScreen] = useState(false);

  // Handle special UI cases
  useEffect(() => {
    // Auto-collapse sidebars when showing dataset screen for more space
    if (showDatasetScreen) {
      setSidebarCollapsed(true);
      setRightSidebarVisible(false);
    }
  }, [showDatasetScreen]);

  // Apply text size classes to body
  useEffect(() => {
    document.body.classList.remove('text-sm', 'text-base', 'text-lg');
    document.body.classList.add(textSize === 'small' ? 'text-sm' : textSize === 'medium' ? 'text-base' : 'text-lg');
    
    if (headerSearchQuery) {
      filterProjects(headerSearchQuery);
    }
  }, [textSize, headerSearchQuery]);

  // Handler functions
  const toggleSidebar = () => {
    if (!showDatasetScreen) {
      setSidebarCollapsed(!sidebarCollapsed);
    }
  };

  const toggleRightSidebar = () => {
    if (!showDatasetScreen) {
      setRightSidebarVisible(!rightSidebarVisible);
    }
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handleHeaderSearch = (query: string) => {
    setHeaderSearchQuery(query);
  };

  const handleTextSizeChange = (size: TextSizeType) => {
    setTextSize(size);
    console.log(`Text size changed to: ${size}`);
  };

  const getDashboardName = () => {
    return `Dashboard-${activeDashboard}`;
  };

  // Function to filter projects based on header search
  const filterProjects = (query: string) => {
    console.log(`Filtering projects with query: ${query}`);
    // Implementation would go here in a real app
  };

  return {
    sidebarCollapsed,
    rightSidebarVisible,
    searchQuery,
    headerSearchQuery,
    textSize,
    showMetricScreen,
    showDatasetScreen,
    activeDashboard,
    toggleSidebar,
    toggleRightSidebar,
    handleSearch,
    handleHeaderSearch,
    handleTextSizeChange,
    setShowMetricScreen,
    setShowDatasetScreen,
    getDashboardName
  };
}
