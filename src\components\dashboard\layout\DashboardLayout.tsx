
import React from 'react';
import Header from '@/components/dashboard/Header';
import LeftSidebar from '@/components/dashboard/LeftSidebar';
import RightSidebar from '@/components/dashboard/RightSidebar';
import Content from '@/components/dashboard/Content';
import DashboardSettings from './DashboardSettings';
import DashboardContainer from './DashboardContainer';
import DashboardMain from './DashboardMain';
import DashboardContent from './DashboardContent';
import { useDashboardState } from '@/hooks/useDashboardState';
import MinimizedSidebar from '@/components/dashboard/MinimizedSidebar';

interface DashboardLayoutProps {
  initialDashboard?: 1 | 2 | 3;
  children?: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ initialDashboard = 1, children }) => {
  const {
    sidebarCollapsed,
    rightSidebarVisible,
    searchQuery,
    headerSearchQuery,
    textSize,
    showMetricScreen,
    showDatasetScreen,
    activeDashboard,
    toggleSidebar,
    toggleRightSidebar,
    handleSearch,
    handleHeaderSearch,
    handleTextSizeChange,
    setShowMetricScreen,
    setShowDatasetScreen,
    getDashboardName
  } = useDashboardState(initialDashboard);

  const handleNewChat = () => {
    console.log("Creating new chat");
  };

  const handleDatasetIconClick = () => {
    setShowDatasetScreen(!showDatasetScreen);
    // Close metric screen if open
    if (showMetricScreen) {
      setShowMetricScreen(false);
    }
  };

  return (
    <>
      <DashboardSettings textSize={textSize} onTextSizeChange={handleTextSizeChange} />
      
      <DashboardContainer textSize={textSize}>
        <Header 
          toggleSidebar={toggleSidebar} 
          sidebarCollapsed={sidebarCollapsed}
          rightSidebarVisible={rightSidebarVisible}
          dashboardName={getDashboardName()}
          toggleRightSidebar={activeDashboard !== 2 ? toggleRightSidebar : undefined}
          onHeaderSearch={handleHeaderSearch}
          dashboardType={activeDashboard}
        />
        
        <DashboardMain>
          <div className="flex h-full w-full">
            {/* Dashboard 2 specific rendering */}
            {activeDashboard === 2 ? (
              <>
                {sidebarCollapsed ? (
                  <MinimizedSidebar 
                    isCollapsed={sidebarCollapsed}
                    onToggle={toggleSidebar}
                  />
                ) : (
                  <LeftSidebar 
                    collapsed={false} 
                    searchQuery={searchQuery}
                    handleSearch={handleSearch}
                    searchResults={[]}
                    onNewChat={handleNewChat}
                    onTextSizeChange={handleTextSizeChange}
                    dashboardType={activeDashboard}
                    onDatasetIconClick={handleDatasetIconClick}
                    isDatasetScreenOpen={showDatasetScreen}
                    onToggle={toggleSidebar}
                  />
                )}
              </>
            ) : (
              /* Dashboard 1 and 3 rendering - only show sidebar if not collapsed */
              !sidebarCollapsed && (
                <LeftSidebar 
                  collapsed={false} 
                  searchQuery=""
                  handleSearch={() => {}}
                  onTextSizeChange={handleTextSizeChange}
                  dashboardType={activeDashboard}
                  onDatasetIconClick={handleDatasetIconClick}
                  isDatasetScreenOpen={showDatasetScreen}
                  onToggle={toggleSidebar}
                />
              )
            )}
            
            <DashboardContent
              sidebarCollapsed={sidebarCollapsed}
              rightSidebarVisible={rightSidebarVisible}
              dashboardType={activeDashboard}
            >
              <Content 
                dashboardType={activeDashboard} 
                textSize={textSize}
                searchQuery={searchQuery}
                headerSearchQuery={headerSearchQuery}
                showMetricScreen={showMetricScreen} 
                showDatasetScreen={showDatasetScreen}
                setShowMetricScreen={setShowMetricScreen}
                setShowDatasetScreen={setShowDatasetScreen} 
              />
            </DashboardContent>
            
            {/* Only show right sidebar if visible and not dataset screen and not dashboard 2 */}
            {rightSidebarVisible && !showDatasetScreen && activeDashboard !== 2 && (
              <RightSidebar 
                dashboardType={activeDashboard} 
                toggleVisibility={toggleRightSidebar}
              />
            )}
          </div>
        </DashboardMain>
      </DashboardContainer>
    </>
  );
};

export default DashboardLayout;
