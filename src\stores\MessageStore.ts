
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Message } from '@/components/dashboard/models';

// Replace mobx implementation with Redux implementation
export class MessageStore {
  messages: Message[] = [];

  constructor() {
    // Initialize with empty messages
  }

  addMessage(message: Message) {
    this.messages.push(message);
  }

  getMessages() {
    return this.messages;
  }

  clearMessages() {
    this.messages = [];
  }
}

export default MessageStore;
