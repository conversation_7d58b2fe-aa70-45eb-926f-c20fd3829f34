
import React, { forwardRef, useEffect } from 'react';

interface ChatTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  dashboardType?: 1 | 2 | 3;
}

export const ChatTextarea = forwardRef<HTMLTextAreaElement, ChatTextareaProps>(
  ({ dashboardType = 3, className, value, ...props }, ref) => {
    // Determine the padding based on dashboard type
    const paddingClass = dashboardType === 3 || dashboardType === 1
      ? 'pl-12'
      : 'pl-4';

    // Function to adjust textarea height
    const adjustHeight = (element: HTMLTextAreaElement) => {
      element.style.height = 'auto'; // Reset height to recalculate
      
      // Calculate new height: min 44px (about 2 lines), max 120px (about 5 lines)
      const newHeight = Math.min(Math.max(element.scrollHeight, 44), 120);
      element.style.height = `${newHeight}px`;
    };

    // Effect to handle height adjustment when value changes
    useEffect(() => {
      const textarea = (ref as React.RefObject<HTMLTextAreaElement>)?.current;
      if (textarea) {
        adjustHeight(textarea);
      }
    }, [value, ref]);

    // Handle input events for pasting and typing
    const handleInput = (e: React.FormEvent<HTMLTextAreaElement>) => {
      adjustHeight(e.currentTarget);
      props.onInput?.(e);
    };

    return (
      <textarea
        ref={ref}
        className={`bg-white-500 block w-full ${paddingClass} pr-12 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent min-h-[44px] max-h-[120px] resize-none ${className || ''}`}
        onInput={handleInput}
        value={value}
        rows={1}
        style={{ 
          scrollbarWidth: 'thin',
          scrollbarColor: '#CBD5E1 transparent',
          overflowY: value && String(value).split('\n').length > 5 ? 'auto' : 'hidden'
        }}
        {...props}
      />
    );
  }
);

ChatTextarea.displayName = 'ChatTextarea';
