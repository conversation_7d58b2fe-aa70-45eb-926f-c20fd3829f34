
import { useState, useCallback } from 'react';
import { ChartCacheKey } from './types';

export const useChartCache = () => {
  const [lastExecutionParams, setLastExecutionParams] = useState<string>('');

  const createExecutionHash = useCallback((params: ChartCacheKey) => {
    return JSON.stringify({
      xAxisColumn: params.xAxisColumn,
      yAxisColumns: params.yAxisColumns.sort(),
      xAxisTable: params.xAxisTable,
      yAxisTables: params.yAxisTables.sort(),
      aggregationType: params.aggregationType
    });
  }, []);

  const shouldSkipExecution = useCallback((
    params: ChartCacheKey,
    isEditMode: boolean,
    hasExistingData: boolean
  ) => {
    const currentExecutionHash = createExecutionHash(params);
    
    return isEditMode && 
           hasExistingData && 
           currentExecutionHash === lastExecutionParams;
  }, [createExecutionHash, lastExecutionParams]);

  const updateExecutionParams = useCallback((params: ChartCacheKey) => {
    const hash = createExecutionHash(params);
    setLastExecutionParams(hash);
  }, [createExecutionHash]);

  const clearCache = useCallback(() => {
    setLastExecutionParams('');
  }, []);

  return {
    shouldSkipExecution,
    updateExecutionParams,
    clearCache
  };
};
