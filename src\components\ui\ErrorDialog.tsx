
import React, { useState } from 'react';
import { <PERSON>, Refresh<PERSON>w, Alert<PERSON>riangle, Wifi, WifiOff } from 'lucide-react';
import { useAppDispatch, useAppSelector } from '@/hooks/useRedux';
import { clearError } from '@/stores/errorSlice';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { centralApiClient } from '@/services/api/centralApiClient';

const ErrorDialog: React.FC = () => {
  const dispatch = useAppDispatch();
  const { isVisible, message, statusCode } = useAppSelector((state) => state.error);
  const [isRetrying, setIsRetrying] = useState(false);

  const getErrorIcon = () => {
    if (statusCode === 503) {
      return <WifiOff size={48} className="text-orange-500 bg-orange-100 p-2 rounded-full" />;
    } else if (statusCode && statusCode >= 500) {
      return <Cable size={48} className="text-red-500 bg-red-100 p-2 rounded-full" />;
    } else if (statusCode === 404) {
      return <AlertTriangle size={48} className="text-yellow-500 bg-yellow-100 p-2 rounded-full" />;
    } else if (statusCode === 401 || statusCode === 403) {
      return <AlertTriangle size={48} className="text-purple-500 bg-purple-100 p-2 rounded-full" />;
    }
    return <Cable size={48} className="text-red-500 bg-red-100 p-2 rounded-full" />;
  };

  const getErrorTitle = () => {
    if (statusCode === 503) {
      return 'Service Temporarily Unavailable';
    } else if (statusCode && statusCode >= 500) {
      return 'Server Connection Failed';
    } else if (statusCode === 404) {
      return 'Resource Not Found';
    } else if (statusCode === 401 || statusCode === 403) {
      return 'Authentication Error';
    }
    return 'Connection Error';
  };

  const getErrorDescription = () => {
    if (statusCode === 503) {
      return 'The service is temporarily unavailable due to repeated connection failures. Please wait a moment before trying again.';
    } else if (statusCode && statusCode >= 500) {
      return 'Unable to connect to the server. Please check your internet connection and try again.';
    } else if (statusCode === 404) {
      return 'The requested resource could not be found. Please check the URL or contact support.';
    } else if (statusCode === 401 || statusCode === 403) {
      return 'You are not authorized to access this resource. Please check your credentials.';
    }
    return 'An unexpected error occurred. Please try again or contact support if the problem persists.';
  };

  const handleRetry = async () => {
    setIsRetrying(true);
    
    try {
      // Wait a moment before retrying
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Close the error dialog
      dispatch(clearError());
      
      // Refresh the page to retry failed requests
      window.location.reload();
    } catch (error) {
      console.error('Retry failed:', error);
    } finally {
      setIsRetrying(false);
    }
  };

  const handleClose = () => {
    dispatch(clearError());
  };

  const showRetryButton = statusCode && (statusCode >= 500 || statusCode === 503);

  return (
    <Dialog open={isVisible} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-gray-800">{getErrorTitle()}</DialogTitle>
        </DialogHeader>
        
        <div className="flex flex-col items-center p-6">
          <div className="mb-4">
            {getErrorIcon()}
          </div>
          
          <p className="text-center text-gray-600 mb-2">{getErrorDescription()}</p>
          
          <div className="text-center">
            <p className="text-center text-gray-800 mb-2 font-medium">{message}</p>
            {statusCode && (
              <p className="text-sm text-gray-500">Error Code: {statusCode}</p>
            )}
          </div>
        </div>

        <DialogFooter className="flex justify-center space-x-2">
          <Button variant="outline" onClick={handleClose}>
            Close
          </Button>
          {showRetryButton && (
            <Button 
              onClick={handleRetry} 
              disabled={isRetrying}
              className="min-w-[100px]"
            >
              {isRetrying ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Retrying...
                </>
              ) : (
                <>
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Retry
                </>
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ErrorDialog;
