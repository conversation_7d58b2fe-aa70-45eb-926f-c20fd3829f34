import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface SimplePaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  label?: string;
  totalItems: number;
  itemsPerPage: number;
}

const SimplePagination: React.FC<SimplePaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  label,
  totalItems,
  itemsPerPage
}) => {
  // Internal state to ensure component updates
  const [internalPage, setInternalPage] = useState(currentPage);
  
  // Update internal state when props change
  useEffect(() => {
    setInternalPage(currentPage);
    console.log('📄 SimplePagination received new currentPage:', currentPage);
  }, [currentPage]);
  
  // Don't show pagination if there are no items or only 1 page
  if (totalItems === 0 || totalPages <= 1) {
    return null;
  }
  
  const handlePrevious = () => {
    if (internalPage > 1) {
      const newPage = internalPage - 1;
      console.log('📄 SimplePagination going to previous page:', newPage);
      setInternalPage(newPage);
      onPageChange(newPage);
    }
  };
  
  const handleNext = () => {
    if (internalPage < totalPages) {
      const newPage = internalPage + 1;
      console.log('📄 SimplePagination going to next page:', newPage);
      setInternalPage(newPage);
      onPageChange(newPage);
    }
  };
  
  // Calculate start and end item numbers
  const startItem = ((internalPage - 1) * itemsPerPage) + 1;
  const endItem = Math.min(internalPage * itemsPerPage, totalItems);
  
  return (
    <div className="flex justify-center items-center mt-3 pt-3 border-t border-gray-200">
      <div className="flex items-center space-x-3">
        <button
          onClick={handlePrevious}
          disabled={internalPage === 1}
          className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
          aria-label="Previous page"
        >
          <ChevronLeft className="h-4 w-4" />
        </button>
        
        <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
          {internalPage}
        </div>
        
        <button
          onClick={handleNext}
          disabled={internalPage === totalPages}
          className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
          aria-label="Next page"
        >
          <ChevronRight className="h-4 w-4" />
        </button>
        
        <span className="text-sm text-gray-500 ml-2">
          {label || `${startItem}-${endItem} of ${totalItems}`}
        </span>
      </div>
    </div>
  );
};

export default SimplePagination;
