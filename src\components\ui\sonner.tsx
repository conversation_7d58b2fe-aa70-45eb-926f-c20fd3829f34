
import { useTheme } from "next-themes"
import { Toaster as Son<PERSON> } from "sonner"
import { X } from "lucide-react"

type ToasterProps = React.ComponentProps<typeof Sonner>

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = "system" } = useTheme()

  return (
    <Sonner
      theme={theme as ToasterProps["theme"]}
      className="toaster group"
      closeButton={true}
      toastOptions={{
        classNames: {
          toast:
            "group toast group-[.toaster]:bg-white group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",
          description: "group-[.toast]:text-muted-foreground",
          actionButton:
            "group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",
          cancelButton:
            "group-[.toast]:bg-muted group-[.toast]:text-muted-foreground",
          success: "!bg-white [&>div]:!text-green-600 [&>div>p]:!text-green-600 [&>div>svg]:!text-green-600 [&_.description]:!text-green-600/90 !border-green-100",
          error: "!bg-white [&>div]:!text-red-600 [&>div>p]:!text-red-600 [&>div>svg]:!text-red-600 [&_.description]:!text-red-600/90 !border-red-100",
          info: "!bg-white [&>div]:!text-yellow-600 [&>div>p]:!text-yellow-600 [&>div>svg]:!text-yellow-600 [&_.description]:!text-yellow-600/90 !border-yellow-100",
          closeButton: "absolute right-2 top-2 p-1 rounded-full !bg-white hover:!bg-gray-100 !text-gray-500",
        },
        duration: 4000,
      }}
      {...props}
    />
  )
}

export { Toaster }
