
import React from 'react';
import { ColumnDefinition } from '@/stores/datasetSlice';

interface SelectedColumnsListProps {
  selectedColumns: ColumnDefinition[];
  localSelectedColumns: any[];
  handleToggleColumn: (column: any, tableName: string) => void;
}

export const SelectedColumnsList: React.FC<SelectedColumnsListProps> = ({
  selectedColumns,
  localSelectedColumns,
  handleToggleColumn
}) => {
  return (
    <div className="border border-gray-200 p-4 rounded-lg">
      <h3 className="font-medium text-sm mb-2">Selected Columns</h3>
      <div className="border border-gray-300 rounded-md h-72 overflow-y-auto p-2">
        {selectedColumns.length === 0 ? (
          <div className="text-gray-500 text-sm italic p-2">No columns selected</div>
        ) : (
          <ul className='text-sm'>
            {selectedColumns.map((col) => {
              const isSelected = localSelectedColumns.some(c => 
                c.name === col.name && c.tableOrigin === col.tableOrigin
              );
              return (
                <li 
                  key={`${col.tableOrigin}_${col.name}`}
                  className={`py-1 cursor-pointer hover:bg-blue-50 pl-1 rounded ${
                    isSelected ? 'bg-blue-100' : ''
                  }`}
                  onClick={() => handleToggleColumn(col, col.tableOrigin || '')}
                >
                  {col.name} ({col.dataType}) from {col.tableOrigin || 'Unknown'}
                </li>
              );
            })}
          </ul>
        )}
      </div>
    </div>
  );
};
