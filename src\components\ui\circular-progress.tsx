import React from 'react';

interface CircularProgressProps {
  percentage: number;
  size?: number;
  strokeWidth?: number;
  className?: string;
  showPercentage?: boolean;
  color?: string;
  backgroundColor?: string;
}

const CircularProgress: React.FC<CircularProgressProps> = ({
  percentage,
  size = 120,
  strokeWidth = 8,
  className = '',
  showPercentage = true,
  color,
  backgroundColor = '#e5e7eb'
}) => {
  // Ensure percentage is between 0 and 100
  const normalizedPercentage = Math.min(Math.max(percentage, 0), 100);
  
  // Calculate the radius and circumference
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  
  // Calculate the stroke dash offset for the progress
  const strokeDashoffset = circumference - (normalizedPercentage / 100) * circumference;
  
  // Determine color based on percentage if not provided
  const progressColor = color || (normalizedPercentage >= 100 ? '#10b981' : '#f59e0b');
  
  return (
    <div className={`relative inline-flex items-center justify-center ${className}`}>
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
      >
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={backgroundColor}
          strokeWidth={strokeWidth}
          fill="transparent"
        />
        
        {/* Progress circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={progressColor}
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeDasharray={circumference}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className="transition-all duration-700 ease-out"
          style={{
            filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1))'
          }}
        />
      </svg>
      
      {/* Percentage text in the center */}
      {showPercentage && (
        <div className="absolute inset-0 flex items-center justify-center">
          <span
            className="text-2xl font-bold transition-colors duration-300"
            style={{
              color: progressColor,
              textShadow: '0 1px 2px rgba(0, 0, 0, 0.1)'
            }}
          >
            {Math.round(normalizedPercentage)}%
          </span>
        </div>
      )}
    </div>
  );
};

export default CircularProgress;
