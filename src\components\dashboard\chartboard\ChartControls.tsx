import React, { useState, useEffect } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { X, Save,Eraser, CirclePlay } from "lucide-react";
import { useDispatch } from 'react-redux';
import { setSelectedColumns } from '@/stores/chartSlice';
import { Switch } from "@/components/ui/switch";
import FilterSection, { FilterCondition } from './FilterSection';
import { DataGridBuilder } from '../datagrid';

// View Type Selector Component
interface ViewTypeSelectorProps {
  selectedView: 'chart' | 'kpi' | 'datagrid';
  onViewChange: (view: 'chart' | 'kpi' | 'datagrid') => void;
}

const ViewTypeSelector: React.FC<ViewTypeSelectorProps> = ({ selectedView, onViewChange }) => {
  const viewOptions = [
    { value: 'chart', label: 'Chart' },
    { value: 'kpi', label: 'KPI Card' },
    { value: 'datagrid', label: 'DataGrid' }
  ];

  return (
    <div className="flex items-center space-x-8 mb-4 p-3 bg-gray-50 rounded-lg border">
      <span className="text-sm font-medium text-gray-700">View Type:</span>

      <RadioGroup
        value={selectedView}
        onValueChange={onViewChange}
        className="flex items-center space-x-10"
      >
        {viewOptions.map((option) => (
          <div key={option.value} className="flex items-center space-x-2">
            <RadioGroupItem
              value={option.value}
              id={`view-${option.value}`}
              className="text-blue-600 border-blue-300 focus:ring-blue-500"
            />
            <Label
              htmlFor={`view-${option.value}`}
              className={`text-sm font-medium cursor-pointer ${
                selectedView === option.value ? 'text-blue-600' : 'text-gray-700'
              }`}
            >
              {option.label}
            </Label>
          </div>
        ))}
      </RadioGroup>
    </div>
  );
};

interface ChartControlsProps {
  chartStyle: 'bar' | 'line' | 'pie' | 'doughnut';
  setChartStyle: (style: 'bar' | 'line' | 'pie' | 'doughnut') => void;
  xAxisColumn: string;
  setXAxisColumn: (column: string) => void;
  yAxisColumns: string[];
  setYAxisColumns: (columns: string[]) => void;
  aggregationType: string | null;
  setAggregationType: (type: string | null) => void;
  isLoadingTableData: boolean;
  handleExecute: (filterConditions?: FilterCondition[]) => void; // Update to accept filter conditions
  handleClearFilters: () => void;
  showChart: boolean;
  handleXAxisDrop: (e: React.DragEvent) => void;
  handleYAxisDrop: (e: React.DragEvent) => void;
  handleDragOver: (e: React.DragEvent) => void;
  handleSaveButtonClick?: () => void;
  xAxisTable: string;
  setXAxisTable: (table: string) => void;
  yAxisTables: string[];
  setYAxisTables: (tables: string[]) => void;
  availableColumns?: Array<{ id: string; name: string; tableName: string }>;
  initialFilterConditions?: FilterCondition[];
  // New props for view type selection
  selectedViewType?: 'chart' | 'kpi' | 'datagrid';
  onViewTypeChange?: (view: 'chart' | 'kpi' | 'datagrid') => void;
}

const ChartControls: React.FC<ChartControlsProps> = ({
  chartStyle,
  setChartStyle,
  xAxisColumn,
  setXAxisColumn,
  yAxisColumns,
  setYAxisColumns,
  aggregationType,
  setAggregationType,
  isLoadingTableData,
  handleExecute,
  handleClearFilters,
  showChart,
  handleXAxisDrop,
  handleYAxisDrop,
  handleDragOver,
  handleSaveButtonClick,
  xAxisTable,
  setXAxisTable,
  yAxisTables,
  setYAxisTables,
  availableColumns = [],
  initialFilterConditions = [],
  selectedViewType = 'chart',
  onViewTypeChange
}) => {
  const dispatch = useDispatch();
  
  // Initialize filter toggle based on whether we have initial filter conditions
  const [showFilters, setShowFilters] = useState(initialFilterConditions.length > 0);
  
  // Initialize filter conditions with provided initial values or default empty one
  const [filterConditions, setFilterConditions] = useState<FilterCondition[]>(
    initialFilterConditions.length > 0 
      ? initialFilterConditions 
      : [{ column: '', operator: '=', value: '' }]
  );
  
  // Add useEffect to update filter conditions when initialFilterConditions changes
  useEffect(() => {
    if (initialFilterConditions.length > 0) {
      setFilterConditions(initialFilterConditions);
      setShowFilters(true);
    }
  }, [initialFilterConditions]);

  // Existing handlers
  const handleRemoveXAxisColumn = () => {
    setXAxisColumn('');
    dispatch(setSelectedColumns({ 
      tableName: xAxisTable, 
      columns: [] 
    }));
  };

  const handleRemoveYAxisColumn = (index: number) => {
    const newYAxisColumns = [...yAxisColumns];
    newYAxisColumns.splice(index, 1);
    setYAxisColumns(newYAxisColumns);
    
    if (yAxisTables.length > index) {
      const newYAxisTables = [...yAxisTables];
      const removedTable = newYAxisTables.splice(index, 1)[0];
      setYAxisTables(newYAxisTables);
      
      dispatch(setSelectedColumns({ 
        tableName: removedTable, 
        columns: [] 
      }));
    }
  };

  // Determine if Execute button should be disabled
  const isExecuteDisabled = !xAxisColumn || yAxisColumns.length === 0 || isLoadingTableData;
  
  // Determine if Clear button should be shown
  const showClearButton = xAxisColumn || yAxisColumns.length > 0;

  return (
    <div className="bg-white rounded-lg shadow p-4 mb-6">
      {/* View Type Selector */}
      {onViewTypeChange && (
        <ViewTypeSelector
          selectedView={selectedViewType}
          onViewChange={onViewTypeChange}
        />
      )}

      {/* DataGrid Builder - Only show for datagrid view */}
      {selectedViewType === 'datagrid' && (
        <DataGridBuilder />
      )}

      {/* Controls in a 4-column grid - Only show for chart view */}
      {selectedViewType === 'chart' && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
          {/* Chart Type */}
          <div className="flex flex-col">
            <label className="text-sm font-medium text-gray-700 mb-1">
              Chart Type:
            </label>
            <Select
              value={chartStyle}
              onValueChange={(value) => setChartStyle(value as 'bar' | 'line' | 'pie' | 'doughnut')}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select chart type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="bar">Bar</SelectItem>
                <SelectItem value="line">Line</SelectItem>
                <SelectItem value="pie">Pie</SelectItem>
                <SelectItem value="doughnut">Doughnut</SelectItem>
              </SelectContent>
            </Select>
          </div>

        {/* X-Axis Column */}
        <div className="flex flex-col">
          <label className="text-sm font-medium text-gray-700 mb-1">
            X-Axis Column:
          </label>
          <div
            className="h-10 w-full border rounded px-3 py-2 flex items-center"
            onDrop={handleXAxisDrop}
            onDragOver={handleDragOver}
          >
            {xAxisColumn ? (
              <div className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm flex items-center">
                {xAxisColumn}
                <button
                  className="ml-1 text-blue-600 hover:text-blue-800"
                  onClick={handleRemoveXAxisColumn}
                >
                  <X size={14} />
                </button>
              </div>
            ) : (
              <span className="text-gray-400 text-sm">Select column</span>
            )}
          </div>
        </div>

        {/* Y-Axis Column */}
        <div className="flex flex-col">
          <label className="text-sm font-medium text-gray-700 mb-1">
            Y-Axis Column:
          </label>
          <div
            className="h-10 w-full border rounded px-3 py-2 flex items-center"
            onDrop={handleYAxisDrop}
            onDragOver={handleDragOver}
          >
            {yAxisColumns.length > 0 ? (
              <div className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm flex items-center">
                {yAxisColumns[0]}
                <button
                  className="ml-1 text-green-600 hover:text-green-800"
                  onClick={() => handleRemoveYAxisColumn(0)}
                >
                  <X size={14} />
                </button>
              </div>
            ) : (
              <span className="text-gray-400 text-sm">Select column</span>
            )}
          </div>
        </div>

        {/* Aggregation Type */}
        <div className="flex flex-col">
          <label className="text-sm font-medium text-gray-700 mb-1">
            Aggregation:
          </label>
          <Select
            value={aggregationType || ""}
            onValueChange={(value) => setAggregationType(value || null)}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">None</SelectItem>
              <SelectItem value="SUM">SUM</SelectItem>
              <SelectItem value="AVG">AVG</SelectItem>
              <SelectItem value="COUNT">COUNT</SelectItem>
              <SelectItem value="MIN">MIN</SelectItem>
              <SelectItem value="MAX">MAX</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      )}

      {/* Filters Section - Only show when toggle is on and chart view */}
      {selectedViewType === 'chart' && showFilters && (
        <FilterSection
          filterConditions={filterConditions}
          setFilterConditions={setFilterConditions}
          handleDragOver={handleDragOver}
        />
      )}

      {/* Action Buttons in a separate row - Only show for chart view */}
      {selectedViewType === 'chart' && (
        <div className="flex justify-between mt-4">
          <div className="flex gap-2 items-center">
            <Button
              onClick={() => {
                console.log("Execute button clicked with filters:", showFilters ? filterConditions : undefined);
                handleExecute(showFilters ? filterConditions : undefined);
              }}
              disabled={isExecuteDisabled}
              variant="blue"
            >
              < CirclePlay size={16} className="mr-1.5" />
              {isLoadingTableData ? 'Loading...' : 'Execute'}
            </Button>

            {/* Only show Clear button and Filter toggle if there are selections to clear */}
            {showClearButton && (
              <>
                <Button
                  variant="outline"
                  onClick={handleClearFilters}
                  disabled={isLoadingTableData}
                >
                  <Eraser size={16} className="mr-1.5" />
                  Clear
                </Button>

                {/* Filter Toggle */}
                <div className="flex items-center space-x-2 ml-4">
                  <span className="text-sm font-medium text-gray-700">Filters</span>
                  <Switch
                    checked={showFilters}
                    onCheckedChange={setShowFilters}
                    aria-label="Toggle filters"
                  />
                </div>
              </>
            )}
          </div>

          {/* Save Chart button - only show if chart is displayed and handler is provided */}
          {showChart && handleSaveButtonClick && (
            <Button
              onClick={handleSaveButtonClick}
              variant='greenmind'
            >
              <Save size={16} className="mr-1.5" />
              Save Chart
            </Button>
          )}
        </div>
      )}
    </div>
  );
};

export default ChartControls;
