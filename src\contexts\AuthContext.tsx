import React, { createContext, useState, useContext, useEffect, ReactNode } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { toast } from 'sonner';
import { useDispatch } from 'react-redux';
import { msalInstance, validateAzureToken } from '@/services/auth/msalService';
import { loginRequest } from '@/services/auth/msalConfig';
import { useConnectionManager } from '@/hooks/useConnectionManager';
import { disconnectDatabase } from '@/services/api/chart/databaseService';
import { resetChartState } from '@/stores/chartSlice';
import { resetDadaState } from '@/stores/dadaSlice';

interface User {
  username: string;
  email: string;
  role?: 'admin' | 'chatuser' | 'transcriptuser' | 'general';
}

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  googleLogin: (email: string) => Promise<void>;
  azureLogin: () => Promise<void>; // Add Azure login method
  signup: (username: string, email: string, password: string) => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
  hasAccess: (feature: 'chatbot' | 'dada' | 'tracker' | 'chartbuilder') => boolean;
  checkSharedAccess: () => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Default users for testing
const DEFAULT_USERS = [
  { username: 'testuser', email: '<EMAIL>', password: 'password123', role: 'general' },
  { username: 'admin', email: '<EMAIL>', password: 'admin123', role: 'admin' },
  { username: 'chatuser', email: '<EMAIL>', password: 'user@123', role: 'chatuser' },
  { username: 'transcriptuser', email: '<EMAIL>', password: 'User@123', role: 'transcriptuser' }
];

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();

  // Check if user is already logged in
  useEffect(() => {
    const checkUserSession = () => {
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        const userData = JSON.parse(storedUser);
        const currentTime = new Date().getTime();
        
        if (userData.expiryTime && userData.expiryTime > currentTime) {
          // User session is still valid
          const { expiryTime, ...userInfo } = userData; // Remove expiryTime from user data
          setUser(userInfo);
        } else {
          // Session expired, remove from localStorage
          localStorage.removeItem('user');
          setUser(null);
          if (location.pathname !== '/signin' && location.pathname !== '/signup' && !location.pathname.includes('/sharedview')) {
            navigate('/signin');
            toast.error('Your session has expired. Please sign in again.');
          }
        }
      }
    };
    
    checkUserSession();
  }, [navigate, location.pathname]);

  // Add Azure login function
  const azureLogin = async () => {
    try {
      // Use silent SSO first if possible
      let response;
      try {
        // Try to get token silently first (if user is already logged in to Azure)
        const accounts = msalInstance.getAllAccounts();
        if (accounts.length > 0) {
          response = await msalInstance.acquireTokenSilent({
            ...loginRequest,
            account: accounts[0]
          });
        } else {
          // If no accounts, use popup
          response = await msalInstance.loginPopup(loginRequest);
        }
      } catch (silentError) {
        console.log("Silent token acquisition failed, using popup", silentError);
        response = await msalInstance.loginPopup(loginRequest);
      }
      
      if (response.account) {
        // Create user info from MSAL account data
        const userInfo: User = { 
          username: response.account.name || 'Azure User', 
          email: response.account.username,
          role: 'admin'
        };
        
        setUser(userInfo);
        
        // Store user info with expiration time
        const userInfoWithExpiry = {
          ...userInfo,
          expiryTime: new Date().getTime() + (1 * 60 * 60 * 1000)
        };
        
        localStorage.setItem('user', JSON.stringify(userInfoWithExpiry));
        toast.success('Azure login successful');
        
        // Navigate to the return URL
        const from = location.state?.from || '/';
        navigate(from, { replace: true });
      }
    } catch (error) {
      console.error('Azure login error:', error);
      toast.error('Azure login failed: ' + (error instanceof Error ? error.message : 'Please try again'));
      throw error;
    }
  };

  // Function to check if user has access to a specific feature
  const hasAccess = (feature: 'chatbot' | 'dada' | 'tracker' | 'chartbuilder'): boolean => {
    if (!user) return false;
    
    // Add logic for chartbuilder access
    if (feature === 'chartbuilder') {
      return user.role === 'admin' || user.role === 'transcriptuser';
    }
    
    // Add logic for tracker access
    if (feature === 'tracker') {
      return user.role === 'admin' || user.role === 'transcriptuser';
    }
    
    // Existing logic for other features
    if (feature === 'chatbot') {
      return user.role === 'admin' || user.role === 'chatuser';
    }
    
    if (feature === 'dada') {
      return user.role === 'admin';
    }
    
    return false;
  };

  const checkSharedAccess = () => {
    const isSharedTranscriptUrl = location.pathname.includes('/sharedview');
    const storedUser = localStorage.getItem('user');
    
    if (storedUser) {
      const userData = JSON.parse(storedUser);
      const currentTime = new Date().getTime();
      return isSharedTranscriptUrl && userData.expiryTime > currentTime;
    }
    
    return false;
  };

  const login = async (email: string, password: string) => {
    try {
      // Simple validation
      if (!email || !password) {
        throw new Error('Please enter both email and password');
      }

      // Check if user exists in our default users or if it's stored in localStorage
      const storedUsers = localStorage.getItem('users');
      let users = DEFAULT_USERS;
      
      if (storedUsers) {
        users = [...users, ...JSON.parse(storedUsers)];
      }
      
      const matchedUser = users.find(u => u.email === email && u.password === password);
      
      if (matchedUser) {
        const userInfo = { 
          username: matchedUser.username, 
          email: matchedUser.email,
          role: matchedUser.role as 'admin' | 'chatuser' | 'transcriptuser' | 'general'
        };
        setUser(userInfo);
        // Store user info with expiration time (7 hours from now)
        const userInfoWithExpiry = {
          ...userInfo,
          expiryTime: new Date().getTime() + (6 * 60 * 60 * 1000) // Current time + 7 hours in milliseconds
        };
        localStorage.setItem('user', JSON.stringify(userInfoWithExpiry));
        toast.success('Login successful');
        navigate('/');
      } else {
        throw new Error('Invalid credentials. Use <EMAIL> with password123');
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Login failed');
      throw error;
    }
  };

  const googleLogin = async (email: string) => {
    try {
      const userInfo: User = { 
        username: 'Google User', 
        email: email,
        role: 'general'
      };
      setUser(userInfo);
      const userInfoWithExpiry = {
        ...userInfo,
        expiryTime: new Date().getTime() + (6 * 60 * 60 * 1000) // Current time + 7 hours in milliseconds
      };
      localStorage.setItem('user', JSON.stringify(userInfoWithExpiry));
      toast.success('Google login successful');
      navigate('/');
    } catch (error) {
      toast.error('Google login failed');
      throw error;
    }
  };

  const signup = async (username: string, email: string, password: string) => {
    try {
      // Simple validation
      if (!email || !password || !username) {
        throw new Error('Please fill all required fields');
      }

      // Check if user already exists
      const storedUsers = localStorage.getItem('users');
      const users = storedUsers ? JSON.parse(storedUsers) : [];
      
      const userExists = [...DEFAULT_USERS, ...users].some(u => u.email === email);
      
      if (userExists) {
        throw new Error('User with this email already exists');
      }

      // Add new user with general role
      const newUser = { username, email, password, role: 'general' };
      users.push(newUser);
      localStorage.setItem('users', JSON.stringify(users));
      
      // Log in the new user
      const userInfo = { username, email, role: 'general' as const };
      setUser(userInfo);
      localStorage.setItem('user', JSON.stringify(userInfo));
      
      toast.success('Account created successfully');
      navigate('/');
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Signup failed');
      throw error;
    }
  };

  const logout = async () => {
    console.log('Starting logout process...');
    
    // Step 1: Clear session storage FIRST
    try {
      sessionStorage.removeItem('chartbuilder_session_connection');
      sessionStorage.removeItem('chartbuilder_session_database');
      sessionStorage.removeItem('dada_session_connection');
      sessionStorage.removeItem('dada_session_database');
      console.log('Session storage cleared');
    } catch (error) {
      console.error('Failed to clear session storage:', error);
    }
    
    // Step 2: Reset Redux states
    try {
      dispatch(resetChartState());
      dispatch(resetDadaState());
      console.log('Redux states reset');
    } catch (error) {
      console.error('Failed to reset Redux states:', error);
    }
    
    // Step 3: Disconnect from database connections
    try {
      const chartConnectionId = sessionStorage.getItem('chartbuilder_session_connection');
      if (chartConnectionId) {
        console.log('Disconnecting chart connection on logout:', chartConnectionId);
        await disconnectDatabase(chartConnectionId);
      }
      
      const dadaConnectionId = sessionStorage.getItem('dada_session_connection');
      if (dadaConnectionId) {
        console.log('Disconnecting dada connection on logout:', dadaConnectionId);
        await disconnectDatabase(dadaConnectionId);
      }
    } catch (error) {
      console.error('Failed to disconnect databases on logout:', error);
      // Continue with logout even if disconnect fails
    }
    
    // Step 4: Clear user state and localStorage
    setUser(null);
    localStorage.removeItem('user');
    
    toast.success('Logged out successfully');
    navigate('/');
    msalInstance.logoutPopup().catch(e => console.error("Logout error", e));
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        login,
        googleLogin,
        azureLogin, // Add the new Azure login function
        signup,
        logout,
        isAuthenticated: !!user,
        hasAccess,
        checkSharedAccess
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
