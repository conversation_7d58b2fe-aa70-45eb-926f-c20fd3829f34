
import { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from './useRedux';
import { 
  setEditingChartData, 
  clearEditingChartData, 
  selectEditingChartData,
  setConnectionId,
  setSelectedDatabase
} from '@/stores/chartSlice';
import { createNavigationPayload, validateNavigationPayload } from '@/components/dashboard/chartboard/navigationUtils';
import { toast } from 'sonner';

export const useChartNavigation = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const editingChartData = useAppSelector(selectEditingChartData);

  /**
   * Navigate to chart builder with a saved chart - optimized to eliminate redundant API calls
   */
  const navigateToChartBuilder = useCallback(async (savedChart: any, existingConnectionId?: string) => {
    console.log("Navigating to chart builder with chart:", savedChart);
    
    try {
      // Create a deep copy of the chart to avoid modifying the original
      const chartCopy = JSON.parse(JSON.stringify(savedChart));
      
      // OPTIMIZATION: Use existing connection ID - no new connection API calls in edit mode
      let connectionId = existingConnectionId || savedChart.connection_id;
      
      if (connectionId) {
        console.log("Using existing connection for edit mode - no API calls:", connectionId);
        // Set the connection in Redux without making API calls
        dispatch(setConnectionId(connectionId));
        dispatch(setSelectedDatabase(chartCopy.db_type || 'postgres'));
      } else {
        console.warn('No connection ID available, chart builder will handle connection creation');
      }
      
      // Prepare chart data for navigation - handle different chart types
      if (chartCopy.chart_type === 'pie') {
        console.log('Preparing pie chart data for navigation');
        
        if (!chartCopy.group_by && chartCopy.x_axis) {
          chartCopy.group_by = chartCopy.x_axis;
        }
      }
      
      // Handle aggregation data structure
      if (chartCopy.y_axis && typeof chartCopy.y_axis === 'object') {
        console.log('Chart has aggregation data in y_axis:', chartCopy.y_axis);
        
        chartCopy.originalYAxis = chartCopy.y_axis;
        
        if (Array.isArray(chartCopy.y_axis) && chartCopy.y_axis.length > 0 && 
            typeof chartCopy.y_axis[0] === 'object' && chartCopy.y_axis[0].aggregation) {
          
          chartCopy.aggregationType = chartCopy.y_axis[0].aggregation;
          chartCopy.simplifiedYAxis = chartCopy.y_axis.map(item => item.column);
        }
      }
      
      // Create the navigation payload
      const payload = createNavigationPayload(chartCopy, connectionId);
      
      // OPTIMIZATION: Prepare comprehensive payload to avoid redundant API calls
      const extendedPayload = {
        ...payload.chartData,
        connectionId: connectionId,
        originalChart: chartCopy,
        // Include existing chart data to skip data fetching API call
        chartData: chartCopy.chart_data || chartCopy.chart_response?.data || [],
        // Add aggregation information
        aggregationType: chartCopy.aggregationType || (typeof chartCopy.y_axis === 'object' && chartCopy.y_axis.aggregation) || null,
        // Add simplified y-axis if available
        simplifiedYAxis: chartCopy.simplifiedYAxis || null,
        // Add filters if available
        filters: chartCopy.filters || {},
        // CRITICAL: Mark as edit mode to skip unnecessary API calls
        isEditMode: true,
        // CRITICAL: Pass existing data flag to avoid re-fetching
        hasExistingData: Boolean(chartCopy.chart_data || chartCopy.chart_response?.data),
        // Pass database type to avoid database selection API calls
        db_type: chartCopy.db_type
      };
      
      console.log("Storing optimized chart data in Redux for navigation (edit mode):", extendedPayload);
      
      // Store the chart data in Redux
      dispatch(setEditingChartData(extendedPayload));
      
      // Navigate to chart builder with the edit route if we have a chart_id
      if (savedChart.chart_id) {
        navigate(`/chartbuilder/edit/${savedChart.chart_id}`);
      } else {
        navigate('/chartbuilder');
      }
      
      return true;
    } catch (error) {
      console.error('Error navigating to chart builder:', error);
      toast.error('Failed to open chart in builder');
      return false;
    }
  }, [navigate, dispatch]);

  // Clear editing chart data
  const clearChartData = useCallback(() => {
    dispatch(clearEditingChartData());
  }, [dispatch]);

  // Get current editing chart data
  const getCurrentChartData = useCallback(() => {
    return editingChartData;
  }, [editingChartData]);

  return {
    navigateToChartBuilder,
    clearChartData,
    getCurrentChartData,
    editingChartData
  };
};
