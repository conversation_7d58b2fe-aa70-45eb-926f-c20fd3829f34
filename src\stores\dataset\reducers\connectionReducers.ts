
import { PayloadAction } from '@reduxjs/toolkit';
import { DatasetState } from '../types';

export const connectionReducers = {
  toggleConnectionSelection: (state: DatasetState, action: PayloadAction<string>) => {
    const connection = action.payload;
    if (state.selectedConnections.includes(connection)) {
      state.selectedConnections = state.selectedConnections.filter(c => c !== connection);
    } else {
      state.selectedConnections.push(connection);
    }
  },
  
  addToSelectedConnections: (state: DatasetState, action: PayloadAction<string[]>) => {
    action.payload.forEach(connection => {
      if (!state.selectedConnections.includes(connection)) {
        state.selectedConnections.push(connection);
      }
    });
  },
  
  removeFromSelectedConnections: (state: DatasetState, action: PayloadAction<string[]>) => {
    state.selectedConnections = state.selectedConnections.filter(
      c => !action.payload.includes(c)
    );
  },
};
