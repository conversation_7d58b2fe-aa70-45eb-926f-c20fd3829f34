
import React from 'react';
import { useLocation } from 'react-router-dom';

interface DashboardContentProps {
  children: React.ReactNode;
  sidebarCollapsed: boolean;
  rightSidebarVisible: boolean;
  dashboardType?: 1 | 2 | 3;
}

const DashboardContent: React.FC<DashboardContentProps> = ({ 
  children,
  sidebarCollapsed,
  rightSidebarVisible,
  dashboardType
}) => {
  const location = useLocation();
  const isTrackerView = location.pathname.includes('/transcript/tracker/');

  // For dashboards 1 and 3, expand content when sidebars are collapsed
  const shouldExpandContent = (dashboardType === 1 || dashboardType === 3);
  
  // Calculate dynamic width based on sidebar states
  const getContentWidth = () => {
    if (!shouldExpandContent) return 'flex-1';
    
    // When both sidebars are collapsed, take full width
    if (sidebarCollapsed && !rightSidebarVisible) {
      return 'w-full';
    }
    // When only left sidebar is collapsed
    if (sidebarCollapsed && rightSidebarVisible) {
      return 'flex-1';
    }
    // When only right sidebar is collapsed
    if (!sidebarCollapsed && !rightSidebarVisible) {
      return 'flex-1';
    }
    // Default when both are visible
    return 'flex-1';
  };
  
  return (
    <div className={`transition-all duration-300 ${getContentWidth()} ${
      isTrackerView ? 'overflow-auto' : ''
    }`}>
      {React.cloneElement(children as React.ReactElement, {
        sidebarCollapsed,
        rightSidebarVisible,
        shouldExpandContent: shouldExpandContent && sidebarCollapsed && !rightSidebarVisible
      })}
    </div>
  );
};

export default DashboardContent;
