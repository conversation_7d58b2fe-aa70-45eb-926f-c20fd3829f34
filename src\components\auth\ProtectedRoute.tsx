import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';

export const ProtectedRoute = ({ 
  children, 
  requiredFeature,
  isSharedRoute = false
}: { 
  children: React.ReactNode, 
  requiredFeature?: 'chatbot' | 'dada' | 'tracker' | 'chartbuilder',
  isSharedRoute?: boolean
}) => {
  const { isAuthenticated, hasAccess } = useAuth();
  const location = useLocation();
  
  // Check localStorage for valid session
  const hasValidSession = () => {
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      const userData = JSON.parse(storedUser);
      const currentTime = new Date().getTime();
      return userData.expiryTime && userData.expiryTime > currentTime;
    }
    return false;
  };
  
  const isUserAuthenticated = isAuthenticated || hasValidSession();
  
  // For shared routes, check authentication
  if (isSharedRoute && isUserAuthenticated) {
    return <>{children}</>;
  }
  
  // For non-authenticated users, redirect to signin
  if (!isUserAuthenticated) {
    return <Navigate to="/signin" state={{ from: location }} replace />;
  }
  
  // For regular routes, check feature access
  if (requiredFeature && !hasAccess(requiredFeature)) {
    return <Navigate to="/" replace />;
  }
  
  return <>{children}</>;
};

