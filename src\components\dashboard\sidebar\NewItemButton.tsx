
import React from 'react';
import { Plus } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface NewItemButtonProps {
  label: string;
  onClick: () => void;
}

const NewItemButton: React.FC<NewItemButtonProps> = ({ label, onClick }) => {
  return (
    <div className="bg-blue-100 rounded-md p-1 flex justify-between items-center">
      <span className="text-blue-600 font-medium pl-1">{label}</span>
      <Button
        variant="ghost"
        size="sm"
        onClick={onClick}
        className="text-blue-600 hover:bg-blue-200 p-1 h-8 w-8"
      >
        <Plus size={18} />
      </Button>
    </div>
  );
};

export default NewItemButton;
