import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Lightbulb, Settings, Plus, Trash2, HelpCircle } from 'lucide-react';
import <PERSON>Field from '@/components/admin/FormField';
import <PERSON>Field from '@/components/admin/SelectField';
import { Autocomplete } from '@/components/ui/autocomplete';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { toast } from 'sonner';

interface ColumnMapping {
  leftColumn: string;
  rightColumn: string;
  joinOperator: string;
}

interface TableRelationship {
  id: string;
  name: string;
  dbConnection: string;
  leftTable: string;
  rightTable: string;
  joinType: string;
  leftColumn: string;
  rightColumn: string;
  joinOperator: string;
  rightJoinOperator: string;
  columnMappings?: ColumnMapping[];
}

interface DatabaseConnection {
  connection_id: string;
  connection_name: string;
  database_name: string;
}

interface TableInfo {
  table_name: string;
  columns: ColumnInfo[];
}

interface ColumnInfo {
  column_name: string;
  data_type: string;
}

interface RelationshipFormProps {
  relationship: TableRelationship;
  dbConnections: DatabaseConnection[];
  tables: TableInfo[];
  isNewRelationship: boolean;
  onSave: (relationship: TableRelationship) => void;
  onCancel: () => void;
  onAIRecommendation: () => void;
  onConnectionChange?: (connectionId: string) => void;
}

const RelationshipForm: React.FC<RelationshipFormProps> = ({
  relationship,
  dbConnections,
  tables,
  isNewRelationship,
  onSave,
  onCancel,
  onAIRecommendation,
  onConnectionChange
}) => {
  const [formData, setFormData] = useState<TableRelationship>(relationship);
  const [availableColumns, setAvailableColumns] = useState<{left: string[], right: string[]}>({
    left: [],
    right: []
  });
  const [isLoadingTables, setIsLoadingTables] = useState(false);
  const [isLoadingLeftColumns, setIsLoadingLeftColumns] = useState(false);
  const [isLoadingRightColumns, setIsLoadingRightColumns] = useState(false);
  const [columnMappings, setColumnMappings] = useState<ColumnMapping[]>(
    relationship.columnMappings || [{ leftColumn: '', rightColumn: '', joinOperator: '=' }]
  );

  useEffect(() => {
    setFormData(relationship);
    setColumnMappings(relationship.columnMappings || [{ leftColumn: '', rightColumn: '', joinOperator: '=' }]);
  }, [relationship]);

  // Debug: Log when tables prop changes
  useEffect(() => {
    console.log('🔍 Tables prop changed in RelationshipForm:', tables);
    console.log('🔍 Number of tables:', tables.length);
    if (tables.length > 0) {
      console.log('🔍 First table:', tables[0]);
    }
  }, [tables]);

  // Update available columns when tables change
  useEffect(() => {
    console.log('🔍 Tables available for columns:', tables);
    console.log('🔍 Selected left table:', formData.leftTable);
    console.log('🔍 Selected right table:', formData.rightTable);

    const leftTable = tables.find(t => t.table_name === formData.leftTable);
    const rightTable = tables.find(t => t.table_name === formData.rightTable);

    console.log('🔍 Found left table:', leftTable);
    console.log('🔍 Found right table:', rightTable);

    setAvailableColumns({
      left: leftTable?.columns.map(col => col.column_name) || [],
      right: rightTable?.columns.map(col => col.column_name) || []
    });
  }, [formData.leftTable, formData.rightTable, tables]);

  const handleInputChange = (field: keyof TableRelationship, value: string) => {
    // If DB connection changes, trigger table loading and clear everything
    if (field === 'dbConnection' && onConnectionChange) {
      console.log('🔄 DB Connection changed to:', value);
      console.log('🔍 Available connections:', dbConnections);

      // Find the connection ID from the connection name
      const selectedConnection = dbConnections.find(conn => conn.connection_name === value);
      console.log('🔍 Found selected connection:', selectedConnection);

      if (selectedConnection) {
        console.log('🚀 Calling onConnectionChange with ID:', selectedConnection.connection_id);
        setIsLoadingTables(true);
        onConnectionChange(selectedConnection.connection_id);

        // Clear table and column selections when connection changes
        setFormData(prev => ({
          ...prev,
          dbConnection: value,
          leftTable: '',
          rightTable: '',
          leftColumn: '',
          rightColumn: ''
        }));

        // Also clear available columns immediately
        setAvailableColumns({
          left: [],
          right: []
        });

        return; // Exit early to avoid duplicate updates
      } else {
        console.error('❌ Could not find connection with name:', value);
      }
    }

    // Clear column selections when table changes and fetch new columns
    if (field === 'leftTable') {
      setFormData(prev => ({
        ...prev,
        [field]: value,
        leftColumn: '' // Clear left column when left table changes
      }));

      // Fetch columns for the selected left table
      if (value && formData.dbConnection) {
        fetchTableColumns(value, true);
      } else {
        // Clear left columns if no table selected
        setAvailableColumns(prev => ({ ...prev, left: [] }));
      }
      return;
    }

    if (field === 'rightTable') {
      setFormData(prev => ({
        ...prev,
        [field]: value,
        rightColumn: '' // Clear right column when right table changes
      }));

      // Fetch columns for the selected right table
      if (value && formData.dbConnection) {
        fetchTableColumns(value, false);
      } else {
        // Clear right columns if no table selected
        setAvailableColumns(prev => ({ ...prev, right: [] }));
      }
      return;
    }

    // For all other fields, just update the value
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Update loading state when tables change
  useEffect(() => {
    setIsLoadingTables(false);
  }, [tables]);

  // Clear columns when tables array changes (due to connection change)
  useEffect(() => {
    if (tables.length === 0) {
      // If no tables available, clear all column selections
      setFormData(prev => ({
        ...prev,
        leftColumn: '',
        rightColumn: ''
      }));
      setAvailableColumns({
        left: [],
        right: []
      });
    }
  }, [tables]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.name.trim()) {
      toast.error('Please enter a relationship name');
      return;
    }

    if (!formData.dbConnection) {
      toast.error('Please select a database connection');
      return;
    }

    if (!formData.leftTable) {
      toast.error('Please select a left table');
      return;
    }

    if (!formData.rightTable) {
      toast.error('Please select a right table');
      return;
    }

    if (!formData.joinType) {
      toast.error('Please select a join type');
      return;
    }

    // Validate column mappings
    const validColumnMappings = columnMappings.filter(mapping =>
      mapping.leftColumn && mapping.rightColumn && mapping.joinOperator
    );

    if (validColumnMappings.length === 0) {
      toast.error('Please configure at least one column mapping');
      return;
    }

    try {
      if (isNewRelationship) {
        await createTableJoinRule();
      } else {
        // For updates, still use the existing onSave method
        const updatedFormData = {
          ...formData,
          columnMappings: columnMappings
        };
        onSave(updatedFormData);
      }
    } catch (error) {
      console.error('Error saving relationship:', error);
    }
  };

  // Add new column mapping
  const addColumnMapping = () => {
    setColumnMappings(prev => [
      ...prev,
      { leftColumn: '', rightColumn: '', joinOperator: '=' }
    ]);
  };

  // Remove column mapping
  const removeColumnMapping = (index: number) => {
    if (columnMappings.length > 1) {
      setColumnMappings(prev => prev.filter((_, i) => i !== index));
    }
  };

  // Update column mapping
  const updateColumnMapping = (index: number, field: keyof ColumnMapping, value: string) => {
    setColumnMappings(prev => prev.map((mapping, i) =>
      i === index ? { ...mapping, [field]: value } : mapping
    ));
  };

  // Fetch columns for a specific table
  const fetchTableColumns = async (tableName: string, isLeftTable: boolean = true) => {
    try {
      // Find the selected connection to get connection_id
      const selectedConnection = dbConnections.find(conn => conn.connection_name === formData.dbConnection);
      if (!selectedConnection) {
        console.error('No database connection selected');
        return [];
      }

      const setLoading = isLeftTable ? setIsLoadingLeftColumns : setIsLoadingRightColumns;
      setLoading(true);

      const url = `http://***********:8001/account-management/get_columns_of_table?connection_id=${selectedConnection.connection_id}&table_name=${tableName}`;

      console.log(`🔍 Fetching columns for ${isLeftTable ? 'left' : 'right'} table "${tableName}"`);
      console.log(`🔗 Request URL: ${url}`);
      console.log(`📋 Connection ID: ${selectedConnection.connection_id}`);
      console.log(`📋 Table Name: ${tableName}`);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log(`📥 Get columns response status for ${tableName}:`, response.status);

      if (!response.ok) {
        const errorData = await response.text();
        console.error(`❌ Get columns error for ${tableName}:`, errorData);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorData}`);
      }

      const data = await response.json();
      console.log(`✅ Columns fetched for ${tableName}:`, data);

      // Extract column names from the response
      let columnNames: string[] = [];

      if (Array.isArray(data)) {
        // If response is direct array of column names
        columnNames = data;
      } else if (data && Array.isArray(data.columns)) {
        // If response has columns array
        columnNames = data.columns;
      } else if (data && data.column_list && Array.isArray(data.column_list)) {
        // If response has column_list array
        columnNames = data.column_list;
      } else if (data && typeof data === 'object') {
        // If response is object, try to extract column names
        const keys = Object.keys(data);
        if (keys.length > 0 && Array.isArray(data[keys[0]])) {
          columnNames = data[keys[0]];
        }
      }

      console.log(`📋 Extracted column names for ${tableName}:`, columnNames);

      // Patch columns to the respective side (left or right)
      const side = isLeftTable ? 'left' : 'right';
      console.log(`🔄 Patching ${columnNames.length} columns to ${side} side for table "${tableName}"`);

      setAvailableColumns(prev => {
        const updated = {
          ...prev,
          [side]: columnNames
        };
        console.log(`✅ Updated available columns:`, updated);
        return updated;
      });

      // Clear any existing column selections for this side when new columns are loaded
      if (isLeftTable) {
        // Clear left column selections in all column mappings
        setColumnMappings(prev => prev.map(mapping => ({
          ...mapping,
          leftColumn: ''
        })));
        console.log(`🧹 Cleared left column selections in column mappings`);
      } else {
        // Clear right column selections in all column mappings
        setColumnMappings(prev => prev.map(mapping => ({
          ...mapping,
          rightColumn: ''
        })));
        console.log(`🧹 Cleared right column selections in column mappings`);
      }

      return columnNames;

    } catch (error) {
      console.error(`❌ Error fetching columns for ${tableName}:`, error);
      toast.error(`Failed to fetch columns for ${tableName}: ${error.message}`);
      return [];
    } finally {
      const setLoading = isLeftTable ? setIsLoadingLeftColumns : setIsLoadingRightColumns;
      setLoading(false);
    }
  };

  // Create table join rule via API
  const createTableJoinRule = async () => {
    try {
      // Find the selected connection to get connection_id and database_name
      const selectedConnection = dbConnections.find(conn => conn.connection_name === formData.dbConnection);
      if (!selectedConnection) {
        toast.error('Selected database connection not found');
        return;
      }

      // Prepare column join rules
      const validColumnMappings = columnMappings.filter(mapping =>
        mapping.leftColumn && mapping.rightColumn && mapping.joinOperator
      );

      const requestBody = {
        table_join_relationship_name: formData.name,
        left_table_name: formData.leftTable,
        right_table_name: formData.rightTable,
        join_type: formData.joinType,
        database_name: selectedConnection.database_name,
        connection_id: selectedConnection.connection_id,
        column_join_rules: validColumnMappings.map(mapping => ({
          left_column_name: mapping.leftColumn,
          right_column_name: mapping.rightColumn,
          join_operator: mapping.joinOperator
        }))
      };

      console.log('🚀 Creating table join rule with data:', requestBody);

      const response = await fetch('http://***********:8001/account-management/create-table-join-rule', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      console.log('📥 Create table join rule response status:', response.status);

      if (!response.ok) {
        const errorData = await response.text();
        console.error('❌ Create table join rule error:', errorData);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorData}`);
      }

      const result = await response.json();
      console.log('✅ Table join rule created successfully:', result);

      toast.success('Table relationship created successfully!');

      // Call the onSave callback to update the parent component
      const updatedFormData = {
        ...formData,
        columnMappings: columnMappings
      };
      onSave(updatedFormData);

    } catch (error) {
      console.error('❌ Error creating table join rule:', error);
      toast.error(`Failed to create table relationship: ${error.message}`);
    }
  };

  const joinTypes = ['Inner Join', 'Left Join', 'Right Join', 'Full Outer Join'];
  const operators = ['=', '!=', '<', '>', '<=', '>=', 'LIKE', 'IN'];

  return (
    <div className="bg-white rounded-lg border p-6">
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Tables Header */}
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            {isNewRelationship ? 'New Relationship' : 'Tables'}
          </h3>
        </div>

        {/* Main Form Content */}
        <div className="space-y-4">
          {/* First Row: Relationship Name and DB Connection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div>
                    <FormField
                      label="Relationship Name"
                      required
                      value={formData.name}
                      onChange={(value) => handleInputChange('name', value)}
                      placeholder="RSP_TABLE1_TABLE2_ID1"
                    />
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Enter a unique name for this table relationship (e.g., Patient_Claims_Join)</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <div className="flex items-end">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex-1">
                      <SelectField
                        label="DB Connection"
                        required
                        value={formData.dbConnection}
                        onChange={(value) => handleInputChange('dbConnection', value)}
                        options={dbConnections.map(conn => ({
                          value: conn.connection_name,
                          label: conn.connection_name
                        }))}
                        placeholder="Select connection"
                        className="w-full"
                      />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Choose the database connection that contains the tables you want to join</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="ml-2 text-gray-500 hover:text-gray-700 h-10"
              >
                <Settings className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Second Row: Left Table and Right Table */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Left Table <span className="text-red-500">*</span>
                      </label>
                      <Autocomplete
                        options={tables.map(table => {
                          console.log('🔍 Mapping table for autocomplete:', table);
                          return {
                            value: table.table_name,
                            label: table.table_name
                          };
                        })}
                        value={formData.leftTable}
                        onValueChange={(value) => handleInputChange('leftTable', value as string)}
                        placeholder={
                          isLoadingTables
                            ? "Loading tables..."
                            : tables.length === 0
                            ? "Select DB connection first"
                            : "Search and select table"
                        }
                        emptyMessage="No tables found"
                        searchPlaceholder="Search tables..."
                        disabled={isLoadingTables || tables.length === 0}
                        className="w-full"
                      />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Select the primary table for the join relationship (e.g., patient_data)</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              {/* Debug info */}
              {process.env.NODE_ENV === 'development' && (
                <div className="text-xs text-gray-500 mt-1">
                  Tables available: {tables.length} | Loading: {isLoadingTables.toString()}
                </div>
              )}
            </div>
            <div>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Right Table <span className="text-red-500">*</span>
                      </label>
                      <Autocomplete
                        options={tables.map(table => ({
                          value: table.table_name,
                          label: table.table_name
                        }))}
                        value={formData.rightTable}
                        onValueChange={(value) => handleInputChange('rightTable', value as string)}
                        placeholder={
                          isLoadingTables
                            ? "Loading tables..."
                            : tables.length === 0
                            ? "Select DB connection first"
                            : "Search and select table"
                        }
                        emptyMessage="No tables found"
                        searchPlaceholder="Search tables..."
                        disabled={isLoadingTables || tables.length === 0}
                        className="w-full"
                      />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Select the secondary table to join with the left table (e.g., claims)</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              {/* Debug info */}
              {process.env.NODE_ENV === 'development' && (
                <div className="text-xs text-gray-500 mt-1">
                  Tables available: {tables.length} | Loading: {isLoadingTables.toString()}
                </div>
              )}
            </div>
          </div>

          {/* Third Row: Join Type */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div>
                    <SelectField
                      label="Join Type"
                      required
                      value={formData.joinType}
                      onChange={(value) => handleInputChange('joinType', value)}
                      options={joinTypes.map(type => ({ value: type, label: type }))}
                      placeholder="Inner Join"
                    />
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Choose how tables should be joined: Inner (matching rows only), Left (all left table rows), Right (all right table rows), or Full Outer (all rows)</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <div></div> {/* Empty space for alignment */}
          </div>

          {/* Column Mappings Section */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <h4 className="text-sm font-medium text-gray-700 cursor-help">Column Mappings</h4>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Define which columns from each table should be matched in the join relationship</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      type="button"
                      onClick={addColumnMapping}
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2 text-teal-600 border-teal-300 hover:bg-teal-50"
                    >
                      <Plus className="w-4 h-4" />
                      Add Column
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Add another column mapping to create complex multi-column joins</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>

            {/* Column Mapping Headers */}
            <div className="grid grid-cols-12 gap-2 text-xs font-medium text-gray-500 px-2">
              <div className="col-span-4">Left Column</div>
              <div className="col-span-2 text-center">Join Operator</div>
              <div className="col-span-4">Right Column</div>
              <div className="col-span-2 text-center">Action</div>
            </div>

            {/* Dynamic Column Mappings */}
            {columnMappings.map((mapping, index) => (
              <div key={index} className="grid grid-cols-12 gap-2 items-end">
                {/* Left Column */}
                <div className="col-span-4">
                  <SelectField
                    label=""
                    value={mapping.leftColumn}
                    onChange={(value) => updateColumnMapping(index, 'leftColumn', value)}
                    options={availableColumns.left.map(col => ({ value: col, label: col }))}
                    placeholder={
                      isLoadingLeftColumns
                        ? "Loading columns..."
                        : !formData.leftTable
                        ? "Select left table first"
                        : availableColumns.left.length === 0
                        ? "No columns available"
                        : "Select left column"
                    }
                    disabled={isLoadingLeftColumns || !formData.leftTable || availableColumns.left.length === 0}
                  />
                </div>

                {/* Join Operator */}
                <div className="col-span-2">
                  <SelectField
                    label=""
                    value={mapping.joinOperator}
                    onChange={(value) => updateColumnMapping(index, 'joinOperator', value)}
                    options={operators.map(op => ({ value: op, label: op }))}
                    placeholder="="
                  />
                </div>

                {/* Right Column */}
                <div className="col-span-4">
                  <SelectField
                    label=""
                    value={mapping.rightColumn}
                    onChange={(value) => updateColumnMapping(index, 'rightColumn', value)}
                    options={availableColumns.right.map(col => ({ value: col, label: col }))}
                    placeholder={
                      isLoadingRightColumns
                        ? "Loading columns..."
                        : !formData.rightTable
                        ? "Select right table first"
                        : availableColumns.right.length === 0
                        ? "No columns available"
                        : "Select right column"
                    }
                    disabled={isLoadingRightColumns || !formData.rightTable || availableColumns.right.length === 0}
                  />
                </div>

                {/* Remove Button */}
                <div className="col-span-2 flex justify-center">
                  <Button
                    type="button"
                    onClick={() => removeColumnMapping(index)}
                    variant="ghost"
                    size="sm"
                    disabled={columnMappings.length === 1}
                    className="text-red-500 hover:text-red-700 hover:bg-red-50 p-2"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>

          {/* AI Recommendation Button */}
          <div className="flex justify-start">
            <Button
              type="button"
              variant="outline"
              onClick={onAIRecommendation}
              className="bg-yellow-50 border-yellow-200 text-yellow-800 hover:bg-yellow-100"
            >
              <Lightbulb className="w-4 h-4 mr-2" />
              AI Recommendation
            </Button>
          </div>

          {/* Form Action Buttons */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <Button
              type="button"
              onClick={onCancel}
              variant="outline"
              className="px-6 py-2"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="px-6 py-2 text-white hover:opacity-90 transition-opacity"
              style={{ backgroundColor: 'rgb(0, 130, 130)' }}
            >
              {isNewRelationship ? 'Create Relationship' : 'Update Relationship'}
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default RelationshipForm;
