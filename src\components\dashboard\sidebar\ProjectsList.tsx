
import React from 'react';
import { Share, Plus } from 'lucide-react';
import { Project } from './types';

interface ProjectsListProps {
  items?: Project[];
  toggleItem?: (id: string) => void;
  addChatToItem?: (id: string) => void;
  // For backward compatibility
  projects?: Project[];
  toggleProject?: (id: string) => void;
  addChatToProject?: (id: string) => void;
}

const ProjectsList: React.FC<ProjectsListProps> = ({ 
  items,
  toggleItem,
  addChatToItem,
  // Support both naming conventions for backward compatibility
  projects,
  toggleProject,
  addChatToProject
}) => {
  // Use items if provided, otherwise use projects, or default to empty array
  const projectsList = items || projects || [];
  // Use the provided toggle function or default to a no-op function
  const handleToggle = toggleItem || toggleProject || ((id: string) => {});
  // Use the provided add function or default to undefined
  const handleAddChat = addChatToItem || addChatToProject;

  return (
    <div className="space-y-2">
      {projectsList.map((project) => (
        <div key={project.id} className="mb-1">
          <div 
            className="flex items-center justify-between p-2 hover:bg-gray-100 rounded cursor-pointer"
            onClick={() => handleToggle(project.id)}
          >
            <span className={`text-sm ${project.expanded ? 'font-medium text-green-700' : 'text-gray-700'}`}>
              {project.name}
            </span>
            <div className="flex items-center space-x-2">
              <button 
                className="text-gray-500 hover:text-gray-700 p-1"
                onClick={(e) => {
                  e.stopPropagation();
                  handleAddChat && handleAddChat(project.id);
                }}
              >
                <Plus size={16} />
              </button>
              <button 
                className="text-gray-500 hover:text-gray-700 p-1"
                onClick={(e) => e.stopPropagation()}
              >
                <Share size={16} />
              </button>
            </div>
          </div>
          
          {project.expanded && project.chats && project.chats.length > 0 && (
            <div className="ml-4 mt-1 space-y-1">
              {project.chats.map((chat, index) => (
                <div 
                  key={index}
                  className="p-2 hover:bg-gray-100 rounded cursor-pointer text-sm text-gray-600"
                >
                  {chat}
                </div>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default ProjectsList;
