import React from 'react';
import { KPIFormFieldsProps, KPI_FIELD_CONFIGS } from './types/kpiTypes';
import KPISQLInput from './KPISQLInput';

const KPIFormFields: React.FC<KPIFormFieldsProps> = ({
  formData,
  onFormChange,
  errors = {},
  onValidateSQL,
  isValidating = {},
  validationResults = {},
  onValidateField
}) => {
  // Track which SQL fields have content to show/hide helper text
  const [showHelperText, setShowHelperText] = React.useState<Record<string, boolean>>({});

  const handleInputChange = (field: string, value: string) => {
    onFormChange({ [field]: value });

    // Show helper text when user starts typing in SQL fields
    if (field === 'sql' || field === 'priorSql') {
      setShowHelperText(prev => ({
        ...prev,
        [field]: value.trim().length > 0 && !validationResults[field]?.isValid
      }));
    }
  };

  // Clear validation state when view type changes
  React.useEffect(() => {
    setShowHelperText({});
  }, [formData.viewType]);

  const handleFieldBlur = (fieldName: string) => {
    // Validate field when user leaves the field
    if (onValidateField) {
      onValidateField(fieldName);
    }
  };

  const handleValidate = async (sql: string, fieldKey: string) => {
    if (onValidateSQL && sql.trim()) {
      const result = await onValidateSQL(sql, fieldKey);
      // Hide helper text after validation (successful or failed)
      setShowHelperText(prev => ({
        ...prev,
        [fieldKey]: false
      }));
      return result;
    }
  };

  const renderField = (fieldKey: string, config: any) => {
    const value = formData[fieldKey as keyof typeof formData] || '';
    const error = errors[fieldKey];
    const isSqlField = fieldKey === 'sql' || fieldKey === 'priorSql';
    const hasValue = typeof value === 'string' ? value.trim() !== '' : Boolean(value);
    const showRequiredError = config.required && !hasValue && error;

    return (
      <div key={fieldKey} className="flex flex-col">
        {isSqlField ? (
          <KPISQLInput
            value={value}
            onChange={(newValue) => handleInputChange(fieldKey, newValue)}
            onValidate={(sql) => handleValidate(sql, fieldKey)}
            onBlur={() => handleFieldBlur(fieldKey)}
            label={config.label}
            required={config.required}
            placeholder={config.placeholder}
            error={error}
            viewType={formData.viewType}
            fieldKey={fieldKey}
          />
        ) : config.type === 'textarea' ? (
          <div className="flex flex-col">
            <div className="flex items-center mb-1">
              <label className="text-sm font-medium text-gray-700">
                {config.required && <span className="text-red-500 mr-1">*</span>}
                {config.label}:
              </label>
              {error && (
                <span className="text-red-500 text-xs ml-2">{error}</span>
              )}
            </div>
            <textarea
              value={value}
              onChange={(e) => handleInputChange(fieldKey, e.target.value)}
              onBlur={() => handleFieldBlur(fieldKey)}
              placeholder={config.placeholder}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 resize-none transition-colors h-20 placeholder:text-sm ${
                error
                  ? 'border-red-500 focus:ring-red-200'
                  : config.required && !hasValue
                    ? 'border-orange-200 focus:ring-blue-500'
                    : 'border-gray-300 focus:ring-blue-500'
              }`}
              rows={3}
            />
          </div>
        ) : (
          <div className="flex flex-col">
            <div className="flex items-center mb-1">
              <label className="text-sm font-medium text-gray-700">
                {config.required && <span className="text-red-500 mr-1">*</span>}
                {config.label}:
              </label>
              {error && (
                <span className="text-red-500 text-xs ml-2">{error}</span>
              )}
            </div>
            <input
              type="text"
              value={value}
              onChange={(e) => handleInputChange(fieldKey, e.target.value)}
              onBlur={() => handleFieldBlur(fieldKey)}
              placeholder={config.placeholder}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 transition-colors placeholder:text-sm ${
                error
                  ? 'border-red-500 focus:ring-red-200'
                  : config.required && !hasValue
                    ? 'border-orange-200 focus:ring-blue-500'
                    : 'border-gray-300 focus:ring-blue-500'
              }`}
            />
          </div>
        )}
      </div>
    );
  };

  const getFieldsForViewType = () => {
    const configs = KPI_FIELD_CONFIGS[formData.viewType];
    const fields: { [key: string]: any } = {};
    
    // Map field configurations to actual form field keys
    configs.forEach((config) => {
      switch (config.label) {
        case 'KPI Label':
          fields.label = config;
          break;
        case 'KPI Current Value':
          fields.currentValue = config;
          break;
        case 'KPI Prior Value':
          fields.priorValue = config;
          break;
        case 'KPI Current Label':
          fields.currentLabel = config;
          break;
        case 'KPI Prior Label':
          fields.priorLabel = config;
          break;
        case 'Target':
          fields.target = config;
          break;
        case 'SQL Query':
        case 'Current SQL Query':
          fields.sql = config;
          break;
        case 'Prior SQL Query':
          fields.priorSql = config;
          break;
        case 'Additional Info':
          fields.additionalInfo = config;
          break;
      }
    });
    
    return fields;
  };

  const fieldsToRender = getFieldsForViewType();

  return (
    <div className="space-y-4">
      {/* First row - KPI Label and conditional fields */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {fieldsToRender.label && renderField('label', fieldsToRender.label)}
        {fieldsToRender.currentValue && renderField('currentValue', fieldsToRender.currentValue)}
        {fieldsToRender.priorValue && renderField('priorValue', fieldsToRender.priorValue)}
        {fieldsToRender.currentLabel && renderField('currentLabel', fieldsToRender.currentLabel)}
        {fieldsToRender.priorLabel && renderField('priorLabel', fieldsToRender.priorLabel)}
        {fieldsToRender.target && renderField('target', fieldsToRender.target)}
      </div>

      {/* Second row - KPI SQL */}
      {fieldsToRender.sql && (
        <div className="grid grid-cols-1">
          {renderField('sql', fieldsToRender.sql)}
        </div>
      )}

      {/* Third row - SQL Prior Value (only for current-vs-prior) */}
      {fieldsToRender.priorSql && (
        <div className="grid grid-cols-1">
          {renderField('priorSql', fieldsToRender.priorSql)}
        </div>
      )}

      {/* Fourth row - Additional Info */}
      {fieldsToRender.additionalInfo && (
        <div className="grid grid-cols-1">
          {renderField('additionalInfo', fieldsToRender.additionalInfo)}
        </div>
      )}
    </div>
  );
};

export default KPIFormFields;
