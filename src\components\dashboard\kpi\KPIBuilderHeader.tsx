import React from 'react';
import KPIViewSelector from './KPIViewSelector';
import { KPIViewType } from './types/kpiTypes';

interface KPIBuilderHeaderProps {
  selectedView: KPIViewType;
  onViewChange: (view: KPIViewType) => void;
}

const KPIBuilderHeader: React.FC<KPIBuilderHeaderProps> = ({
  selectedView,
  onViewChange
}) => {
  return (
    <div className="space-y-4">
      {/* KPI View Type Selector */}
      <KPIViewSelector
        selectedView={selectedView}
        onViewChange={onViewChange}
      />
    </div>
  );
};

export default KPIBuilderHeader;
