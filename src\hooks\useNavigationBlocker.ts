import { useEffect, useCallback } from 'react';
import { useNavigate, useLocation, useBlocker } from 'react-router-dom';

/**
 * Custom hook to block navigation when there are unsaved changes
 * @param shouldBlock Boolean indicating if navigation should be blocked
 * @param message Message to display in the confirmation dialog
 */
export const useNavigationBlocker = (shouldBlock: boolean, message: string) => {
  const navigate = useNavigate();
  const location = useLocation();
  
  // In React Router v6, useBlocker takes a function that returns a boolean
  const blocker = useBlocker(
    useCallback(
      ({ currentLocation, nextLocation }) => 
        shouldBlock && nextLocation.pathname !== currentLocation.pathname,
      [shouldBlock]
    )
  );
  
  // Handle the blocker state
  useEffect(() => {
    if (blocker.state === 'blocked') {
      if (window.confirm(message)) {
        blocker.proceed();
      } else {
        blocker.reset();
      }
    }
  }, [blocker, message]);

  // <PERSON>le clicks on links that would navigate away
  useEffect(() => {
    if (!shouldBlock) return;
    
    const handleLinkClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      const link = target.closest('a');
      
      if (!link) return;
      
      // Skip if it's an external link or has a modifier key pressed
      if (
        link.target === '_blank' || 
        e.ctrlKey || 
        e.metaKey || 
        e.shiftKey || 
        e.altKey
      ) return;
      
      // Check if this is an internal navigation link
      const href = link.getAttribute('href');
      if (!href || href.startsWith('http') || href.startsWith('mailto:')) return;
      
      // If we have unsaved changes, confirm before navigating
      if (shouldBlock) {
        e.preventDefault();
        if (window.confirm(message)) {
          navigate(href);
        }
      }
    };

    // Add event listener for link clicks
    document.addEventListener('click', handleLinkClick);

    return () => {
      document.removeEventListener('click', handleLinkClick);
    };
  }, [shouldBlock, message, navigate]);
};




