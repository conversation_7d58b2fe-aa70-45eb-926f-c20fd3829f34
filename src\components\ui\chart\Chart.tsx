import React from 'react';
import UniversalChart, { UniversalChartProps } from '@/components/charts/UniversalChart';

/**
 * Chart UI Component - Bridge to UniversalChart
 * This provides compatibility with the shadcn/ui chart system
 */
interface ChartProps extends UniversalChartProps {
  // Additional UI-specific props can be added here
}

const Chart: React.FC<ChartProps> = (props) => {
  return <UniversalChart {...props} />;
};

export { Chart };
export default Chart;
export type { ChartProps };