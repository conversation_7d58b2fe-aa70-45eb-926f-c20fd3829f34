import React, { useMemo, useState } from 'react';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Edit, Save, X } from 'lucide-react';

export interface Permission {
  id?: string;
  dbConnection: string;
  table: string;
  column: string;
  access: 'Y' | 'N' | '';
  maxRowsRead: string;
  tableLevelFilters: string;
  anonymise: string;
  anonymiseRule: string;
}

interface PermissionTableProps {
  permissions: Permission[];
  onPermissionChange: (index: number, field: keyof Permission, value: string) => void;
  searchTable?: string;
  searchColumn?: string;
  searchAccess?: string;
  onSearchTableChange?: (value: string) => void;
  onSearchColumnChange?: (value: string) => void;
  onSearchAccessChange?: (value: string) => void;
}

export const PermissionTable: React.FC<PermissionTableProps> = ({
  permissions,
  onPermissionChange,
  searchTable = '',
  searchColumn = '',
  searchAccess = '',
  onSearchTableChange,
  onSearchColumnChange,
  onSearchAccessChange,
}) => {
  // Edit state management
  const [editingRowIndex, setEditingRowIndex] = useState<number | null>(null);

  // Filter permissions based on search criteria
  const filteredPermissions = useMemo(() => {
    return permissions.filter(permission => {
      const matchesTable = permission.table.toLowerCase().includes(searchTable.toLowerCase());
      const matchesColumn = permission.column.toLowerCase().includes(searchColumn.toLowerCase());
      const matchesAccess = permission.access.toLowerCase().includes(searchAccess.toLowerCase());
      return matchesTable && matchesColumn && matchesAccess;
    });
  }, [permissions, searchTable, searchColumn, searchAccess]);

  // Handle edit actions
  const handleEdit = (rowIndex: number) => {
    setEditingRowIndex(rowIndex);
  };

  const handleSave = () => {
    setEditingRowIndex(null);
  };

  const handleCancel = () => {
    setEditingRowIndex(null);
  };

  // Define table headers
  const headers = [
    'DB_Connection',
    'Table',
    'Column',
    'Access',
    'MaxRows/Read',
    'TableLevelFilters',
    'Anonymise',
    'AnonymiseRule',
    'Actions'
  ];

  // Render cell content based on column and edit state
  const renderCell = (permission: Permission, columnIndex: number, rowIndex: number) => {
    const isEditing = editingRowIndex === rowIndex;

    switch (columnIndex) {
      case 0: // DB_Connection
        return <div className="font-normal text-black">{permission.dbConnection}</div>;
      case 1: // Table
        return <div className="font-normal text-black">{permission.table}</div>;
      case 2: // Column
        return <div className="font-normal text-black">{permission.column}</div>;
      case 3: // Access
        if (isEditing) {
          return (
            <Select
              value={permission.access}
              onValueChange={(value) => onPermissionChange(rowIndex, 'access', value)}
            >
              <SelectTrigger className="w-16 h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">-</SelectItem>
                <SelectItem value="Y">Y</SelectItem>
                <SelectItem value="N">N</SelectItem>
              </SelectContent>
            </Select>
          );
        }
        return <div className="font-normal text-black">{permission.access || '-'}</div>;
      case 4: // MaxRows/Read
        if (isEditing) {
          return (
            <Input
              value={permission.maxRowsRead}
              onChange={(e) => onPermissionChange(rowIndex, 'maxRowsRead', e.target.value)}
              className="w-24 h-8"
              placeholder="Unlimited"
              type="number"
              min="0"
            />
          );
        }
        return <div className="font-normal text-black">{permission.maxRowsRead || 'Unlimited'}</div>;
      case 5: // TableLevelFilters
        if (isEditing) {
          return (
            <Input
              value={permission.tableLevelFilters}
              onChange={(e) => onPermissionChange(rowIndex, 'tableLevelFilters', e.target.value)}
              className="w-32 h-8"
              placeholder="No filters"
            />
          );
        }
        return <div className="font-normal text-black">{permission.tableLevelFilters || 'No filters'}</div>;
      case 6: // Anonymise
        if (isEditing) {
          return (
            <Select
              value={permission.anonymise}
              onValueChange={(value) => onPermissionChange(rowIndex, 'anonymise', value)}
            >
              <SelectTrigger className="w-24 h-8">
                <SelectValue placeholder="None" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">None</SelectItem>
                <SelectItem value="hash">Hash</SelectItem>
                <SelectItem value="mask">Mask</SelectItem>
                <SelectItem value="encrypt">Encrypt</SelectItem>
              </SelectContent>
            </Select>
          );
        }
        return <div className="font-normal text-black">{permission.anonymise || 'None'}</div>;
      case 7: // AnonymiseRule
        if (isEditing) {
          return (
            <Input
              value={permission.anonymiseRule}
              onChange={(e) => onPermissionChange(rowIndex, 'anonymiseRule', e.target.value)}
              className="w-32 h-8"
              placeholder="Default rule"
            />
          );
        }
        return <div className="font-normal text-black">{permission.anonymiseRule || 'Default rule'}</div>;
      case 8: // Actions
        if (isEditing) {
          return (
            <div className="flex items-center space-x-2">
              <Button
                onClick={handleSave}
                size="sm"
                className="h-8 w-8 p-0 text-white hover:opacity-90"
                style={{ backgroundColor: 'rgb(0, 130, 130)' }}
              >
                <Save className="h-4 w-4" />
              </Button>
              <Button
                onClick={handleCancel}
                size="sm"
                variant="outline"
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          );
        }
        return (
          <Button
            onClick={() => handleEdit(rowIndex)}
            size="sm"
            variant="outline"
            className="h-8 w-8 p-0"
          >
            <Edit className="h-4 w-4" />
          </Button>
        );
      default:
        return null;
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
      <div className="overflow-x-auto">
        <div className="w-full">
          <table className="w-full border-collapse">
            <thead className="bg-white border-b border-gray-200">
              <tr>
                {headers.map((header, index) => (
                  <th
                    key={index}
                    className="px-4 py-3 text-left text-sm font-normal text-black border-r border-gray-200 last:border-r-0"
                  >
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {filteredPermissions.map((permission, rowIndex) => (
                <tr key={rowIndex} className="border-b border-gray-200 hover:bg-gray-50">
                  {headers.map((_, colIndex) => (
                    <td
                      key={colIndex}
                      className="px-4 py-3 text-sm border-r border-gray-200 last:border-r-0"
                    >
                      {renderCell(permission, colIndex, rowIndex)}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default PermissionTable;
