interface CacheEntry<T> {
  data: T;
  timestamp: number;
  lastAccessed: number;
}

export class TableDataCache<T> {
  private cache = new Map<string, CacheEntry<T>>();
  private maxSize: number;
  private ttl: number; // time to live in milliseconds

  constructor(maxSize = 100, ttl = 5 * 60 * 1000) { // 5 minutes default TTL
    this.maxSize = maxSize;
    this.ttl = ttl;
  }

  private generateKey(connectionId: string, tableName?: string): string {
    return tableName ? `${connectionId}:${tableName}` : connectionId;
  }

  private isExpired(entry: CacheEntry<T>): boolean {
    return Date.now() - entry.timestamp > this.ttl;
  }

  private evictLRU(): void {
    let lruKey = '';
    let lruTime = Date.now();
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.lastAccessed < lruTime) {
        lruTime = entry.lastAccessed;
        lruKey = key;
      }
    }
    
    if (lruKey) {
      this.cache.delete(lruKey);
    }
  }

  get(connectionId: string, tableName?: string): T | null {
    const key = this.generateKey(connectionId, tableName);
    const entry = this.cache.get(key);
    
    if (!entry || this.isExpired(entry)) {
      if (entry) {
        this.cache.delete(key);
      }
      return null;
    }
    
    // Update last accessed time
    entry.lastAccessed = Date.now();
    return entry.data;
  }

  set(connectionId: string, data: T, tableName?: string): void {
    const key = this.generateKey(connectionId, tableName);
    
    // Evict if at max size
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      this.evictLRU();
    }
    
    const now = Date.now();
    this.cache.set(key, {
      data,
      timestamp: now,
      lastAccessed: now
    });
  }

  invalidate(connectionId: string, tableName?: string): void {
    if (tableName) {
      const key = this.generateKey(connectionId, tableName);
      this.cache.delete(key);
    } else {
      // Invalidate all entries for this connection
      const prefix = `${connectionId}:`;
      for (const key of this.cache.keys()) {
        if (key.startsWith(prefix) || key === connectionId) {
          this.cache.delete(key);
        }
      }
    }
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }

  // Clean expired entries
  cleanup(): void {
    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        this.cache.delete(key);
      }
    }
  }
}