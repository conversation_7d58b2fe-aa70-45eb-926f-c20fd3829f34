
import { PayloadAction } from '@reduxjs/toolkit';
import { ColumnDefinition, DatasetState } from '../types';

export const columnReducers = {
  addColumn: (state: DatasetState, action: PayloadAction<ColumnDefinition>) => {
    const columnExists = state.selectedColumns.some(col => 
      col.name === action.payload.name && col.tableOrigin === action.payload.tableOrigin
    );
    
    if (!columnExists) {
      state.selectedColumns.push(action.payload);
    }
  },
  
  removeColumn: (state: DatasetState, action: PayloadAction<{name: string, tableOrigin?: string}>) => {
    const { name, tableOrigin } = action.payload;
    
    if (tableOrigin) {
      // Remove specific column with matching name and tableOrigin
      state.selectedColumns = state.selectedColumns.filter(col => 
        !(col.name === name && col.tableOrigin === tableOrigin)
      );
    } else {
      // Backward compatibility: remove all columns with matching name
      state.selectedColumns = state.selectedColumns.filter(col => col.name !== name);
    }
  },
};
