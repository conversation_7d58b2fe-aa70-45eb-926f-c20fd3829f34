
import React, { useState } from 'react';
import { File, Trash } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface FileItemProps {
  fileName: string;
  onFileClick: () => void;
  onDeleteClick: (e: React.MouseEvent) => void;
  isTracker?: boolean;
}

const FileItem: React.FC<FileItemProps> = ({
  fileName,
  onFileClick,
  onDeleteClick,
  isTracker = false
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const truncateFileName = (name: string) => {
    const lastDotIndex = name.lastIndexOf('.');
    if (lastDotIndex === -1) return name;

    const nameWithoutExt = name.slice(0, lastDotIndex);
    const extension = name.slice(lastDotIndex);

    return nameWithoutExt.length > 10 
      ? `${nameWithoutExt.slice(0, 10)}...${extension}`
      : name;
  };

  return (
    <div
      className="text-sm text-gray-600 py-1 cursor-pointer hover:text-blue-600 flex justify-between items-start group"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div 
              className="flex-1 pr-2 break-all" 
              onClick={onFileClick}
            >
              <div className="flex items-center">
                <File className="h-4 w-4 mr-2" />
                {truncateFileName(fileName)}
              </div>
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>{fileName}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      
      {!isTracker && isHovered && (
        <span
          className="text-gray-400 pr-1 hover:text-red-500"
          aria-label="Delete"
          onClick={onDeleteClick}
        >
          <Trash size={16} />
        </span>
      )}
    </div>
  );
};

export default FileItem;
