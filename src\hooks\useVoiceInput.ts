
import { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';

interface UseVoiceInputReturn {
  isListening: boolean;
  setIsListening: (isListening: boolean) => void;
  toggleVoiceInput: (onActivate: () => void) => void;
}

export const useVoiceInput = (
  setInputValue: (value: string) => void, 
  isLoading: boolean
): UseVoiceInputReturn => {
  const [isListening, setIsListening] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    let recognition: any = null;
    
    if (isListening) {
      if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        // @ts-ignore - TypeScript doesn't know about the SpeechRecognition API
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        recognition = new SpeechRecognition();
        recognition.continuous = true;
        recognition.interimResults = true;
        
        recognition.onresult = (event: any) => {
          const transcript = Array.from(event.results)
            .map((result: any) => result[0])
            .map((result: any) => result.transcript)
            .join('');
          
          setInputValue(transcript);
        };
        
        recognition.onerror = (event: any) => {
          console.error('Speech recognition error', event.error);
          toast({
            title: "Speech Recognition Error",
            description: `Error: ${event.error}`,
            variant: "destructive"
          });
          setIsListening(false);
        };
        
        recognition.start();
      } else {
        toast({
          title: "Speech Recognition Not Supported",
          description: "Your browser doesn't support speech recognition.",
          variant: "destructive"
        });
        setIsListening(false);
      }
    }
    
    return () => {
      if (recognition) {
        recognition.stop();
      }
    };
  }, [isListening, setInputValue, toast]);

  const toggleVoiceInput = (onActivate: () => void) => {
    if (isListening) {
      setIsListening(false);
    } else if (!isLoading) {
      setIsListening(true);
      onActivate();
    }
  };

  return {
    isListening,
    setIsListening,
    toggleVoiceInput
  };
};
