
// Types for SavedCharts functionality
export interface TableInfo {
  table_name: string;
  columns: string[];
}

export interface SavedChart {
  id?: string;
  chart_id: string;
  chart_name: string;
  chart_image: string;
  description: string | null;
  chart_type: string;
  table_name: string;
  db_type: string;
  x_axis: string;
  y_axis: string;
  tables: TableInfo[];
  clipboard: boolean;
  editable: boolean;
  refresh: boolean;
  shareable: boolean;
  export: boolean;
  filters: any;
  sort_order: any;
  group_by: any;
  verified: boolean;
  views: number;
  published_by: string;
  created_at: string;
  connection_id?: string;
  chart_response?: {
    status: string;
    data: ChartDataPoint[];
    metadata?: {
      tables: string[];
      status: string;
    };
  };
  chart_data?: ChartDataPoint[];
  // Add this property to support displaying only chart or table
  displayComponent?: 'chart' | 'table';
}

export interface ChartDataPoint {
  [key: string]: any;
}

export type ChartViewMode = 'grid' | 'detail';
