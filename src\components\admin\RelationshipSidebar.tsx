import React from 'react';
import { Button } from '@/components/ui/button';
import { Plus, Trash2 } from 'lucide-react';

interface TableRelationship {
  id: string;
  name: string;
  dbConnection: string;
  leftTable: string;
  rightTable: string;
  joinType: string;
  leftColumn: string;
  rightColumn: string;
  joinOperator: string;
  rightJoinOperator: string;
}

interface RelationshipSidebarProps {
  relationships: TableRelationship[];
  selectedRelationship: TableRelationship | null;
  onSelectRelationship: (relationship: TableRelationship) => void;
  onNewRelationship: () => void;
  onDeleteRelationship: (relationshipId: string) => void;
  isNewRelationship?: boolean;
}

const RelationshipSidebar: React.FC<RelationshipSidebarProps> = ({
  relationships,
  selectedRelationship,
  onSelectRelationship,
  onNewRelationship,
  onDeleteRelationship,
  isNewRelationship = false
}) => {
  return (
    <div className="w-64 bg-white border-r border-gray-200 p-4 space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">Relationships</h3>
        <Button
          size="sm"
          onClick={onNewRelationship}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          <Plus className="w-4 h-4" />
        </Button>
      </div>

      {/* New Relationship Button */}
      <Button
        variant="outline"
        onClick={onNewRelationship}
        className={`w-full justify-start text-left ${
          isNewRelationship ? 'bg-blue-50 border-blue-200 text-blue-700' : ''
        }`}
      >
        <Plus className="w-4 h-4 mr-2" />
        New Relationship
        {isNewRelationship && <span className="ml-auto text-xs">(Active)</span>}
      </Button>

      {/* Relationships List */}
      <div className="space-y-2">
        {relationships.length === 0 ? (
          <div className="text-sm text-gray-500 text-center py-4">
            No relationships created yet
          </div>
        ) : (
          relationships.map((relationship) => (
            <div
              key={relationship.id}
              className={`group relative p-3 rounded-lg border cursor-pointer transition-colors ${
                selectedRelationship?.id === relationship.id
                  ? 'bg-blue-50 border-blue-200'
                  : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
              }`}
              onClick={() => onSelectRelationship(relationship)}
            >
              <div className="pr-8">
                <div className="font-medium text-sm text-gray-900 truncate">
                  {relationship.name || 'Unnamed Relationship'}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {relationship.leftTable} → {relationship.rightTable}
                </div>
                <div className="text-xs text-gray-400 mt-1">
                  {relationship.dbConnection}
                </div>
              </div>
              
              {/* Delete Button */}
              <Button
                size="sm"
                variant="ghost"
                onClick={(e) => {
                  e.stopPropagation();
                  onDeleteRelationship(relationship.id);
                }}
                className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity p-1 h-6 w-6 text-red-500 hover:text-red-700 hover:bg-red-50"
              >
                <Trash2 className="w-3 h-3" />
              </Button>
            </div>
          ))
        )}
      </div>

      {/* Sidebar Footer */}
      <div className="pt-4 border-t border-gray-200">
        <div className="text-xs text-gray-500">
          {relationships.length} relationship{relationships.length !== 1 ? 's' : ''} total
        </div>
      </div>
    </div>
  );
};

export default RelationshipSidebar;
