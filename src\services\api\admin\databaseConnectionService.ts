import { centralApiClient } from '@/services/api/centralApiClient';

// Define the DatabaseConnection interface
export interface DatabaseConnection {
  id?: number;
  connection_name: string;
  db_dialect: string;
  database_name: string;
  username: string;
  password: string;
  host: string;
  port: number;
  user_id: number;
  ssl_mode: string;
  created_at?: string;
  updated_at?: string;
}

// Define interface for database connection list item (used in sidebar)
export interface DatabaseConnectionListItem {
  id: number;
  connection_name: string;
  db_dialect: string;
  database_name: string;
}

// Define interface for table information
export interface DatabaseTable {
  table_name: string;
  table_type: string;
  table_schema?: string;
}

// Define interface for table data
export interface TableData {
  columns: string[];
  rows: any[][];
  total_rows?: number;
}

// Function to create a new database connection
export const createDatabaseConnection = async (connectionData: Omit<DatabaseConnection, 'id' | 'created_at' | 'updated_at'>): Promise<DatabaseConnection> => {
  try {
    console.log('Creating database connection with data:', connectionData);

    const responseData = await centralApiClient.makeRequest<DatabaseConnection>('dada', '/database-connections/', {
      method: 'POST',
      body: connectionData
    });

    console.log('Database connection created:', responseData);
    return responseData;
  } catch (error) {
    console.error('Error creating database connection:', error);
    throw error;
  }
};

// Function to get all database connections
export const getDatabaseConnections = async (): Promise<DatabaseConnection[]> => {
  try {
    const data = await centralApiClient.makeRequest<any>('dada', '/database-connections/');
    
    // Handle the response format
    if (data && typeof data === 'object' && 'connections' in data) {
      return data.connections;
    }
    
    // Fallback to direct array if the response structure is different
    if (Array.isArray(data)) {
      return data;
    }
    
    // Return empty array if no valid data format is found
    console.warn('Unexpected response format from /database-connections/ endpoint:', data);
    return [];
  } catch (error) {
    console.error('Error fetching database connections:', error);
    throw error;
  }
};

// Function to get database connection names for sidebar
export const getDatabaseConnectionNames = async (): Promise<DatabaseConnectionListItem[]> => {
  try {
    const data = await centralApiClient.makeRequest<any>('dada', '/database-connections/');
    
    // Handle the nested connections array structure
    if (data && data.connections && Array.isArray(data.connections)) {
      return data.connections.map((connection: DatabaseConnection) => ({
        id: connection.id!,
        connection_name: connection.connection_name,
        db_dialect: connection.db_dialect,
        database_name: connection.database_name
      }));
    }
    
    // Fallback to direct array if the response structure changes
    if (Array.isArray(data)) {
      return data.map((connection: DatabaseConnection) => ({
        id: connection.id!,
        connection_name: connection.connection_name,
        db_dialect: connection.db_dialect,
        database_name: connection.database_name
      }));
    }
    
    return [];
  } catch (error) {
    console.error('Error fetching database connection names:', error);
    throw error;
  }
};

// Function to get a database connection by ID
export const getDatabaseConnectionById = async (connectionId: number): Promise<DatabaseConnection> => {
  try {
    const data = await centralApiClient.makeRequest<DatabaseConnection>('dada', `/database-connections/${connectionId}`);
    return data;
  } catch (error) {
    console.error(`Error fetching database connection with ID ${connectionId}:`, error);
    throw error;
  }
};

// Function to update a database connection
export const updateDatabaseConnection = async (connectionId: number, connectionData: Partial<DatabaseConnection>): Promise<DatabaseConnection> => {
  try {
    console.log('Updating database connection:', connectionId, connectionData);

    const responseData = await centralApiClient.makeRequest<DatabaseConnection>('dada', `/database-connections/${connectionId}`, {
      method: 'PUT',
      body: connectionData
    });

    console.log('Database connection updated:', responseData);
    return responseData;
  } catch (error) {
    console.error('Error updating database connection:', error);
    throw error;
  }
};

// Function to delete a database connection
export const deleteDatabaseConnection = async (connectionId: number): Promise<void> => {
  try {
    await centralApiClient.makeRequest<void>('dada', `/database-connections/${connectionId}`, {
      method: 'DELETE'
    });
  } catch (error) {
    console.error('Error deleting database connection:', error);
    throw error;
  }
};

// Function to test a database connection
export const testDatabaseConnection = async (connectionData: Omit<DatabaseConnection, 'id' | 'created_at' | 'updated_at'>): Promise<{ status: string; message: string }> => {
  try {
    console.log('Testing database connection:', connectionData);

    const responseData = await centralApiClient.makeRequest<{ status: string; message: string }>('dada', '/database-connections/test', {
      method: 'POST',
      body: connectionData
    });

    return responseData;
  } catch (error) {
    console.error('Error testing database connection:', error);
    throw error;
  }
};

// Function to get tables from a database connection
export const getDatabaseTables = async (connectionId: number): Promise<DatabaseTable[]> => {
  try {
    const data = await centralApiClient.makeRequest<any>('dada', `/database-connections/${connectionId}/tables`);
    
    if (data && Array.isArray(data.tables)) {
      return data.tables;
    }
    
    if (Array.isArray(data)) {
      return data;
    }
    
    return [];
  } catch (error) {
    console.error('Error fetching database tables:', error);
    throw error;
  }
};

// Function to get table data
export const getTableData = async (connectionId: number, tableName: string, limit: number = 100, offset: number = 0): Promise<TableData> => {
  try {
    const data = await centralApiClient.makeRequest<TableData>('dada', `/database-connections/${connectionId}/tables/${tableName}/data?limit=${limit}&offset=${offset}`);
    return data;
  } catch (error) {
    console.error('Error fetching table data:', error);
    throw error;
  }
};
