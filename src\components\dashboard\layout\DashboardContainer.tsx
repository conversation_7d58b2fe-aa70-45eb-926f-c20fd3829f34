
import React from 'react';
import { textSizeClasses } from './DashboardSettings';

interface DashboardContainerProps {
  children: React.ReactNode;
  textSize: 'small' | 'medium' | 'large';
}

const DashboardContainer: React.FC<DashboardContainerProps> = ({ 
  children, 
  textSize 
}) => {
  return (
    <div className={`h-screen flex flex-col bg-gray-50 overflow-hidden ${textSizeClasses[textSize].base}`}>
      {children}
    </div>
  );
};

export default DashboardContainer;
