
import { useEffect, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

/**
 * A hook to detect navigation events and run callbacks
 * 
 * @param beforeNavigate - Function to run before navigation occurs
 * @param afterNavigate - Function to run after navigation completes
 */
export function useNavigationEvents(
  beforeNavigate?: () => void,
  afterNavigate?: (newPath: string) => void
) {
  const location = useLocation();
  const previousPathRef = useRef(location.pathname);

  useEffect(() => {
    // If the path has changed, we've navigated
    if (previousPathRef.current !== location.pathname) {
      // Run the after navigation callback with the new path
      if (afterNavigate) {
        afterNavigate(location.pathname);
      }
      
      // Update the previous path
      previousPathRef.current = location.pathname;
    }
  }, [location.pathname, afterNavigate]);

  // Create a wrapped navigate function that calls beforeNavigate
  const navigate = useNavigate();
  const safeNavigate = (path: string, options?: any) => {
    if (beforeNavigate) {
      beforeNavigate();
    }
    navigate(path, options);
  };

  return { safeNavigate, currentPath: location.pathname };
}
