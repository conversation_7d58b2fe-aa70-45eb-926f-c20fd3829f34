
import { PayloadAction } from '@reduxjs/toolkit';
import { Message, MessageState } from './types';
import { filterMessages } from './utils';
import { QueryResultData } from '@/components/dashboard/models';

export const messageReducers = {
  setInputValue: (state: MessageState, action: PayloadAction<{ value: string, dashboardType: 1 | 2 | 3 }>) => {
    const { value, dashboardType } = action.payload;
    switch (dashboardType) {
      case 1:
        state.chatbotInputValue = value;
        break;
      case 2:
        state.transcriptInputValue = value;
        break;
      case 3:
        state.dadaInputValue = value;
        break;
      default:
        state.inputValue = value;
    }
  },

  setSearchQuery: (state: MessageState, action: PayloadAction<string>) => {
    state.searchQuery = action.payload;
    state.filteredMessages = filterMessages(state);
  },

  setHeaderSearchQuery: (state: MessageState, action: PayloadAction<string>) => {
    state.headerSearchQuery = action.payload;
    state.filteredMessages = filterMessages(state);
  },

  setUploadedFile: (state: MessageState, action: PayloadAction<any>) => {
    state.uploadedFile = action.payload;
  },

  deleteMessage: (state: MessageState, action: PayloadAction<number>) => {
    const index = action.payload;
    if (state.messages[index].type === 'query' && 
        index + 1 < state.messages.length && 
        state.messages[index + 1].type === 'response') {
      state.messages.splice(index, 2);
    } else {
      state.messages.splice(index, 1);
    }
    state.filteredMessages = filterMessages(state);
  },

  toggleMinimizeMessage: (state: MessageState, action: PayloadAction<number>) => {
    const index = action.payload;
    if (index < state.messages.length) {
      state.messages[index].minimized = !state.messages[index].minimized;
      state.filteredMessages = filterMessages(state);
    }
  },

  updateQueryResultForMessage: (state: MessageState, action: PayloadAction<{ index: number, queryResult: QueryResultData }>) => {
    const { index, queryResult } = action.payload;
    if (index < state.messages.length) {
      state.messages[index].queryResult = queryResult;
      state.filteredMessages = filterMessages(state);
    }
  },

  setMessages: (state: MessageState, action: PayloadAction<Message[]>) => {
    state.messages = action.payload;
    state.filteredMessages = action.payload;
  }
};
