
import React from 'react';
import ChartControls from './ChartControls';
import { FilterCondition } from './FilterSection';

interface ChartConfigurationPanelProps {
  chartStyle: 'bar' | 'line' | 'pie' | 'doughnut';
  xAxisColumn: string;
  yAxisColumns: string[];
  xAxisTable: string;
  yAxisTables: string[];
  aggregationType: string | null;
  showChart: boolean;
  isLoading: boolean;
  updateChartState: (updates: any) => void;
  resetChartState: () => void;
  onExecute: (filterConditions?: FilterCondition[]) => void; // Update to accept filter conditions
  onSave: () => void;
  initialFilterConditions?: FilterCondition[];
  // New props for view type selection
  selectedViewType?: 'chart' | 'kpi' | 'datagrid';
  onViewTypeChange?: (view: 'chart' | 'kpi' | 'datagrid') => void;
}

const ChartConfigurationPanel: React.FC<ChartConfigurationPanelProps> = ({
  chartStyle,
  xAxisColumn,
  yAxisColumns,
  xAxisTable,
  yAxisTables,
  aggregationType,
  showChart,
  isLoading,
  updateChartState,
  resetChartState,
  onExecute,
  onSave,
  initialFilterConditions = [],
  selectedViewType = 'chart',
  onViewTypeChange
}) => {
  const handleXAxisDrop = (e: React.DragEvent) => {
    e.preventDefault();
    
    // Try to get column data from different formats
    let columnName = '';
    let columnType = '';
    let tableName = '';
    
    // First try to get individual properties
    columnName = e.dataTransfer.getData('column-name');
    columnType = e.dataTransfer.getData('column-type');
    tableName = e.dataTransfer.getData('table-name');
    
    // If that fails, try to get JSON data
    if (!columnName) {
      try {
        const jsonData = e.dataTransfer.getData('application/json');
        if (jsonData) {
          const columnData = JSON.parse(jsonData);
          columnName = columnData.name;
          columnType = columnData.type;
        }
      } catch (error) {
        console.error('Error parsing JSON data:', error);
      }
    }
    
    // If all else fails, use plain text
    if (!columnName) {
      columnName = e.dataTransfer.getData('text/plain');
    }
    
    if (columnName) {
      updateChartState({
        xAxisColumn: columnName,
        xAxisTable: tableName
      });
    }
  };

  const handleYAxisDrop = (e: React.DragEvent) => {
    e.preventDefault();
    
    // Try to get column data from different formats
    let columnName = '';
    let columnType = '';
    let tableName = '';
    
    // First try to get individual properties
    columnName = e.dataTransfer.getData('column-name');
    columnType = e.dataTransfer.getData('column-type');
    tableName = e.dataTransfer.getData('table-name');
    
    // If that fails, try to get JSON data
    if (!columnName) {
      try {
        const jsonData = e.dataTransfer.getData('application/json');
        if (jsonData) {
          const columnData = JSON.parse(jsonData);
          columnName = columnData.name;
          columnType = columnData.type;
        }
      } catch (error) {
        console.error('Error parsing JSON data:', error);
      }
    }
    
    // If all else fails, use plain text
    if (!columnName) {
      columnName = e.dataTransfer.getData('text/plain');
    }
    
    if (columnName && !yAxisColumns.includes(columnName)) {
      updateChartState({
        yAxisColumns: [...yAxisColumns, columnName],
        yAxisTables: [...yAxisTables, tableName]
      });
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  return (
    <ChartControls
      chartStyle={chartStyle}
      setChartStyle={(style) => updateChartState({ chartStyle: style })}
      xAxisColumn={xAxisColumn}
      setXAxisColumn={(column) => updateChartState({ xAxisColumn: column })}
      yAxisColumns={yAxisColumns}
      setYAxisColumns={(columns) => updateChartState({ yAxisColumns: columns })}
      aggregationType={aggregationType}
      setAggregationType={(type) => updateChartState({ aggregationType: type })}
      isLoadingTableData={isLoading}
      handleExecute={onExecute} // Pass the updated onExecute that accepts filter conditions
      handleClearFilters={resetChartState}
      showChart={showChart}
      handleXAxisDrop={handleXAxisDrop}
      handleYAxisDrop={handleYAxisDrop}
      handleDragOver={handleDragOver}
      handleSaveButtonClick={onSave}
      xAxisTable={xAxisTable}
      setXAxisTable={(table) => updateChartState({ xAxisTable: table })}
      yAxisTables={yAxisTables}
      setYAxisTables={(tables) => updateChartState({ yAxisTables: tables })}
      initialFilterConditions={initialFilterConditions} // Pass filter conditions
      selectedViewType={selectedViewType}
      onViewTypeChange={onViewTypeChange}
    />
  );
};

export default ChartConfigurationPanel;
